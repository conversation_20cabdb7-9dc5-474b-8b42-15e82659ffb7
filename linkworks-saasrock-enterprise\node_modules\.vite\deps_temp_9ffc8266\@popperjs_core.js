import {
  afterMain,
  afterRead,
  afterWrite,
  applyStyles_default,
  arrow_default,
  auto,
  basePlacements,
  beforeMain,
  beforeRead,
  beforeWrite,
  bottom,
  clippingParents,
  computeStyles_default,
  createPopper,
  createPopper2,
  createPopper3,
  detectOverflow,
  end,
  eventListeners_default,
  flip_default,
  hide_default,
  left,
  main,
  modifierPhases,
  offset_default,
  placements,
  popper,
  popperGenerator,
  popperOffsets_default,
  preventOverflow_default,
  read,
  reference,
  right,
  start,
  top,
  variationPlacements,
  viewport,
  write
} from "./chunk-T5OIEPFY.js";
import "./chunk-PLDDJCW6.js";
export {
  afterMain,
  afterRead,
  afterWrite,
  applyStyles_default as applyStyles,
  arrow_default as arrow,
  auto,
  basePlacements,
  beforeMain,
  beforeRead,
  beforeWrite,
  bottom,
  clippingParents,
  computeStyles_default as computeStyles,
  createPopper3 as createPopper,
  createPopper as createPopperBase,
  createPopper2 as createPopperLite,
  detectOverflow,
  end,
  eventListeners_default as eventListeners,
  flip_default as flip,
  hide_default as hide,
  left,
  main,
  modifierPhases,
  offset_default as offset,
  placements,
  popper,
  popperGenerator,
  popperOffsets_default as popperOffsets,
  preventOverflow_default as preventOverflow,
  read,
  reference,
  right,
  start,
  top,
  variationPlacements,
  viewport,
  write
};
//# sourceMappingURL=@popperjs_core.js.map
