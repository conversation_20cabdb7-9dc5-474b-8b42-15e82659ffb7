import { Link,useSubmit } from "react-router";
import { useTranslation } from "react-i18next";
import TableSimple from "~/components/ui/tables/TableSimple";
import DateUtils from "~/utils/shared/DateUtils";
import { StepFormWizardWithDetails } from "../db/stepFormWizard.db.server";
import StepFormWizardBadge from "./StepFormWizardBadge";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { useRef } from "react";
export default function StepFormWizardsList({ items, groupByStatus }: { items: StepFormWizardWithDetails[]; groupByStatus: { status: string; count: number }[] }) {
  const { t } = useTranslation();
  const submit = useSubmit();
  const refConfirm = useRef<RefConfirmModal>(null);
  
  const handleDelete = (id: string) => {
    refConfirm.current?.setValue(id);
    refConfirm.current?.show(
      t("stepFormWizard.prompts.deleteStepFormWizard.title"),
      t("shared.confirm"),
      t("shared.cancel"),
      t("stepFormWizard.prompts.deleteStepFormWizard.description")
    );
  };

  const onConfirmDelete = (id: string) => {
    const form = new FormData();
    form.set("action", "delete");
    form.set("id", id);
    submit(form, { method: "post" });
  };
  return (
    <>
    <TableSimple
      items={items}
      actions={[
        { title: t("shared.overview"), onClickRoute: (_, i) => `${i.id}` },
        {
          title: t("shared.delete"),
          destructive: true,
          onClick: (_, i) => handleDelete(i.id),
          renderIsDestructive: () => true,
          renderTitle: () => t("shared.delete"),
        },
      ]}
      headers={[
        {
          name: "title",
          title: t("stepFormWizard.object.title"),
          className: "w-full",
          value: (i) => (
            <Link to={`/admin/step-form-wizard/step-form-wizards/${i.id}`} className="group flex flex-col">
              <div className="flex items-center space-x-2">
                <div className="text-base font-bold group-hover:underline">{i.title}</div>
                <div>
                  <StepFormWizardBadge item={i} />
                </div>
                {i.createdAt ? (
                  <>
                    <div>•</div>
                    <div className="text-muted-foreground text-sm">
                      <span>{DateUtils.dateAgo(i.createdAt)}</span>
                    </div>
                  </>
                ) : null}
              </div>
              <div className="flex items-center space-x-2">
                <div className="text-muted-foreground text-sm">
                  {i.sessions.length} {t("stepFormWizard.session.plural").toLowerCase()}
                </div>
                <div>•</div>
                <div className="text-muted-foreground text-sm">
                  {i.sessions.filter((f) => f.status === "active").length} {t("stepFormWizard.object.sessions.active").toLowerCase()}
                </div>
                <div>•</div>
                <div className="text-muted-foreground text-sm">
                  {i.sessions.filter((f) => f.status === "started").length} {t("stepFormWizard.object.sessions.started").toLowerCase()}
                </div>
                <div>•</div>
                <div className="text-muted-foreground text-sm">
                  {i.sessions.filter((f) => f.status === "dismissed").length} {t("stepFormWizard.object.sessions.dismissed").toLowerCase()}
                </div>
                <div>•</div>
                <div className="text-muted-foreground text-sm">
                  {i.sessions.filter((f) => f.status === "completed").length} {t("stepFormWizard.object.sessions.completed").toLowerCase()}
                </div>
              </div>
            </Link>
          ),
        },
      ]}
      noRecords={
        <div className="p-12 text-center">
          <h3 className="text-foreground mt-1 text-sm font-medium">{t("stepFormWizard.object.empty.title")}</h3>
          <p className="text-muted-foreground mt-1 text-sm">{t("stepFormWizard.object.empty.description")}</p>
        </div>
      }
    />
          <ConfirmModal ref={refConfirm} onYes={onConfirmDelete} />
    </>
  );
}
