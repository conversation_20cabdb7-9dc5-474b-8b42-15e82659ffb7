import { Form, useActionData, useSubmit, useNavigation } from "react-router";
import clsx from "clsx";
import { FormEvent, forwardRef, ReactNode, Ref, useEffect, useImperativeHandle, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import ButtonSecondary from "../buttons/ButtonSecondary";
import LoadingButton from "../buttons/LoadingButton";
import ConfirmModal, { RefConfirmModal } from "../modals/ConfirmModal";
import ErrorModal, { RefErrorModal } from "../modals/ErrorModal";
import InfoBanner from "../banners/InfoBanner";
import ErrorBanner from "../banners/ErrorBanner";
import PageProgressSteps from "~/custom/components/PageProgressSteps/PageProgressSteps";
import ButtonPrimary from "../buttons/ButtonPrimary";
import Stepper from "~/custom/components/CreatePageStepper";
import Modal from "../modals/Modal";
export interface RefFormGroup {
  submitForm: () => void;
}

interface Props {
  id?: string | undefined;
  isDrawer?: boolean;
  onCancel?: () => void;
  entity?: any;
  children: ReactNode;
  className?: string;
  classNameFooter?: string;
  editing?: boolean;
  canUpdate?: boolean;
  completedStepIndex?: number;
  canDelete?: boolean;
  canSubmit?: boolean;
  onSubmit?: (e: FormData) => void | undefined;
  onDelete?: () => void;
  onCreatedRedirect?: string;
  confirmationPrompt?: {
    title: string;
    yesTitle?: string;
    noTitle?: string;
    description?: string;
  };
  deleteRedirect?: string;
  actionNames?: {
    create?: string;
    update?: string;
    delete?: string;
  };
  state?: { loading?: boolean; submitting?: boolean };
  message?: {
    success?: string;
    error?: string;
  };
  labels?: {
    create?: string;
  };
  withErrorModal?: boolean;
  submitDisabled?: boolean;
  steps?: any[];
  currentStepIndex?: number | null;
  currentStep?: any;
  handleNext?: () => void;
  handleBack?: () => void;
  handleComplete?: () => void;
  handleSaveForLater?: () => void;
}

const FormGroup = (
  {
    id,
    onCancel,
    isDrawer,
    entity,
    children,
    className,
    classNameFooter,
    editing,
    canUpdate = true,
    canDelete = true,
    canSubmit = true,
    confirmationPrompt,
    onSubmit,
    onCreatedRedirect,
    completedStepIndex = 0,
    deleteRedirect,
    onDelete,
    actionNames,
    state,
    message,
    labels,
    withErrorModal = true,
    submitDisabled,
    currentStepIndex = null,
    steps = [],
    currentStep = null,
    handleBack = () => {},
    handleNext = () => {},
    handleComplete = () => {},
    handleSaveForLater = () => {},
  }: Props,
  ref: Ref<RefFormGroup>
) => {
  const { t } = useTranslation();

  const formRef = useRef<HTMLFormElement>(null);
  useImperativeHandle(ref, () => ({
    submitForm,
  }));
  function submitForm() {
    const formData = new FormData(formRef.current!);
    if (onSubmit !== undefined) {
      onSubmit(formData);
    } else {
      submit(formData, {
        method: "post",
      });
    }
  }
  const actionData = useActionData<{
    error?: string;
  }>();
  const navigation = useNavigation();
  const loading = navigation.state === "submitting" || state?.submitting;
  const submit = useSubmit();

  const confirmRemove = useRef<RefConfirmModal>(null);
  const confirmSubmit = useRef<RefConfirmModal>(null);
  const errorModal = useRef<RefErrorModal>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [error, setError] = useState<string>();
  const [formData, setFormData] = useState<FormData>();

  useEffect(() => {
    setError(actionData?.error);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actionData]);

  useEffect(() => {
    setError(undefined);
    if (error) {
      errorModal.current?.show(t("shared.error"), error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error]);

  function remove() {
    confirmRemove.current?.show(t("shared.confirmDelete"), t("shared.delete"), t("shared.cancel"), t("shared.warningCannotUndo"));
  }

  function yesRemove() {
    if (onDelete) {
      onDelete();
    } else {
      const form = new FormData();
      form.set("action", actionNames?.delete ?? "delete");
      form.set("id", id ?? "");
      form.set("redirect", deleteRedirect ?? "");
      submit(form, {
        method: "post",
      });
    }
  }

  function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.stopPropagation();
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    if (confirmationPrompt) {
      setFormData(formData);
      confirmSubmit.current?.show(confirmationPrompt.title, confirmationPrompt.yesTitle, confirmationPrompt.noTitle, confirmationPrompt.description);
    } else {
      if (onSubmit !== undefined) {
        onSubmit(formData);
      } else {
        submit(formData, {
          method: "post",
        });
      }
    }
  }

  function yesSubmit() {
    if (formData) {
      if (onSubmit !== undefined) {
        onSubmit(formData);
      } else {
        submit(formData, {
          method: "post",
        });
      }
    }
  }

  return (
    <>
      <div className="sticky top-0 z-50 px-4">
        {entity?.isStepWizard && isDrawer ? (
          <div
            style={{ boxShadow: "0px 4px 14.9px 0px #B7B7B740" }}
            className="bg-card border-input mb-4 flex h-[57px] w-full justify-center rounded-[8px] border-[1px] p-4"
          >
            <Stepper steps={steps} currentStep={currentStepIndex} handleCurrentStep={() => {}} />
          </div>
        ) : (
          <></>
        )}
      </div>

      <Form ref={formRef} method="post" acceptCharset="utf-8" className={clsx(className, "py-1")} onSubmit={handleSubmit}>
        <input type="hidden" name="action" value={id ? (actionNames?.update ?? "edit") : (actionNames?.create ?? "create")} hidden readOnly />
        <input type="hidden" name="id" value={id ?? ""} hidden readOnly />
        <div className="space-y-3">
          {children}

          {(!id || editing) && canSubmit && (
            <div
              className={clsx(
                classNameFooter,
                isDrawer && "absolute right-0 bottom-0 left-0 z-50 flex justify-between space-x-2 border-t border-[#E5E5E5] bg-white px-5"
              )}
            >
              <Modal open={isModalOpen} setOpen={() => setIsModalOpen(true)}>
                <div className="p-4">
                  <h2 className="mb-4 text-lg font-semibold">Are you sure you want to save and exit?</h2>

                  <div className="flex justify-end gap-3">
                    <ButtonSecondary onClick={() => setIsModalOpen(false)} className="cursor-pointer border border-[#E6E6E6]">
                      <div>{t("shared.cancel")}</div>
                    </ButtonSecondary>

                    <ButtonPrimary
                      onClick={() => {
                        setIsModalOpen(false);
                        handleSaveForLater();
                      }} // your logic here
                    >
                      Yes
                    </ButtonPrimary>
                  </div>
                </div>
              </Modal>

              {entity?.isStepFormWizard ? (
                <div className="my-4 flex w-full justify-between">
                  <div className="flex">
                    <ButtonSecondary
                      onClick={() => setIsModalOpen(true)}
                      className="rounded border border-gray-400 px-4 py-2 text-gray-700 disabled:opacity-50"
                    >
                      Save and Exit
                    </ButtonSecondary>
                  </div>
                  <div className="flex justify-between gap-5">
                    {/* <ButtonSecondary
                      onClick={handleBack}
                      disabled={currentStepIndex === 0}
                      className="px-4 py-2 border border-gray-400 text-gray-700 rounded disabled:opacity-50"
                    >
                      Back
                    </ButtonSecondary> */}
                    <ButtonSecondary
                      onClick={handleBack}
                      disabled={currentStepIndex === 0}
                      className="!cursor-pointer rounded border border-gray-400 px-4 py-2 text-gray-700 disabled:opacity-50"
                    >
                      Back
                    </ButtonSecondary>
                    {currentStepIndex === steps.length - 1 ? (
                      <ButtonPrimary onClick={handleComplete} className="cursor-pointer rounded px-4 py-2 text-white">
                        Complete
                      </ButtonPrimary>
                    ) : (
                      <ButtonPrimary
                        onClick={handleNext}
                        disabled={currentStepIndex === steps.length - 1}
                        className="cursor-pointer rounded px-4 py-2 text-white"
                      >
                        Next
                      </ButtonPrimary>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex w-full items-end justify-between gap-2 py-3">
                    <div className="flex-1">
                      {id && canDelete && (
                        <ButtonSecondary disabled={loading || !canDelete} destructive={true} type="button" className="cursor-pointer" onClick={remove}>
                          <div>{t("shared.delete")}</div>
                        </ButtonSecondary>
                      )}
                    </div>
                    <div className="flex items-end gap-3">
                      <div>
                        {onCancel && (
                          <ButtonSecondary onClick={onCancel} className="cursor-pointer border border-[#E6E6E6]">
                            <div>{t("shared.cancel")}</div>
                          </ButtonSecondary>
                        )}
                      </div>

                      <div>
                        {id === undefined && onCreatedRedirect === "addAnother" ? (
                          <LoadingButton isLoading={state?.submitting} type="submit" disabled={loading || submitDisabled}>
                            <div>{t("shared.saveAndAdd")}</div>
                          </LoadingButton>
                        ) : (
                          <LoadingButton isLoading={state?.submitting} type="submit" disabled={loading || (id !== undefined && !canUpdate) || submitDisabled}>
                            {labels?.create ?? t("shared.saveDetails")}
                          </LoadingButton>
                        )}
                      </div>
                    </div>
                  </div>

                  {message && (
                    <div>
                      {<InfoBanner title={t("shared.success")} text={message.success} />}
                      {<ErrorBanner title={t("shared.error")} text={message.error} />}
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
        <ConfirmModal ref={confirmSubmit} onYes={yesSubmit} />
        <ConfirmModal ref={confirmRemove} onYes={yesRemove} destructive />
        {withErrorModal && canSubmit && <ErrorModal ref={errorModal} />}
      </Form>
    </>
  );
};

export default forwardRef(FormGroup);
