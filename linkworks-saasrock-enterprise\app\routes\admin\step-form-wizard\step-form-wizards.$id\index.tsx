import { ActionFunction, LoaderFunctionArgs, MetaFunction, redirect, useLoaderData } from "react-router";
import { Form, useActionData, useSubmit, Link } from "react-router";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import InfoBanner from "~/components/ui/banners/InfoBanner";
import WarningBanner from "~/components/ui/banners/WarningBanner";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import ServerError from "~/components/ui/errors/ServerError";
import InputGroup from "~/components/ui/forms/InputGroup";
import InputText from "~/components/ui/input/InputText";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { getTranslations } from "~/locale/i18next.server";
import {
  StepFormWizardWithDetails,
  getStepFormWizard,
  updateStepFormWizard,
  deleteStepFormWizard,
} from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import { StepFormWizardFilterMetadataDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterMetadataDto";
import StepFormWizardService from "~/custom/modules/stepFormWizard/services/StepFormWizardService";
import StepFormWizardFilterUtils from "~/custom/modules/stepFormWizard/utils/StepFormWizardFilterUtils";
import StepFormWizardStepUtils from "~/custom/modules/stepFormWizard/utils/StepFormWizardStepUtils";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";
import { getUserHasPermission } from "~/utils/helpers/PermissionsHelper";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
type LoaderData = {
  meta: MetaTagsDto;
  item: StepFormWizardWithDetails;
  metadata: StepFormWizardFilterMetadataDto;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  const data: LoaderData = {
    meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
    item,
    metadata: await StepFormWizardService.getMetadata(),
  };
  return data;
};

type ActionData = {
  error?: string;
  success?: string;
};
export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (action === "update") {
    const title = form.get("title")?.toString();
    await updateStepFormWizard(item.id, {
      title: title !== undefined ? title : undefined,
    });
    return Response.json({ success: "Step form wizard updated" });
  } else if (action === "activate") {
    const active = form.get("active");
    if (active === undefined) {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
    }
    const isActive = active !== undefined ? active === "true" : undefined;
    await updateStepFormWizard(item.id, {
      active: isActive,
    });
    if (isActive) {
      return Response.json({ success: "Step form wizard activated" });
    } else {
      return Response.json({ success: "Step form wizard deactivated" });
    }
  } else if (action === "delete") {
    await verifyUserHasPermission(request, "admin.stepFormWizard.delete");
    await deleteStepFormWizard(item.id);
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();
  const appOrAdminData = useAppOrAdminData();

  const [title, setTitle] = useState(data.item.title);

  const modalConfirm = useRef<RefConfirmModal>(null);

  function onActivate(active: boolean) {
    modalConfirm.current?.setValue(active);
    if (active) {
      modalConfirm.current?.show(
        t("stepFormWizard.prompts.activate.title"),
        t("shared.confirm"),
        t("shared.back"),
        t("stepFormWizard.prompts.activate.description")
      );
    } else {
      modalConfirm.current?.show(
        t("stepFormWizard.prompts.deactivate.title"),
        t("shared.confirm"),
        t("shared.back"),
        t("stepFormWizard.prompts.deactivate.description")
      );
    }
  }

  function onConfirmActivate(active: boolean) {
    const form = new FormData();
    form.set("action", "activate");
    form.set("active", active ? "true" : "false");
    submit(form, {
      method: "post",
    });
  }

  function canBeActivated() {
    if (data.item.filters.length > 0 && data.item.steps.length > 0) {
      return true;
    }
  }
  function canBeInactivated() {
    return true;
  }
  return (
    <div className="mx-auto max-w-2xl flex-1 space-y-5 overflow-x-auto px-2 py-2 xl:overflow-y-auto">
      {data.item.active && <InfoBanner title={t("shared.active")} text="This step form wizard is active and will be shown to users." />}

      <InputGroup title={t("shared.details")}>
        <Form method="post" className="divide-y-gray-200 space-y-4 divide-y">
          <input name="action" value="update" hidden readOnly />
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-6">
            <div className="sm:col-span-6">
              <InputText disabled={data.item.active} name="title" title={t("stepFormWizard.object.title")} value={title} setValue={setTitle} required />
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <ButtonSecondary disabled={data.item.active || !getUserHasPermission(appOrAdminData, "admin.stepFormWizard.update")} type="submit">
              {t("shared.save")}
            </ButtonSecondary>
          </div>
        </Form>
      </InputGroup>

      <InputGroup
        title={t("stepFormWizard.step.plural")}
        right={
          <>
            <Link
              to={`/admin/step-form-wizard/step-form-wizards/${data.item.id}/steps`}
              className="text-muted-foreground hover:text-foreground text-sm font-medium hover:underline"
            >
              {t("stepFormWizard.step.set")}
            </Link>
          </>
        }
      >
        <div className="space-y-2">
          {data.item.steps.length === 0 && (
            <Link
              to={`/admin/step-form-wizard/step-form-wizards/${data.item.id}/steps`}
              className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-4 text-center focus:ring-2 focus:ring-gray-500 focus:outline-hidden"
            >
              <span className="text-muted-foreground block text-xs font-normal">{t("stepFormWizard.step.empty.title")}</span>
            </Link>
          )}
          {data.item.steps.map((step, idx) => {
            return (
              <div key={idx} className="border-border bg-secondary relative block w-full rounded-lg border-2 border-dashed p-3 text-center">
                {StepFormWizardStepUtils.getStepDescription(StepFormWizardStepUtils.parseStepToBlock(step))}
              </div>
            );
          })}
        </div>
      </InputGroup>

      <InputGroup
        title={t("stepFormWizard.filter.plural")}
        right={
          <>
            <Link
              to={`/admin/step-form-wizard/step-form-wizards/${data.item.id}/filters`}
              className="text-muted-foreground hover:text-foreground text-sm font-medium hover:underline"
            >
              {t("stepFormWizard.filter.set")}
            </Link>
          </>
        }
      >
        <div className="space-y-2">
          {data.item.filters.length === 0 && (
            <Link
              to={`/admin/step-form-wizard/step-form-wizards/${data.item.id}/filters`}
              className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-4 text-center focus:ring-2 focus:ring-gray-500 focus:outline-hidden"
            >
              <span className="text-muted-foreground block text-xs font-normal">{t("stepFormWizard.filter.empty.title")}</span>
            </Link>
          )}
          {data.item.filters.map((filter, idx) => {
            return (
              <div key={idx} className="border-border bg-secondary relative block w-full rounded-lg border-2 border-dashed p-3 text-center">
                <div className="flex items-center space-x-2 text-sm">
                  <div className="font-medium">{filter.type}</div>
                  {filter.value !== null && (
                    <>
                      <div>→</div>
                      <div className="text-muted-foreground italic">{StepFormWizardFilterUtils.parseValue({ t, filter, metadata: data.metadata })}</div>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </InputGroup>

      {!canBeActivated() && (
        <WarningBanner title={t("stepFormWizard.errors.cannotBeActivated.title")} text={t("stepFormWizard.errors.cannotBeActivated.description")} />
      )}

      <div className="flex justify-between">
        <div></div>
        <div className="flex justify-between space-x-2">
          {!data.item.active ? (
            <ButtonPrimary
              disabled={!canBeActivated() || !getUserHasPermission(appOrAdminData, "admin.stepFormWizard.update")}
              onClick={() => onActivate(true)}
              className="bg-teal-600 text-white hover:bg-teal-700"
            >
              {t("stepFormWizard.prompts.activate.title")}
            </ButtonPrimary>
          ) : (
            <ButtonPrimary
              destructive
              disabled={!canBeInactivated() || !getUserHasPermission(appOrAdminData, "admin.stepFormWizard.update")}
              onClick={() => onActivate(false)}
            >
              {t("stepFormWizard.prompts.deactivate.title")}
            </ButtonPrimary>
          )}
        </div>
      </div>

      <ConfirmModal ref={modalConfirm} onYes={onConfirmActivate} />
      <ActionResultModal actionData={actionData} />
    </div>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
