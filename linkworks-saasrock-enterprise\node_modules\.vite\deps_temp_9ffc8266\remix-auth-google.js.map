{"version": 3, "sources": ["../../debug/src/common.js", "../../debug/src/browser.js", "../../uuid/dist/commonjs-browser/rng.js", "../../uuid/dist/commonjs-browser/regex.js", "../../uuid/dist/commonjs-browser/validate.js", "../../uuid/dist/commonjs-browser/stringify.js", "../../uuid/dist/commonjs-browser/v1.js", "../../uuid/dist/commonjs-browser/parse.js", "../../uuid/dist/commonjs-browser/v35.js", "../../uuid/dist/commonjs-browser/md5.js", "../../uuid/dist/commonjs-browser/v3.js", "../../uuid/dist/commonjs-browser/native.js", "../../uuid/dist/commonjs-browser/v4.js", "../../uuid/dist/commonjs-browser/sha1.js", "../../uuid/dist/commonjs-browser/v5.js", "../../uuid/dist/commonjs-browser/nil.js", "../../uuid/dist/commonjs-browser/version.js", "../../uuid/dist/commonjs-browser/index.js", "../../remix-auth-oauth2/build/index.js", "../../remix-auth-google/build/index.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(/\\s+/g, ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug') || exports.storage.getItem('DEBUG') ;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = rng;\n// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\n\nfunction rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _regex = _interopRequireDefault(require(\"./regex.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex.default.test(uuid);\n}\n\nvar _default = validate;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.unsafeStringify = unsafeStringify;\n\nvar _validate = _interopRequireDefault(require(\"./validate.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0, _validate.default)(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nvar _default = stringify;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _rng = _interopRequireDefault(require(\"./rng.js\"));\n\nvar _stringify = require(\"./stringify.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || _rng.default)();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || (0, _stringify.unsafeStringify)(b);\n}\n\nvar _default = v1;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _validate = _interopRequireDefault(require(\"./validate.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction parse(uuid) {\n  if (!(0, _validate.default)(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nvar _default = parse;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.URL = exports.DNS = void 0;\nexports.default = v35;\n\nvar _stringify = require(\"./stringify.js\");\n\nvar _parse = _interopRequireDefault(require(\"./parse.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nconst DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexports.DNS = DNS;\nconst URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexports.URL = URL;\n\nfunction v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = (0, _parse.default)(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return (0, _stringify.unsafeStringify)(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\n/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  const output = [];\n  const length32 = input.length * 32;\n  const hexTab = '0123456789abcdef';\n\n  for (let i = 0; i < length32; i += 8) {\n    const x = input[i >> 5] >>> i % 32 & 0xff;\n    const hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  let a = 1732584193;\n  let b = -271733879;\n  let c = -1732584194;\n  let d = 271733878;\n\n  for (let i = 0; i < x.length; i += 16) {\n    const olda = a;\n    const oldb = b;\n    const oldc = c;\n    const oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  const length8 = input.length * 8;\n  const output = new Uint32Array(getOutputLength(length8));\n\n  for (let i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  const lsw = (x & 0xffff) + (y & 0xffff);\n  const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nvar _default = md5;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _v = _interopRequireDefault(require(\"./v35.js\"));\n\nvar _md = _interopRequireDefault(require(\"./md5.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nconst v3 = (0, _v.default)('v3', 0x30, _md.default);\nvar _default = v3;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nvar _default = {\n  randomUUID\n};\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _native = _interopRequireDefault(require(\"./native.js\"));\n\nvar _rng = _interopRequireDefault(require(\"./rng.js\"));\n\nvar _stringify = require(\"./stringify.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction v4(options, buf, offset) {\n  if (_native.default.randomUUID && !buf && !options) {\n    return _native.default.randomUUID();\n  }\n\n  options = options || {};\n\n  const rnds = options.random || (options.rng || _rng.default)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0, _stringify.unsafeStringify)(rnds);\n}\n\nvar _default = v4;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\n// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n\n    M[i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nvar _default = sha1;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _v = _interopRequireDefault(require(\"./v35.js\"));\n\nvar _sha = _interopRequireDefault(require(\"./sha1.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nconst v5 = (0, _v.default)('v5', 0x50, _sha.default);\nvar _default = v5;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = '00000000-0000-0000-0000-000000000000';\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _validate = _interopRequireDefault(require(\"./validate.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction version(uuid) {\n  if (!(0, _validate.default)(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.slice(14, 15), 16);\n}\n\nvar _default = version;\nexports.default = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"NIL\", {\n  enumerable: true,\n  get: function get() {\n    return _nil.default;\n  }\n});\nObject.defineProperty(exports, \"parse\", {\n  enumerable: true,\n  get: function get() {\n    return _parse.default;\n  }\n});\nObject.defineProperty(exports, \"stringify\", {\n  enumerable: true,\n  get: function get() {\n    return _stringify.default;\n  }\n});\nObject.defineProperty(exports, \"v1\", {\n  enumerable: true,\n  get: function get() {\n    return _v.default;\n  }\n});\nObject.defineProperty(exports, \"v3\", {\n  enumerable: true,\n  get: function get() {\n    return _v2.default;\n  }\n});\nObject.defineProperty(exports, \"v4\", {\n  enumerable: true,\n  get: function get() {\n    return _v3.default;\n  }\n});\nObject.defineProperty(exports, \"v5\", {\n  enumerable: true,\n  get: function get() {\n    return _v4.default;\n  }\n});\nObject.defineProperty(exports, \"validate\", {\n  enumerable: true,\n  get: function get() {\n    return _validate.default;\n  }\n});\nObject.defineProperty(exports, \"version\", {\n  enumerable: true,\n  get: function get() {\n    return _version.default;\n  }\n});\n\nvar _v = _interopRequireDefault(require(\"./v1.js\"));\n\nvar _v2 = _interopRequireDefault(require(\"./v3.js\"));\n\nvar _v3 = _interopRequireDefault(require(\"./v4.js\"));\n\nvar _v4 = _interopRequireDefault(require(\"./v5.js\"));\n\nvar _nil = _interopRequireDefault(require(\"./nil.js\"));\n\nvar _version = _interopRequireDefault(require(\"./version.js\"));\n\nvar _validate = _interopRequireDefault(require(\"./validate.js\"));\n\nvar _stringify = _interopRequireDefault(require(\"./stringify.js\"));\n\nvar _parse = _interopRequireDefault(require(\"./parse.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.OAuth2Strategy = void 0;\nconst server_runtime_1 = require(\"@remix-run/server-runtime\");\nconst debug_1 = __importDefault(require(\"debug\"));\nconst remix_auth_1 = require(\"remix-auth\");\nconst uuid_1 = require(\"uuid\");\nlet debug = (0, debug_1.default)(\"OAuth2Strategy\");\n/**\n * The OAuth 2.0 authentication strategy authenticates requests using the OAuth\n * 2.0 framework.\n *\n * OAuth 2.0 provides a facility for delegated authentication, whereby users can\n * authenticate using a third-party service such as Facebook.  Delegating in\n * this manner involves a sequence of events, including redirecting the user to\n * the third-party service for authorization.  Once authorization has been\n * granted, the user is redirected back to the application and an authorization\n * code can be used to obtain credentials.\n *\n * Applications must supply a `verify` callback, for which the function\n * signature is:\n *\n *     function(accessToken, refreshToken, profile) { ... }\n *\n * The verify callback is responsible for finding or creating the user, and\n * returning the resulting user object.\n *\n * An AuthorizationError should be raised to indicate an authentication failure.\n *\n * Options:\n * - `authorizationURL`  URL used to obtain an authorization grant\n * - `tokenURL`          URL used to obtain an access token\n * - `clientID`          identifies client to service provider\n * - `clientSecret`      secret used to establish ownership of the client identifier\n * - `callbackURL`       URL to which the service provider will redirect the user after obtaining authorization\n *\n * @example\n * authenticator.use(new OAuth2Strategy(\n *   {\n *     authorizationURL: 'https://www.example.com/oauth2/authorize',\n *     tokenURL: 'https://www.example.com/oauth2/token',\n *     clientID: '123-456-789',\n *     clientSecret: 'shhh-its-a-secret'\n *     callbackURL: 'https://www.example.net/auth/example/callback'\n *   },\n *   async ({ accessToken, refreshToken, profile }) => {\n *     return await User.findOrCreate(...);\n *   }\n * ));\n */\nclass OAuth2Strategy extends remix_auth_1.Strategy {\n    constructor(options, verify) {\n        var _a, _b;\n        super(verify);\n        this.name = \"oauth2\";\n        this.sessionStateKey = \"oauth2:state\";\n        this.authorizationURL = options.authorizationURL;\n        this.tokenURL = options.tokenURL;\n        this.clientID = options.clientID;\n        this.clientSecret = options.clientSecret;\n        this.callbackURL = options.callbackURL;\n        this.scope = options.scope;\n        this.responseType = (_a = options.responseType) !== null && _a !== void 0 ? _a : \"code\";\n        this.useBasicAuthenticationHeader =\n            (_b = options.useBasicAuthenticationHeader) !== null && _b !== void 0 ? _b : false;\n    }\n    async authenticate(request, sessionStorage, options) {\n        var _a;\n        debug(\"Request URL\", request.url);\n        let url = new URL(request.url);\n        let session = await sessionStorage.getSession(request.headers.get(\"Cookie\"));\n        let user = (_a = session.get(options.sessionKey)) !== null && _a !== void 0 ? _a : null;\n        // User is already authenticated\n        if (user) {\n            debug(\"User is authenticated\");\n            return this.success(user, request, sessionStorage, options);\n        }\n        let callbackURL = this.getCallbackURL(request);\n        debug(\"Callback URL\", callbackURL);\n        // Redirect the user to the callback URL\n        if (url.pathname !== callbackURL.pathname) {\n            debug(\"Redirecting to callback URL\");\n            let state = this.generateState();\n            debug(\"State\", state);\n            session.set(this.sessionStateKey, state);\n            throw (0, server_runtime_1.redirect)(this.getAuthorizationURL(request, state).toString(), {\n                headers: { \"Set-Cookie\": await sessionStorage.commitSession(session) },\n            });\n        }\n        // Validations of the callback URL params\n        let stateUrl = url.searchParams.get(\"state\");\n        debug(\"State from URL\", stateUrl);\n        if (!stateUrl) {\n            return await this.failure(\"Missing state on URL.\", request, sessionStorage, options, new Error(\"Missing state on URL.\"));\n        }\n        let stateSession = session.get(this.sessionStateKey);\n        debug(\"State from session\", stateSession);\n        if (!stateSession) {\n            return await this.failure(\"Missing state on session.\", request, sessionStorage, options, new Error(\"Missing state on session.\"));\n        }\n        if (stateSession === stateUrl) {\n            debug(\"State is valid\");\n            session.unset(this.sessionStateKey);\n        }\n        else {\n            return await this.failure(\"State doesn't match.\", request, sessionStorage, options, new Error(\"State doesn't match.\"));\n        }\n        let code = url.searchParams.get(\"code\");\n        if (!code) {\n            return await this.failure(\"Missing code.\", request, sessionStorage, options, new Error(\"Missing code.\"));\n        }\n        try {\n            // Get the access token\n            let params = new URLSearchParams(this.tokenParams());\n            params.set(\"grant_type\", \"authorization_code\");\n            params.set(\"redirect_uri\", callbackURL.toString());\n            let { accessToken, refreshToken, extraParams } = await this.fetchAccessToken(code, params);\n            // Get the profile\n            let profile = await this.userProfile(accessToken, extraParams);\n            // Verify the user and return it, or redirect\n            user = await this.verify({\n                accessToken,\n                refreshToken,\n                extraParams,\n                profile,\n                context: options.context,\n                request,\n            });\n        }\n        catch (error) {\n            debug(\"Failed to verify user\", error);\n            // Allow responses to pass-through\n            if (error instanceof Response)\n                throw error;\n            if (error instanceof Error) {\n                return await this.failure(error.message, request, sessionStorage, options, error);\n            }\n            if (typeof error === \"string\") {\n                return await this.failure(error, request, sessionStorage, options, new Error(error));\n            }\n            return await this.failure(\"Unknown error\", request, sessionStorage, options, new Error(JSON.stringify(error, null, 2)));\n        }\n        debug(\"User authenticated\");\n        return await this.success(user, request, sessionStorage, options);\n    }\n    /**\n     * Retrieve user profile from service provider.\n     *\n     * OAuth 2.0-based authentication strategies can override this function in\n     * order to load the user's profile from the service provider.  This assists\n     * applications (and users of those applications) in the initial registration\n     * process by automatically submitting required information.\n     */\n    async userProfile(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    accessToken, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    params) {\n        return { provider: \"oauth2\" };\n    }\n    /**\n     * Return extra parameters to be included in the authorization request.\n     *\n     * Some OAuth 2.0 providers allow additional, non-standard parameters to be\n     * included when requesting authorization.  Since these parameters are not\n     * standardized by the OAuth 2.0 specification, OAuth 2.0-based authentication\n     * strategies can override this function in order to populate these\n     * parameters as required by the provider.\n     */\n    authorizationParams(params) {\n        return new URLSearchParams(params);\n    }\n    /**\n     * Return extra parameters to be included in the token request.\n     *\n     * Some OAuth 2.0 providers allow additional, non-standard parameters to be\n     * included when requesting an access token.  Since these parameters are not\n     * standardized by the OAuth 2.0 specification, OAuth 2.0-based authentication\n     * strategies can override this function in order to populate these\n     * parameters as required by the provider.\n     */\n    tokenParams() {\n        return new URLSearchParams();\n    }\n    async getAccessToken(response) {\n        let { access_token, refresh_token, ...extraParams } = await response.json();\n        return {\n            accessToken: access_token,\n            refreshToken: refresh_token,\n            extraParams,\n        };\n    }\n    getCallbackURL(request) {\n        var _a, _b;\n        if (this.callbackURL.startsWith(\"http:\") ||\n            this.callbackURL.startsWith(\"https:\")) {\n            return new URL(this.callbackURL);\n        }\n        let host = (_b = (_a = request.headers.get(\"X-Forwarded-Host\")) !== null && _a !== void 0 ? _a : request.headers.get(\"host\")) !== null && _b !== void 0 ? _b : new URL(request.url).host;\n        let protocol = host.includes(\"localhost\") ? \"http\" : \"https\";\n        if (this.callbackURL.startsWith(\"/\")) {\n            return new URL(this.callbackURL, `${protocol}://${host}`);\n        }\n        return new URL(`${protocol}://${this.callbackURL}`);\n    }\n    getAuthorizationURL(request, state) {\n        let params = new URLSearchParams(this.authorizationParams(new URL(request.url).searchParams));\n        params.set(\"response_type\", this.responseType);\n        params.set(\"client_id\", this.clientID);\n        params.set(\"redirect_uri\", this.getCallbackURL(request).toString());\n        params.set(\"state\", state);\n        // We need to check if `authorizationParams` has not set scopes to avoid regressions on dependent libraries\n        if (!params.has(\"scope\") && this.scope) {\n            params.set(\"scope\", this.scope);\n        }\n        let url = new URL(this.authorizationURL);\n        url.search = params.toString();\n        return url;\n    }\n    generateState() {\n        return (0, uuid_1.v4)();\n    }\n    /**\n     * Format the data to be sent in the request body to the token endpoint.\n     */\n    async fetchAccessToken(code, params) {\n        let headers = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n        };\n        if (this.useBasicAuthenticationHeader) {\n            const b64EncodedCredentials = Buffer.from(`${this.clientID}:${this.clientSecret}`).toString(\"base64\");\n            headers = {\n                ...headers,\n                Authorization: `Basic ${b64EncodedCredentials}`,\n            };\n        }\n        else {\n            params.set(\"client_id\", this.clientID);\n            params.set(\"client_secret\", this.clientSecret);\n        }\n        if (params.get(\"grant_type\") === \"refresh_token\") {\n            params.set(\"refresh_token\", code);\n        }\n        else {\n            params.set(\"code\", code);\n        }\n        let response = await fetch(this.tokenURL, {\n            method: \"POST\",\n            headers,\n            body: params,\n        });\n        if (!response.ok) {\n            let body = await response.text();\n            throw body;\n        }\n        return await this.getAccessToken(response.clone());\n    }\n}\nexports.OAuth2Strategy = OAuth2Strategy;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GoogleStrategy = exports.GoogleStrategyDefaultName = exports.GoogleStrategyDefaultScopes = exports.GoogleStrategyScopeSeperator = void 0;\nconst remix_auth_oauth2_1 = require(\"remix-auth-oauth2\");\nexports.GoogleStrategyScopeSeperator = ' ';\nexports.GoogleStrategyDefaultScopes = [\n    'openid',\n    'https://www.googleapis.com/auth/userinfo.profile',\n    'https://www.googleapis.com/auth/userinfo.email',\n].join(exports.GoogleStrategyScopeSeperator);\nexports.GoogleStrategyDefaultName = 'google';\nclass GoogleStrategy extends remix_auth_oauth2_1.OAuth2Strategy {\n    constructor({ clientID, clientSecret, callbackURL, scope, accessType, includeGrantedScopes, prompt, hd, loginHint, }, verify) {\n        super({\n            clientID,\n            clientSecret,\n            callbackURL,\n            authorizationURL: 'https://accounts.google.com/o/oauth2/v2/auth',\n            tokenURL: 'https://oauth2.googleapis.com/token',\n        }, verify);\n        this.name = exports.GoogleStrategyDefaultName;\n        this.userInfoURL = 'https://www.googleapis.com/oauth2/v3/userinfo';\n        this.scope = this.parseScope(scope);\n        this.accessType = accessType !== null && accessType !== void 0 ? accessType : 'online';\n        this.includeGrantedScopes = includeGrantedScopes !== null && includeGrantedScopes !== void 0 ? includeGrantedScopes : false;\n        this.prompt = prompt;\n        this.hd = hd;\n        this.loginHint = loginHint;\n    }\n    authorizationParams() {\n        const params = new URLSearchParams({\n            access_type: this.accessType,\n            include_granted_scopes: String(this.includeGrantedScopes),\n        });\n        if (this.prompt) {\n            params.set('prompt', this.prompt);\n        }\n        if (this.hd) {\n            params.set('hd', this.hd);\n        }\n        if (this.loginHint) {\n            params.set('login_hint', this.loginHint);\n        }\n        return params;\n    }\n    async userProfile(accessToken) {\n        const response = await fetch(this.userInfoURL, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n            },\n        });\n        const raw = await response.json();\n        const profile = {\n            provider: 'google',\n            id: raw.sub,\n            displayName: raw.name,\n            name: {\n                familyName: raw.family_name,\n                givenName: raw.given_name,\n            },\n            emails: [{ value: raw.email }],\n            photos: [{ value: raw.picture }],\n            _json: raw,\n        };\n        return profile;\n    }\n    // Allow users the option to pass a scope string, or typed array\n    parseScope(scope) {\n        if (!scope) {\n            return exports.GoogleStrategyDefaultScopes;\n        }\n        else if (Array.isArray(scope)) {\n            return scope.join(exports.GoogleStrategyScopeSeperator);\n        }\n        return scope;\n    }\n}\nexports.GoogleStrategy = GoogleStrategy;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAMA,aAAS,MAAM,KAAK;AACnB,kBAAY,QAAQ;AACpB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,WAAW;AACvB,kBAAY,UAAU;AAEtB,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAO;AAC/B,oBAAY,GAAG,IAAI,IAAI,GAAG;AAAA,MAC3B,CAAC;AAMD,kBAAY,QAAQ,CAAC;AACrB,kBAAY,QAAQ,CAAC;AAOrB,kBAAY,aAAa,CAAC;AAQ1B,eAAS,YAAY,WAAW;AAC/B,YAAI,OAAO;AAEX,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,kBAAS,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACpD,kBAAQ;AAAA,QACT;AAEA,eAAO,YAAY,OAAO,KAAK,IAAI,IAAI,IAAI,YAAY,OAAO,MAAM;AAAA,MACrE;AACA,kBAAY,cAAc;AAS1B,eAAS,YAAY,WAAW;AAC/B,YAAI;AACJ,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAEJ,iBAAS,SAAS,MAAM;AAEvB,cAAI,CAAC,MAAM,SAAS;AACnB;AAAA,UACD;AAEA,gBAAM,OAAO;AAGb,gBAAM,OAAO,OAAO,oBAAI,KAAK,CAAC;AAC9B,gBAAM,KAAK,QAAQ,YAAY;AAC/B,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,qBAAW;AAEX,eAAK,CAAC,IAAI,YAAY,OAAO,KAAK,CAAC,CAAC;AAEpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAEhC,iBAAK,QAAQ,IAAI;AAAA,UAClB;AAGA,cAAI,QAAQ;AACZ,eAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,CAAC,OAAO,WAAW;AAE7D,gBAAI,UAAU,MAAM;AACnB,qBAAO;AAAA,YACR;AACA;AACA,kBAAM,YAAY,YAAY,WAAW,MAAM;AAC/C,gBAAI,OAAO,cAAc,YAAY;AACpC,oBAAM,MAAM,KAAK,KAAK;AACtB,sBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,mBAAK,OAAO,OAAO,CAAC;AACpB;AAAA,YACD;AACA,mBAAO;AAAA,UACR,CAAC;AAGD,sBAAY,WAAW,KAAK,MAAM,IAAI;AAEtC,gBAAM,QAAQ,KAAK,OAAO,YAAY;AACtC,gBAAM,MAAM,MAAM,IAAI;AAAA,QACvB;AAEA,cAAM,YAAY;AAClB,cAAM,YAAY,YAAY,UAAU;AACxC,cAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,cAAM,SAAS;AACf,cAAM,UAAU,YAAY;AAE5B,eAAO,eAAe,OAAO,WAAW;AAAA,UACvC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,KAAK,MAAM;AACV,gBAAI,mBAAmB,MAAM;AAC5B,qBAAO;AAAA,YACR;AACA,gBAAI,oBAAoB,YAAY,YAAY;AAC/C,gCAAkB,YAAY;AAC9B,6BAAe,YAAY,QAAQ,SAAS;AAAA,YAC7C;AAEA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,OAAK;AACT,6BAAiB;AAAA,UAClB;AAAA,QACD,CAAC;AAGD,YAAI,OAAO,YAAY,SAAS,YAAY;AAC3C,sBAAY,KAAK,KAAK;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,OAAO,WAAW,WAAW;AACrC,cAAM,WAAW,YAAY,KAAK,aAAa,OAAO,cAAc,cAAc,MAAM,aAAa,SAAS;AAC9G,iBAAS,MAAM,KAAK;AACpB,eAAO;AAAA,MACR;AASA,eAAS,OAAO,YAAY;AAC3B,oBAAY,KAAK,UAAU;AAC3B,oBAAY,aAAa;AAEzB,oBAAY,QAAQ,CAAC;AACrB,oBAAY,QAAQ,CAAC;AAErB,cAAM,SAAS,OAAO,eAAe,WAAW,aAAa,IAC3D,KAAK,EACL,QAAQ,QAAQ,GAAG,EACnB,MAAM,GAAG,EACT,OAAO,OAAO;AAEhB,mBAAW,MAAM,OAAO;AACvB,cAAI,GAAG,CAAC,MAAM,KAAK;AAClB,wBAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO;AACN,wBAAY,MAAM,KAAK,EAAE;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAUA,eAAS,gBAAgB,QAAQ,UAAU;AAC1C,YAAI,cAAc;AAClB,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAChB,YAAI,aAAa;AAEjB,eAAO,cAAc,OAAO,QAAQ;AACnC,cAAI,gBAAgB,SAAS,WAAW,SAAS,aAAa,MAAM,OAAO,WAAW,KAAK,SAAS,aAAa,MAAM,MAAM;AAE5H,gBAAI,SAAS,aAAa,MAAM,KAAK;AACpC,0BAAY;AACZ,2BAAa;AACb;AAAA,YACD,OAAO;AACN;AACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,IAAI;AAE5B,4BAAgB,YAAY;AAC5B;AACA,0BAAc;AAAA,UACf,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,eAAO,gBAAgB,SAAS,UAAU,SAAS,aAAa,MAAM,KAAK;AAC1E;AAAA,QACD;AAEA,eAAO,kBAAkB,SAAS;AAAA,MACnC;AAQA,eAAS,UAAU;AAClB,cAAM,aAAa;AAAA,UAClB,GAAG,YAAY;AAAA,UACf,GAAG,YAAY,MAAM,IAAI,eAAa,MAAM,SAAS;AAAA,QACtD,EAAE,KAAK,GAAG;AACV,oBAAY,OAAO,EAAE;AACrB,eAAO;AAAA,MACR;AASA,eAAS,QAAQ,MAAM;AACtB,mBAAW,QAAQ,YAAY,OAAO;AACrC,cAAI,gBAAgB,MAAM,IAAI,GAAG;AAChC,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,mBAAW,MAAM,YAAY,OAAO;AACnC,cAAI,gBAAgB,MAAM,EAAE,GAAG;AAC9B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AASA,eAAS,OAAO,KAAK;AACpB,YAAI,eAAe,OAAO;AACzB,iBAAO,IAAI,SAAS,IAAI;AAAA,QACzB;AACA,eAAO;AAAA,MACR;AAMA,eAAS,UAAU;AAClB,gBAAQ,KAAK,uIAAuI;AAAA,MACrJ;AAEA,kBAAY,OAAO,YAAY,KAAK,CAAC;AAErC,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnSjB;AAAA;AAMA,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,aAAa;AAC/B,YAAQ,UAAW,uBAAM;AACxB,UAAI,SAAS;AAEb,aAAO,MAAM;AACZ,YAAI,CAAC,QAAQ;AACZ,mBAAS;AACT,kBAAQ,KAAK,uIAAuI;AAAA,QACrJ;AAAA,MACD;AAAA,IACD,GAAG;AAMH,YAAQ,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAWA,aAAS,YAAY;AAIpB,UAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,SAAS,cAAc,OAAO,QAAQ,SAAS;AACrH,eAAO;AAAA,MACR;AAGA,UAAI,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,GAAG;AAChI,eAAO;AAAA,MACR;AAEA,UAAI;AAKJ,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAEtI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,cAAc,IAAI,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK;AAAA,MAEpJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC1H;AAQA,aAAS,WAAW,MAAM;AACzB,WAAK,CAAC,KAAK,KAAK,YAAY,OAAO,MAClC,KAAK,aACJ,KAAK,YAAY,QAAQ,OAC1B,KAAK,CAAC,KACL,KAAK,YAAY,QAAQ,OAC1B,MAAM,OAAO,QAAQ,SAAS,KAAK,IAAI;AAExC,UAAI,CAAC,KAAK,WAAW;AACpB;AAAA,MACD;AAEA,YAAM,IAAI,YAAY,KAAK;AAC3B,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,WAAS;AACvC,YAAI,UAAU,MAAM;AACnB;AAAA,QACD;AACA;AACA,YAAI,UAAU,MAAM;AAGnB,kBAAQ;AAAA,QACT;AAAA,MACD,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACxB;AAUA,YAAQ,MAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,IAAC;AAQtD,aAAS,KAAK,YAAY;AACzB,UAAI;AACH,YAAI,YAAY;AACf,kBAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QAC5C,OAAO;AACN,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACnC;AAAA,MACD,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAQA,aAAS,OAAO;AACf,UAAI;AACJ,UAAI;AACH,YAAI,QAAQ,QAAQ,QAAQ,OAAO,KAAK,QAAQ,QAAQ,QAAQ,OAAO;AAAA,MACxE,SAAS,OAAO;AAAA,MAGhB;AAGA,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC7D,YAAI,QAAQ,IAAI;AAAA,MACjB;AAEA,aAAO;AAAA,IACR;AAaA,aAAS,eAAe;AACvB,UAAI;AAGH,eAAO;AAAA,MACR,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAEA,WAAO,UAAU,iBAAoB,OAAO;AAE5C,QAAM,EAAC,WAAU,IAAI,OAAO;AAM5B,eAAW,IAAI,SAAU,GAAG;AAC3B,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;AAAA,MACxB,SAAS,OAAO;AACf,eAAO,iCAAiC,MAAM;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA;;;AC/QA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAIlB,QAAI;AACJ,QAAM,QAAQ,IAAI,WAAW,EAAE;AAE/B,aAAS,MAAM;AAEb,UAAI,CAAC,iBAAiB;AAEpB,0BAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM;AAE/G,YAAI,CAAC,iBAAiB;AACpB,gBAAM,IAAI,MAAM,0GAA0G;AAAA,QAC5H;AAAA,MACF;AAEA,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA;AAAA;;;ACxBA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACPlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,SAAS,uBAAuB,eAAqB;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,SAAS,MAAM;AACtB,aAAO,OAAO,SAAS,YAAY,OAAO,QAAQ,KAAK,IAAI;AAAA,IAC7D;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AChBlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,YAAQ,kBAAkB;AAE1B,QAAI,YAAY,uBAAuB,kBAAwB;AAE/D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAM9F,QAAM,YAAY,CAAC;AAEnB,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,gBAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,IAClD;AAEA,aAAS,gBAAgB,KAAK,SAAS,GAAG;AAGxC,aAAO,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;AAAA,IACnf;AAEA,aAAS,UAAU,KAAK,SAAS,GAAG;AAClC,YAAM,OAAO,gBAAgB,KAAK,MAAM;AAMxC,UAAI,EAAE,GAAG,UAAU,SAAS,IAAI,GAAG;AACjC,cAAM,UAAU,6BAA6B;AAAA,MAC/C;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AC3ClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,OAAO,uBAAuB,aAAmB;AAErD,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAM9F,QAAI;AAEJ,QAAI;AAGJ,QAAI,aAAa;AACjB,QAAI,aAAa;AAEjB,aAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,UAAI,IAAI,OAAO,UAAU;AACzB,YAAM,IAAI,OAAO,IAAI,MAAM,EAAE;AAC7B,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,QAAQ,QAAQ;AAC3B,UAAI,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAInE,UAAI,QAAQ,QAAQ,YAAY,MAAM;AACpC,cAAM,YAAY,QAAQ,WAAW,QAAQ,OAAO,KAAK,SAAS;AAElE,YAAI,QAAQ,MAAM;AAEhB,iBAAO,UAAU,CAAC,UAAU,CAAC,IAAI,GAAM,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,QAC7G;AAEA,YAAI,YAAY,MAAM;AAEpB,qBAAW,aAAa,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK;AAAA,QAC9D;AAAA,MACF;AAMA,UAAI,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,KAAK,IAAI;AAGnE,UAAI,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,aAAa;AAEvE,YAAM,KAAK,QAAQ,cAAc,QAAQ,cAAc;AAEvD,UAAI,KAAK,KAAK,QAAQ,aAAa,QAAW;AAC5C,mBAAW,WAAW,IAAI;AAAA,MAC5B;AAIA,WAAK,KAAK,KAAK,QAAQ,eAAe,QAAQ,UAAU,QAAW;AACjE,gBAAQ;AAAA,MACV;AAGA,UAAI,SAAS,KAAO;AAClB,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AAEA,mBAAa;AACb,mBAAa;AACb,kBAAY;AAEZ,eAAS;AAET,YAAM,OAAO,QAAQ,aAAa,MAAQ,SAAS;AACnD,QAAE,GAAG,IAAI,OAAO,KAAK;AACrB,QAAE,GAAG,IAAI,OAAO,KAAK;AACrB,QAAE,GAAG,IAAI,OAAO,IAAI;AACpB,QAAE,GAAG,IAAI,KAAK;AAEd,YAAM,MAAM,QAAQ,aAAc,MAAQ;AAC1C,QAAE,GAAG,IAAI,QAAQ,IAAI;AACrB,QAAE,GAAG,IAAI,MAAM;AAEf,QAAE,GAAG,IAAI,QAAQ,KAAK,KAAM;AAE5B,QAAE,GAAG,IAAI,QAAQ,KAAK;AAEtB,QAAE,GAAG,IAAI,aAAa,IAAI;AAE1B,QAAE,GAAG,IAAI,WAAW;AAEpB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAE,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,MACnB;AAEA,aAAO,QAAQ,GAAG,WAAW,iBAAiB,CAAC;AAAA,IACjD;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AC1GlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,YAAY,uBAAuB,kBAAwB;AAE/D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,MAAM,MAAM;AACnB,UAAI,EAAE,GAAG,UAAU,SAAS,IAAI,GAAG;AACjC,cAAM,UAAU,cAAc;AAAA,MAChC;AAEA,UAAI;AACJ,YAAM,MAAM,IAAI,WAAW,EAAE;AAE7B,UAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,UAAI,CAAC,IAAI,MAAM,KAAK;AACpB,UAAI,CAAC,IAAI,MAAM,IAAI;AACnB,UAAI,CAAC,IAAI,IAAI;AAEb,UAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,UAAI,CAAC,IAAI,IAAI;AAEb,UAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,UAAI,CAAC,IAAI,IAAI;AAEb,UAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,UAAI,CAAC,IAAI,IAAI;AAGb,UAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,UAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,UAAI,EAAE,IAAI,MAAM,KAAK;AACrB,UAAI,EAAE,IAAI,MAAM,KAAK;AACrB,UAAI,EAAE,IAAI,MAAM,IAAI;AACpB,UAAI,EAAE,IAAI,IAAI;AACd,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AC5ClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,MAAM,QAAQ,MAAM;AAC5B,YAAQ,UAAU;AAElB,QAAI,aAAa;AAEjB,QAAI,SAAS,uBAAuB,eAAqB;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,cAAc,KAAK;AAC1B,YAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,YAAM,QAAQ,CAAC;AAEf,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,cAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,MAC9B;AAEA,aAAO;AAAA,IACT;AAEA,QAAM,MAAM;AACZ,YAAQ,MAAM;AACd,QAAMA,OAAM;AACZ,YAAQ,MAAMA;AAEd,aAAS,IAAI,MAAM,SAAS,UAAU;AACpC,eAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,YAAI;AAEJ,YAAI,OAAO,UAAU,UAAU;AAC7B,kBAAQ,cAAc,KAAK;AAAA,QAC7B;AAEA,YAAI,OAAO,cAAc,UAAU;AACjC,uBAAa,GAAG,OAAO,SAAS,SAAS;AAAA,QAC3C;AAEA,cAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,IAAI;AACpG,gBAAM,UAAU,kEAAkE;AAAA,QACpF;AAKA,YAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,cAAM,IAAI,SAAS;AACnB,cAAM,IAAI,OAAO,UAAU,MAAM;AACjC,gBAAQ,SAAS,KAAK;AACtB,cAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,cAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAE7B,YAAI,KAAK;AACP,mBAAS,UAAU;AAEnB,mBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,gBAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT;AAEA,gBAAQ,GAAG,WAAW,iBAAiB,KAAK;AAAA,MAC9C;AAGA,UAAI;AACF,qBAAa,OAAO;AAAA,MACtB,SAAS,KAAK;AAAA,MAAC;AAGf,mBAAa,MAAM;AACnB,mBAAa,MAAMA;AACnB,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/EA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAsBlB,aAAS,IAAI,OAAO;AAClB,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,gBAAQ,IAAI,WAAW,IAAI,MAAM;AAEjC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,gBAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,QAC7B;AAAA,MACF;AAEA,aAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAAA,IAC/E;AAMA,aAAS,qBAAqB,OAAO;AACnC,YAAM,SAAS,CAAC;AAChB,YAAM,WAAW,MAAM,SAAS;AAChC,YAAM,SAAS;AAEf,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,cAAM,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACrC,cAAM,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAChF,eAAO,KAAK,GAAG;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,gBAAgB,cAAc;AACrC,cAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAAA,IAC/C;AAMA,aAAS,WAAW,GAAG,KAAK;AAE1B,QAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,QAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,UAAI,IAAI;AACR,UAAI,IAAI;AACR,UAAI,IAAI;AACR,UAAI,IAAI;AAER,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,cAAM,OAAO;AACb,cAAM,OAAO;AACb,cAAM,OAAO;AACb,cAAM,OAAO;AACb,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,YAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,YAAI,QAAQ,GAAG,IAAI;AACnB,YAAI,QAAQ,GAAG,IAAI;AACnB,YAAI,QAAQ,GAAG,IAAI;AACnB,YAAI,QAAQ,GAAG,IAAI;AAAA,MACrB;AAEA,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AAOA,aAAS,aAAa,OAAO;AAC3B,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,UAAU,MAAM,SAAS;AAC/B,YAAM,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AAEvD,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,eAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,MACjD;AAEA,aAAO;AAAA,IACT;AAOA,aAAS,QAAQ,GAAG,GAAG;AACrB,YAAM,OAAO,IAAI,UAAW,IAAI;AAChC,YAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,aAAO,OAAO,KAAK,MAAM;AAAA,IAC3B;AAMA,aAAS,cAAc,KAAK,KAAK;AAC/B,aAAO,OAAO,MAAM,QAAQ,KAAK;AAAA,IACnC;AAMA,aAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,aAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAAA,IAC3E;AAEA,aAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,aAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC7C;AAEA,aAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,aAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC7C;AAEA,aAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,aAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IACxC;AAEA,aAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,aAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC3C;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AC9NlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,KAAK,uBAAuB,aAAmB;AAEnD,QAAI,MAAM,uBAAuB,aAAmB;AAEpD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,QAAM,MAAM,GAAG,GAAG,SAAS,MAAM,IAAM,IAAI,OAAO;AAClD,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACflB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,QAAI,WAAW;AAAA,MACb;AAAA,IACF;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACVlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,UAAU,uBAAuB,gBAAsB;AAE3D,QAAI,OAAO,uBAAuB,aAAmB;AAErD,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,UAAI,QAAQ,QAAQ,cAAc,CAAC,OAAO,CAAC,SAAS;AAClD,eAAO,QAAQ,QAAQ,WAAW;AAAA,MACpC;AAEA,gBAAU,WAAW,CAAC;AAEtB,YAAM,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK,SAAS;AAG7D,WAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,WAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,UAAI,KAAK;AACP,iBAAS,UAAU;AAEnB,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,cAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,QAC1B;AAEA,eAAO;AAAA,MACT;AAEA,cAAQ,GAAG,WAAW,iBAAiB,IAAI;AAAA,IAC7C;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;AC1ClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAIlB,aAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,cAAQ,GAAG;AAAA,QACT,KAAK;AACH,iBAAO,IAAI,IAAI,CAAC,IAAI;AAAA,QAEtB,KAAK;AACH,iBAAO,IAAI,IAAI;AAAA,QAEjB,KAAK;AACH,iBAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,QAE7B,KAAK;AACH,iBAAO,IAAI,IAAI;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,KAAK,GAAG,GAAG;AAClB,aAAO,KAAK,IAAI,MAAM,KAAK;AAAA,IAC7B;AAEA,aAAS,KAAK,OAAO;AACnB,YAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,YAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AAErE,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,gBAAQ,CAAC;AAET,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,gBAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,gBAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,MAC1C;AAEA,YAAM,KAAK,GAAI;AACf,YAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,YAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,YAAM,IAAI,IAAI,MAAM,CAAC;AAErB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,cAAM,MAAM,IAAI,YAAY,EAAE;AAE9B,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,cAAI,CAAC,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,QACpI;AAEA,UAAE,CAAC,IAAI;AAAA,MACT;AAEA,QAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,QAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,QAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AAExC,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,cAAM,IAAI,IAAI,YAAY,EAAE;AAE5B,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,QACf;AAEA,iBAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC5B,YAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,QAC5D;AAEA,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AAEX,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,gBAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,gBAAM,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC3D,cAAI;AACJ,cAAI;AACJ,cAAI,KAAK,GAAG,EAAE,MAAM;AACpB,cAAI;AACJ,cAAI;AAAA,QACN;AAEA,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,MACtB;AAEA,aAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AAAA,IACjW;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACvGlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,KAAK,uBAAuB,aAAmB;AAEnD,QAAI,OAAO,uBAAuB,cAAoB;AAEtD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,QAAM,MAAM,GAAG,GAAG,SAAS,MAAM,IAAM,KAAK,OAAO;AACnD,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACflB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACPlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,YAAY,uBAAuB,kBAAwB;AAE/D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,QAAQ,MAAM;AACrB,UAAI,EAAE,GAAG,UAAU,SAAS,IAAI,GAAG;AACjC,cAAM,UAAU,cAAc;AAAA,MAChC;AAEA,aAAO,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE;AAAA,IACxC;AAEA,QAAI,WAAW;AACf,YAAQ,UAAU;AAAA;AAAA;;;ACpBlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,OAAO;AAAA,MACpC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,SAAS;AAAA,MACtC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,aAAa;AAAA,MAC1C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,MAAM;AAAA,MACnC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,MAAM;AAAA,MACnC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,IAAI;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,MAAM;AAAA,MACnC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,IAAI;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,MAAM;AAAA,MACnC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,IAAI;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,UAAU;AAAA,MACnB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,WAAW;AAAA,MACxC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,SAAS;AAAA,MAClB;AAAA,IACF,CAAC;AAED,QAAI,KAAK,uBAAuB,YAAkB;AAElD,QAAI,MAAM,uBAAuB,YAAkB;AAEnD,QAAI,MAAM,uBAAuB,YAAkB;AAEnD,QAAI,MAAM,uBAAuB,YAAkB;AAEnD,QAAI,OAAO,uBAAuB,aAAmB;AAErD,QAAI,WAAW,uBAAuB,iBAAuB;AAE7D,QAAI,YAAY,uBAAuB,kBAAwB;AAE/D,QAAI,aAAa,uBAAuB,mBAAyB;AAEjE,QAAI,SAAS,uBAAuB,eAAqB;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAAA;AAAA;;;AC9E9F,IAAAC,iBAAA;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAM,mBAAmB;AACzB,QAAM,UAAU,gBAAgB,iBAAgB;AAChD,QAAM,eAAe;AACrB,QAAM,SAAS;AACf,QAAI,SAAS,GAAG,QAAQ,SAAS,gBAAgB;AA2CjD,QAAM,iBAAN,cAA6B,aAAa,SAAS;AAAA,MAC/C,YAAY,SAAS,QAAQ;AACzB,YAAI,IAAI;AACR,cAAM,MAAM;AACZ,aAAK,OAAO;AACZ,aAAK,kBAAkB;AACvB,aAAK,mBAAmB,QAAQ;AAChC,aAAK,WAAW,QAAQ;AACxB,aAAK,WAAW,QAAQ;AACxB,aAAK,eAAe,QAAQ;AAC5B,aAAK,cAAc,QAAQ;AAC3B,aAAK,QAAQ,QAAQ;AACrB,aAAK,gBAAgB,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AACjF,aAAK,gCACA,KAAK,QAAQ,kCAAkC,QAAQ,OAAO,SAAS,KAAK;AAAA,MACrF;AAAA,MACA,MAAM,aAAa,SAAS,gBAAgB,SAAS;AACjD,YAAI;AACJ,cAAM,eAAe,QAAQ,GAAG;AAChC,YAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,YAAI,UAAU,MAAM,eAAe,WAAW,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AAC3E,YAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK;AAEnF,YAAI,MAAM;AACN,gBAAM,uBAAuB;AAC7B,iBAAO,KAAK,QAAQ,MAAM,SAAS,gBAAgB,OAAO;AAAA,QAC9D;AACA,YAAI,cAAc,KAAK,eAAe,OAAO;AAC7C,cAAM,gBAAgB,WAAW;AAEjC,YAAI,IAAI,aAAa,YAAY,UAAU;AACvC,gBAAM,6BAA6B;AACnC,cAAI,QAAQ,KAAK,cAAc;AAC/B,gBAAM,SAAS,KAAK;AACpB,kBAAQ,IAAI,KAAK,iBAAiB,KAAK;AACvC,iBAAO,GAAG,iBAAiB,UAAU,KAAK,oBAAoB,SAAS,KAAK,EAAE,SAAS,GAAG;AAAA,YACtF,SAAS,EAAE,cAAc,MAAM,eAAe,cAAc,OAAO,EAAE;AAAA,UACzE,CAAC;AAAA,QACL;AAEA,YAAI,WAAW,IAAI,aAAa,IAAI,OAAO;AAC3C,cAAM,kBAAkB,QAAQ;AAChC,YAAI,CAAC,UAAU;AACX,iBAAO,MAAM,KAAK,QAAQ,yBAAyB,SAAS,gBAAgB,SAAS,IAAI,MAAM,uBAAuB,CAAC;AAAA,QAC3H;AACA,YAAI,eAAe,QAAQ,IAAI,KAAK,eAAe;AACnD,cAAM,sBAAsB,YAAY;AACxC,YAAI,CAAC,cAAc;AACf,iBAAO,MAAM,KAAK,QAAQ,6BAA6B,SAAS,gBAAgB,SAAS,IAAI,MAAM,2BAA2B,CAAC;AAAA,QACnI;AACA,YAAI,iBAAiB,UAAU;AAC3B,gBAAM,gBAAgB;AACtB,kBAAQ,MAAM,KAAK,eAAe;AAAA,QACtC,OACK;AACD,iBAAO,MAAM,KAAK,QAAQ,wBAAwB,SAAS,gBAAgB,SAAS,IAAI,MAAM,sBAAsB,CAAC;AAAA,QACzH;AACA,YAAI,OAAO,IAAI,aAAa,IAAI,MAAM;AACtC,YAAI,CAAC,MAAM;AACP,iBAAO,MAAM,KAAK,QAAQ,iBAAiB,SAAS,gBAAgB,SAAS,IAAI,MAAM,eAAe,CAAC;AAAA,QAC3G;AACA,YAAI;AAEA,cAAI,SAAS,IAAI,gBAAgB,KAAK,YAAY,CAAC;AACnD,iBAAO,IAAI,cAAc,oBAAoB;AAC7C,iBAAO,IAAI,gBAAgB,YAAY,SAAS,CAAC;AACjD,cAAI,EAAE,aAAa,cAAc,YAAY,IAAI,MAAM,KAAK,iBAAiB,MAAM,MAAM;AAEzF,cAAI,UAAU,MAAM,KAAK,YAAY,aAAa,WAAW;AAE7D,iBAAO,MAAM,KAAK,OAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,QAAQ;AAAA,YACjB;AAAA,UACJ,CAAC;AAAA,QACL,SACO,OAAO;AACV,gBAAM,yBAAyB,KAAK;AAEpC,cAAI,iBAAiB;AACjB,kBAAM;AACV,cAAI,iBAAiB,OAAO;AACxB,mBAAO,MAAM,KAAK,QAAQ,MAAM,SAAS,SAAS,gBAAgB,SAAS,KAAK;AAAA,UACpF;AACA,cAAI,OAAO,UAAU,UAAU;AAC3B,mBAAO,MAAM,KAAK,QAAQ,OAAO,SAAS,gBAAgB,SAAS,IAAI,MAAM,KAAK,CAAC;AAAA,UACvF;AACA,iBAAO,MAAM,KAAK,QAAQ,iBAAiB,SAAS,gBAAgB,SAAS,IAAI,MAAM,KAAK,UAAU,OAAO,MAAM,CAAC,CAAC,CAAC;AAAA,QAC1H;AACA,cAAM,oBAAoB;AAC1B,eAAO,MAAM,KAAK,QAAQ,MAAM,SAAS,gBAAgB,OAAO;AAAA,MACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,MAAM,YAEN,aAEA,QAAQ;AACJ,eAAO,EAAE,UAAU,SAAS;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,oBAAoB,QAAQ;AACxB,eAAO,IAAI,gBAAgB,MAAM;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,cAAc;AACV,eAAO,IAAI,gBAAgB;AAAA,MAC/B;AAAA,MACA,MAAM,eAAe,UAAU;AAC3B,YAAI,EAAE,cAAc,eAAe,GAAG,YAAY,IAAI,MAAM,SAAS,KAAK;AAC1E,eAAO;AAAA,UACH,aAAa;AAAA,UACb,cAAc;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,eAAe,SAAS;AACpB,YAAI,IAAI;AACR,YAAI,KAAK,YAAY,WAAW,OAAO,KACnC,KAAK,YAAY,WAAW,QAAQ,GAAG;AACvC,iBAAO,IAAI,IAAI,KAAK,WAAW;AAAA,QACnC;AACA,YAAI,QAAQ,MAAM,KAAK,QAAQ,QAAQ,IAAI,kBAAkB,OAAO,QAAQ,OAAO,SAAS,KAAK,QAAQ,QAAQ,IAAI,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,IAAI,QAAQ,GAAG,EAAE;AACpL,YAAI,WAAW,KAAK,SAAS,WAAW,IAAI,SAAS;AACrD,YAAI,KAAK,YAAY,WAAW,GAAG,GAAG;AAClC,iBAAO,IAAI,IAAI,KAAK,aAAa,GAAG,QAAQ,MAAM,IAAI,EAAE;AAAA,QAC5D;AACA,eAAO,IAAI,IAAI,GAAG,QAAQ,MAAM,KAAK,WAAW,EAAE;AAAA,MACtD;AAAA,MACA,oBAAoB,SAAS,OAAO;AAChC,YAAI,SAAS,IAAI,gBAAgB,KAAK,oBAAoB,IAAI,IAAI,QAAQ,GAAG,EAAE,YAAY,CAAC;AAC5F,eAAO,IAAI,iBAAiB,KAAK,YAAY;AAC7C,eAAO,IAAI,aAAa,KAAK,QAAQ;AACrC,eAAO,IAAI,gBAAgB,KAAK,eAAe,OAAO,EAAE,SAAS,CAAC;AAClE,eAAO,IAAI,SAAS,KAAK;AAEzB,YAAI,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;AACpC,iBAAO,IAAI,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,YAAI,MAAM,IAAI,IAAI,KAAK,gBAAgB;AACvC,YAAI,SAAS,OAAO,SAAS;AAC7B,eAAO;AAAA,MACX;AAAA,MACA,gBAAgB;AACZ,gBAAQ,GAAG,OAAO,IAAI;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM,iBAAiB,MAAM,QAAQ;AACjC,YAAI,UAAU;AAAA,UACV,gBAAgB;AAAA,QACpB;AACA,YAAI,KAAK,8BAA8B;AACnC,gBAAM,wBAAwB,OAAO,KAAK,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,EAAE,EAAE,SAAS,QAAQ;AACpG,oBAAU;AAAA,YACN,GAAG;AAAA,YACH,eAAe,SAAS,qBAAqB;AAAA,UACjD;AAAA,QACJ,OACK;AACD,iBAAO,IAAI,aAAa,KAAK,QAAQ;AACrC,iBAAO,IAAI,iBAAiB,KAAK,YAAY;AAAA,QACjD;AACA,YAAI,OAAO,IAAI,YAAY,MAAM,iBAAiB;AAC9C,iBAAO,IAAI,iBAAiB,IAAI;AAAA,QACpC,OACK;AACD,iBAAO,IAAI,QAAQ,IAAI;AAAA,QAC3B;AACA,YAAI,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,UACtC,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,QACV,CAAC;AACD,YAAI,CAAC,SAAS,IAAI;AACd,cAAI,OAAO,MAAM,SAAS,KAAK;AAC/B,gBAAM;AAAA,QACV;AACA,eAAO,MAAM,KAAK,eAAe,SAAS,MAAM,CAAC;AAAA,MACrD;AAAA,IACJ;AACA,YAAQ,iBAAiB;AAAA;AAAA;;;ACrQzB,IAAAC,iBAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB,QAAQ,4BAA4B,QAAQ,8BAA8B,QAAQ,+BAA+B;AAC1I,QAAM,sBAAsB;AAC5B,YAAQ,+BAA+B;AACvC,YAAQ,8BAA8B;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,IACJ,EAAE,KAAK,QAAQ,4BAA4B;AAC3C,YAAQ,4BAA4B;AACpC,QAAM,iBAAN,cAA6B,oBAAoB,eAAe;AAAA,MAC5D,YAAY,EAAE,UAAU,cAAc,aAAa,OAAO,YAAY,sBAAsB,QAAQ,IAAI,UAAW,GAAG,QAAQ;AAC1H,cAAM;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,kBAAkB;AAAA,UAClB,UAAU;AAAA,QACd,GAAG,MAAM;AACT,aAAK,OAAO,QAAQ;AACpB,aAAK,cAAc;AACnB,aAAK,QAAQ,KAAK,WAAW,KAAK;AAClC,aAAK,aAAa,eAAe,QAAQ,eAAe,SAAS,aAAa;AAC9E,aAAK,uBAAuB,yBAAyB,QAAQ,yBAAyB,SAAS,uBAAuB;AACtH,aAAK,SAAS;AACd,aAAK,KAAK;AACV,aAAK,YAAY;AAAA,MACrB;AAAA,MACA,sBAAsB;AAClB,cAAM,SAAS,IAAI,gBAAgB;AAAA,UAC/B,aAAa,KAAK;AAAA,UAClB,wBAAwB,OAAO,KAAK,oBAAoB;AAAA,QAC5D,CAAC;AACD,YAAI,KAAK,QAAQ;AACb,iBAAO,IAAI,UAAU,KAAK,MAAM;AAAA,QACpC;AACA,YAAI,KAAK,IAAI;AACT,iBAAO,IAAI,MAAM,KAAK,EAAE;AAAA,QAC5B;AACA,YAAI,KAAK,WAAW;AAChB,iBAAO,IAAI,cAAc,KAAK,SAAS;AAAA,QAC3C;AACA,eAAO;AAAA,MACX;AAAA,MACA,MAAM,YAAY,aAAa;AAC3B,cAAM,WAAW,MAAM,MAAM,KAAK,aAAa;AAAA,UAC3C,SAAS;AAAA,YACL,eAAe,UAAU,WAAW;AAAA,UACxC;AAAA,QACJ,CAAC;AACD,cAAM,MAAM,MAAM,SAAS,KAAK;AAChC,cAAM,UAAU;AAAA,UACZ,UAAU;AAAA,UACV,IAAI,IAAI;AAAA,UACR,aAAa,IAAI;AAAA,UACjB,MAAM;AAAA,YACF,YAAY,IAAI;AAAA,YAChB,WAAW,IAAI;AAAA,UACnB;AAAA,UACA,QAAQ,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC;AAAA,UAC7B,QAAQ,CAAC,EAAE,OAAO,IAAI,QAAQ,CAAC;AAAA,UAC/B,OAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA;AAAA,MAEA,WAAW,OAAO;AACd,YAAI,CAAC,OAAO;AACR,iBAAO,QAAQ;AAAA,QACnB,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,iBAAO,MAAM,KAAK,QAAQ,4BAA4B;AAAA,QAC1D;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,YAAQ,iBAAiB;AAAA;AAAA;", "names": ["URL", "require_build", "require_build"]}