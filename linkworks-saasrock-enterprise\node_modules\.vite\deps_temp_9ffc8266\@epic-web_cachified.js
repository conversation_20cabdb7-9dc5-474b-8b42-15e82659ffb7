import "./chunk-PLDDJCW6.js";

// node_modules/@epic-web/cachified/dist/index.mjs
var k = Symbol();
var w = Symbol();
function M({ fallbackToCache: e, reporter: t, checkValue: a, ...r }) {
  let n = r.ttl ?? 1 / 0, u = r.swr ?? r.staleWhileRevalidate ?? 0, c = { checkValue: typeof a == "function" ? a : typeof a == "object" ? (l, o) => a.parseAsync(l).then((i) => o(i, false)) : () => true, ttl: n, staleWhileRevalidate: u, fallbackToCache: e === false ? 0 : e === true || e === void 0 ? 1 / 0 : e, staleRefreshTimeout: 0, forceFresh: false, ...r, metadata: T({ ttl: n, swr: u }) }, d = t?.(c) || (() => {
  });
  return { ...c, report: d };
}
function C(e) {
  return (typeof e.swr > "u" ? e.swv : e.swr) || null;
}
function y(e) {
  return e ? e.ttl === null ? 1 / 0 : (e.ttl || 0) + (C(e) || 0) : 0;
}
function T({ ttl: e = null, swr: t = 0, createdTime: a = Date.now() } = {}) {
  return { ttl: e === 1 / 0 ? null : e, swr: t === 1 / 0 ? null : t, createdTime: a };
}
function E(e, t) {
  return { value: e, metadata: T(t) };
}
var I = (e) => `${Math.round(e)}ms`;
function F(e, t) {
  let a = C(e);
  return e.ttl == null || a == null ? `forever${e.ttl != null ? ` (revalidation after ${t(e.ttl)})` : ""}` : `${t(e.ttl)} + ${t(a)} stale`;
}
function _({ formatDuration: e = I, logger: t = console, performance: a = global.performance || Date } = {}) {
  return ({ key: r, fallbackToCache: n, forceFresh: u, metadata: m, cache: c }) => {
    let d = c.name || c.toString().toString().replace(/^\[object (.*?)]$/, "$1"), l, o, i, s;
    return (h) => {
      switch (h.name) {
        case "getCachedValueRead":
          l = h.entry;
          break;
        case "checkCachedValueError":
          t.warn(`check failed for cached value of ${r}
Reason: ${h.reason}.
Deleting the cache key and trying to get a fresh value.`, l);
          break;
        case "getCachedValueError":
          t.error(`error with cache at ${r}. Deleting the cache key and trying to get a fresh value.`, h.error);
          break;
        case "getFreshValueError":
          t.error(`getting a fresh value for ${r} failed`, { fallbackToCache: n, forceFresh: u }, h.error);
          break;
        case "getFreshValueStart":
          i = a.now();
          break;
        case "writeFreshValueSuccess": {
          let p = a.now() - i;
          h.written ? t.log(`Updated the cache value for ${r}.`, `Getting a fresh value for this took ${e(p)}.`, `Caching for ${F(m, e)} in ${d}.`) : t.log(`Not updating the cache value for ${r}.`, `Getting a fresh value for this took ${e(p)}.`, `Thereby exceeding caching time of ${F(m, e)}`);
          break;
        }
        case "writeFreshValueError":
          t.error(`error setting cache: ${r}`, h.error);
          break;
        case "getFreshValueSuccess":
          o = h.value;
          break;
        case "checkFreshValueError":
          t.error(`check failed for fresh value of ${r}
Reason: ${h.reason}.`, o);
          break;
        case "refreshValueStart":
          s = a.now();
          break;
        case "refreshValueSuccess":
          t.log(`Background refresh for ${r} successful.`, `Getting a fresh value for this took ${e(a.now() - s)}.`, `Caching for ${F(m, e)} in ${d}.`);
          break;
        case "refreshValueError":
          t.log(`Background refresh for ${r} failed.`, h.error);
          break;
      }
    };
  };
}
function B(...e) {
  return (t) => {
    let a = e.map((r) => r?.(t));
    return (r) => {
      a.forEach((n) => n?.(r));
    };
  };
}
function q(e) {
  return { name: e.name || "LRU", set(t, a) {
    let r = y(a?.metadata);
    return e.set(t, a, { ttl: r === 1 / 0 ? void 0 : r, start: a?.metadata?.createdTime });
  }, get(t) {
    return e.get(t);
  }, delete(t) {
    return e.delete(t);
  } };
}
function X(e) {
  return { name: e.name || "Redis3", set(t, a) {
    return new Promise((r, n) => {
      let u = y(a?.metadata), m = a?.metadata?.createdTime, c = (d) => {
        if (d) return n(d);
        r();
      };
      u > 0 && u < 1 / 0 && typeof m == "number" ? e.multi().set(t, JSON.stringify(a)).expireat(t, (u + m) / 1e3).exec(c) : e.set(t, JSON.stringify(a), c);
    });
  }, get(t) {
    return new Promise((a, r) => {
      e.get(t, (n, u) => {
        if (n) r(n);
        else if (u == null) a(null);
        else try {
          a(JSON.parse(u));
        } catch (m) {
          r(m);
        }
      });
    });
  }, delete(t) {
    return new Promise((a, r) => {
      e.del(t, (n) => {
        n && r(n), a();
      });
    });
  } };
}
function z(e) {
  return { name: e.name || "Redis", set(t, a) {
    let r = y(a?.metadata), n = a?.metadata?.createdTime;
    return e.set(t, JSON.stringify(a), r > 0 && r < 1 / 0 && typeof n == "number" ? { EXAT: Math.ceil((r + n) / 1e3) } : void 0);
  }, async get(t) {
    let a = await e.get(t);
    return a == null ? null : JSON.parse(a);
  }, delete(t) {
    return e.del(t);
  } };
}
function W(e, t = true) {
  let a = [], r = 0, n = false, u = new R(), m = () => {
    if (n) throw new Error("Can not add to batch after submission");
  }, c = async () => {
    if (r !== 0) return t = true, u.promise;
    if (m(), n = true, a.length === 0) {
      u.resolve();
      return;
    }
    try {
      (await Promise.resolve(e(a.map(([o]) => o)))).forEach((o, i) => a[i][1](o)), u.resolve();
    } catch (l) {
      a.forEach(([o, i, s]) => s(l)), u.resolve();
    }
  }, d = () => {
    r--, t !== false && c();
  };
  return { ...t === false ? { submit: c } : {}, add(l, o) {
    m(), r++;
    let i = false;
    return Object.assign((s) => new Promise((h, p) => {
      a.push([l, (g) => {
        o?.({ ...s, value: g }), h(g);
      }, p]), i || (i = true, d());
    }), { [k]: () => {
      i || (i = true, d());
    } });
  } };
}
var R = class {
  constructor() {
    this.promise = new Promise((t, a) => {
      this.resolve = t, this.reject = a;
    });
  }
};
function P(e) {
  return e ? `for ${e} ` : "";
}
function S(e, t) {
  if (!G(e)) throw new Error(`Cache entry ${P(t)}is not a cache entry object, it's a ${typeof e}`);
  if (!G(e.metadata) || typeof e.metadata.createdTime != "number" || e.metadata.ttl != null && typeof e.metadata.ttl != "number" || e.metadata.swr != null && typeof e.metadata.swr != "number") throw new Error(`Cache entry ${P(t)}does not have valid metadata property`);
  if (!("value" in e)) throw new Error(`Cache entry for ${P(t)}does not have a value property`);
}
function G(e) {
  return typeof e == "object" && e !== null && !Array.isArray(e);
}
function V(e) {
  if (e.ttl !== null) {
    let t = e.createdTime + (e.ttl || 0), a = t + (C(e) || 0), r = Date.now();
    return r <= t ? false : r <= a ? "stale" : "now";
  }
  return false;
}
async function x(e, t) {
  try {
    let a = await e.checkValue(t, (r, n = true) => ({ [w]: n, value: r }));
    return typeof a == "string" ? { success: false, reason: a } : a == null || a === true ? { success: true, value: t, migrated: false } : a && typeof a[w] == "boolean" ? { success: true, migrated: a[w], value: a.value } : { success: false, reason: "unknown" };
  } catch (a) {
    return { success: false, reason: a };
  }
}
var f = Symbol();
async function v({ key: e, cache: t }, a) {
  a({ name: "getCachedValueStart" });
  let r = await t.get(e);
  return a({ name: "getCachedValueRead", entry: r }), r ? (S(r, e), r) : f;
}
async function $(e, t, a) {
  let { key: r, cache: n, staleWhileRevalidate: u, staleRefreshTimeout: m, metadata: c, getFreshValue: { [k]: d } } = e;
  try {
    let l = await v(e, t);
    if (l === f) return t({ name: "getCachedValueEmpty" }), f;
    let o = V(l.metadata), i = o === "stale" || o === "now" && u === 1 / 0;
    if (o === "now" && t({ name: "getCachedValueOutdated", ...l }), i && setTimeout(() => {
      t({ name: "refreshValueStart" }), b({ ...e, reporter: () => () => {
      }, getFreshValue({ metadata: s }) {
        return e.getFreshValue({ metadata: s, background: true });
      }, forceFresh: true, fallbackToCache: false }).then((s) => {
        t({ name: "refreshValueSuccess", value: s });
      }).catch((s) => {
        t({ name: "refreshValueError", error: s });
      });
    }, m), !o || i) {
      let s = await x(e, l.value);
      if (s.success) return t({ name: "getCachedValueSuccess", value: s.value, migrated: s.migrated }), i || d?.(), s.migrated && setTimeout(async () => {
        try {
          let h = await e.cache.get(e.key);
          h && h.metadata.createdTime === c.createdTime && !a() && await e.cache.set(e.key, { ...h, value: s.value });
        } catch {
        }
      }, 0), s.value;
      t({ name: "checkCachedValueErrorObj", reason: s.reason }), t({ name: "checkCachedValueError", reason: s.reason instanceof Error ? s.reason.message : String(s.reason) }), await n.delete(r);
    }
  } catch (l) {
    t({ name: "getCachedValueError", error: l }), await n.delete(r);
  }
  return f;
}
async function A(e, t, a) {
  let { fallbackToCache: r, key: n, getFreshValue: u, forceFresh: m, cache: c } = e, d;
  try {
    a({ name: "getFreshValueStart" });
    let o = await u({ metadata: e.metadata, background: false });
    d = o, a({ name: "getFreshValueSuccess", value: o });
  } catch (o) {
    if (a({ name: "getFreshValueError", error: o }), m && r > 0) {
      let i = await v(e, a);
      if (i === f || i.metadata.createdTime + r < Date.now()) throw o;
      d = i.value, a({ name: "getFreshValueCacheFallback", value: d });
    } else throw o;
  }
  let l = await x(e, d);
  if (!l.success) throw a({ name: "checkFreshValueErrorObj", reason: l.reason }), a({ name: "checkFreshValueError", reason: l.reason instanceof Error ? l.reason.message : String(l.reason) }), new Error(`check failed for fresh value of ${n}`, { cause: l.reason });
  try {
    let o = V(t) !== "now";
    o && await c.set(n, E(d, t)), a({ name: "writeFreshValueSuccess", metadata: t, migrated: l.migrated, written: o });
  } catch (o) {
    a({ name: "writeFreshValueError", error: o });
  }
  return l.value;
}
var O = /* @__PURE__ */ new WeakMap();
async function b(e) {
  let t = M(e), { key: a, cache: r, forceFresh: n, report: u, metadata: m } = t;
  O.has(r) || O.set(r, /* @__PURE__ */ new Map());
  let c = O.get(r), l = n ? f : await $(t, u, () => c.has(a));
  if (l !== f) return u({ name: "done", value: l }), l;
  if (c.has(a)) {
    let { value: h, metadata: p } = c.get(a);
    if (!V(p)) {
      u({ name: "getFreshValueHookPending" });
      let g = await h;
      return u({ name: "done", value: g }), g;
    }
  }
  let o, i = Promise.race([A(t, m, u), new Promise((h) => {
    o = h;
  })]).finally(() => {
    c.delete(a);
  });
  if (c.has(a)) {
    let { resolve: h } = c.get(a);
    i.then((p) => h(p));
  }
  c.set(a, { metadata: m, value: i, resolve: o });
  let s = await i;
  return u({ name: "done", value: s }), s;
}
async function j({ cache: e, key: t, ...a }) {
  let r = a.swr ?? a.staleWhileRevalidate, n = await v({ cache: e, key: t }, () => {
  });
  if (n === f || V(n.metadata)) return;
  let u = n.metadata.ttl || 1 / 0, m = C(n.metadata) || 0, c = Date.now() - n.metadata.createdTime;
  await e.set(t, E(n.value, { ttl: 0, swr: r === void 0 ? u + m : r + c, createdTime: n.metadata.createdTime }));
}
export {
  S as assertCacheEntry,
  b as cachified,
  W as createBatch,
  E as createCacheEntry,
  b as default,
  q as lruCacheAdapter,
  B as mergeReporters,
  X as redis3CacheAdapter,
  z as redisCacheAdapter,
  V as shouldRefresh,
  j as softPurge,
  C as staleWhileRevalidate,
  y as totalTtl,
  _ as verboseReporter
};
//# sourceMappingURL=@epic-web_cachified.js.map
