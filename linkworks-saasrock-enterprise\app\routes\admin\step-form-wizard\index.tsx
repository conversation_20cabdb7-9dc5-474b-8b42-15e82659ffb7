import { LoaderFunctionArgs, MetaFunction } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import { StepFormWizardSummaryApi } from "~/custom/modules/stepFormWizard/routes/api/StepFormWizardSummaryApi.server";
import StepFormWizardOverviewRoute from "~/custom/modules/stepFormWizard/routes/components/StepFormWizardSummaryRoute";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
export const loader = (args: LoaderFunctionArgs) => StepFormWizardSummaryApi.loader(args);

export default () => <StepFormWizardOverviewRoute />;

export function ErrorBoundary() {
  return <ServerError />;
}
