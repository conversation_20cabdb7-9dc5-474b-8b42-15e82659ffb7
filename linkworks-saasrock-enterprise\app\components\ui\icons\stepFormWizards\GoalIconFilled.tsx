export default function GoalIconFilled({ className }: { className?: string }) {
  return (
    <svg className={className} fill="currentColor" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="50" height="50" viewBox="0 0 50 50">
      <path d="M 25 2 C 12.318 2 2 12.318 2 25 C 2 37.682 12.318 48 25 48 C 37.682 48 48 37.682 48 25 C 48 20.403 46.639641 16.119484 44.306641 12.521484 L 43.414062 13.414062 L 42.828125 14 L 42 14 L 40.242188 14 L 37.509766 16.732422 C 39.080766 19.103422 40 21.943 40 25 C 40 33.284 33.284 40 25 40 C 16.716 40 10 33.284 10 25 C 10 16.716 16.716 10 25 10 C 28.057 10 30.896578 10.919234 33.267578 12.490234 L 36 9.7578125 L 36 8 L 36 7.171875 L 36.585938 6.5859375 L 37.474609 5.6972656 C 33.878609 3.3632656 29.597 2 25 2 z M 43 3 L 38 8 L 38 10.585938 L 24.292969 24.292969 A 1.0001 1.0001 0 1 0 25.707031 25.707031 L 39.414062 12 L 42 12 L 47 7 L 43 7 L 43 3 z M 25 16 C 20.038 16 16 20.038 16 25 C 16 29.962 20.038 34 25 34 C 29.962 34 34 29.962 34 25 C 34 23.613 33.675281 22.304859 33.113281 21.130859 L 27.121094 27.121094 C 26.555094 27.688094 25.801 28 25 28 C 24.199 28 23.444906 27.688094 22.878906 27.121094 C 22.311906 26.555094 22 25.801 22 25 C 22 24.199 22.311906 23.444906 22.878906 22.878906 L 28.871094 16.886719 C 27.698094 16.321719 26.387 16 25 16 z"></path>
    </svg>
  );
}
