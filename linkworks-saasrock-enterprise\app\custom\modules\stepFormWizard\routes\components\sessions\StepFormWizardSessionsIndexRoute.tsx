import { useActionData, useLoaderData, useSubmit } from "react-router";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import InputFilters from "~/components/ui/input/InputFilters";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import StepFormWizardSessionsTable from "~/custom/modules/stepFormWizard/components/StepFormWizardSessionsTable";
import { StepFormWizardSessionWithDetails } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { StepFormWizardSessionsIndexApi } from "../../api/sessions/StepFormWizardSessionsIndexApi.server";

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<StepFormWizardSessionsIndexApi.LoaderData>();
  const actionData = useActionData<StepFormWizardSessionsIndexApi.ActionData>();
  const submit = useSubmit();

  const modalConfirmDelete = useRef<RefConfirmModal>(null);

  function onDelete(item: StepFormWizardSessionWithDetails) {
    modalConfirmDelete.current?.setValue(item.id);
    modalConfirmDelete.current?.show(
      t("stepFormWizard.prompts.deleteSession.title"),
      t("shared.confirm"),
      t("shared.back"),
      t("stepFormWizard.prompts.deleteSession.description")
    );
  }
  function onConfirmDelete(id: string) {
    const form = new FormData();
    form.set("action", "delete");
    form.set("id", id);
    submit(form, {
      method: "post",
    });
  }

  return (
    <>
      <div className="md:border-border md:border-b md:py-2">
        <div className="flex items-center justify-between">
          <h3 className="text-foreground text-lg font-medium leading-6">Sessions</h3>
          <div className="flex items-center space-x-2">
            <InputFilters filters={data.filterableProperties} />
          </div>
        </div>
      </div>

      <StepFormWizardSessionsTable items={data.items} metadata={data.metadata} onDelete={onDelete} />
      <ConfirmModal ref={modalConfirmDelete} onYes={onConfirmDelete} />
      <ActionResultModal actionData={actionData} showSuccess={false} />
    </>
  );
}
