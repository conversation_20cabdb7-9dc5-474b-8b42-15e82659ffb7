{"version": 3, "sources": ["../../nanoid/url-alphabet/index.js", "../../nanoid/index.browser.js"], "sourcesContent": ["let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nexport { urlAlphabet }\n", "import { urlAlphabet } from './url-alphabet/index.js'\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    byte &= 63\n    if (byte < 36) {\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n"], "mappings": ";;;AAAA,IAAI,cACF;;;ACAF,IAAI,SAAS,WAAS,OAAO,gBAAgB,IAAI,WAAW,KAAK,CAAC;AAClE,IAAI,eAAe,CAAC,UAAU,aAAa,cAAc;AACvD,MAAI,QAAQ,KAAM,KAAK,IAAI,SAAS,SAAS,CAAC,IAAI,KAAK,OAAQ;AAC/D,MAAI,OAAO,CAAC,EAAG,MAAM,OAAO,cAAe,SAAS;AACpD,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,WAAO,MAAM;AACX,UAAI,QAAQ,UAAU,IAAI;AAC1B,UAAI,IAAI,OAAO;AACf,aAAO,KAAK;AACV,cAAM,SAAS,MAAM,CAAC,IAAI,IAAI,KAAK;AACnC,YAAI,GAAG,WAAW,KAAM,QAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,CAAC,UAAU,OAAO,OACrC,aAAa,UAAU,MAAM,MAAM;AACrC,IAAI,SAAS,CAAC,OAAO,OACnB,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS;AAChE,UAAQ;AACR,MAAI,OAAO,IAAI;AACb,UAAM,KAAK,SAAS,EAAE;AAAA,EACxB,WAAW,OAAO,IAAI;AACpB,WAAO,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY;AAAA,EAC7C,WAAW,OAAO,IAAI;AACpB,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AACA,SAAO;AACT,GAAG,EAAE;", "names": []}