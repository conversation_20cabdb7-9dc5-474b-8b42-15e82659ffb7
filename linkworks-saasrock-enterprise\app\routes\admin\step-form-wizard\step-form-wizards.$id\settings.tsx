import { Tenant } from "@prisma/client";
import { ActionFunction, LoaderFunctionArgs, MetaFunction, redirect, useLoaderData } from "react-router";
import { useSubmit, Form, useActionData } from "react-router";
import { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { SubscriptionProductDto } from "~/application/dtos/subscriptions/SubscriptionProductDto";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import ServerError from "~/components/ui/errors/ServerError";
import InputGroup from "~/components/ui/forms/InputGroup";
import InputSelector from "~/components/ui/input/InputSelector";
import InputText from "~/components/ui/input/InputText";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { getTranslations } from "~/locale/i18next.server";
import {
  StepFormWizardWithDetails,
  getStepFormWizard,
  updateStepFormWizard,
  deleteStepFormWizard,
} from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import { StepFormWizardFilterDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterDto";
import { StepFormWizardFilterType, StepFormWizardFilterTypes } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterTypes";
import SessionFilterModal from "~/modules/shared/components/SessionFilterModal";
import { getAllRolesNames } from "~/utils/db/permissions/roles.db.server";
import { getAllSubscriptionProducts } from "~/utils/db/subscriptionProducts.db.server";
import { adminGetAllTenants } from "~/utils/db/tenants.db.server";
import { UserWithNames, adminGetAllUsersNames } from "~/utils/db/users.db.server";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
type LoaderData = {
  meta: MetaTagsDto;
  item: StepFormWizardWithDetails;
  metadata: {
    users: UserWithNames[];
    tenants: Tenant[];
    subscriptionProducts: SubscriptionProductDto[];
    roles: { id: string; name: string }[];
  };
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  const metadata = {
    users: await adminGetAllUsersNames(),
    tenants: await adminGetAllTenants(),
    subscriptionProducts: await getAllSubscriptionProducts(),
    roles: await getAllRolesNames(),
  };
  const data: LoaderData = {
    meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
    item,
    metadata,
  };
  return data;
};

type ActionData = {
  error?: string;
  success?: string;
};
export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (action === "update") {
    const title = form.get("title")?.toString();
    const type = form.get("type")?.toString();
    const active = form.get("active");
    await updateStepFormWizard(item.id, {
      title: title !== undefined ? title : undefined,
      type: type !== undefined ? (type as "modal" | "page") : undefined,
      active: active !== undefined ? Boolean(active) : undefined,
    });
    return Response.json({ success: "Step form wizard updated" });
  } else if (action === "delete") {
    await verifyUserHasPermission(request, "admin.stepFormWizard.delete");
    await deleteStepFormWizard(item.id);
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();

  const submit = useSubmit();

  const [showFilterModal, setShowFilterModal] = useState<{ item?: StepFormWizardFilterDto; idx?: number }>();

  const [title, setTitle] = useState(data.item.title);
  const [type, setType] = useState(data.item.type);
  const [filters, setFilters] = useState<StepFormWizardFilterDto[]>(
    data.item.filters.map((f) => {
      return {
        type: f.type as StepFormWizardFilterDto["type"],
        value: f.value,
      };
    })
  );

  // const modalConfirm = useRef<RefConfirmModal>(null);
  const modalConfirmDelete = useRef<RefConfirmModal>(null);

  // function activate() {
  //   modalConfirm.current?.show(t("stepFormWizard.prompts.activate.title"), t("shared.confirm"), t("shared.back"), t("stepFormWizard.prompts.activate.description"));
  // }

  // function onConfirmActivate() {
  //   const form = new FormData();
  //   form.set("action", "activate");
  //   submit(form, {
  //     method: "post",
  //   });
  // }

  function onDelete() {
    modalConfirmDelete.current?.show(
      t("stepFormWizard.prompts.deleteStepFormWizard.title"),
      t("shared.confirm"),
      t("shared.back"),
      t("stepFormWizard.prompts.deleteStepFormWizard.description")
    );
  }
  function onConfirmDelete() {
    const form = new FormData();
    form.set("action", "delete");
    submit(form, {
      method: "post",
    });
  }

  function onSaveFilter(item: { type: StepFormWizardFilterType; value: string | null }) {
    const idx = showFilterModal?.idx;
    if (idx !== undefined) {
      filters[idx] = item;
    } else {
      filters.push(item);
    }
    setFilters([...filters]);
    setShowFilterModal(undefined);
  }

  return (
    <div className="mx-auto max-w-2xl flex-1 space-y-5 overflow-x-auto px-2 py-2 xl:overflow-y-auto">
      <InputGroup title={t("shared.details")}>
        <Form method="post" className="divide-y-gray-200 space-y-4 divide-y">
          <input name="action" value="update" hidden readOnly />
          <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-6 sm:gap-x-6">
            <div className="sm:col-span-3">
              <InputText name="title" title={t("stepFormWizard.object.title")} value={title} setValue={setTitle} />
            </div>
            <div className="sm:col-span-3">
              <InputSelector
                name="type"
                title={t("stepFormWizard.object.type")}
                value={type}
                withSearch={false}
                options={[
                  { value: "modal", name: "Modal" },
                  { value: "page", name: "Page" },
                ]}
                setValue={(e) => setType(e?.toString() ?? "")}
              />
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <LoadingButton actionName="update" type="submit">
              {t("shared.save")}
            </LoadingButton>
          </div>
        </Form>
      </InputGroup>

      <InputGroup title={t("stepFormWizard.object.filters")}>
        <Form method="post" className="space-y-2">
          <input name="action" value="set-filters" hidden readOnly />
          {filters.map((filter, index) => {
            return <input type="hidden" name="filters[]" value={JSON.stringify(filter)} key={index} hidden readOnly />;
          })}

          <p className="text-muted-foreground text-sm">Filters are used to determine if the step form wizard should be shown to the user.</p>

          <div className="space-y-2">
            {filters.map((filter, idx) => {
              return (
                <button
                  key={idx}
                  type="button"
                  onClick={() => setShowFilterModal({ item: filter, idx })}
                  className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-3 text-center focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"
                >
                  <div className="flex items-center space-x-1">
                    <div className="font-medium">{filter.type}</div>
                    <div className="italic">{filter.value === null ? <span className="text-red-500">null</span> : filter.value}</div>
                  </div>
                </button>
              );
            })}

            <div className="">
              <button
                type="button"
                onClick={() => setShowFilterModal({ item: undefined, idx: undefined })}
                className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-3 text-center focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"
              >
                <span className="text-foreground block text-sm font-medium">Add filter</span>
              </button>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <LoadingButton actionName="set-filters" type="submit">
              {t("shared.save")}
            </LoadingButton>
          </div>

          <SessionFilterModal
            filters={StepFormWizardFilterTypes.map((f) => f.toString())}
            open={showFilterModal !== undefined}
            item={showFilterModal?.item}
            idx={showFilterModal?.idx}
            onClose={() => setShowFilterModal(undefined)}
            onSave={({ type, value }) => onSaveFilter({ type: type as StepFormWizardFilterType, value })}
            metadata={data.metadata}
            onRemove={(idx) => {
              filters.splice(idx, 1);
              setFilters([...filters]);
            }}
          />
        </Form>
      </InputGroup>

      <InputGroup title={t("shared.dangerZone")} className="bg-red-50">
        <ButtonSecondary destructive onClick={onDelete}>
          {t("shared.delete")}
        </ButtonSecondary>
      </InputGroup>

      <ConfirmModal ref={modalConfirmDelete} onYes={onConfirmDelete} />
      <ActionResultModal actionData={actionData} />
    </div>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
