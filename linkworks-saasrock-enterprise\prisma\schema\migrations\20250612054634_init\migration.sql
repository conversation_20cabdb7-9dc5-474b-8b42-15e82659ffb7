-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "email" TEXT NOT NULL,
    "passwordHash" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "avatar" TEXT,
    "phone" TEXT,
    "defaultTenantId" TEXT,
    "verifyToken" TEXT,
    "githubId" TEXT,
    "azureId" TEXT,
    "googleId" TEXT,
    "locale" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminUser" (
    "userId" TEXT NOT NULL,

    CONSTRAINT "AdminUser_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "Tenant" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "slug" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "icon" TEXT,
    "subscriptionId" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "deactivatedReason" TEXT,

    CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantUser" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "joined" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "TenantUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantUserInvitation" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "pending" BOOLEAN NOT NULL,
    "createdUserId" TEXT,
    "fromUserId" TEXT,

    CONSTRAINT "TenantUserInvitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Registration" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "slug" TEXT,
    "token" TEXT NOT NULL,
    "ipAddress" TEXT,
    "company" TEXT,
    "selectedSubscriptionPriceId" TEXT,
    "createdTenantId" TEXT,

    CONSTRAINT "Registration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Blacklist" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "type" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "registerAttempts" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "Blacklist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantIpAddress" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "userId" TEXT,
    "apiKeyId" TEXT,
    "ip" TEXT NOT NULL,
    "fromUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TenantIpAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantType" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "titlePlural" TEXT NOT NULL,
    "description" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "TenantType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Loyality" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "mail" TEXT NOT NULL,
    "team" TEXT NOT NULL,

    CONSTRAINT "Loyality_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Milestone" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "threshold" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,
    "rewardId" TEXT NOT NULL,

    CONSTRAINT "Milestone_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Reward" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "cost" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,
    "link" TEXT NOT NULL,
    "couponCode" TEXT,

    CONSTRAINT "Reward_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowMilestone" (
    "loyalityId" TEXT NOT NULL,
    "milestoneId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RowMilestone_pkey" PRIMARY KEY ("loyalityId","milestoneId")
);

-- CreateTable
CREATE TABLE "RowReward" (
    "loyalityId" TEXT NOT NULL,
    "rewardId" TEXT NOT NULL,
    "status" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RowReward_pkey" PRIMARY KEY ("loyalityId","rewardId","status")
);

-- CreateTable
CREATE TABLE "RowCoins" (
    "id" TEXT NOT NULL,
    "loyalityId" TEXT NOT NULL,
    "coins" INTEGER NOT NULL,
    "message" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT,

    CONSTRAINT "RowCoins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnalyticsSettings" (
    "id" TEXT NOT NULL,
    "public" BOOLEAN NOT NULL DEFAULT false,
    "ignorePages" TEXT NOT NULL,
    "onlyPages" TEXT NOT NULL,

    CONSTRAINT "AnalyticsSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnalyticsUniqueVisitor" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "cookie" TEXT NOT NULL,
    "via" TEXT,
    "httpReferrer" TEXT,
    "browser" TEXT,
    "browserVersion" TEXT,
    "os" TEXT,
    "osVersion" TEXT,
    "device" TEXT,
    "source" TEXT,
    "medium" TEXT,
    "campaign" TEXT,
    "content" TEXT,
    "term" TEXT,
    "country" TEXT,
    "city" TEXT,
    "fromUrl" TEXT,
    "fromRoute" TEXT,
    "userId" TEXT,
    "portalId" TEXT,
    "portalUserId" TEXT,

    CONSTRAINT "AnalyticsUniqueVisitor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnalyticsPageView" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uniqueVisitorId" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "route" TEXT,
    "portalId" TEXT,
    "portalUserId" TEXT,

    CONSTRAINT "AnalyticsPageView_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnalyticsEvent" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uniqueVisitorId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "category" TEXT,
    "label" TEXT,
    "value" TEXT,
    "url" TEXT,
    "route" TEXT,
    "featureFlagId" TEXT,
    "portalId" TEXT,
    "portalUserId" TEXT,

    CONSTRAINT "AnalyticsEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BlogCategory" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "color" INTEGER NOT NULL,

    CONSTRAINT "BlogCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BlogTag" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "color" INTEGER NOT NULL,

    CONSTRAINT "BlogTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BlogPostTag" (
    "id" TEXT NOT NULL,
    "postId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,

    CONSTRAINT "BlogPostTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BlogPost" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "tenantId" TEXT,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "image" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "readingTime" TEXT NOT NULL,
    "published" BOOLEAN NOT NULL,
    "authorId" TEXT,
    "categoryId" TEXT,
    "contentType" TEXT NOT NULL DEFAULT 'markdown',

    CONSTRAINT "BlogPost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AppConfiguration" (
    "id" TEXT NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "theme" TEXT,
    "authRequireEmailVerification" BOOLEAN NOT NULL DEFAULT false,
    "authRequireOrganization" BOOLEAN NOT NULL DEFAULT true,
    "authRequireName" BOOLEAN NOT NULL DEFAULT true,
    "authRecaptchaSiteKey" TEXT,
    "analyticsEnabled" BOOLEAN NOT NULL DEFAULT true,
    "analyticsSimpleAnalytics" BOOLEAN NOT NULL DEFAULT false,
    "analyticsPlausibleAnalytics" BOOLEAN NOT NULL DEFAULT false,
    "analyticsGoogleAnalyticsTrackingId" TEXT,
    "subscriptionRequired" BOOLEAN NOT NULL DEFAULT true,
    "subscriptionAllowSubscribeBeforeSignUp" BOOLEAN NOT NULL DEFAULT true,
    "subscriptionAllowSignUpBeforeSubscribe" BOOLEAN NOT NULL DEFAULT true,
    "cookiesEnabled" BOOLEAN NOT NULL DEFAULT false,
    "metricsEnabled" BOOLEAN NOT NULL DEFAULT false,
    "metricsLogToConsole" BOOLEAN NOT NULL DEFAULT false,
    "metricsSaveToDatabase" BOOLEAN NOT NULL DEFAULT false,
    "metricsIgnoreUrls" TEXT,
    "brandingLogo" TEXT,
    "brandingLogoDarkMode" TEXT,
    "brandingIcon" TEXT,
    "brandingIconDarkMode" TEXT,
    "brandingFavicon" TEXT,
    "headScripts" TEXT,
    "bodyScripts" TEXT,
    "emailProvider" TEXT,
    "emailFromEmail" TEXT,
    "emailFromName" TEXT,
    "emailSupportEmail" TEXT,

    CONSTRAINT "AppConfiguration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Log" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "userId" TEXT,
    "apiKeyId" TEXT,
    "rowId" TEXT,
    "url" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "details" TEXT,
    "commentId" TEXT,

    CONSTRAINT "Log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKey" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "alias" TEXT NOT NULL,
    "expires" TIMESTAMP(3),
    "active" BOOLEAN NOT NULL,

    CONSTRAINT "ApiKey_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKeyLog" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "apiKeyId" TEXT,
    "tenantId" TEXT,
    "ip" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "params" TEXT NOT NULL,
    "status" INTEGER,
    "duration" INTEGER,
    "error" TEXT,
    "type" TEXT,

    CONSTRAINT "ApiKeyLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Event" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "userId" TEXT,
    "name" TEXT NOT NULL,
    "data" TEXT NOT NULL,
    "resource" TEXT,
    "description" TEXT,

    CONSTRAINT "Event_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EventWebhookAttempt" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "startedAt" TIMESTAMP(3),
    "finishedAt" TIMESTAMP(3),
    "eventId" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "success" BOOLEAN,
    "status" INTEGER,
    "message" TEXT,
    "body" TEXT,

    CONSTRAINT "EventWebhookAttempt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MetricLog" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "env" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "route" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "function" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "userId" TEXT,
    "tenantId" TEXT,

    CONSTRAINT "MetricLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FileUploadProgress" (
    "id" TEXT NOT NULL,
    "fileName" TEXT NOT NULL,
    "progressServer" INTEGER NOT NULL,
    "progressStorage" INTEGER NOT NULL,
    "url" TEXT,
    "error" TEXT,

    CONSTRAINT "FileUploadProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FileChunk" (
    "id" SERIAL NOT NULL,
    "fileUploadId" TEXT NOT NULL,
    "index" INTEGER NOT NULL,
    "data" BYTEA NOT NULL,

    CONSTRAINT "FileChunk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "IpAddress" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ip" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "countryCode" TEXT NOT NULL,
    "countryName" TEXT NOT NULL,
    "regionCode" TEXT NOT NULL,
    "regionName" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "zipCode" TEXT NOT NULL,
    "latitude" DECIMAL(65,30),
    "longitude" DECIMAL(65,30),
    "metadata" TEXT NOT NULL,

    CONSTRAINT "IpAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "IpAddressLog" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ip" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "success" BOOLEAN NOT NULL,
    "error" TEXT,
    "metadata" TEXT,
    "ipAddressId" TEXT,

    CONSTRAINT "IpAddressLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Widget" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "appearance" TEXT NOT NULL,
    "metadata" TEXT NOT NULL,

    CONSTRAINT "Widget_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Credential" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "value" JSONB NOT NULL,

    CONSTRAINT "Credential_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantInboundAddress" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "address" TEXT NOT NULL,

    CONSTRAINT "TenantInboundAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Email" (
    "id" TEXT NOT NULL,
    "tenantInboundAddressId" TEXT,
    "messageId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "subject" TEXT NOT NULL,
    "fromEmail" TEXT NOT NULL,
    "fromName" TEXT,
    "toEmail" TEXT NOT NULL,
    "toName" TEXT,
    "textBody" TEXT NOT NULL,
    "htmlBody" TEXT NOT NULL,

    CONSTRAINT "Email_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailRead" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "emailId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "EmailRead_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailCc" (
    "id" TEXT NOT NULL,
    "emailId" TEXT NOT NULL,
    "toEmail" TEXT NOT NULL,
    "toName" TEXT,

    CONSTRAINT "EmailCc_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailAttachment" (
    "id" TEXT NOT NULL,
    "emailId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "length" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "publicUrl" TEXT,
    "storageBucket" TEXT,
    "storageProvider" TEXT,

    CONSTRAINT "EmailAttachment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailSender" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT,
    "provider" TEXT NOT NULL,
    "stream" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "fromEmail" TEXT NOT NULL,
    "fromName" TEXT,
    "replyToEmail" TEXT,

    CONSTRAINT "EmailSender_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Campaign" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT,
    "emailSenderId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "htmlBody" TEXT NOT NULL,
    "textBody" TEXT,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "track" BOOLEAN NOT NULL,
    "sentAt" TIMESTAMP(3),

    CONSTRAINT "Campaign_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutboundEmail" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "campaignId" TEXT,
    "contactRowId" TEXT,
    "email" TEXT NOT NULL,
    "fromSenderId" TEXT NOT NULL,
    "isPreview" BOOLEAN,
    "error" TEXT,
    "sentAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "bouncedAt" TIMESTAMP(3),
    "spamComplainedAt" TIMESTAMP(3),
    "unsubscribedAt" TIMESTAMP(3),

    CONSTRAINT "OutboundEmail_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutboundEmailOpen" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "firstOpen" BOOLEAN NOT NULL,
    "outboundEmailId" TEXT NOT NULL,

    CONSTRAINT "OutboundEmailOpen_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutboundEmailClick" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "link" TEXT NOT NULL,
    "outboundEmailId" TEXT NOT NULL,

    CONSTRAINT "OutboundEmailClick_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Entity" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "moduleId" TEXT,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "prefix" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'app',
    "title" TEXT NOT NULL,
    "titlePlural" TEXT NOT NULL,
    "isAutogenerated" BOOLEAN NOT NULL,
    "hasApi" BOOLEAN NOT NULL,
    "icon" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL,
    "showInSidebar" BOOLEAN NOT NULL DEFAULT true,
    "hasTags" BOOLEAN NOT NULL DEFAULT true,
    "hasComments" BOOLEAN NOT NULL DEFAULT true,
    "hasTasks" BOOLEAN NOT NULL DEFAULT true,
    "hasActivity" BOOLEAN NOT NULL DEFAULT true,
    "hasBulkDelete" BOOLEAN NOT NULL DEFAULT false,
    "hasViews" BOOLEAN NOT NULL DEFAULT true,
    "defaultVisibility" TEXT NOT NULL DEFAULT 'private',
    "onCreated" TEXT DEFAULT 'redirectToOverview',
    "onEdit" TEXT DEFAULT 'editRoute',
    "promptFlowGroupId" TEXT,

    CONSTRAINT "Entity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Property" (
    "id" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "subtype" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "isHidden" BOOLEAN NOT NULL DEFAULT false,
    "isDisplay" BOOLEAN NOT NULL DEFAULT false,
    "isUnique" BOOLEAN NOT NULL DEFAULT false,
    "isReadOnly" BOOLEAN NOT NULL DEFAULT false,
    "isSortable" BOOLEAN NOT NULL DEFAULT false,
    "isSearchable" BOOLEAN NOT NULL DEFAULT false,
    "isFilterable" BOOLEAN NOT NULL DEFAULT false,
    "showInCreate" BOOLEAN NOT NULL DEFAULT true,
    "canUpdate" BOOLEAN NOT NULL DEFAULT true,
    "isOverviewHeaderProperty" BOOLEAN NOT NULL DEFAULT false,
    "isOverviewSecondaryHeaderProperty" BOOLEAN NOT NULL DEFAULT false,
    "isMetaProperty" BOOLEAN NOT NULL DEFAULT false,
    "formulaId" TEXT,
    "tenantId" TEXT,

    CONSTRAINT "Property_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityView" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdByUserId" TEXT,
    "entityId" TEXT NOT NULL,
    "tenantId" TEXT,
    "userId" TEXT,
    "layout" TEXT NOT NULL DEFAULT 'table',
    "order" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "pageSize" INTEGER NOT NULL,
    "isDefault" BOOLEAN NOT NULL,
    "isSystem" BOOLEAN NOT NULL DEFAULT false,
    "gridColumns" INTEGER DEFAULT 5,
    "gridColumnsSm" INTEGER DEFAULT 2,
    "gridColumnsMd" INTEGER DEFAULT 3,
    "gridColumnsLg" INTEGER DEFAULT 4,
    "gridColumnsXl" INTEGER DEFAULT 5,
    "gridColumns2xl" INTEGER DEFAULT 6,
    "gridGap" TEXT DEFAULT 'sm',
    "groupByPropertyId" TEXT,

    CONSTRAINT "EntityView_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityViewProperty" (
    "id" TEXT NOT NULL,
    "entityViewId" TEXT NOT NULL,
    "propertyId" TEXT,
    "name" TEXT,
    "order" INTEGER NOT NULL,

    CONSTRAINT "EntityViewProperty_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityViewFilter" (
    "id" TEXT NOT NULL,
    "entityViewId" TEXT NOT NULL,
    "match" TEXT NOT NULL DEFAULT 'and',
    "name" TEXT NOT NULL,
    "condition" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "EntityViewFilter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityViewSort" (
    "id" TEXT NOT NULL,
    "entityViewId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "asc" BOOLEAN NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "EntityViewSort_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PropertyAttribute" (
    "id" TEXT NOT NULL,
    "propertyId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "PropertyAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PropertyOption" (
    "id" TEXT NOT NULL,
    "propertyId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "value" TEXT NOT NULL,
    "name" TEXT,
    "color" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "PropertyOption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityTag" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "entityId" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "color" INTEGER NOT NULL,

    CONSTRAINT "EntityTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityTenantUserPermission" (
    "id" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "level" INTEGER NOT NULL,

    CONSTRAINT "EntityTenantUserPermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityWebhook" (
    "id" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,

    CONSTRAINT "EntityWebhook_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityWebhookLog" (
    "id" TEXT NOT NULL,
    "webhookId" TEXT NOT NULL,
    "logId" TEXT NOT NULL,
    "status" INTEGER NOT NULL,
    "error" TEXT,

    CONSTRAINT "EntityWebhookLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityRelationship" (
    "id" TEXT NOT NULL,
    "parentId" TEXT NOT NULL,
    "childId" TEXT NOT NULL,
    "order" INTEGER,
    "title" TEXT,
    "type" TEXT NOT NULL DEFAULT 'one-to-many',
    "required" BOOLEAN NOT NULL DEFAULT false,
    "cascade" BOOLEAN NOT NULL DEFAULT false,
    "distinct" BOOLEAN NOT NULL DEFAULT false,
    "readOnly" BOOLEAN NOT NULL DEFAULT false,
    "hiddenIfEmpty" BOOLEAN NOT NULL DEFAULT false,
    "childEntityViewId" TEXT,
    "parentEntityViewId" TEXT,

    CONSTRAINT "EntityRelationship_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SampleCustomEntity" (
    "rowId" TEXT NOT NULL,
    "customText" TEXT NOT NULL,
    "customNumber" DECIMAL(65,30) NOT NULL,
    "customDate" TIMESTAMP(3) NOT NULL,
    "customBoolean" BOOLEAN NOT NULL,
    "customSelect" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "RowRelationship" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "relationshipId" TEXT NOT NULL,
    "parentId" TEXT NOT NULL,
    "childId" TEXT NOT NULL,
    "metadata" TEXT,

    CONSTRAINT "RowRelationship_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Row" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "entityId" TEXT NOT NULL,
    "tenantId" TEXT,
    "folio" INTEGER NOT NULL,
    "createdByUserId" TEXT,
    "createdByApiKeyId" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "loyalityId" TEXT,

    CONSTRAINT "Row_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowValue" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "rowId" TEXT NOT NULL,
    "propertyId" TEXT NOT NULL,
    "textValue" TEXT,
    "numberValue" DECIMAL(65,30),
    "dateValue" TIMESTAMP(3),
    "booleanValue" BOOLEAN,

    CONSTRAINT "RowValue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowValueMultiple" (
    "id" TEXT NOT NULL,
    "rowValueId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "RowValueMultiple_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowValueRange" (
    "rowValueId" TEXT NOT NULL,
    "numberMin" DECIMAL(65,30),
    "numberMax" DECIMAL(65,30),
    "dateMin" TIMESTAMP(3),
    "dateMax" TIMESTAMP(3)
);

-- CreateTable
CREATE TABLE "RowPermission" (
    "id" TEXT NOT NULL,
    "rowId" TEXT NOT NULL,
    "tenantId" TEXT,
    "roleId" TEXT,
    "groupId" TEXT,
    "userId" TEXT,
    "public" BOOLEAN,
    "access" TEXT NOT NULL DEFAULT 'view',

    CONSTRAINT "RowPermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowMedia" (
    "id" TEXT NOT NULL,
    "rowValueId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "file" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "publicUrl" TEXT,
    "storageBucket" TEXT,
    "storageProvider" TEXT,

    CONSTRAINT "RowMedia_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowTag" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "rowId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,

    CONSTRAINT "RowTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowComment" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT NOT NULL,
    "rowId" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "isDeleted" BOOLEAN,

    CONSTRAINT "RowComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowCommentReaction" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT NOT NULL,
    "rowCommentId" TEXT NOT NULL,
    "reaction" TEXT NOT NULL,

    CONSTRAINT "RowCommentReaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RowTask" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT NOT NULL,
    "rowId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL,
    "completedAt" TIMESTAMP(3),
    "completedByUserId" TEXT,
    "priority" TEXT NOT NULL,
    "assignedToUserId" TEXT,
    "deadline" TIMESTAMP(3),

    CONSTRAINT "RowTask_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityTemplate" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "entityId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "config" TEXT NOT NULL,

    CONSTRAINT "EntityTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityGroup" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "order" INTEGER NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "collapsible" BOOLEAN NOT NULL,
    "section" TEXT,

    CONSTRAINT "EntityGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityGroupEntity" (
    "id" TEXT NOT NULL,
    "entityGroupId" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "allViewId" TEXT,
    "selectMin" INTEGER,
    "selectMax" INTEGER,

    CONSTRAINT "EntityGroupEntity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Formula" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "resultAs" TEXT NOT NULL,
    "calculationTrigger" TEXT NOT NULL,
    "withLogs" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Formula_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FormulaComponent" (
    "id" TEXT NOT NULL,
    "formulaId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "FormulaComponent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FormulaLog" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "formulaId" TEXT NOT NULL,
    "userId" TEXT,
    "tenantId" TEXT,
    "originalTrigger" TEXT,
    "triggeredBy" TEXT NOT NULL,
    "expression" TEXT NOT NULL,
    "result" TEXT NOT NULL,
    "duration" INTEGER NOT NULL DEFAULT 0,
    "error" TEXT,
    "rowValueId" TEXT,

    CONSTRAINT "FormulaLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FormulaComponentLog" (
    "id" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "rowId" TEXT,
    "formulaLogId" TEXT NOT NULL,

    CONSTRAINT "FormulaComponentLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityGroupConfiguration" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "entityGroupId" TEXT NOT NULL,
    "title" TEXT NOT NULL,

    CONSTRAINT "EntityGroupConfiguration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityGroupConfigurationRow" (
    "id" TEXT NOT NULL,
    "entityGroupConfigurationId" TEXT NOT NULL,
    "rowId" TEXT NOT NULL,

    CONSTRAINT "EntityGroupConfigurationRow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKeyEntity" (
    "id" TEXT NOT NULL,
    "apiKeyId" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "create" BOOLEAN NOT NULL,
    "read" BOOLEAN NOT NULL,
    "update" BOOLEAN NOT NULL,
    "delete" BOOLEAN NOT NULL,

    CONSTRAINT "ApiKeyEntity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSettingsRow" (
    "tenantId" TEXT NOT NULL,
    "rowId" TEXT NOT NULL,

    CONSTRAINT "TenantSettingsRow_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "FeatureFlag" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "FeatureFlag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FeatureFlagFilter" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "featureFlagId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT,
    "action" TEXT,

    CONSTRAINT "FeatureFlagFilter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Feedback" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT,
    "userId" TEXT,
    "message" TEXT NOT NULL,
    "fromUrl" TEXT NOT NULL,

    CONSTRAINT "Feedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Survey" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "title" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "isEnabled" BOOLEAN NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "minSubmissions" INTEGER NOT NULL DEFAULT 0,
    "image" TEXT,

    CONSTRAINT "Survey_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveyItem" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "surveyId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "shortName" TEXT,
    "type" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "categories" JSONB NOT NULL,
    "href" TEXT,
    "color" TEXT,
    "options" JSONB NOT NULL,
    "style" TEXT,

    CONSTRAINT "SurveyItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveySubmission" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "surveyId" TEXT NOT NULL,
    "userAnalyticsId" TEXT NOT NULL,
    "ipAddress" TEXT NOT NULL,

    CONSTRAINT "SurveySubmission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveySubmissionResult" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "surveySubmissionId" TEXT NOT NULL,
    "surveItemTitle" TEXT NOT NULL,
    "surveItemType" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "other" TEXT,

    CONSTRAINT "SurveySubmissionResult_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBase" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "basePath" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "defaultLanguage" TEXT NOT NULL,
    "layout" TEXT NOT NULL,
    "color" INTEGER NOT NULL,
    "enabled" BOOLEAN NOT NULL,
    "languages" TEXT NOT NULL,
    "links" TEXT NOT NULL,
    "logo" TEXT NOT NULL,
    "seoImage" TEXT NOT NULL,

    CONSTRAINT "KnowledgeBase_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseCategory" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "seoImage" TEXT NOT NULL,

    CONSTRAINT "KnowledgeBaseCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseCategorySection" (
    "id" TEXT NOT NULL,
    "categoryId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "KnowledgeBaseCategorySection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseArticle" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "knowledgeBaseId" TEXT NOT NULL,
    "categoryId" TEXT,
    "sectionId" TEXT,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "contentDraft" TEXT NOT NULL,
    "contentPublished" TEXT NOT NULL DEFAULT '',
    "contentPublishedAsText" TEXT NOT NULL DEFAULT '',
    "contentType" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "featuredOrder" INTEGER,
    "seoImage" TEXT NOT NULL,
    "publishedAt" TIMESTAMP(3),
    "relatedInArticleId" TEXT,
    "createdByUserId" TEXT,

    CONSTRAINT "KnowledgeBaseArticle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseRelatedArticle" (
    "id" TEXT NOT NULL,
    "articleId" TEXT NOT NULL,
    "relatedArticleId" TEXT NOT NULL,

    CONSTRAINT "KnowledgeBaseRelatedArticle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseViews" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "knowledgeBaseId" TEXT NOT NULL,
    "userAnalyticsId" TEXT NOT NULL,

    CONSTRAINT "KnowledgeBaseViews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KnowledgeBaseArticleViews" (
    "knowledgeBaseArticleId" TEXT NOT NULL,
    "userAnalyticsId" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "KnowledgeBaseArticleUpvotes" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "knowledgeBaseArticleId" TEXT NOT NULL,
    "userAnalyticsId" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "KnowledgeBaseArticleDownvotes" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "knowledgeBaseArticleId" TEXT NOT NULL,
    "userAnalyticsId" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "StepFormWizard" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "realtime" BOOLEAN NOT NULL DEFAULT false,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "canBeDismissed" BOOLEAN NOT NULL DEFAULT true,
    "height" TEXT,

    CONSTRAINT "StepFormWizard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardFilter" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "stepFormWizardId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT,

    CONSTRAINT "StepFormWizardFilter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardStep" (
    "id" TEXT NOT NULL,
    "stepFormWizardId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "block" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardStep_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSession" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "stepFormWizardId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tenantId" TEXT,
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "dismissedAt" TIMESTAMP(3),
    "createdRealtime" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StepFormWizardSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionAction" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "stepFormWizardSessionId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardSessionAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionFilterMatch" (
    "id" TEXT NOT NULL,
    "stepFormWizardFilterId" TEXT NOT NULL,
    "stepFormWizardSessionId" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardSessionFilterMatch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionStep" (
    "id" TEXT NOT NULL,
    "stepFormWizardSessionId" TEXT NOT NULL,
    "stepId" TEXT NOT NULL,
    "seenAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "StepFormWizardSessionStep_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Page" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "slug" TEXT NOT NULL,
    "isPublished" BOOLEAN NOT NULL DEFAULT false,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Page_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PageMetaTag" (
    "id" TEXT NOT NULL,
    "pageId" TEXT,
    "order" INTEGER,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "PageMetaTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PageBlock" (
    "id" TEXT NOT NULL,
    "pageId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "PageBlock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "assignToNewUsers" BOOLEAN NOT NULL,
    "isDefault" BOOLEAN NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Permission" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "isDefault" BOOLEAN NOT NULL,
    "order" INTEGER NOT NULL,
    "entityId" TEXT,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RolePermission" (
    "id" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,

    CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserRole" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "tenantId" TEXT,

    CONSTRAINT "UserRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Group" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdByUserId" TEXT NOT NULL,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "color" INTEGER NOT NULL,

    CONSTRAINT "Group_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GroupUser" (
    "id" TEXT NOT NULL,
    "groupId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "GroupUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Portal" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "createdByUserId" TEXT,
    "subdomain" TEXT NOT NULL,
    "domain" TEXT,
    "title" TEXT NOT NULL,
    "isPublished" BOOLEAN NOT NULL DEFAULT false,
    "stripeAccountId" TEXT,
    "themeColor" TEXT,
    "themeScheme" TEXT,
    "seoTitle" TEXT,
    "seoDescription" TEXT,
    "seoImage" TEXT,
    "seoThumbnail" TEXT,
    "seoTwitterCreator" TEXT,
    "seoTwitterSite" TEXT,
    "seoKeywords" TEXT,
    "authRequireEmailVerification" BOOLEAN NOT NULL DEFAULT false,
    "authRequireOrganization" BOOLEAN NOT NULL DEFAULT true,
    "authRequireName" BOOLEAN NOT NULL DEFAULT true,
    "analyticsSimpleAnalytics" BOOLEAN NOT NULL DEFAULT false,
    "analyticsPlausibleAnalytics" BOOLEAN NOT NULL DEFAULT false,
    "analyticsGoogleAnalyticsTrackingId" TEXT,
    "brandingLogo" TEXT,
    "brandingLogoDarkMode" TEXT,
    "brandingIcon" TEXT,
    "brandingIconDarkMode" TEXT,
    "brandingFavicon" TEXT,
    "affiliatesRewardfulApiKey" TEXT,
    "affiliatesRewardfulUrl" TEXT,
    "metadata" JSONB,

    CONSTRAINT "Portal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalUser" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "portalId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "passwordHash" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "avatar" TEXT,
    "phone" TEXT,
    "verifyToken" TEXT,
    "githubId" TEXT,
    "googleId" TEXT,
    "locale" TEXT,

    CONSTRAINT "PortalUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalPage" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "portalId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "attributes" JSONB,

    CONSTRAINT "PortalPage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalUserRegistration" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "portalId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "slug" TEXT,
    "token" TEXT NOT NULL,
    "ipAddress" TEXT,
    "company" TEXT,
    "selectedSubscriptionPriceId" TEXT,
    "createdPortalUserId" TEXT,

    CONSTRAINT "PortalUserRegistration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalSubscriptionProduct" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "stripeId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL,
    "model" INTEGER NOT NULL,
    "public" BOOLEAN NOT NULL,
    "groupTitle" TEXT,
    "groupDescription" TEXT,
    "description" TEXT,
    "badge" TEXT,
    "billingAddressCollection" TEXT NOT NULL DEFAULT 'auto',
    "hasQuantity" BOOLEAN NOT NULL DEFAULT false,
    "canBuyAgain" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PortalSubscriptionProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalSubscriptionPrice" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "stripeId" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "billingPeriod" INTEGER NOT NULL,
    "price" DECIMAL(65,30) NOT NULL,
    "currency" TEXT NOT NULL,
    "trialDays" INTEGER NOT NULL,
    "active" BOOLEAN NOT NULL,

    CONSTRAINT "PortalSubscriptionPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalSubscriptionFeature" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "value" INTEGER NOT NULL,
    "href" TEXT,
    "badge" TEXT,
    "accumulate" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PortalSubscriptionFeature_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalUserSubscription" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "portalUserId" TEXT NOT NULL,
    "stripeCustomerId" TEXT,

    CONSTRAINT "PortalUserSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalUserSubscriptionProduct" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "portalUserSubscriptionId" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "cancelledAt" TIMESTAMP(3),
    "endsAt" TIMESTAMP(3),
    "stripeSubscriptionId" TEXT,
    "quantity" INTEGER,
    "fromCheckoutSessionId" TEXT,
    "currentPeriodStart" TIMESTAMP(3),
    "currentPeriodEnd" TIMESTAMP(3),

    CONSTRAINT "PortalUserSubscriptionProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalUserSubscriptionProductPrice" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "portalUserSubscriptionProductId" TEXT NOT NULL,
    "subscriptionPriceId" TEXT,

    CONSTRAINT "PortalUserSubscriptionProductPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PortalCheckoutSessionStatus" (
    "id" TEXT NOT NULL,
    "portalId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "pending" BOOLEAN NOT NULL DEFAULT true,
    "email" TEXT NOT NULL,
    "fromUrl" TEXT NOT NULL,
    "fromUserId" TEXT,
    "createdUserId" TEXT
);

-- CreateTable
CREATE TABLE "PromptFlowGroup" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "PromptFlowGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowGroupTemplate" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "promptFlowGroupId" TEXT NOT NULL,

    CONSTRAINT "PromptFlowGroupTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowGroupEntity" (
    "entityId" TEXT NOT NULL,
    "promptFlowGroupId" TEXT NOT NULL,

    CONSTRAINT "PromptFlowGroupEntity_pkey" PRIMARY KEY ("entityId","promptFlowGroupId")
);

-- CreateTable
CREATE TABLE "PromptFlow" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "model" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "actionTitle" TEXT,
    "executionType" TEXT NOT NULL DEFAULT 'sequential',
    "promptFlowGroupId" TEXT,
    "stream" BOOLEAN NOT NULL DEFAULT false,
    "public" BOOLEAN NOT NULL DEFAULT true,
    "inputEntityId" TEXT,

    CONSTRAINT "PromptFlow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowInputVariable" (
    "id" TEXT NOT NULL,
    "promptFlowId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "isRequired" BOOLEAN NOT NULL,

    CONSTRAINT "PromptFlowInputVariable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptTemplate" (
    "id" TEXT NOT NULL,
    "flowId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "template" TEXT NOT NULL,
    "temperature" DECIMAL(65,30) NOT NULL,
    "maxTokens" INTEGER,
    "generations" INTEGER,

    CONSTRAINT "PromptTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowOutput" (
    "id" TEXT NOT NULL,
    "promptFlowId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,

    CONSTRAINT "PromptFlowOutput_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowOutputMapping" (
    "id" TEXT NOT NULL,
    "promptFlowOutputId" TEXT NOT NULL,
    "promptTemplateId" TEXT NOT NULL,
    "propertyId" TEXT NOT NULL,

    CONSTRAINT "PromptFlowOutputMapping_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptFlowExecution" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "flowId" TEXT NOT NULL,
    "model" TEXT,
    "userId" TEXT,
    "tenantId" TEXT,
    "status" TEXT NOT NULL,
    "error" TEXT,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "duration" INTEGER,

    CONSTRAINT "PromptFlowExecution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptTemplateResult" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "flowExecutionId" TEXT NOT NULL,
    "templateId" TEXT,
    "order" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "response" TEXT,
    "error" TEXT,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "PromptTemplateResult_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionProduct" (
    "id" TEXT NOT NULL,
    "stripeId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL,
    "model" INTEGER NOT NULL,
    "public" BOOLEAN NOT NULL,
    "groupTitle" TEXT,
    "groupDescription" TEXT,
    "description" TEXT,
    "badge" TEXT,
    "billingAddressCollection" TEXT NOT NULL DEFAULT 'auto',
    "hasQuantity" BOOLEAN NOT NULL DEFAULT false,
    "canBuyAgain" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SubscriptionProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionPrice" (
    "id" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "stripeId" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "billingPeriod" INTEGER NOT NULL,
    "price" DECIMAL(65,30) NOT NULL,
    "currency" TEXT NOT NULL,
    "trialDays" INTEGER NOT NULL,
    "active" BOOLEAN NOT NULL,

    CONSTRAINT "SubscriptionPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionUsageBasedPrice" (
    "id" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "stripeId" TEXT NOT NULL,
    "billingPeriod" INTEGER NOT NULL,
    "currency" TEXT NOT NULL,
    "unit" TEXT NOT NULL,
    "unitTitle" TEXT NOT NULL,
    "unitTitlePlural" TEXT NOT NULL,
    "usageType" TEXT NOT NULL,
    "aggregateUsage" TEXT NOT NULL,
    "tiersMode" TEXT NOT NULL,
    "billingScheme" TEXT NOT NULL,

    CONSTRAINT "SubscriptionUsageBasedPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionUsageBasedTier" (
    "id" TEXT NOT NULL,
    "subscriptionUsageBasedPriceId" TEXT NOT NULL,
    "from" INTEGER NOT NULL,
    "to" INTEGER,
    "perUnitPrice" DECIMAL(65,30),
    "flatFeePrice" DECIMAL(65,30),

    CONSTRAINT "SubscriptionUsageBasedTier_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionFeature" (
    "id" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" INTEGER NOT NULL,
    "value" INTEGER NOT NULL,
    "href" TEXT,
    "badge" TEXT,
    "accumulate" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SubscriptionFeature_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSubscription" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "stripeCustomerId" TEXT,

    CONSTRAINT "TenantSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSubscriptionProduct" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantSubscriptionId" TEXT NOT NULL,
    "subscriptionProductId" TEXT NOT NULL,
    "cancelledAt" TIMESTAMP(3),
    "endsAt" TIMESTAMP(3),
    "stripeSubscriptionId" TEXT,
    "quantity" INTEGER,
    "fromCheckoutSessionId" TEXT,
    "currentPeriodStart" TIMESTAMP(3),
    "currentPeriodEnd" TIMESTAMP(3),

    CONSTRAINT "TenantSubscriptionProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSubscriptionProductPrice" (
    "id" TEXT NOT NULL,
    "tenantSubscriptionProductId" TEXT NOT NULL,
    "subscriptionPriceId" TEXT,
    "subscriptionUsageBasedPriceId" TEXT,

    CONSTRAINT "TenantSubscriptionProductPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSubscriptionUsageRecord" (
    "id" TEXT NOT NULL,
    "tenantSubscriptionProductPriceId" TEXT NOT NULL,
    "timestamp" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "stripeSubscriptionItemId" TEXT,

    CONSTRAINT "TenantSubscriptionUsageRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CheckoutSessionStatus" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "id" TEXT NOT NULL,
    "pending" BOOLEAN NOT NULL DEFAULT true,
    "email" TEXT NOT NULL,
    "fromUrl" TEXT NOT NULL,
    "fromUserId" TEXT,
    "fromTenantId" TEXT,
    "createdUserId" TEXT,
    "createdTenantId" TEXT
);

-- CreateTable
CREATE TABLE "Credit" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "tenantId" TEXT NOT NULL,
    "userId" TEXT,
    "amount" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "objectId" TEXT,

    CONSTRAINT "Credit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Workflow" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "tenantId" TEXT,
    "createdByUserId" TEXT,
    "appliesToAllTenants" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Workflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowBlock" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "isTrigger" BOOLEAN NOT NULL DEFAULT false,
    "isBlock" BOOLEAN NOT NULL DEFAULT false,
    "input" TEXT NOT NULL,

    CONSTRAINT "WorkflowBlock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowBlockConditionGroup" (
    "id" TEXT NOT NULL,
    "workflowBlockId" TEXT NOT NULL,
    "index" INTEGER NOT NULL,
    "type" TEXT NOT NULL,

    CONSTRAINT "WorkflowBlockConditionGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowBlockCondition" (
    "id" TEXT NOT NULL,
    "workflowBlockConditionGroupId" TEXT NOT NULL,
    "index" INTEGER NOT NULL,
    "variable" TEXT NOT NULL,
    "operator" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "WorkflowBlockCondition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowBlockToBlock" (
    "id" TEXT NOT NULL,
    "fromBlockId" TEXT NOT NULL,
    "toBlockId" TEXT NOT NULL,
    "condition" TEXT,

    CONSTRAINT "WorkflowBlockToBlock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowExecution" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "workflowId" TEXT NOT NULL,
    "tenantId" TEXT,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "input" TEXT NOT NULL,
    "output" TEXT,
    "duration" INTEGER,
    "endedAt" TIMESTAMP(3),
    "error" TEXT,
    "waitingBlockId" TEXT,
    "createdByUserId" TEXT,
    "appliesToAllTenants" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "WorkflowExecution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowInputExample" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "workflowId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "input" TEXT,

    CONSTRAINT "WorkflowInputExample_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowBlockExecution" (
    "id" TEXT NOT NULL,
    "workflowExecutionId" TEXT NOT NULL,
    "workflowBlockId" TEXT NOT NULL,
    "fromWorkflowBlockId" TEXT,
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3) NOT NULL,
    "input" TEXT,
    "output" TEXT,
    "duration" INTEGER,
    "endedAt" TIMESTAMP(3),
    "error" TEXT,

    CONSTRAINT "WorkflowBlockExecution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowVariable" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "createdByUserId" TEXT,
    "userId" TEXT,

    CONSTRAINT "WorkflowVariable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkflowCredential" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "tenantId" TEXT,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "createdByUserId" TEXT,
    "userId" TEXT,

    CONSTRAINT "WorkflowCredential_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_TenantToTenantType" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_TenantToTenantType_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_SubscriptionProductToTenantType" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_SubscriptionProductToTenantType_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_githubId_key" ON "User"("githubId");

-- CreateIndex
CREATE UNIQUE INDEX "User_azureId_key" ON "User"("azureId");

-- CreateIndex
CREATE UNIQUE INDEX "User_googleId_key" ON "User"("googleId");

-- CreateIndex
CREATE UNIQUE INDEX "AdminUser_userId_key" ON "AdminUser"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_slug_key" ON "Tenant"("slug");

-- CreateIndex
CREATE INDEX "Tenant_slug_idx" ON "Tenant"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "TenantUserInvitation_createdUserId_key" ON "TenantUserInvitation"("createdUserId");

-- CreateIndex
CREATE UNIQUE INDEX "Registration_email_key" ON "Registration"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Registration_token_key" ON "Registration"("token");

-- CreateIndex
CREATE UNIQUE INDEX "Registration_createdTenantId_key" ON "Registration"("createdTenantId");

-- CreateIndex
CREATE UNIQUE INDEX "TenantIpAddress_tenantId_ip_userId_apiKeyId_key" ON "TenantIpAddress"("tenantId", "ip", "userId", "apiKeyId");

-- CreateIndex
CREATE UNIQUE INDEX "TenantType_title_key" ON "TenantType"("title");

-- CreateIndex
CREATE UNIQUE INDEX "Loyality_mail_key" ON "Loyality"("mail");

-- CreateIndex
CREATE UNIQUE INDEX "Milestone_order_key" ON "Milestone"("order");

-- CreateIndex
CREATE UNIQUE INDEX "Milestone_rewardId_key" ON "Milestone"("rewardId");

-- CreateIndex
CREATE UNIQUE INDEX "AnalyticsUniqueVisitor_cookie_key" ON "AnalyticsUniqueVisitor"("cookie");

-- CreateIndex
CREATE UNIQUE INDEX "BlogCategory_tenantId_name_key" ON "BlogCategory"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "BlogTag_name_key" ON "BlogTag"("name");

-- CreateIndex
CREATE UNIQUE INDEX "BlogPost_tenantId_slug_key" ON "BlogPost"("tenantId", "slug");

-- CreateIndex
CREATE INDEX "ApiKey_key_idx" ON "ApiKey"("key");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_tenantId_alias_key" ON "ApiKey"("tenantId", "alias");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_tenantId_key_key" ON "ApiKey"("tenantId", "key");

-- CreateIndex
CREATE INDEX "api_key_log_tenant" ON "ApiKeyLog"("tenantId");

-- CreateIndex
CREATE INDEX "api_key_log_tenant_created_at" ON "ApiKeyLog"("tenantId", "createdAt");

-- CreateIndex
CREATE INDEX "api_key_log_tenant_type" ON "ApiKeyLog"("tenantId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "IpAddress_ip_key" ON "IpAddress"("ip");

-- CreateIndex
CREATE UNIQUE INDEX "Widget_tenantId_name_key" ON "Widget"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Credential_name_key" ON "Credential"("name");

-- CreateIndex
CREATE UNIQUE INDEX "TenantInboundAddress_address_key" ON "TenantInboundAddress"("address");

-- CreateIndex
CREATE UNIQUE INDEX "Email_messageId_key" ON "Email"("messageId");

-- CreateIndex
CREATE UNIQUE INDEX "Entity_name_key" ON "Entity"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Entity_slug_key" ON "Entity"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Entity_prefix_key" ON "Entity"("prefix");

-- CreateIndex
CREATE INDEX "entity_name" ON "Entity"("name");

-- CreateIndex
CREATE INDEX "entity_slug" ON "Entity"("slug");

-- CreateIndex
CREATE INDEX "entity_property" ON "Property"("entityId");

-- CreateIndex
CREATE INDEX "entity_property_name" ON "Property"("entityId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Property_entityId_name_tenantId_key" ON "Property"("entityId", "name", "tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "Property_entityId_title_tenantId_key" ON "Property"("entityId", "title", "tenantId");

-- CreateIndex
CREATE INDEX "entity_view" ON "EntityView"("entityId");

-- CreateIndex
CREATE INDEX "entity_view_name" ON "EntityView"("entityId", "name");

-- CreateIndex
CREATE INDEX "entity_view_property" ON "EntityViewProperty"("entityViewId");

-- CreateIndex
CREATE INDEX "entity_view_property_name" ON "EntityViewProperty"("entityViewId", "name");

-- CreateIndex
CREATE INDEX "entity_view_filter" ON "EntityViewFilter"("entityViewId");

-- CreateIndex
CREATE INDEX "entity_view_filter_name" ON "EntityViewFilter"("entityViewId", "name");

-- CreateIndex
CREATE INDEX "entity_view_sort" ON "EntityViewSort"("entityViewId");

-- CreateIndex
CREATE INDEX "entity_view_sort_name" ON "EntityViewSort"("entityViewId", "name");

-- CreateIndex
CREATE INDEX "property_attribute" ON "PropertyAttribute"("propertyId");

-- CreateIndex
CREATE INDEX "property_attribute_name" ON "PropertyAttribute"("propertyId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "PropertyAttribute_propertyId_name_key" ON "PropertyAttribute"("propertyId", "name");

-- CreateIndex
CREATE INDEX "property_option" ON "PropertyOption"("propertyId");

-- CreateIndex
CREATE INDEX "property_option_name" ON "PropertyOption"("propertyId", "name");

-- CreateIndex
CREATE INDEX "entity_tag" ON "EntityTag"("entityId");

-- CreateIndex
CREATE INDEX "entity_tag_value" ON "EntityTag"("entityId", "value");

-- CreateIndex
CREATE INDEX "parent_entity_relationship" ON "EntityRelationship"("parentId");

-- CreateIndex
CREATE INDEX "child_entity_relationship" ON "EntityRelationship"("childId");

-- CreateIndex
CREATE INDEX "parent_child_entity_relationship" ON "EntityRelationship"("parentId", "childId");

-- CreateIndex
CREATE INDEX "parent_child_entity_relationship_order" ON "EntityRelationship"("parentId", "childId", "order");

-- CreateIndex
CREATE UNIQUE INDEX "EntityRelationship_parentId_childId_title_key" ON "EntityRelationship"("parentId", "childId", "title");

-- CreateIndex
CREATE UNIQUE INDEX "SampleCustomEntity_rowId_key" ON "SampleCustomEntity"("rowId");

-- CreateIndex
CREATE INDEX "parent_row_relationship" ON "RowRelationship"("parentId");

-- CreateIndex
CREATE INDEX "child_row_relationship" ON "RowRelationship"("childId");

-- CreateIndex
CREATE INDEX "parent_child_row_relationship" ON "RowRelationship"("parentId", "childId");

-- CreateIndex
CREATE UNIQUE INDEX "RowRelationship_parentId_childId_key" ON "RowRelationship"("parentId", "childId");

-- CreateIndex
CREATE INDEX "row_deletedAt" ON "Row"("deletedAt");

-- CreateIndex
CREATE INDEX "row_entity" ON "Row"("entityId");

-- CreateIndex
CREATE INDEX "row_entity_tenant" ON "Row"("entityId", "tenantId");

-- CreateIndex
CREATE INDEX "row_entity_tenant_created_at" ON "Row"("entityId", "tenantId", "createdAt");

-- CreateIndex
CREATE INDEX "row_tenant" ON "Row"("tenantId");

-- CreateIndex
CREATE INDEX "row_createdByUserId" ON "Row"("createdByUserId");

-- CreateIndex
CREATE INDEX "row_value_row" ON "RowValue"("rowId");

-- CreateIndex
CREATE INDEX "row_value_row_property" ON "RowValue"("rowId", "propertyId");

-- CreateIndex
CREATE INDEX "row_value_multiple_row_value" ON "RowValueMultiple"("rowValueId");

-- CreateIndex
CREATE UNIQUE INDEX "RowValueRange_rowValueId_key" ON "RowValueRange"("rowValueId");

-- CreateIndex
CREATE INDEX "row_value_range_row_value" ON "RowValueRange"("rowValueId");

-- CreateIndex
CREATE INDEX "row_permission_row" ON "RowPermission"("rowId");

-- CreateIndex
CREATE INDEX "row_permission_row_tenant" ON "RowPermission"("rowId", "tenantId");

-- CreateIndex
CREATE INDEX "row_permission_row_role" ON "RowPermission"("rowId", "roleId");

-- CreateIndex
CREATE INDEX "row_permission_row_group" ON "RowPermission"("rowId", "groupId");

-- CreateIndex
CREATE INDEX "row_permission_row_user" ON "RowPermission"("rowId", "userId");

-- CreateIndex
CREATE INDEX "row_permission_public" ON "RowPermission"("public");

-- CreateIndex
CREATE INDEX "row_media_row_value" ON "RowMedia"("rowValueId");

-- CreateIndex
CREATE INDEX "row_media_row_value_name" ON "RowMedia"("rowValueId", "name");

-- CreateIndex
CREATE INDEX "row_tag_row" ON "RowTag"("rowId");

-- CreateIndex
CREATE INDEX "row_tag_row_tag" ON "RowTag"("rowId", "tagId");

-- CreateIndex
CREATE INDEX "row_comment_row" ON "RowComment"("rowId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityTemplate_tenantId_entityId_title_key" ON "EntityTemplate"("tenantId", "entityId", "title");

-- CreateIndex
CREATE UNIQUE INDEX "EntityGroup_slug_key" ON "EntityGroup"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "EntityGroupConfigurationRow_entityGroupConfigurationId_rowI_key" ON "EntityGroupConfigurationRow"("entityGroupConfigurationId", "rowId");

-- CreateIndex
CREATE UNIQUE INDEX "TenantSettingsRow_tenantId_key" ON "TenantSettingsRow"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_name_description_key" ON "FeatureFlag"("name", "description");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBase_slug_key" ON "KnowledgeBase"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseCategory_knowledgeBaseId_slug_key" ON "KnowledgeBaseCategory"("knowledgeBaseId", "slug");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseArticle_knowledgeBaseId_slug_key" ON "KnowledgeBaseArticle"("knowledgeBaseId", "slug");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseViews_knowledgeBaseId_userAnalyticsId_key" ON "KnowledgeBaseViews"("knowledgeBaseId", "userAnalyticsId");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseArticleViews_knowledgeBaseArticleId_userAnalyt_key" ON "KnowledgeBaseArticleViews"("knowledgeBaseArticleId", "userAnalyticsId");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseArticleUpvotes_knowledgeBaseArticleId_userAnal_key" ON "KnowledgeBaseArticleUpvotes"("knowledgeBaseArticleId", "userAnalyticsId");

-- CreateIndex
CREATE UNIQUE INDEX "KnowledgeBaseArticleDownvotes_knowledgeBaseArticleId_userAn_key" ON "KnowledgeBaseArticleDownvotes"("knowledgeBaseArticleId", "userAnalyticsId");

-- CreateIndex
CREATE UNIQUE INDEX "StepFormWizardSession_stepFormWizardId_userId_tenantId_key" ON "StepFormWizardSession"("stepFormWizardId", "userId", "tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "Page_slug_key" ON "Page"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "PageMetaTag_pageId_name_value_key" ON "PageMetaTag"("pageId", "name", "value");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Permission_name_key" ON "Permission"("name");

-- CreateIndex
CREATE INDEX "group_createdByUserId" ON "Group"("createdByUserId");

-- CreateIndex
CREATE UNIQUE INDEX "Portal_subdomain_key" ON "Portal"("subdomain");

-- CreateIndex
CREATE UNIQUE INDEX "Portal_domain_key" ON "Portal"("domain");

-- CreateIndex
CREATE INDEX "PortalUser_portalId_idx" ON "PortalUser"("portalId");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUser_portalId_email_key" ON "PortalUser"("portalId", "email");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUser_portalId_githubId_key" ON "PortalUser"("portalId", "githubId");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUser_portalId_googleId_key" ON "PortalUser"("portalId", "googleId");

-- CreateIndex
CREATE UNIQUE INDEX "PortalPage_portalId_name_key" ON "PortalPage"("portalId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUserRegistration_token_key" ON "PortalUserRegistration"("token");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUserRegistration_createdPortalUserId_key" ON "PortalUserRegistration"("createdPortalUserId");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUserRegistration_portalId_email_key" ON "PortalUserRegistration"("portalId", "email");

-- CreateIndex
CREATE UNIQUE INDEX "PortalUserSubscription_portalUserId_key" ON "PortalUserSubscription"("portalUserId");

-- CreateIndex
CREATE UNIQUE INDEX "PortalCheckoutSessionStatus_id_key" ON "PortalCheckoutSessionStatus"("id");

-- CreateIndex
CREATE UNIQUE INDEX "PromptFlowOutputMapping_promptFlowOutputId_promptTemplateId_key" ON "PromptFlowOutputMapping"("promptFlowOutputId", "promptTemplateId", "propertyId");

-- CreateIndex
CREATE UNIQUE INDEX "TenantSubscription_tenantId_key" ON "TenantSubscription"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "CheckoutSessionStatus_id_key" ON "CheckoutSessionStatus"("id");

-- CreateIndex
CREATE INDEX "Credit_tenantId_userId_idx" ON "Credit"("tenantId", "userId");

-- CreateIndex
CREATE INDEX "Credit_tenantId_createdAt_idx" ON "Credit"("tenantId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "WorkflowBlockToBlock_fromBlockId_toBlockId_key" ON "WorkflowBlockToBlock"("fromBlockId", "toBlockId");

-- CreateIndex
CREATE UNIQUE INDEX "WorkflowInputExample_workflowId_title_key" ON "WorkflowInputExample"("workflowId", "title");

-- CreateIndex
CREATE UNIQUE INDEX "WorkflowVariable_tenantId_name_key" ON "WorkflowVariable"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "WorkflowCredential_tenantId_name_key" ON "WorkflowCredential"("tenantId", "name");

-- CreateIndex
CREATE INDEX "_TenantToTenantType_B_index" ON "_TenantToTenantType"("B");

-- CreateIndex
CREATE INDEX "_SubscriptionProductToTenantType_B_index" ON "_SubscriptionProductToTenantType"("B");

-- AddForeignKey
ALTER TABLE "AdminUser" ADD CONSTRAINT "AdminUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUser" ADD CONSTRAINT "TenantUser_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUser" ADD CONSTRAINT "TenantUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUserInvitation" ADD CONSTRAINT "TenantUserInvitation_fromUserId_fkey" FOREIGN KEY ("fromUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUserInvitation" ADD CONSTRAINT "TenantUserInvitation_createdUserId_fkey" FOREIGN KEY ("createdUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUserInvitation" ADD CONSTRAINT "TenantUserInvitation_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Registration" ADD CONSTRAINT "Registration_createdTenantId_fkey" FOREIGN KEY ("createdTenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantIpAddress" ADD CONSTRAINT "TenantIpAddress_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantIpAddress" ADD CONSTRAINT "TenantIpAddress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantIpAddress" ADD CONSTRAINT "TenantIpAddress_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Milestone" ADD CONSTRAINT "Milestone_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES "Reward"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowMilestone" ADD CONSTRAINT "RowMilestone_loyalityId_fkey" FOREIGN KEY ("loyalityId") REFERENCES "Loyality"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowMilestone" ADD CONSTRAINT "RowMilestone_milestoneId_fkey" FOREIGN KEY ("milestoneId") REFERENCES "Milestone"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowReward" ADD CONSTRAINT "RowReward_loyalityId_fkey" FOREIGN KEY ("loyalityId") REFERENCES "Loyality"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowReward" ADD CONSTRAINT "RowReward_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES "Reward"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowCoins" ADD CONSTRAINT "RowCoins_loyalityId_fkey" FOREIGN KEY ("loyalityId") REFERENCES "Loyality"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowCoins" ADD CONSTRAINT "RowCoins_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsUniqueVisitor" ADD CONSTRAINT "AnalyticsUniqueVisitor_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsUniqueVisitor" ADD CONSTRAINT "AnalyticsUniqueVisitor_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsUniqueVisitor" ADD CONSTRAINT "AnalyticsUniqueVisitor_portalUserId_fkey" FOREIGN KEY ("portalUserId") REFERENCES "PortalUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsPageView" ADD CONSTRAINT "AnalyticsPageView_uniqueVisitorId_fkey" FOREIGN KEY ("uniqueVisitorId") REFERENCES "AnalyticsUniqueVisitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsPageView" ADD CONSTRAINT "AnalyticsPageView_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsPageView" ADD CONSTRAINT "AnalyticsPageView_portalUserId_fkey" FOREIGN KEY ("portalUserId") REFERENCES "PortalUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsEvent" ADD CONSTRAINT "AnalyticsEvent_uniqueVisitorId_fkey" FOREIGN KEY ("uniqueVisitorId") REFERENCES "AnalyticsUniqueVisitor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsEvent" ADD CONSTRAINT "AnalyticsEvent_featureFlagId_fkey" FOREIGN KEY ("featureFlagId") REFERENCES "FeatureFlag"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsEvent" ADD CONSTRAINT "AnalyticsEvent_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalyticsEvent" ADD CONSTRAINT "AnalyticsEvent_portalUserId_fkey" FOREIGN KEY ("portalUserId") REFERENCES "PortalUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogCategory" ADD CONSTRAINT "BlogCategory_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogTag" ADD CONSTRAINT "BlogTag_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogPostTag" ADD CONSTRAINT "BlogPostTag_postId_fkey" FOREIGN KEY ("postId") REFERENCES "BlogPost"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogPostTag" ADD CONSTRAINT "BlogPostTag_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "BlogTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogPost" ADD CONSTRAINT "BlogPost_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogPost" ADD CONSTRAINT "BlogPost_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BlogPost" ADD CONSTRAINT "BlogPost_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "BlogCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Log" ADD CONSTRAINT "Log_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Log" ADD CONSTRAINT "Log_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "RowComment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Log" ADD CONSTRAINT "Log_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Log" ADD CONSTRAINT "Log_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Log" ADD CONSTRAINT "Log_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKeyLog" ADD CONSTRAINT "ApiKeyLog_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKeyLog" ADD CONSTRAINT "ApiKeyLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Event" ADD CONSTRAINT "Event_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Event" ADD CONSTRAINT "Event_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventWebhookAttempt" ADD CONSTRAINT "EventWebhookAttempt_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MetricLog" ADD CONSTRAINT "MetricLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MetricLog" ADD CONSTRAINT "MetricLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FileChunk" ADD CONSTRAINT "FileChunk_fileUploadId_fkey" FOREIGN KEY ("fileUploadId") REFERENCES "FileUploadProgress"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "IpAddressLog" ADD CONSTRAINT "IpAddressLog_ipAddressId_fkey" FOREIGN KEY ("ipAddressId") REFERENCES "IpAddress"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Widget" ADD CONSTRAINT "Widget_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantInboundAddress" ADD CONSTRAINT "TenantInboundAddress_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Email" ADD CONSTRAINT "Email_tenantInboundAddressId_fkey" FOREIGN KEY ("tenantInboundAddressId") REFERENCES "TenantInboundAddress"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailRead" ADD CONSTRAINT "EmailRead_emailId_fkey" FOREIGN KEY ("emailId") REFERENCES "Email"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailRead" ADD CONSTRAINT "EmailRead_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailCc" ADD CONSTRAINT "EmailCc_emailId_fkey" FOREIGN KEY ("emailId") REFERENCES "Email"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailAttachment" ADD CONSTRAINT "EmailAttachment_emailId_fkey" FOREIGN KEY ("emailId") REFERENCES "Email"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailSender" ADD CONSTRAINT "EmailSender_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Campaign" ADD CONSTRAINT "Campaign_emailSenderId_fkey" FOREIGN KEY ("emailSenderId") REFERENCES "EmailSender"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Campaign" ADD CONSTRAINT "Campaign_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmail" ADD CONSTRAINT "OutboundEmail_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmail" ADD CONSTRAINT "OutboundEmail_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "Campaign"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmail" ADD CONSTRAINT "OutboundEmail_contactRowId_fkey" FOREIGN KEY ("contactRowId") REFERENCES "Row"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmail" ADD CONSTRAINT "OutboundEmail_fromSenderId_fkey" FOREIGN KEY ("fromSenderId") REFERENCES "EmailSender"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmailOpen" ADD CONSTRAINT "OutboundEmailOpen_outboundEmailId_fkey" FOREIGN KEY ("outboundEmailId") REFERENCES "OutboundEmail"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutboundEmailClick" ADD CONSTRAINT "OutboundEmailClick_outboundEmailId_fkey" FOREIGN KEY ("outboundEmailId") REFERENCES "OutboundEmail"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Property" ADD CONSTRAINT "Property_formulaId_fkey" FOREIGN KEY ("formulaId") REFERENCES "Formula"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Property" ADD CONSTRAINT "Property_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Property" ADD CONSTRAINT "Property_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityView" ADD CONSTRAINT "EntityView_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityView" ADD CONSTRAINT "EntityView_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityView" ADD CONSTRAINT "EntityView_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityView" ADD CONSTRAINT "EntityView_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityView" ADD CONSTRAINT "EntityView_groupByPropertyId_fkey" FOREIGN KEY ("groupByPropertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityViewProperty" ADD CONSTRAINT "EntityViewProperty_entityViewId_fkey" FOREIGN KEY ("entityViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityViewProperty" ADD CONSTRAINT "EntityViewProperty_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityViewFilter" ADD CONSTRAINT "EntityViewFilter_entityViewId_fkey" FOREIGN KEY ("entityViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityViewSort" ADD CONSTRAINT "EntityViewSort_entityViewId_fkey" FOREIGN KEY ("entityViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PropertyAttribute" ADD CONSTRAINT "PropertyAttribute_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PropertyOption" ADD CONSTRAINT "PropertyOption_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityTag" ADD CONSTRAINT "EntityTag_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityTenantUserPermission" ADD CONSTRAINT "EntityTenantUserPermission_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityWebhook" ADD CONSTRAINT "EntityWebhook_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityWebhookLog" ADD CONSTRAINT "EntityWebhookLog_logId_fkey" FOREIGN KEY ("logId") REFERENCES "Log"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityWebhookLog" ADD CONSTRAINT "EntityWebhookLog_webhookId_fkey" FOREIGN KEY ("webhookId") REFERENCES "EntityWebhook"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityRelationship" ADD CONSTRAINT "EntityRelationship_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityRelationship" ADD CONSTRAINT "EntityRelationship_childId_fkey" FOREIGN KEY ("childId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityRelationship" ADD CONSTRAINT "EntityRelationship_childEntityViewId_fkey" FOREIGN KEY ("childEntityViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityRelationship" ADD CONSTRAINT "EntityRelationship_parentEntityViewId_fkey" FOREIGN KEY ("parentEntityViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SampleCustomEntity" ADD CONSTRAINT "SampleCustomEntity_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowRelationship" ADD CONSTRAINT "RowRelationship_relationshipId_fkey" FOREIGN KEY ("relationshipId") REFERENCES "EntityRelationship"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowRelationship" ADD CONSTRAINT "RowRelationship_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowRelationship" ADD CONSTRAINT "RowRelationship_childId_fkey" FOREIGN KEY ("childId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_createdByApiKeyId_fkey" FOREIGN KEY ("createdByApiKeyId") REFERENCES "ApiKey"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_loyalityId_fkey" FOREIGN KEY ("loyalityId") REFERENCES "Loyality"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowValue" ADD CONSTRAINT "RowValue_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowValue" ADD CONSTRAINT "RowValue_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowValueMultiple" ADD CONSTRAINT "RowValueMultiple_rowValueId_fkey" FOREIGN KEY ("rowValueId") REFERENCES "RowValue"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowValueRange" ADD CONSTRAINT "RowValueRange_rowValueId_fkey" FOREIGN KEY ("rowValueId") REFERENCES "RowValue"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowPermission" ADD CONSTRAINT "RowPermission_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowPermission" ADD CONSTRAINT "RowPermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowPermission" ADD CONSTRAINT "RowPermission_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowPermission" ADD CONSTRAINT "RowPermission_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowPermission" ADD CONSTRAINT "RowPermission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowMedia" ADD CONSTRAINT "RowMedia_rowValueId_fkey" FOREIGN KEY ("rowValueId") REFERENCES "RowValue"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTag" ADD CONSTRAINT "RowTag_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTag" ADD CONSTRAINT "RowTag_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "EntityTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowComment" ADD CONSTRAINT "RowComment_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowComment" ADD CONSTRAINT "RowComment_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowCommentReaction" ADD CONSTRAINT "RowCommentReaction_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowCommentReaction" ADD CONSTRAINT "RowCommentReaction_rowCommentId_fkey" FOREIGN KEY ("rowCommentId") REFERENCES "RowComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTask" ADD CONSTRAINT "RowTask_assignedToUserId_fkey" FOREIGN KEY ("assignedToUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTask" ADD CONSTRAINT "RowTask_completedByUserId_fkey" FOREIGN KEY ("completedByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTask" ADD CONSTRAINT "RowTask_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RowTask" ADD CONSTRAINT "RowTask_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityTemplate" ADD CONSTRAINT "EntityTemplate_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityTemplate" ADD CONSTRAINT "EntityTemplate_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupEntity" ADD CONSTRAINT "EntityGroupEntity_entityGroupId_fkey" FOREIGN KEY ("entityGroupId") REFERENCES "EntityGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupEntity" ADD CONSTRAINT "EntityGroupEntity_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupEntity" ADD CONSTRAINT "EntityGroupEntity_allViewId_fkey" FOREIGN KEY ("allViewId") REFERENCES "EntityView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaComponent" ADD CONSTRAINT "FormulaComponent_formulaId_fkey" FOREIGN KEY ("formulaId") REFERENCES "Formula"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaLog" ADD CONSTRAINT "FormulaLog_formulaId_fkey" FOREIGN KEY ("formulaId") REFERENCES "Formula"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaLog" ADD CONSTRAINT "FormulaLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaLog" ADD CONSTRAINT "FormulaLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaComponentLog" ADD CONSTRAINT "FormulaComponentLog_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormulaComponentLog" ADD CONSTRAINT "FormulaComponentLog_formulaLogId_fkey" FOREIGN KEY ("formulaLogId") REFERENCES "FormulaLog"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupConfiguration" ADD CONSTRAINT "EntityGroupConfiguration_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupConfiguration" ADD CONSTRAINT "EntityGroupConfiguration_entityGroupId_fkey" FOREIGN KEY ("entityGroupId") REFERENCES "EntityGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupConfigurationRow" ADD CONSTRAINT "EntityGroupConfigurationRow_entityGroupConfigurationId_fkey" FOREIGN KEY ("entityGroupConfigurationId") REFERENCES "EntityGroupConfiguration"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntityGroupConfigurationRow" ADD CONSTRAINT "EntityGroupConfigurationRow_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKeyEntity" ADD CONSTRAINT "ApiKeyEntity_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKeyEntity" ADD CONSTRAINT "ApiKeyEntity_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSettingsRow" ADD CONSTRAINT "TenantSettingsRow_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSettingsRow" ADD CONSTRAINT "TenantSettingsRow_rowId_fkey" FOREIGN KEY ("rowId") REFERENCES "Row"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FeatureFlagFilter" ADD CONSTRAINT "FeatureFlagFilter_featureFlagId_fkey" FOREIGN KEY ("featureFlagId") REFERENCES "FeatureFlag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Survey" ADD CONSTRAINT "Survey_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SurveyItem" ADD CONSTRAINT "SurveyItem_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SurveySubmission" ADD CONSTRAINT "SurveySubmission_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SurveySubmissionResult" ADD CONSTRAINT "SurveySubmissionResult_surveySubmissionId_fkey" FOREIGN KEY ("surveySubmissionId") REFERENCES "SurveySubmission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseCategory" ADD CONSTRAINT "KnowledgeBaseCategory_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseCategorySection" ADD CONSTRAINT "KnowledgeBaseCategorySection_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "KnowledgeBaseCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticle" ADD CONSTRAINT "KnowledgeBaseArticle_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticle" ADD CONSTRAINT "KnowledgeBaseArticle_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "KnowledgeBaseCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticle" ADD CONSTRAINT "KnowledgeBaseArticle_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "KnowledgeBaseCategorySection"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticle" ADD CONSTRAINT "KnowledgeBaseArticle_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseRelatedArticle" ADD CONSTRAINT "KnowledgeBaseRelatedArticle_articleId_fkey" FOREIGN KEY ("articleId") REFERENCES "KnowledgeBaseArticle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseRelatedArticle" ADD CONSTRAINT "KnowledgeBaseRelatedArticle_relatedArticleId_fkey" FOREIGN KEY ("relatedArticleId") REFERENCES "KnowledgeBaseArticle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseViews" ADD CONSTRAINT "KnowledgeBaseViews_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticleViews" ADD CONSTRAINT "KnowledgeBaseArticleViews_knowledgeBaseArticleId_fkey" FOREIGN KEY ("knowledgeBaseArticleId") REFERENCES "KnowledgeBaseArticle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticleUpvotes" ADD CONSTRAINT "KnowledgeBaseArticleUpvotes_knowledgeBaseArticleId_fkey" FOREIGN KEY ("knowledgeBaseArticleId") REFERENCES "KnowledgeBaseArticle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBaseArticleDownvotes" ADD CONSTRAINT "KnowledgeBaseArticleDownvotes_knowledgeBaseArticleId_fkey" FOREIGN KEY ("knowledgeBaseArticleId") REFERENCES "KnowledgeBaseArticle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardFilter" ADD CONSTRAINT "StepFormWizardFilter_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardStep" ADD CONSTRAINT "StepFormWizardStep_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionAction" ADD CONSTRAINT "StepFormWizardSessionAction_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionFilterMatch" ADD CONSTRAINT "StepFormWizardSessionFilterMatch_stepFormWizardFilterId_fkey" FOREIGN KEY ("stepFormWizardFilterId") REFERENCES "StepFormWizardFilter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionFilterMatch" ADD CONSTRAINT "StepFormWizardSessionFilterMatch_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionStep" ADD CONSTRAINT "StepFormWizardSessionStep_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionStep" ADD CONSTRAINT "StepFormWizardSessionStep_stepId_fkey" FOREIGN KEY ("stepId") REFERENCES "StepFormWizardStep"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PageMetaTag" ADD CONSTRAINT "PageMetaTag_pageId_fkey" FOREIGN KEY ("pageId") REFERENCES "Page"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PageBlock" ADD CONSTRAINT "PageBlock_pageId_fkey" FOREIGN KEY ("pageId") REFERENCES "Page"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Permission" ADD CONSTRAINT "Permission_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Group" ADD CONSTRAINT "Group_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Group" ADD CONSTRAINT "Group_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GroupUser" ADD CONSTRAINT "GroupUser_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GroupUser" ADD CONSTRAINT "GroupUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Portal" ADD CONSTRAINT "Portal_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Portal" ADD CONSTRAINT "Portal_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUser" ADD CONSTRAINT "PortalUser_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUser" ADD CONSTRAINT "PortalUser_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalPage" ADD CONSTRAINT "PortalPage_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserRegistration" ADD CONSTRAINT "PortalUserRegistration_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserRegistration" ADD CONSTRAINT "PortalUserRegistration_createdPortalUserId_fkey" FOREIGN KEY ("createdPortalUserId") REFERENCES "PortalUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalSubscriptionProduct" ADD CONSTRAINT "PortalSubscriptionProduct_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalSubscriptionPrice" ADD CONSTRAINT "PortalSubscriptionPrice_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalSubscriptionPrice" ADD CONSTRAINT "PortalSubscriptionPrice_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "PortalSubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalSubscriptionFeature" ADD CONSTRAINT "PortalSubscriptionFeature_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalSubscriptionFeature" ADD CONSTRAINT "PortalSubscriptionFeature_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "PortalSubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscription" ADD CONSTRAINT "PortalUserSubscription_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscription" ADD CONSTRAINT "PortalUserSubscription_portalUserId_fkey" FOREIGN KEY ("portalUserId") REFERENCES "PortalUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProduct" ADD CONSTRAINT "PortalUserSubscriptionProduct_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProduct" ADD CONSTRAINT "PortalUserSubscriptionProduct_portalUserSubscriptionId_fkey" FOREIGN KEY ("portalUserSubscriptionId") REFERENCES "PortalUserSubscription"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProduct" ADD CONSTRAINT "PortalUserSubscriptionProduct_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "PortalSubscriptionProduct"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProductPrice" ADD CONSTRAINT "PortalUserSubscriptionProductPrice_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProductPrice" ADD CONSTRAINT "PortalUserSubscriptionProductPrice_portalUserSubscriptionP_fkey" FOREIGN KEY ("portalUserSubscriptionProductId") REFERENCES "PortalUserSubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalUserSubscriptionProductPrice" ADD CONSTRAINT "PortalUserSubscriptionProductPrice_subscriptionPriceId_fkey" FOREIGN KEY ("subscriptionPriceId") REFERENCES "PortalSubscriptionPrice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PortalCheckoutSessionStatus" ADD CONSTRAINT "PortalCheckoutSessionStatus_portalId_fkey" FOREIGN KEY ("portalId") REFERENCES "Portal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowGroupTemplate" ADD CONSTRAINT "PromptFlowGroupTemplate_promptFlowGroupId_fkey" FOREIGN KEY ("promptFlowGroupId") REFERENCES "PromptFlowGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowGroupEntity" ADD CONSTRAINT "PromptFlowGroupEntity_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowGroupEntity" ADD CONSTRAINT "PromptFlowGroupEntity_promptFlowGroupId_fkey" FOREIGN KEY ("promptFlowGroupId") REFERENCES "PromptFlowGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlow" ADD CONSTRAINT "PromptFlow_inputEntityId_fkey" FOREIGN KEY ("inputEntityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlow" ADD CONSTRAINT "PromptFlow_promptFlowGroupId_fkey" FOREIGN KEY ("promptFlowGroupId") REFERENCES "PromptFlowGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowInputVariable" ADD CONSTRAINT "PromptFlowInputVariable_promptFlowId_fkey" FOREIGN KEY ("promptFlowId") REFERENCES "PromptFlow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptTemplate" ADD CONSTRAINT "PromptTemplate_flowId_fkey" FOREIGN KEY ("flowId") REFERENCES "PromptFlow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowOutput" ADD CONSTRAINT "PromptFlowOutput_promptFlowId_fkey" FOREIGN KEY ("promptFlowId") REFERENCES "PromptFlow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowOutput" ADD CONSTRAINT "PromptFlowOutput_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowOutputMapping" ADD CONSTRAINT "PromptFlowOutputMapping_promptFlowOutputId_fkey" FOREIGN KEY ("promptFlowOutputId") REFERENCES "PromptFlowOutput"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowOutputMapping" ADD CONSTRAINT "PromptFlowOutputMapping_promptTemplateId_fkey" FOREIGN KEY ("promptTemplateId") REFERENCES "PromptTemplate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowOutputMapping" ADD CONSTRAINT "PromptFlowOutputMapping_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowExecution" ADD CONSTRAINT "PromptFlowExecution_flowId_fkey" FOREIGN KEY ("flowId") REFERENCES "PromptFlow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowExecution" ADD CONSTRAINT "PromptFlowExecution_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptFlowExecution" ADD CONSTRAINT "PromptFlowExecution_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptTemplateResult" ADD CONSTRAINT "PromptTemplateResult_flowExecutionId_fkey" FOREIGN KEY ("flowExecutionId") REFERENCES "PromptFlowExecution"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromptTemplateResult" ADD CONSTRAINT "PromptTemplateResult_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "PromptTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionPrice" ADD CONSTRAINT "SubscriptionPrice_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "SubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionUsageBasedPrice" ADD CONSTRAINT "SubscriptionUsageBasedPrice_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "SubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionUsageBasedTier" ADD CONSTRAINT "SubscriptionUsageBasedTier_subscriptionUsageBasedPriceId_fkey" FOREIGN KEY ("subscriptionUsageBasedPriceId") REFERENCES "SubscriptionUsageBasedPrice"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionFeature" ADD CONSTRAINT "SubscriptionFeature_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "SubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscription" ADD CONSTRAINT "TenantSubscription_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionProduct" ADD CONSTRAINT "TenantSubscriptionProduct_tenantSubscriptionId_fkey" FOREIGN KEY ("tenantSubscriptionId") REFERENCES "TenantSubscription"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionProduct" ADD CONSTRAINT "TenantSubscriptionProduct_subscriptionProductId_fkey" FOREIGN KEY ("subscriptionProductId") REFERENCES "SubscriptionProduct"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionProductPrice" ADD CONSTRAINT "TenantSubscriptionProductPrice_tenantSubscriptionProductId_fkey" FOREIGN KEY ("tenantSubscriptionProductId") REFERENCES "TenantSubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionProductPrice" ADD CONSTRAINT "TenantSubscriptionProductPrice_subscriptionPriceId_fkey" FOREIGN KEY ("subscriptionPriceId") REFERENCES "SubscriptionPrice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionProductPrice" ADD CONSTRAINT "TenantSubscriptionProductPrice_subscriptionUsageBasedPrice_fkey" FOREIGN KEY ("subscriptionUsageBasedPriceId") REFERENCES "SubscriptionUsageBasedPrice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantSubscriptionUsageRecord" ADD CONSTRAINT "TenantSubscriptionUsageRecord_tenantSubscriptionProductPri_fkey" FOREIGN KEY ("tenantSubscriptionProductPriceId") REFERENCES "TenantSubscriptionProductPrice"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Credit" ADD CONSTRAINT "Credit_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Credit" ADD CONSTRAINT "Credit_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Workflow" ADD CONSTRAINT "Workflow_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Workflow" ADD CONSTRAINT "Workflow_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlock" ADD CONSTRAINT "WorkflowBlock_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "Workflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockConditionGroup" ADD CONSTRAINT "WorkflowBlockConditionGroup_workflowBlockId_fkey" FOREIGN KEY ("workflowBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockCondition" ADD CONSTRAINT "WorkflowBlockCondition_workflowBlockConditionGroupId_fkey" FOREIGN KEY ("workflowBlockConditionGroupId") REFERENCES "WorkflowBlockConditionGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockToBlock" ADD CONSTRAINT "WorkflowBlockToBlock_fromBlockId_fkey" FOREIGN KEY ("fromBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockToBlock" ADD CONSTRAINT "WorkflowBlockToBlock_toBlockId_fkey" FOREIGN KEY ("toBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowExecution" ADD CONSTRAINT "WorkflowExecution_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "Workflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowExecution" ADD CONSTRAINT "WorkflowExecution_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowExecution" ADD CONSTRAINT "WorkflowExecution_waitingBlockId_fkey" FOREIGN KEY ("waitingBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowExecution" ADD CONSTRAINT "WorkflowExecution_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowInputExample" ADD CONSTRAINT "WorkflowInputExample_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "Workflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockExecution" ADD CONSTRAINT "WorkflowBlockExecution_workflowExecutionId_fkey" FOREIGN KEY ("workflowExecutionId") REFERENCES "WorkflowExecution"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockExecution" ADD CONSTRAINT "WorkflowBlockExecution_workflowBlockId_fkey" FOREIGN KEY ("workflowBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowBlockExecution" ADD CONSTRAINT "WorkflowBlockExecution_fromWorkflowBlockId_fkey" FOREIGN KEY ("fromWorkflowBlockId") REFERENCES "WorkflowBlock"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowVariable" ADD CONSTRAINT "WorkflowVariable_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowVariable" ADD CONSTRAINT "WorkflowVariable_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowCredential" ADD CONSTRAINT "WorkflowCredential_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkflowCredential" ADD CONSTRAINT "WorkflowCredential_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_TenantToTenantType" ADD CONSTRAINT "_TenantToTenantType_A_fkey" FOREIGN KEY ("A") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_TenantToTenantType" ADD CONSTRAINT "_TenantToTenantType_B_fkey" FOREIGN KEY ("B") REFERENCES "TenantType"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SubscriptionProductToTenantType" ADD CONSTRAINT "_SubscriptionProductToTenantType_A_fkey" FOREIGN KEY ("A") REFERENCES "SubscriptionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SubscriptionProductToTenantType" ADD CONSTRAINT "_SubscriptionProductToTenantType_B_fkey" FOREIGN KEY ("B") REFERENCES "TenantType"("id") ON DELETE CASCADE ON UPDATE CASCADE;
