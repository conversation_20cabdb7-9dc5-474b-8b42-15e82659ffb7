import EventsService from "~/modules/events/services/.server/EventsService";
import { StepFormWizardSessionWithDetails, setStepFormWizardSessionActions, updateStepFormWizardSession } from "../db/stepFormWizardSessions.db.server";
import { updateStepFormWizardSessionStep } from "../db/stepFormWizardSessionSteps.db.server";
import { StepFormWizardSessionActionDto } from "../dtos/StepFormWizardSessionActionDto";
import { StepFormWizardCompletedDto } from "~/modules/events/dtos/StepFormWizardCompletedDto";
import { StepFormWizardStartedDto } from "~/modules/events/dtos/StepFormWizardStartedDto";
import { StepFormWizardDismissedDto } from "~/modules/events/dtos/StepFormWizardDismissedDto";
import { EntityWithDetails, getAllEntities } from "~/utils/db/entities/entities.db.server";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import ApiHelper from "~/utils/helpers/ApiHelper";
import { db } from "~/utils/db.server";
async function started({ request, session }: { request: Request; session: StepFormWizardSessionWithDetails }) {
  if (!session.startedAt) {
    await updateStepFormWizardSession(session.id, {
      status: "started",
      startedAt: new Date(),
    });
    await EventsService.create({
      request,
      event: "stepFormWizard.started",
      tenantId: session.tenantId,
      userId: session.userId,
      data: {
        session: { id: session.id },
        stepFormWizard: { title: session.stepFormWizard.title },
        user: { id: session.userId, email: session.user.email },
      } satisfies StepFormWizardStartedDto,
    });
  }
}

async function dismissed({ request, session }: { request: Request; session: StepFormWizardSessionWithDetails }) {
  if (!session.dismissedAt) {
    await updateStepFormWizardSession(session.id, {
      status: "dismissed",
      dismissedAt: new Date(),
    });
    await EventsService.create({
      request,
      event: "stepFormWizard.dismissed",
      tenantId: session.tenantId,
      userId: session.userId,
      data: {
        session: { id: session.id },
        stepFormWizard: { title: session.stepFormWizard.title },
        user: { id: session.userId, email: session.user.email },
      } satisfies StepFormWizardDismissedDto,
    });
  }
}

async function setStep(session: StepFormWizardSessionWithDetails, data: { fromIdx: number; toIdx: number; actions: StepFormWizardSessionActionDto[] }) {
  const fromStep = session.sessionSteps[data.fromIdx];
  const toStep = session.sessionSteps[data.toIdx];
  if (!fromStep.completedAt) {
    await updateStepFormWizardSessionStep(fromStep.id, {
      completedAt: new Date(),
    });
  }
  if (!toStep.seenAt) {
    await updateStepFormWizardSessionStep(toStep.id, {
      seenAt: new Date(),
    });
  }
  await setActions(session, data.actions);
}

async function addActions(session: StepFormWizardSessionWithDetails, data: { actions: StepFormWizardSessionActionDto[] }) {
  await setActions(session, data.actions);
}

async function setActions(session: StepFormWizardSessionWithDetails, actions: StepFormWizardSessionActionDto[]) {
  const newActions: StepFormWizardSessionActionDto[] = [
    ...session.actions
      .sort((a, b) => (a.createdAt > b.createdAt ? 1 : -1))
      .map((f) => {
        return {
          type: f.type as "click" | "input",
          name: f.name,
          value: f.value,
        };
      }),
  ];
  actions.forEach((action) => {
    if (action.type === "input") {
      const idx = newActions.findIndex((f) => f.type === "input" && f.name === action.name);
      if (idx >= 0) {
        newActions[idx] = action;
      } else {
        newActions.push(action);
      }
    } else {
      newActions.push(action);
    }
  });
  const startDate = new Date();
  newActions.forEach((newAction, idx) => {
    try {
      // add one second to each action to avoid duplicate keys
      newAction.createdAt = new Date(startDate.getTime() + (idx + 1) * 1000);
    } catch (e: any) {
      //
    }
  });
  if (newActions.length > 0) {
    await setStepFormWizardSessionActions(session.id, { actions: newActions });
  }
}

async function complete({
  request,
  session,
  data,
}: {
  request: Request;
  session: StepFormWizardSessionWithDetails;
  data: { fromIdx: number; actions: StepFormWizardSessionActionDto[] };
}) {
  const fromStep = session.sessionSteps[data.fromIdx];
  if (!fromStep.completedAt) {
    await updateStepFormWizardSessionStep(fromStep.id, {
      completedAt: new Date(),
    });
  }

  await setActions(session, data.actions);
  await new Promise((resolve) => setTimeout(resolve, 2000));

  if (!session.completedAt) {
    await updateStepFormWizardSession(session.id, {
      status: "completed",
      completedAt: new Date(),
    });

    await EventsService.create({
      request,
      event: "stepFormWizard.completed",
      tenantId: session.tenantId,
      userId: session.userId,
      data: {
        session: { id: session.id },
        stepFormWizard: { title: session.stepFormWizard.title },
        user: { id: session.userId, email: session.user.email },
      } satisfies StepFormWizardCompletedDto,
    });
  }
}
const triggerRowSessionWebHook = async (entity: EntityWithDetails, rowId: string, currentPageStep: number) => {

  try {
    const { item } = await RowsApi.get(rowId!, { entity });
    const entities = await getAllEntities({ tenantId: null });
    const data = ApiHelper.getApiFormatWithRelationships({
      entities,
      item,
    });

    if (!(entity.isStepFormWizard && entity.stepFormWizard) || !item?.stepFormWizardSession) {
      return;
    }

    const stepFormWizardSession = item.stepFormWizardSession;
    const currentSessionStepIndex = parseInt(stepFormWizardSession.currentStepIndex || "0");
    const steps = stepFormWizardSession.sessionSteps ?? [];
    const step = steps?.find(obj => obj.step.order === currentPageStep + 1);
    const totalSteps = steps?.length || 0;
    const nextStepIndex = currentPageStep >= totalSteps - 1 ? totalSteps : currentPageStep + 1;

    console.log({ totalSteps, nextStepIndex, currentSessionStepIndex, currentPageStep })

    if ((step?.webHookTriggeredAt) && stepFormWizardSession.logicAppRunId) {
      console.log("already triggered for step", currentPageStep);
      return;
    }

    if (!step || !step?.step?.block) {
      console.log('step not found');
      return;
    }

    const stepBlock = JSON.parse(step?.step?.block);
    const stepWebHookURL = stepFormWizardSession?.callBackURL ?? (stepBlock.webhookUrl?.length ? stepBlock.webhookUrl : "");

    if (!stepWebHookURL) {
      const updates = {
        status: (currentPageStep >= totalSteps - 1 ? "completed" : "started") as "completed" | "started",
        currentStepIndex: String(currentPageStep),
      };

      const updatedSession = await updateStepFormWizardSession(stepFormWizardSession?.id?.trim(), updates);
      const stepToBeUpdated = await db.stepFormWizardSessionStep.findFirst({
        where: {
          stepFormWizardSessionId: stepFormWizardSession?.id?.trim(),
          step: {
            is: {
              order: currentPageStep + 1,
            },
          },
        },
      });

      const updatedStep = await db.stepFormWizardSessionStep.update({
        where: {
          id: stepToBeUpdated?.id,
        },
        data: {
          seenAt: new Date(),
          completedAt: new Date(),
        },
      });
      console.log("no webhook configured !");
      return;
    }

    const meta = {
      stepId: step?.stepId,
      stepTitle: stepBlock?.title,
      currentStep: currentPageStep,
      nextStep: nextStepIndex,
      sessionId: stepFormWizardSession?.id,
      steps: stepFormWizardSession?.sessionSteps?.length,
      status: stepFormWizardSession?.status,
      userId: stepFormWizardSession?.userId,
      tenantId: stepFormWizardSession?.tenantId,
      timeStamp: new Date().toISOString(),
    };

    const payload = {
      meta,
      payload: data,
    };


    const res = await fetch(stepWebHookURL, {
      method: stepBlock.webhookMethod || "POST",
      body: JSON.stringify(payload),
      headers: {
        "Content-Type": "application/json",
      },
    });

    // @toDo show webhook trigger success or fail message

    if (!res.ok) {
      throw new Error("Failed to submit form");
    }

    let response = null;
    const text = await res.text();
    response = text ? JSON.parse(text) : null;
    if (response?.WorkflowrunId) {
      if (!stepFormWizardSession.logicAppRunId) {
        await updateStepFormWizardSession(stepFormWizardSession.id, {
          status: "started",
          logicAppRunId: response?.WorkflowrunId + "",
          currentStepIndex: "0",
        });
      }
    }

    await db.stepFormWizardSessionStep.updateMany({
      where: {
        stepId: step?.stepId,
        stepFormWizardSessionId: stepFormWizardSession.id,
      },
      data: {
        seenAt: new Date(),
        webHookTriggeredAt: new Date(),
      }
    });


    return response;
  } catch (error) {
    console.error("Error hitting webhook:", error);
    throw error;
  }
};


export default {
  triggerRowSessionWebHook,
  started,
  dismissed,
  setStep,
  complete,
  addActions,
};

