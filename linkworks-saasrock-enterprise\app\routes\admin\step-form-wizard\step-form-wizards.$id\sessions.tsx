import { ActionFunction, LoaderFunctionArgs, MetaFunction, redirect, useLoaderData } from "react-router";
import { useActionData, useSubmit } from "react-router";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import WarningBanner from "~/components/ui/banners/WarningBanner";
import ServerError from "~/components/ui/errors/ServerError";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { getTranslations } from "~/locale/i18next.server";
import StepFormWizardSessionsTable from "~/custom/modules/stepFormWizard/components/StepFormWizardSessionsTable";
import { StepFormWizardWithDetails, getStepFormWizard } from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import {
  deleteStepFormWizardSession,
  getStepFormWizardSessions,
  StepFormWizardSessionWithDetails,
  getStepFormWizardSession,
} from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { deleteStepFormWizardSessionSteps } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessionSteps.db.server";
import { StepFormWizardFilterMetadataDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterMetadataDto";
import StepFormWizardService from "~/custom/modules/stepFormWizard/services/StepFormWizardService";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
type LoaderData = {
  meta: MetaTagsDto;
  item: StepFormWizardWithDetails;
  items: StepFormWizardSessionWithDetails[];
  metadata: StepFormWizardFilterMetadataDto;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  const items = await getStepFormWizardSessions({
    stepFormWizardId: item.id,
  });
  const data: LoaderData = {
    meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
    item,
    items,
    metadata: await StepFormWizardService.getMetadata(),
  };
  return data;
};

type ActionData = {
  error?: string;
  success?: string;
};
export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (action === "delete") {
    await verifyUserHasPermission(request, "admin.stepFormWizard.delete");
    const id = form.get("id")?.toString() ?? "";
    if (!id) {
      return Response.json({ error: "Session ID is required" }, { status: 400 });
    }
    const session = await getStepFormWizardSession(id);
    // if (session?.status !== "active") {
    //   return Response.json({ error: "Sessions can only be deleted when they are active" }, { status: 400 });
    // }
    await deleteStepFormWizardSessionSteps(session!.sessionSteps.map((s) => s.id));
    await deleteStepFormWizardSession(id);
    return Response.json({ success: "Step form wizard session deleted" });
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();

  const modalConfirmDelete = useRef<RefConfirmModal>(null);

  function onDelete(item: StepFormWizardSessionWithDetails) {
    modalConfirmDelete.current?.setValue(item.id);
    modalConfirmDelete.current?.show(
      t("stepFormWizard.prompts.deleteSession.title"),
      t("shared.confirm"),
      t("shared.back"),
      t("stepFormWizard.prompts.deleteSession.description")
    );
  }
  function onConfirmDelete(id: string) {
    const form = new FormData();
    form.set("action", "delete");
    form.set("id", id);
    submit(form, {
      method: "post",
    });
  }
  return (
    <div className="mx-auto flex-1 space-y-5 overflow-x-auto px-2 py-2 xl:overflow-y-auto">
      <div className="space-y-3">
        <div className="space-y-2">
          <h3 className="text-foreground text-sm leading-3 font-medium">{t("stepFormWizard.session.plural")}</h3>
          <p className="text-muted-foreground text-sm">Sessions represent the current state of an step form wizard process.</p>
        </div>
        {!data.item.active && <WarningBanner title={t("shared.warning")} text="Current step form wizard is not active, sessions will not be created/loaded." />}
        <StepFormWizardSessionsTable items={data.items} withStepFormWizard={false} onDelete={onDelete} metadata={data.metadata} />
      </div>
      <ConfirmModal ref={modalConfirmDelete} onYes={onConfirmDelete} />
      <ActionResultModal actionData={actionData} showSuccess={false} />
    </div>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
