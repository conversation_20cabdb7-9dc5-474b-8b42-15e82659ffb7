import { ActionFunction, LoaderFunctionArgs } from "react-router";
import { getTranslations } from "~/locale/i18next.server";
import { getStepFormWizardSession, StepFormWizardSessionWithDetails } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { StepFormWizardSessionActionDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardSessionActionDto";
import StepFormWizardSessionService from "~/custom/modules/stepFormWizard/services/StepFormWizardSessionService";

type LoaderData = {
  item: StepFormWizardSessionWithDetails | null;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const item = await getStepFormWizardSession(params.id!);
  const data: LoaderData = {
    item,
  };
  return data;
};

export const action: ActionFunction = async ({ request, params }) => {
  const { t } = await getTranslations(request);
  const formData = await request.formData();
  const actionName = formData.get("action");
  const sessionId = params.id!;
  const session = await getStepFormWizardSession(sessionId);
  if (!session) {
    return Response.json({ error: "Session not found" }, { status: 404 });
  }
  const actions = formData.getAll("actions[]").map((action: FormDataEntryValue) => {
    return JSON.parse(action.toString()) as StepFormWizardSessionActionDto;
  });
  switch (actionName) {
    case "started": {
      await StepFormWizardSessionService.started({ session, request });
      break;
    }
    case "dismissed": {
      await StepFormWizardSessionService.dismissed({ session, request });
      break;
    }
    case "add-actions": {
      await StepFormWizardSessionService.addActions(session, { actions });
      break;
    }
    case "set-step": {
      await StepFormWizardSessionService.setStep(session, {
        fromIdx: Number(formData.get("fromIdx")),
        toIdx: Number(formData.get("toIdx")),
        actions,
      });
      break;
    }
    case "complete": {
      await StepFormWizardSessionService.complete({
        session,
        data: { fromIdx: Number(formData.get("fromIdx")), actions },
        request,
      });
      break;
    }
    default: {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
    }
  }
  return Response.json({});
};
