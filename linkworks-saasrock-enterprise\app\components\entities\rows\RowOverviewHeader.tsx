import { Fragment, ReactNode, useRef, useState } from "react";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import TrashEmptyIcon from "~/components/ui/icons/TrashEmptyIcon";
import EntityHelper from "~/utils/helpers/EntityHelper";
import RowTitle from "./RowTitle";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { useLocation, useNavigation, useSubmit } from "react-router";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import { getUserHasPermission, getEntityPermission } from "~/utils/helpers/PermissionsHelper";
import ShareIcon from "~/components/ui/icons/ShareIcon";
import PencilIcon from "~/components/ui/icons/PencilIcon";
import RunPromptFlowButtons from "~/modules/promptBuilder/components/run/RunPromptFlowButtons";
import clsx from "clsx";
import { useTranslation } from "react-i18next";
import TertiaryButton from "~/custom/components/TertiaryButton";
import { useNavigate } from "react-router";
import DropdownButton from "~/custom/components/DropdownButton";
import OverViewSecondaryHeader from "~/custom/components/OverViewSecondaryHeader";
import EntityContactInfo from "~/custom/components/RowOverviewRoute/components/EntityInfo";
import DownIcon from "~/components/layouts/icons/DownIcon";
import UpIcon from "~/components/layouts/icons/UpIcon";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/components/ui/tooltip";
export default function RowOverviewHeader({
  rowData,
  item,
  canUpdate,
  isEditing,
  routes,
  title,
  options,
  buttons,
  customActions,
  truncate = true,
  agent = null,
}: {
  rowData: RowsApi.GetRowData;
  item: RowWithDetails;
  canUpdate: boolean;
  isEditing: boolean;
  routes: EntitiesApi.Routes | undefined;
  title?: React.ReactNode;
  options?: {
    hideTitle?: boolean;
    hideMenu?: boolean;
    hideShare?: boolean;
    hideTags?: boolean;
    hideTasks?: boolean;
    hideActivity?: boolean;
    disableUpdate?: boolean;
    disableDelete?: boolean;
  };
  buttons?: ReactNode;
  customActions?: { entity: string; label: string; action: string }[];
  truncate?: boolean;
  agent?: any;
}) {
  const { t } = useTranslation();
  const appOrAdminData = useAppOrAdminData();
  const navigation = useNavigation();
  const navigate = useNavigate();
  const location = useLocation();
  const submit = useSubmit();
  const [isMoreDetailsOpen, setIsMoreDetailsOpen] = useState(false);

  // Check if there are secondary header properties or onboarding content to show
  const hasSecondaryContent = () => {
    const hasSecondaryHeaderProperties = rowData.entity.properties.some(p => p.isOverviewSecondaryHeaderProperty);
    // const hasOnboardingContent = rowData?.entity?.isOnboarding;
    return hasSecondaryHeaderProperties;
  };

  const confirmDelete = useRef<RefConfirmModal>(null);

  function getEditRoute() {
    if (isEditing) {
      return EntityHelper.getRoutes({ routes, entity: rowData.entity, item })?.overview;
    } else {
      return EntityHelper.getRoutes({ routes, entity: rowData.entity, item })?.edit;
    }
  }
  function canDelete() {
    return !options?.disableDelete && getUserHasPermission(appOrAdminData, getEntityPermission(rowData.entity, "delete")) && rowData.rowPermissions.canDelete;
  }

  function onDelete() {
    confirmDelete.current?.show(t("shared.confirmDelete"), t("shared.delete"), t("shared.cancel"), t("shared.warningCannotUndo"));
  }
  function onDeleteConfirm() {
    const form = new FormData();
    form.set("action", "delete");
    submit(form, {
      method: "post",
    });
  }

  const navigateToShare = () => {
    navigate(`${location.pathname}/share`);
  };

  const navigateToEdit = () => {
    const isStepFormWizard = rowData?.entity?.isStepFormWizard;
    navigate(isStepFormWizard ? `${location.pathname}/edit?editStepFormWizard=true` : `${location.pathname}/edit`);
  };

  return (
    <>
      <div className="bg-card relative items-center justify-between space-y-2 border-b sm:flex sm:space-y-0 sm:space-x-4 flex flex-col max-w-full py-4 mb-[21px] mt-5 rounded-[8px] border border-solid border-input mx-3 !px-[20px]">
        <div className={`flex w-full flex-col sm:flex-row justify-between gap-2.5 sm:pl-[9px]`}>
          <div className={clsx(truncate && "truncate", "flex flex-col", "")}>
            <div className={clsx(truncate && "truncate", "flex items-center space-x-2 text-xl font-bold")}>
              {title ?? <RowTitle entity={rowData.entity} item={item} isOverViewPage={true} rowData={rowData} />}
            </div>
          </div>
          <div className="flex gap-2">
            <EntityContactInfo item={item} rowData={rowData} options={{ hideActivity: false }} agent={agent} />

            <div className="flex shrink-0 items-center space-x-2 sm:justify-end">
              <div className="flex items-center">
                {canUpdate || item.createdByUserId === appOrAdminData?.user?.id || appOrAdminData?.isSuperUser ? (
                  <Fragment>
                    {customActions
                      ?.filter((f) => f.entity === rowData.entity.name)
                      .map((customAction) => {
                        return (
                          <ButtonSecondary
                            key={customAction.action}
                            isLoading={navigation.state === "submitting" && navigation.formData?.get("action") === customAction.action}
                            onClick={() => {
                              const form = new FormData();
                              form.set("action", customAction.action);
                              form.set("id", item.id);
                              submit(form, {
                                method: "post",
                              });
                            }}
                          >
                            <span className="text-xs">{customAction.label}</span>
                          </ButtonSecondary>
                        );
                      })}
                    {buttons}

                    {/* <button
                      disabled={!hasSecondaryContent()}
                      style={{ cursor: hasSecondaryContent() ? "pointer" : "not-allowed" }}
                      onClick={() => setIsMoreDetailsOpen(!isMoreDetailsOpen)}
                      className="text-[#274AFF] font-inter font-normal text-[12px] leading-[100%] tracking-[0%] align-middle underline pr-[8px] cursor-pointer transition-all duration-200 hover:opacity-80"
                    >
                      {isMoreDetailsOpen ? <div className="flex gap-[1px] items-center">
                        <div className="absolute !bottom-[-27px] left-[50%] translate-x-[-50%]">
                          <DownIcon />
                        </div>
                      </div> :
                        <div className="absolute !bottom-[-27px] left-[50%] translate-x-[-50%]  ">
                          <UpIcon />
                        </div>}
                    </button> */}
                    <div className="absolute !bottom-[-27px] left-[50%] translate-x-[-50%]">
                      <Tooltip >
                        <TooltipTrigger asChild>
                          <button
                            disabled={!hasSecondaryContent()}
                            style={{ cursor: hasSecondaryContent() ? "pointer" : "not-allowed" }}
                            onClick={() => setIsMoreDetailsOpen(!isMoreDetailsOpen)}
                            className="text-[#274AFF] font-inter font-normal text-[12px] leading-[100%] tracking-[0%] align-middle underline pr-[8px] cursor-pointer transition-all duration-200 hover:opacity-80"
                          >
                            {isMoreDetailsOpen ? (
                              <div className="flex gap-[1px] items-center">
                                <div className="">
                                  <DownIcon />
                                </div>
                              </div>
                            ) : (
                              <div className="">
                                <UpIcon />
                              </div>
                            )}
                          </button>
                        </TooltipTrigger>

                        {!hasSecondaryContent() && (
                          <TooltipContent side="top" align="center" >No data available</TooltipContent>
                        )}
                      </Tooltip>
                    </div>


                    {!options?.hideShare && (item.createdByUserId === appOrAdminData?.user?.id || appOrAdminData.isSuperAdmin) && (
                      <div className="pr-[8px]">
                        {" "}
                        <TertiaryButton title="Share" icon={<ShareIcon className="text-foreground h-4 w-4" />} onClick={() => navigateToShare()} />
                      </div>
                    )}

                  {rowData.entity.onEdit !== "overviewAlwaysEditable" && rowData?.item?.stepFormWizardSession?.status != "completed" && (
                    <DropdownButton
                      title="Options"
                      className={rowData.entity.onEdit !== "overviewAlwaysEditable" && rowData?.item?.stepFormWizardSession?.status != "completed" ? "" : "hidden"}
                      options={
                        rowData.entity.onEdit !== "overviewAlwaysEditable" && rowData?.item?.stepFormWizardSession?.status != "completed"
                          ? [{ label: "Edit", onclick: () => navigateToEdit() }]
                          : []
                      }
                    />
                  )}

                  {/* {rowData.entity.onEdit !== "overviewAlwaysEditable" && rowData?.item?.stepFormWizardSession?.status != "completed" && (
                  <ButtonSecondary disabled={!EntityHelper.getRoutes({ routes, entity: rowData.entity, item })?.edit} to={getEditRoute()}>
                    <PencilIcon className="text-muted-foreground h-4 w-4" />
                    <span className="text-xs font-normal leading-6">Edit</span>
                  </ButtonSecondary>
                )} */}
                    {isEditing && (
                      <ButtonSecondary onClick={onDelete} disabled={!canDelete()}>
                        <TrashEmptyIcon className="text-muted-foreground h-4 w-4" />
                      </ButtonSecondary>
                    )}
                    <RunPromptFlowButtons type="edit" row={item} promptFlows={rowData.promptFlows} />
                  </Fragment>
                ) : appOrAdminData.isSuperAdmin ? (
                  <ButtonSecondary to="share">
                    <ShareIcon className="text-muted-foreground h-4 w-4" />
                  </ButtonSecondary>
                ) : null}
              </div>
            </div>

            <ConfirmModal ref={confirmDelete} destructive onYes={onDeleteConfirm} />
          </div>
        </div>

        {/* Secondary Header with smooth transition */}
        {hasSecondaryContent() && (
          <div
            className={clsx(
              "w-full overflow-hidden transition-all duration-300 ease-in-out",
              isMoreDetailsOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
            )}
          >
            <div className=" mt-4 pt-4">
              <OverViewSecondaryHeader entity={rowData.entity} item={item} rowData={rowData} />
            </div>
          </div>
        )}
      </div>


      {/* <div className="b-t-0 b-x-0 border-input -mx-[10px] border-b pt-[3px]"></div> */}

    </>
  );
}
