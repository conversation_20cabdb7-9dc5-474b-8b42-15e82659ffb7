import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import ButtonTertiary from "~/components/ui/buttons/ButtonTertiary";
import InputGroup from "~/components/ui/forms/InputGroup";
import XIcon from "~/components/ui/icons/XIcon";
import InputCheckbox from "~/components/ui/input/InputCheckbox";
import InputRadioGroup from "~/components/ui/input/InputRadioGroup";
import InputSelect from "~/components/ui/input/InputSelect";
import InputSelector from "~/components/ui/input/InputSelector";
import InputText from "~/components/ui/input/InputText";
import CollapsibleRow from "~/components/ui/tables/CollapsibleRow";
import StepFormWizardStepUtils from "~/custom/modules/stepFormWizard/utils/StepFormWizardStepUtils";
import GridBlockForm from "~/modules/pageBlocks/components/blocks/shared/grid/GridBlockForm";
import StringUtils from "~/utils/shared/StringUtils";
import { saasrockStepFormWizardStepBlocks } from "./defaultStepFormWizard/saasrockStepFormWizard";
import {
  defaultStepFormWizardBlock,
  defaultStepFormWizardStepBlock,
  StepFormWizardBlockDto,
  StepFormWizardBlockStyle,
  StepFormWizardBlockStyles,
  StepFormWizardInputOptionDto,
  StepFormWizardStepBlockDto,
} from "./StepFormWizardBlockUtils";
import ReactSortablePkg from "react-sortablejs";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import InputCheckboxInline from "~/components/ui/input/InputCheckboxInline";
import clsx from "clsx";
import { t } from "i18next";
import OrderIndexButtons from "~/components/ui/sort/OrderIndexButtons";
import OrderHelper from "~/utils/helpers/OrderHelper";
const { ReactSortable } = ReactSortablePkg;

export default function StepFormWizardBlockForm({
  item,
  onUpdate,
  entities,
  selectedEntitypropertiesMap,
  setSelectedEntitypropertiesMap,
}: {
  item?: StepFormWizardBlockDto;
  onUpdate: (item: StepFormWizardBlockDto) => void;
  entities: EntityWithDetails[];
  selectedEntitypropertiesMap: { [key: string]: number };
  setSelectedEntitypropertiesMap: React.Dispatch<React.SetStateAction<{ [key: string]: number }>>;
}) {
  const { t } = useTranslation();
  const [state, setState] = useState<StepFormWizardBlockDto>(item || defaultStepFormWizardBlock);
  const [entityOptions, setEntityOptions] = useState([{ name: "", value: "" }] as { name: string; value: string }[]);
  const [entityProperites, setEntityProperties] = useState<{ propertyId: string; name: string; title?: string; order: number }[]>([]);

  useEffect(() => {
    onUpdate(state);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);

  useEffect(() => {
    let entityOptions = entities
      .filter((o) => o.isStepFormWizard)
      .map((entity) => ({
        name: entity.name,
        value: entity.name,
      }));
    setEntityOptions(entityOptions);
  }, [entities]);

  useEffect(() => {
    onUpdate(state);
    // eslint-disable-next-line react-hooks/exhaustive-deps

    if (state.entity) {
      const entity = entities.find((e) => e.name === state.entity);
      if (entity) {
        let entityProps = entity.properties.map((property) => ({
          propertyId: property.id,
          name: property.name,
          title: property.title,
          order: 0,
        }));

        setEntityProperties(entityProps);
      }
    }
  }, [state]);

  return (
    <div className="space-y-4">
      <InputGroup title="Design">
        <div className="grid grid-cols-2 gap-2">
          <InputSelector
            withSearch={false}
            className="col-span-2"
            title="Style"
            value={state.style}
            setValue={(value) => setState({ ...state, style: value as StepFormWizardBlockStyle })}
            options={StepFormWizardBlockStyles.map((f) => f)}
          />
          {state.style == "page" ? null : (
            <>
              <InputSelector
                withSearch={false}
                title="Height"
                value={state.height}
                disabled={state.style !== "modal"}
                setValue={(value) => setState({ ...state, height: value as any })}
                options={[
                  { value: "xs", name: "xs" },
                  { value: "sm", name: "sm" },
                  { value: "md", name: "md" },
                  { value: "lg", name: "lg" },
                  { value: "xl", name: "xl" },
                  { value: "auto", name: "auto" },
                ]}
              />
              <InputSelector
                withSearch={false}
                title="Can be dismissed"
                value={state.canBeDismissed ? "true" : "false"}
                disabled={state.style !== "modal"}
                setValue={(value) => setState({ ...state, canBeDismissed: value === "true" })}
                options={[
                  { value: "true", name: "Yes" },
                  { value: "false", name: "No" },
                ]}
              />
            </>
          )}
          {state.style !== "page" && state.style !== "modal" ? null : (
            <InputSelector
              withSearch={false}
              title="Progress"
              value={state.progressBar}
              setValue={(value) => setState({ ...state, progressBar: value as any })}
              options={[
                { value: "horizontal progress bar", name: "Horizontal Progress Bar" },
                { value: "vertical progress bar", name: "Vertical Progress Bar" },
                { value: "tabs", name: "tabs" },
              ]}
            />
          )}
        </div>
      </InputGroup>
      <InputGroup title="Entity">
        <InputSelect
          name="entity"
          title="Entity"
          required={true}
          value={state.entity}
          placeholder="select entity"
          options={entityOptions}
          setValue={(e) => setState({ ...state, entity: e?.toString() })}
          hint="Select the entity to be used in this step form wizard"
          className="w-full"
        />
      </InputGroup>

      <InputGroup title="Steps" description="Steps represent actions or tutorial steps that will be shown to the user.">
        <div className="space-y-2">
          <ReactSortable className="flex flex-col space-y-2" animation={200} list={state.steps} setList={(steps) => setState({ ...state, steps })}>
            {state.steps.map((item, index) => (
              <CollapsibleRow
                draggable
                key={item.id}
                title={item.title || "Untitled step"}
                value={StepFormWizardStepUtils.getStepDescription(item)}
                initial={!item.title}
                onRemove={() => {
                  const steps = state.steps ?? [];
                  const index = steps.findIndex((e) => e.id === item.id);
                  if (index > -1) {
                    steps.splice(index, 1);
                    setState({ ...state, steps });
                  }
                }}
              >
                <StepForm
                  item={item}
                  setSelectedEntitypropertiesMap={setSelectedEntitypropertiesMap}
                  selectedEntitypropertiesMap={selectedEntitypropertiesMap}
                  selectedEntity={state?.entity || ""}
                  entityProperites={entityProperites}
                  entities={entities}
                  index={index}
                  onUpdate={(item) => {
                    const steps = state.steps ?? [];
                    steps[index] = item;
                    setState({ ...state, steps });
                  }}
                />
              </CollapsibleRow>
            ))}
          </ReactSortable>
          <div className="flex justify-between space-x-2">
            <ButtonTertiary
              onClick={() => {
                setState({
                  ...state,
                  steps: [
                    ...(state.steps ?? []),
                    {
                      id: Math.floor(Math.random() * 10000).toString(),
                      // ...defaultStepFormWizardStepBlock,
                      title: "",
                      description: "",
                      links: [],
                      gallery: [],
                      input: [],
                    },
                  ],
                });
              }}
            >
              Add step
            </ButtonTertiary>
            <div>
              {state.steps.length > 0 ? (
                <ButtonTertiary
                  destructive
                  onClick={() => {
                    if (prompt("Are you sure? Type 'yes' to confirm") === "yes") setState({ ...state, steps: [] });
                  }}
                >
                  {t("shared.clear")}
                </ButtonTertiary>
              ) : (
                <ButtonTertiary onClick={() => setState({ ...state, steps: saasrockStepFormWizardStepBlocks })}>Load sample steps</ButtonTertiary>
              )}
            </div>
          </div>
        </div>
      </InputGroup>
    </div>
  );
}

function StepForm({
  item,
  onUpdate,
  entityProperites,
  selectedEntitypropertiesMap,
  setSelectedEntitypropertiesMap,
  index,
}: {
  item: StepFormWizardStepBlockDto;
  onUpdate: (item: StepFormWizardStepBlockDto) => void;
  entityProperites: any;
  selectedEntitypropertiesMap: { [key: string]: number };
  setSelectedEntitypropertiesMap: React.Dispatch<React.SetStateAction<{ [key: string]: number }>>;
  index: number;
}) {
  const [state, setState] = useState<StepFormWizardStepBlockDto>(item || defaultStepFormWizardStepBlock);
  useEffect(() => {
    onUpdate(state);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);
  return (
    <div className="grid grid-cols-1 gap-4 p-2">
      <InputText title="Title" value={state.title} setValue={(e) => setState({ ...state, title: e?.toString() })} required />
      <InputText
        title="Description"
        value={state.description}
        setValue={(e) => setState({ ...state, description: e?.toString() })}
        rows={5}
        hint="Markdown supported"
      />
      <InputText
        title="Icon"
        value={state.icon}
        setValue={(e) => setState({ ...state, icon: e?.toString() })}
        hint={<div className="text-gray-400">SVG or image URL sidebar icon</div>}
        button={
          <div className="absolute inset-y-0 right-0 flex py-0.5 pr-0.5">
            <kbd className="inline-flex w-10 items-center justify-center rounded border border-gray-300 bg-gray-50 px-1 text-center font-sans text-xs font-medium text-gray-500">
              {state.icon ? <EntityIcon className="h-7 w-7 text-gray-600" icon={state.icon} title={state.title} /> : <span className="text-red-600">?</span>}
            </kbd>
          </div>
        }
      />
      <div>
        <InputCheckbox
          name="saveForLater"
          title="Save For Later (enabled or not)"
          value={state?.saveForLater}
          setValue={(e) => setState({ ...state, saveForLater: e == e })}
        />
      </div>

      <InputGroup title="Links">
        <div className="flex flex-col space-y-2">
          {state.links.map((link, index) => (
            <div key={index} className="group relative grid grid-cols-4 gap-2">
              <button
                onClick={() => {
                  const links = state.links;
                  links.splice(index, 1);
                  setState({ ...state, links });
                }}
                type="button"
                className="absolute top-0 right-0 -mt-3 hidden origin-top-right justify-center rounded-full bg-white text-gray-600 group-hover:flex hover:text-red-500"
              >
                <XIcon className="h-6 w-6" />
              </button>
              <InputText
                title="Link"
                value={link.text}
                setValue={(e) => setState({ ...state, links: state.links.map((step, i) => (i === index ? { ...step, text: e.toString() } : step)) })}
              />
              <InputText
                title="Href"
                value={link.href}
                setValue={(e) => setState({ ...state, links: state.links.map((step, i) => (i === index ? { ...step, href: e.toString() } : step)) })}
              />
              <InputCheckbox
                title="Is primary"
                value={link.isPrimary}
                setValue={(e) => setState({ ...state, links: state.links.map((link, i) => (i === index ? { ...link, isPrimary: Boolean(e) } : link)) })}
              />
              <InputCheckbox
                title="target=_blank"
                value={link.target === "_blank"}
                setValue={(e) => setState({ ...state, links: state.links.map((link, i) => (i === index ? { ...link, target: e ? "_blank" : "" } : link)) })}
              />
            </div>
          ))}
          <ButtonTertiary onClick={() => setState({ ...state, links: [...state.links, { text: "Link", href: "#", isPrimary: false }] })}>
            Add link
          </ButtonTertiary>
        </div>
      </InputGroup>

      <InputGroup title="Gallery">
        <div className="flex flex-col space-y-2">
          <ReactSortable className="flex flex-col space-y-2" animation={200} list={state.gallery} setList={(gallery) => setState({ ...state, gallery })}>
            {state.gallery.map((item, index) => (
              <CollapsibleRow
                key={index}
                title={item.title || "Untitled gallery item"}
                value={item.title || "Untitled gallery item"}
                initial={!item.title}
                onRemove={() => {
                  const gallery = state.gallery ?? [];
                  gallery.splice(index, 1);
                  setState({ ...state, gallery });
                }}
              >
                <div className="grid grid-cols-1 gap-2">
                  <InputRadioGroup
                    title="Type"
                    value={item.type}
                    setValue={(e) =>
                      setState({
                        ...state,
                        gallery: state.gallery.map((item, i) => (i === index ? { ...item, type: e?.toString() as "image" | "video" } : item)),
                      })
                    }
                    options={[
                      { name: "Image", value: "image" },
                      { name: "Video", value: "video" },
                    ]}
                  />
                  <InputText
                    title="Title"
                    type="text"
                    value={item.title}
                    setValue={(e) => setState({ ...state, gallery: state.gallery.map((item, i) => (i === index ? { ...item, title: e.toString() } : item)) })}
                  />
                  <InputText
                    title="Src"
                    type="text"
                    value={item.src}
                    setValue={(e) => setState({ ...state, gallery: state.gallery.map((item, i) => (i === index ? { ...item, src: e.toString() } : item)) })}
                  />
                </div>
              </CollapsibleRow>
            ))}
          </ReactSortable>
          <ButtonTertiary
            onClick={() =>
              setState({
                ...state,
                gallery: [
                  ...(state.gallery ?? []),
                  {
                    id: Math.floor(Math.random() * 10000).toString(),
                    type: "image",
                    title: "Item " + state.gallery.length + 1,
                    src: "",
                  },
                ],
              })
            }
          >
            Add gallery item
          </ButtonTertiary>
        </div>
      </InputGroup>

      <InputGroup title="Input">
        <div className="flex flex-col space-y-2">
          {entityProperites.length > 0 && (
            <div className="jusitfy-center mb-2 flex items-center gap-2">
              <div className="h-64 flex-1 space-y-1 overflow-y-scroll rounded-md border-2 border-dashed border-gray-300 bg-gray-50 p-2">
                {entityProperites
                  .filter((item) => {
                    const selectedStep = selectedEntitypropertiesMap[item.propertyId];
                    return selectedStep === undefined || selectedStep === index;
                  })
                  .map((item, idx) => (
                    <div key={item.name}>
                      <InputCheckboxInline
                        name={"properties[" + idx + "].propertyId"}
                        title={
                          <div className="flex items-baseline space-x-1 truncate text-sm">
                            <div className={clsx("truncate", item.name?.includes(".") ? "text-gray-400" : "font-medium text-gray-800")}>
                              {item.title ? t(item.title) : item.name}
                            </div>
                            <div
                              className={clsx("truncate text-xs font-normal", item.name?.includes(".") ? "text-gray-400 italic" : "font-bold text-gray-800")}
                            >
                              ({item?.name})
                            </div>
                          </div>
                        }
                        value={state.properties?.find((f) => f.name === item.name) !== undefined}
                        setValue={(e) => {
                          if (e) {
                            setSelectedEntitypropertiesMap((prev) => ({ ...prev, [item.propertyId]: index }));
                            setState({
                              ...state,
                              properties: [
                                ...(state.properties ?? []),
                                {
                                  propertyId: item.propertyId,
                                  name: item.name,
                                  title: item.title,
                                  order: OrderHelper.getNextOrder(state.properties ?? []),
                                },
                              ],
                            });
                          } else {
                            setSelectedEntitypropertiesMap((prev) => {
                              const updated = { ...prev };
                              delete updated[item.propertyId];
                              return updated;
                            });
                            setState({
                              ...state,
                              properties: (state.properties ?? []).filter((f) => f.name !== item.name),
                            });
                          }
                        }}
                      />
                    </div>
                  ))}
              </div>
              <div className="h-64 flex-1 space-y-1 overflow-y-scroll rounded-md border-2 border-dashed border-gray-300 bg-gray-50 p-2">
                {state.properties
                  ?.sort((a, b) => a.order - b.order)
                  .map((item, idx) => (
                    <div key={idx} className="flex items-baseline space-x-1 text-sm">
                      <div className="flex-shrink-0">
                        <OrderIndexButtons
                          idx={idx}
                          items={
                            state.properties?.map((item, itemIdx) => {
                              return {
                                order: item.order,
                                idx: itemIdx,
                              };
                            }) || []
                          }
                          onChange={(newOrders) => {
                            setState({
                              ...state,
                              properties: state.properties?.map((item, itemIdx) => {
                                const newOrder = newOrders.find((f) => f.idx === itemIdx);
                                if (newOrder) {
                                  return {
                                    ...item,
                                    order: newOrder.order,
                                  };
                                } else {
                                  return item;
                                }
                              }),
                            });
                          }}
                        />
                      </div>
                      <div className={clsx("truncate", item.name?.includes(".") ? "text-gray-400" : "font-medium text-gray-800")}>
                        {item.title ? t(item.title) : ""}{" "}
                        <span className={clsx("text-xs font-normal", item.name?.includes(".") ? "text-gray-400 italic" : "font-bold text-gray-800")}>
                          ({item?.name})
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
          <GridBlockForm item={state.inputGrid} onUpdate={(item) => setState({ ...state, inputGrid: item })} />
          <ReactSortable className="flex flex-col space-y-2" animation={200} list={state.input} setList={(input) => setState({ ...state, input })}>
            {state.input.map((item, index) => (
              <CollapsibleRow
                key={index}
                title={item.title || "Untitled input"}
                value={item.title || "Untitled input"}
                initial={!item.title}
                onRemove={() => {
                  const input = state.input ?? [];
                  input.splice(index, 1);
                  setState({ ...state, input });
                }}
              >
                <div className="grid grid-cols-3 gap-2">
                  <InputText
                    title="Title"
                    type="text"
                    value={item.title}
                    setValue={(e) =>
                      setState({
                        ...state,
                        input: state.input.map((item, i) =>
                          i === index ? { ...item, title: e.toString(), name: StringUtils.toCamelCase(e?.toString().toLowerCase()) } : item
                        ),
                      })
                    }
                  />
                  <InputText
                    title="name"
                    type="text"
                    value={item.name}
                    setValue={(e) => setState({ ...state, input: state.input.map((item, i) => (i === index ? { ...item, name: e.toString() } : item)) })}
                  />
                  <InputCheckbox
                    title="Is required"
                    value={item.isRequired}
                    setValue={(e) => setState({ ...state, input: state.input.map((item, i) => (i === index ? { ...item, isRequired: Boolean(e) } : item)) })}
                  />

                  <InputSelect
                    className="col-span-3"
                    title="Type"
                    value={item.type}
                    setValue={(e) =>
                      setState({
                        ...state,
                        input: state.input.map((item, i) => (i === index ? { ...item, type: e?.toString() as "text" | "select" } : item)),
                      })
                    }
                    options={[
                      { name: "Text", value: "text" },
                      { name: "Select", value: "select" },
                    ]}
                  />
                  {item.type === "select" && (
                    <div className="col-span-3">
                      <SelectOptionsForm
                        options={item.options}
                        setOptions={(options) => setState({ ...state, input: state.input.map((item, i) => (i === index ? { ...item, options } : item)) })}
                      />
                    </div>
                  )}
                </div>
              </CollapsibleRow>
            ))}
          </ReactSortable>
          <ButtonTertiary
            onClick={() =>
              setState({
                ...state,
                input: [
                  ...(state.input ?? []),
                  {
                    id: Math.floor(Math.random() * 10000).toString(),
                    type: "text",
                    name: "",
                    title: "",
                    isRequired: false,
                    options: [],
                  },
                ],
              })
            }
          >
            Add input
          </ButtonTertiary>
        </div>
      </InputGroup>
      <InputGroup title="WebHook">
        <div className="grid grid-cols-2 gap-2">
          <InputText title="URL" value={state.webhookUrl} setValue={(e) => setState({ ...state, webhookUrl: e.toString() })} />
          <InputSelect
            name="Method"
            title="Method"
            required={false}
            value={state.webhookMethod}
            placeholder="select method"
            options={[
              {
                name: "POST",
                value: "POST",
              },
              {
                name: "PUT",
                value: "PUT",
              },
              {
                name: "PATCH",
                value: "PATCH",
              },
              {
                name: "DELETE",
                value: "DELETE",
              },
              {
                name: "GET",
                value: "GET",
              },
            ]}
            setValue={(e) => setState({ ...state, webhookMethod: e?.toString() })}
            className="w-full"
          />
        </div>
      </InputGroup>
    </div>
  );
}

function SelectOptionsForm({
  options,
  setOptions,
}: {
  options: StepFormWizardInputOptionDto[];
  setOptions: (options: StepFormWizardInputOptionDto[]) => void;
}) {
  const [state, setState] = useState<StepFormWizardInputOptionDto[]>(options ?? []);

  useEffect(() => {
    setOptions(state);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);

  return (
    <div className="flex flex-col space-y-2">
      {options.map((option, index) => (
        <div key={index} className="group relative grid grid-cols-2 gap-2">
          <button
            onClick={() => {
              const options = state ?? [];
              options.splice(index, 1);
              setState(options);
            }}
            type="button"
            className="absolute top-0 right-0 -mt-3 hidden origin-top-right justify-center rounded-full bg-white text-gray-600 group-hover:flex hover:text-red-500"
          >
            <XIcon className="h-6 w-6" />
          </button>
          <InputText
            title="Title"
            value={option.name}
            setValue={(e) =>
              setState(
                options.map((option, i) =>
                  i === index ? { ...option, name: e.toString(), value: StringUtils.toCamelCase(e?.toString().toLowerCase()) } : option
                )
              )
            }
          />
          <InputText
            title="Value"
            value={option.value}
            setValue={(e) => setState(options.map((option, i) => (i === index ? { ...option, value: e.toString() } : option)))}
          />
        </div>
      ))}
      <ButtonTertiary onClick={() => setState([...options, { value: "", name: "" }])}>Add option</ButtonTertiary>
    </div>
  );
}
