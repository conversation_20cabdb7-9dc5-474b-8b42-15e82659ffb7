import React from "react";
import classNames from "classnames";
import { useParams } from "react-router";
import { useIsMobile } from "~/hooks/use-mobile";

interface Step {
  id: string | number;
  title: string;
  completedAt?: Date | null;
  seenAt?: Date | null;
  webHookTriggeredAt?: Date | null;
}

interface CircleStepperProps {
  steps: Step[];
  currentStep?: number;
  onStepClick?: (stepIndex: number) => void;
  disablePastSteps?: boolean; // optional config
}

const CircleStepper = ({ steps, currentStep = 0, onStepClick, disablePastSteps = false }: CircleStepperProps) => {

  const params = useParams();
  const isOverViewPage = params.id;
  const isMobile = useIsMobile();
  const isTablePage = !params.id;
  const tableClass = isMobile && isTablePage ? "overflow-x-auto max-w-20" : "";
  return (
    <div className={`flex items-start ${tableClass}`}>
      {steps.map((step, index) => {
        const isCompleted = step?.completedAt != null;
        const isInProgress = step?.seenAt != null || step?.webHookTriggeredAt != null;
        const isCurrent = currentStep === index;
        const isClickable = !disablePastSteps || (!isCompleted && !isCurrent);
        const isFailed=step?.error?.length

        return (
          <div key={step.id ?? index} className="relative flex items-center">
            {/* Step block */}
            <div className="flex flex-col items-center px-2">
              {/* Step title */}
              <div className="mb-1 max-w-[80px] truncate text-center text-xs text-gray-700" title={step.title}>
                {step.title}
              </div>

              {/* Circle button */}
              <button
                onClick={() => isClickable && onStepClick?.(index)}
                disabled={!isClickable}
                aria-label={`Go to step ${index + 1}`}
                data-testid={`step-button-${index}`}
                className={classNames(
                  " flex h-6 w-6 items-center justify-center rounded border bg-[#D4E2FD] text-[#202229] text-[10px] leading-none text-center transition-all duration-300 focus:outline-none !cursor-default",
                  {
                    "border-accent text-[#202229]": isCompleted,
                    "border-input text-[#202229]": isCurrent,
                    "border-input bg-white": !isCompleted && !isCurrent,
                    "cursor-pointer": isClickable,
                    "cursor-not-allowed opacity-50": !isClickable,
                    "border-red-500 text-white bg-red-500 !cursor-not-allowed": isFailed,
                  },
                  { "z-10": !isTablePage }
                )}
              >
                {index + 1}
              </button>
            </div>

            {/* Connecting line */}
          </div>
        );
      })}
    </div>
  );
};

export default CircleStepper;
