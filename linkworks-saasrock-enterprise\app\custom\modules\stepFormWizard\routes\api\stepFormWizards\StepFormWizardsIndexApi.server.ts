import { ActionFunction, LoaderFunctionArgs, redirect } from "react-router";
import { getTranslations } from "~/locale/i18next.server";
import { getUserInfo } from "~/utils/session.server";
import { createStepFormWizard, getStepFormWizards, StepFormWizardWithDetails, deleteStepFormWizard } from "../../../db/stepFormWizard.db.server";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export namespace StepFormWizardIndexApi {
  export type LoaderData = {
    meta: MetaTagsDto;
    items: StepFormWizardWithDetails[];
    groupByStatus: { status: string; count: number }[];
  };
  export const loader = async ({ request }: LoaderFunctionArgs) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.view");
    const { t } = await getTranslations(request);
    const urlSearchParams = new URL(request.url).searchParams;
    // const currentPagination = getPaginationFromCurrentUrl(urlSearchParams);
    const status = urlSearchParams.get("status");
    const items = await getStepFormWizards({
      active: status === "active" ? true : status === "inactive" ? false : undefined,
    });
    const groupByStatus: { status: string; count: number }[] = [];
    items.forEach((item) => {
      if (item.active) {
        const index = groupByStatus.findIndex((item) => item.status === "active");
        if (index === -1) {
          groupByStatus.push({ status: "active", count: 1 });
        } else {
          groupByStatus[index].count++;
        }
      } else if (!item.active) {
        const index = groupByStatus.findIndex((item) => item.status === "inactive");
        if (index === -1) {
          groupByStatus.push({ status: "inactive", count: 1 });
        } else {
          groupByStatus[index].count++;
        }
      }
    });

    const data: LoaderData = {
      meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
      items,
      groupByStatus,
    };
    return data;
  };

  export type ActionData = {
    error?: string;
  };
  export const action: ActionFunction = async ({ request }) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.update");
    const { t } = await getTranslations(request);
    const form = await request.formData();
    const userInfo = await getUserInfo(request);
    const action = form.get("action");
    if (action === "create") {
      await verifyUserHasPermission(request, "admin.stepFormWizard.create");
      const title = form.get("title")?.toString() ?? "";
      if (!title) {
        return Response.json({ error: "Step form wizard title is required" }, { status: 400 });
      }
      const stepFormWizard = await createStepFormWizard({
        title,
        type: "modal",
        active: false,
        realtime: false,
        canBeDismissed: true,
        height: "xl",
        filters: [{ type: "user.is", value: userInfo.userId }],
        steps: [],
      });
      return redirect(`/admin/step-form-wizard/step-form-wizards/${stepFormWizard.id}`);
    } else if (action === "delete") {
      const id = form.get("id")?.toString();
      if (!id) {
        return Response.json({ error: "Missing ID" }, { status: 400 });
      }

      await verifyUserHasPermission(request, "admin.stepFormWizard.delete");

      try {
        await deleteStepFormWizard(id);
        return redirect("/admin/step-form-wizard/step-form-wizards");
      } catch (error) {
        console.error("Delete failed:", error);
        return Response.json({ error: "Delete failed" }, { status: 500 });
      }
    } else {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
    }
  };
}
