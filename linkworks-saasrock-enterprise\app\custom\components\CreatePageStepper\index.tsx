import React from "react";

const Stepper = ({
  steps,
  currentStep,
  handleCurrentStep,
  sessionSteps,
}: {
  steps: { title: string }[];
  currentStep: number;
  handleCurrentStep: (stepIndex: number) => void;
  sessionSteps?:any
}) => {
  return (
    <div className="flex items-center">
      {steps.map((step, index) => {
        let stepStatus:"FAILED" | "PENDING" | "SUCCESS"="PENDING";
        if (sessionSteps.length) {
          stepStatus = sessionSteps[index].status;
        }
        const isCurrent = index === currentStep;

        return (
          <React.Fragment key={index}>
            {/* Step circle */}
            <div className="flex items-center gap-2">
             <div
                onClick={() => handleCurrentStep(index)}
                className={`flex h-[32px] w-[32px] cursor-pointer items-center justify-center rounded-[8px] border text-[14px] font-semibold transition ${
                  stepStatus === "SUCCESS"
                    ? "border-green-600 bg-green-600 text-white"
                    : stepStatus === "FAILED"
                      ? "border-red-600 bg-red-600 text-white"
                      : stepStatus === "PENDING" && index<currentStep
                        ? "border-orange-500 bg-orange-500 text-white"
                        : "border-[#E6E6E6] bg-white text-black"
                }`}
              >
                {stepStatus === "SUCCESS" ? (
                  // ✅ Custom SVG checkmark icon
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                    <g clipPath="url(#clip0_7638_77321)">
                      <path
                        d="M12.0001 4.95969L11.0601 3.96094L6.83344 8.45177L7.77344 9.45052L12.0001 4.95969ZM14.8268 3.96094L7.77344 11.4551L4.98677 8.50135L4.04677 9.5001L7.77344 13.4597L15.7734 4.95969L14.8268 3.96094ZM0.273438 9.5001L4.0001 13.4597L4.9401 12.4609L1.2201 8.50135L0.273438 9.5001Z"
                        fill="#202229"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_7638_77321">
                        <rect width="16" height="17" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                ) : (
                  index + 1
                )}
              </div>

              <span className="pr-2 pl-1 text-[12px] font-normal whitespace-nowrap text-[#202229]">{step.title}</span>
            </div>

            {/* Connector line after step */}
            {index !== steps.length - 1 && (
              <div
                className="mx-[4px] h-[1px] w-[24px]"
                style={{
                  backgroundColor:
                    index < currentStep - 1
                      ? "#64D693" // Green for completed
                      : index === currentStep - 1
                        ? "#274AFF" //Blue for current step connector
                        : "#E6E6E6", //Gray for upcoming
                }}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default Stepper;
