/*
  Warnings:

  - A unique constraint covering the columns `[stepFormWizardId]` on the table `Entity` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[stepFormWizardSessionId]` on the table `Row` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "StepFormWizardSession_stepFormWizardId_userId_tenantId_key";

-- AlterTable
ALTER TABLE "Entity" ADD COLUMN     "isStepFormWizard" BOOLEAN,
ADD COLUMN     "stepFormWizardId" TEXT;

-- AlterTable
ALTER TABLE "StepFormWizard" ADD COLUMN     "entity" TEXT,
ADD COLUMN     "progressBar" TEXT;

-- AlterTable
ALTER TABLE "StepFormWizardSession" ADD COLUMN     "callBackURL" TEXT,
ADD COLUMN     "currentStepIndex" TEXT,
ADD COLUMN     "error" TEXT[],
ADD COLUMN     "logicAppRunId" TEXT;

-- AlterTable
ALTER TABLE "StepFormWizardSessionStep" ADD COLUMN     "webHookTriggeredAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Row" ADD COLUMN     "stepFormWizardId" TEXT,
ADD COLUMN     "stepFormWizardSessionId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Entity_stepFormWizardId_key" ON "Entity"("stepFormWizardId");

-- CreateIndex
CREATE UNIQUE INDEX "Row_stepFormWizardSessionId_key" ON "Row"("stepFormWizardSessionId");

-- AddForeignKey
ALTER TABLE "Entity" ADD CONSTRAINT "Entity_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Row" ADD CONSTRAINT "Row_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;
