{"version": 3, "sources": ["../../swr/dist/mutation/index.mjs"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport useSWR, { useSWRConfig } from '../index/index.mjs';\nimport { IS_REACT_LEGACY, useIsomorphicLayoutEffect, withMiddleware, serialize, mergeObjects, getTimestamp, UNDEFINED } from '../_internal/index.mjs';\n\nconst startTransition = IS_REACT_LEGACY ? (cb)=>{\n    cb();\n} : React.startTransition;\n/**\n * An implementation of state with dependency-tracking.\n * @param initialState - The initial state object.\n */ const useStateWithDeps = (initialState)=>{\n    const [, rerender] = useState({});\n    const unmountedRef = useRef(false);\n    const stateRef = useRef(initialState);\n    // If a state property (data, error, or isValidating) is accessed by the render\n    // function, we mark the property as a dependency so if it is updated again\n    // in the future, we trigger a rerender.\n    // This is also known as dependency-tracking.\n    const stateDependenciesRef = useRef({\n        data: false,\n        error: false,\n        isValidating: false\n    });\n    /**\n   * Updates state and triggers re-render if necessary.\n   * @param payload To change stateRef, pass the values explicitly to setState:\n   * @example\n   * ```js\n   * setState({\n   *   isValidating: false\n   *   data: newData // set data to newData\n   *   error: undefined // set error to undefined\n   * })\n   *\n   * setState({\n   *   isValidating: false\n   *   data: undefined // set data to undefined\n   *   error: err // set error to err\n   * })\n   * ```\n   */ const setState = useCallback((payload)=>{\n        let shouldRerender = false;\n        const currentState = stateRef.current;\n        for(const key in payload){\n            if (Object.prototype.hasOwnProperty.call(payload, key)) {\n                const k = key;\n                // If the property has changed, update the state and mark rerender as\n                // needed.\n                if (currentState[k] !== payload[k]) {\n                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                    currentState[k] = payload[k];\n                    // If the property is accessed by the component, a rerender should be\n                    // triggered.\n                    if (stateDependenciesRef.current[k]) {\n                        shouldRerender = true;\n                    }\n                }\n            }\n        }\n        if (shouldRerender && !unmountedRef.current) {\n            rerender({});\n        }\n    }, []);\n    useIsomorphicLayoutEffect(()=>{\n        unmountedRef.current = false;\n        return ()=>{\n            unmountedRef.current = true;\n        };\n    });\n    return [\n        stateRef,\n        stateDependenciesRef.current,\n        setState\n    ];\n};\n\nconst mutation = ()=>(key, fetcher, config = {})=>{\n        const { mutate } = useSWRConfig();\n        const keyRef = useRef(key);\n        const fetcherRef = useRef(fetcher);\n        const configRef = useRef(config);\n        // Ditch all mutation results that happened earlier than this timestamp.\n        const ditchMutationsUntilRef = useRef(0);\n        const [stateRef, stateDependencies, setState] = useStateWithDeps({\n            data: UNDEFINED,\n            error: UNDEFINED,\n            isMutating: false\n        });\n        const currentState = stateRef.current;\n        const trigger = useCallback(async (arg, opts)=>{\n            const [serializedKey, resolvedKey] = serialize(keyRef.current);\n            if (!fetcherRef.current) {\n                throw new Error('Can’t trigger the mutation: missing fetcher.');\n            }\n            if (!serializedKey) {\n                throw new Error('Can’t trigger the mutation: missing key.');\n            }\n            // Disable cache population by default.\n            const options = mergeObjects(mergeObjects({\n                populateCache: false,\n                throwOnError: true\n            }, configRef.current), opts);\n            // Trigger a mutation, and also track the timestamp. Any mutation that happened\n            // earlier this timestamp should be ignored.\n            const mutationStartedAt = getTimestamp();\n            ditchMutationsUntilRef.current = mutationStartedAt;\n            setState({\n                isMutating: true\n            });\n            try {\n                const data = await mutate(serializedKey, fetcherRef.current(resolvedKey, {\n                    arg\n                }), // We must throw the error here so we can catch and update the states.\n                mergeObjects(options, {\n                    throwOnError: true\n                }));\n                // If it's reset after the mutation, we don't broadcast any state change.\n                if (ditchMutationsUntilRef.current <= mutationStartedAt) {\n                    startTransition(()=>setState({\n                            data,\n                            isMutating: false,\n                            error: undefined\n                        }));\n                    options.onSuccess == null ? void 0 : options.onSuccess.call(options, data, serializedKey, options);\n                }\n                return data;\n            } catch (error) {\n                // If it's reset after the mutation, we don't broadcast any state change\n                // or throw because it's discarded.\n                if (ditchMutationsUntilRef.current <= mutationStartedAt) {\n                    startTransition(()=>setState({\n                            error: error,\n                            isMutating: false\n                        }));\n                    options.onError == null ? undefined : options.onError.call(options, error, serializedKey, options);\n                    if (options.throwOnError) {\n                        throw error;\n                    }\n                }\n            }\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        []);\n        const reset = useCallback(()=>{\n            ditchMutationsUntilRef.current = getTimestamp();\n            setState({\n                data: UNDEFINED,\n                error: UNDEFINED,\n                isMutating: false\n            });\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, []);\n        useIsomorphicLayoutEffect(()=>{\n            keyRef.current = key;\n            fetcherRef.current = fetcher;\n            configRef.current = config;\n        });\n        // We don't return `mutate` here as it can be pretty confusing (e.g. people\n        // calling `mutate` but they actually mean `trigger`).\n        // And also, `mutate` relies on the useSWR hook to exist too.\n        return {\n            trigger,\n            reset,\n            get data () {\n                stateDependencies.data = true;\n                return currentState.data;\n            },\n            get error () {\n                stateDependencies.error = true;\n                return currentState.error;\n            },\n            get isMutating () {\n                stateDependencies.isMutating = true;\n                return currentState.isMutating;\n            }\n        };\n    };\n/**\n * A hook to define and manually trigger remote mutations like POST, PUT, DELETE and PATCH use cases.\n *\n * @link https://swr.vercel.app/docs/mutation\n * @example\n * ```jsx\n * import useSWRMutation from 'swr/mutation'\n *\n * const {\n *   data,\n *   error,\n *   trigger,\n *   reset,\n *   isMutating\n * } = useSWRMutation(key, fetcher, options?)\n * ```\n */ const useSWRMutation = withMiddleware(useSWR, mutation);\n\nexport { useSWRMutation as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,mBAAqD;AAIrD,IAAM,kBAAkB,kBAAkB,CAAC,OAAK;AAC5C,KAAG;AACP,IAAI,aAAAA,QAAM;AAIN,IAAM,mBAAmB,CAAC,iBAAe;AACzC,QAAM,CAAC,EAAE,QAAQ,QAAI,uBAAS,CAAC,CAAC;AAChC,QAAM,mBAAe,qBAAO,KAAK;AACjC,QAAM,eAAW,qBAAO,YAAY;AAKpC,QAAM,2BAAuB,qBAAO;AAAA,IAChC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,EAClB,CAAC;AAkBC,QAAM,eAAW,0BAAY,CAAC,YAAU;AACtC,QAAI,iBAAiB;AACrB,UAAM,eAAe,SAAS;AAC9B,eAAU,OAAO,SAAQ;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,GAAG,GAAG;AACpD,cAAM,IAAI;AAGV,YAAI,aAAa,CAAC,MAAM,QAAQ,CAAC,GAAG;AAEhC,uBAAa,CAAC,IAAI,QAAQ,CAAC;AAG3B,cAAI,qBAAqB,QAAQ,CAAC,GAAG;AACjC,6BAAiB;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,kBAAkB,CAAC,aAAa,SAAS;AACzC,eAAS,CAAC,CAAC;AAAA,IACf;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,4BAA0B,MAAI;AAC1B,iBAAa,UAAU;AACvB,WAAO,MAAI;AACP,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA,qBAAqB;AAAA,IACrB;AAAA,EACJ;AACJ;AAEA,IAAM,WAAW,MAAI,CAAC,KAAK,SAAS,SAAS,CAAC,MAAI;AAC1C,QAAM,EAAE,OAAO,IAAI,aAAa;AAChC,QAAM,aAAS,qBAAO,GAAG;AACzB,QAAM,iBAAa,qBAAO,OAAO;AACjC,QAAM,gBAAY,qBAAO,MAAM;AAE/B,QAAM,6BAAyB,qBAAO,CAAC;AACvC,QAAM,CAAC,UAAU,mBAAmB,QAAQ,IAAI,iBAAiB;AAAA,IAC7D,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,EAChB,CAAC;AACD,QAAM,eAAe,SAAS;AAC9B,QAAM,cAAU;AAAA,IAAY,OAAO,KAAK,SAAO;AAC3C,YAAM,CAAC,eAAe,WAAW,IAAI,UAAU,OAAO,OAAO;AAC7D,UAAI,CAAC,WAAW,SAAS;AACrB,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAClE;AACA,UAAI,CAAC,eAAe;AAChB,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC9D;AAEA,YAAM,UAAU,aAAa,aAAa;AAAA,QACtC,eAAe;AAAA,QACf,cAAc;AAAA,MAClB,GAAG,UAAU,OAAO,GAAG,IAAI;AAG3B,YAAM,oBAAoB,aAAa;AACvC,6BAAuB,UAAU;AACjC,eAAS;AAAA,QACL,YAAY;AAAA,MAChB,CAAC;AACD,UAAI;AACA,cAAM,OAAO,MAAM;AAAA,UAAO;AAAA,UAAe,WAAW,QAAQ,aAAa;AAAA,YACrE;AAAA,UACJ,CAAC;AAAA;AAAA,UACD,aAAa,SAAS;AAAA,YAClB,cAAc;AAAA,UAClB,CAAC;AAAA,QAAC;AAEF,YAAI,uBAAuB,WAAW,mBAAmB;AACrD,0BAAgB,MAAI,SAAS;AAAA,YACrB;AAAA,YACA,YAAY;AAAA,YACZ,OAAO;AAAA,UACX,CAAC,CAAC;AACN,kBAAQ,aAAa,OAAO,SAAS,QAAQ,UAAU,KAAK,SAAS,MAAM,eAAe,OAAO;AAAA,QACrG;AACA,eAAO;AAAA,MACX,SAAS,OAAO;AAGZ,YAAI,uBAAuB,WAAW,mBAAmB;AACrD,0BAAgB,MAAI,SAAS;AAAA,YACrB;AAAA,YACA,YAAY;AAAA,UAChB,CAAC,CAAC;AACN,kBAAQ,WAAW,OAAO,SAAY,QAAQ,QAAQ,KAAK,SAAS,OAAO,eAAe,OAAO;AACjG,cAAI,QAAQ,cAAc;AACtB,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IACA,CAAC;AAAA,EAAC;AACF,QAAM,YAAQ,0BAAY,MAAI;AAC1B,2BAAuB,UAAU,aAAa;AAC9C,aAAS;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IAChB,CAAC;AAAA,EAEL,GAAG,CAAC,CAAC;AACL,4BAA0B,MAAI;AAC1B,WAAO,UAAU;AACjB,eAAW,UAAU;AACrB,cAAU,UAAU;AAAA,EACxB,CAAC;AAID,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,IAAI,OAAQ;AACR,wBAAkB,OAAO;AACzB,aAAO,aAAa;AAAA,IACxB;AAAA,IACA,IAAI,QAAS;AACT,wBAAkB,QAAQ;AAC1B,aAAO,aAAa;AAAA,IACxB;AAAA,IACA,IAAI,aAAc;AACd,wBAAkB,aAAa;AAC/B,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AACJ;AAiBA,IAAM,iBAAiB,eAAe,QAAQ,QAAQ;", "names": ["React"]}