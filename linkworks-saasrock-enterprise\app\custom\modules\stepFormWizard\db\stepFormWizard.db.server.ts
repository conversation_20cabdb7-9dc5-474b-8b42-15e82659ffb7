import { StepForm<PERSON><PERSON>rd, StepF<PERSON><PERSON><PERSON>rd<PERSON>ilter, StepFormWizardStep, StepFormWizardSession, Prisma } from "@prisma/client";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import { db } from "~/utils/db.server";
import { StepFormWizardFilterType } from "../dtos/StepFormWizardFilterTypes";
import { StepFormWizardSessionStatus } from "../dtos/StepFormWizardSessionStatus";
import { getEntitiesByName } from "~/utils/db/entities/entities.db.server";
export type StepFormWizardWithDetails = StepFormWizard & {
  filters: StepFormWizardFilter[];
  steps: StepFormWizardStep[];
  sessions: StepFormWizardSession[];
};

export async function getStepFormWizards({ active, realtime }: { active?: boolean; realtime?: boolean }): Promise<StepFormWizardWithDetails[]> {
  return await db.stepFormWizard.findMany({
    where: { active, realtime },
    include: { filters: { orderBy: { createdAt: "asc" } }, steps: { orderBy: { order: "asc" } }, sessions: true },
    orderBy: { createdAt: "desc" },
  });
}

export async function getStepFormWizardsWithPagination({
  where,
  pagination,
}: {
  where?: Prisma.StepFormWizardWhereInput;
  pagination: { page: number; pageSize: number };
}): Promise<{ items: StepFormWizardWithDetails[]; pagination: PaginationDto }> {
  const items = await db.stepFormWizard.findMany({
    where,
    take: pagination.pageSize,
    skip: (pagination.page - 1) * pagination.pageSize,
    include: { filters: { orderBy: { createdAt: "asc" } }, steps: { orderBy: { order: "asc" } }, sessions: true },
    orderBy: { createdAt: "desc" },
  });
  const totalItems = await db.stepFormWizard.count({
    where,
  });
  return {
    items,
    pagination: {
      page: pagination.page,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages: Math.ceil(totalItems / pagination.pageSize),
    },
  };
}

export async function getStepFormWizard(id: string): Promise<StepFormWizardWithDetails | null> {
  return await db.stepFormWizard.findUnique({
    where: { id },
    include: { filters: { orderBy: { createdAt: "asc" } }, steps: { orderBy: { order: "asc" } }, sessions: true },
  });
}

export async function createStepFormWizard(data: {
  title: string;
  type: "modal" | "page";
  active: boolean;
  realtime: boolean;
  canBeDismissed: boolean;
  height: string;
  progressBar: string;
  filters: { type: StepFormWizardFilterType; value: string | null }[];
  steps: { order: number; block: string }[];
}): Promise<StepFormWizard> {
  return await db.stepFormWizard.create({
    data: {
      title: data.title,
      type: data.type.toString(),
      active: data.active,
      realtime: data.realtime,
      canBeDismissed: data.canBeDismissed,
      height: data.height,
      progressBar: data.progressBar,
      filters: { create: data.filters },
      steps: { create: data.steps },
    },
  });
}

export async function updateStepFormWizard(
  id: string,
  data: {
    title?: string;
    type?: "modal" | "page";
    realtime?: boolean;
    active?: boolean;
    canBeDismissed?: boolean;
    height?: string;
    entity?: string;
    progressBar?: string;
    filters?: {
      type: StepFormWizardFilterType;
      value: string | null;
    }[];
    steps?: { order: number; block: string }[];
  }
): Promise<StepFormWizard> {
  const update: Prisma.StepFormWizardUpdateInput = {
    title: data.title,
    type: data.type?.toString(),
    realtime: data.realtime,
    active: data.active,
    canBeDismissed: data.canBeDismissed,
    height: data.height,
    entity: data.entity,
    progressBar: data.progressBar,
  };
  if (data.filters) {
    update.filters = {
      deleteMany: {},
      create: data.filters,
    };
  }
  if (data.steps) {
    update.steps = {
      deleteMany: {},
      create: data.steps,
    };
  }
  if (update.entity && typeof update.entity === 'string' && id) {
    const entityModel = await getEntitiesByName([update.entity as string]);
    if (entityModel.length && entityModel[0]?.id) {
      // @toDo handle linking of entity model with multiple stepFormWizards : issue
      await db.entity.update({
        where: {
          id: entityModel[0]?.id,
        },
        data: {
          stepFormWizardId: id,
        }
      })
    }
  }
  return await db.stepFormWizard.update({
    where: { id },
    data: update,
  });
}

export async function setStepFormWizardManualSessions(
  id: string,
  data: { userId: string; tenantId: string | null; status: StepFormWizardSessionStatus }[]
): Promise<StepFormWizard> {
  const update: Prisma.StepFormWizardUpdateInput = {
    sessions: {
      deleteMany: {},
      create: data,
    },
  };
  return await db.stepFormWizard.update({
    where: { id },
    data: update,
  });
}

export async function createStepFormWizardSession(
  stepFormWizard: StepFormWizardWithDetails,
  session: { userId: string; tenantId: string | null; rowId?: string, status: StepFormWizardSessionStatus; matchingFilters: StepFormWizardFilter[]; createdRealtime: boolean }
) {
  return await db.stepFormWizardSession.create({
    data: {
      stepFormWizardId: stepFormWizard.id,
      userId: session.userId,
      tenantId: session.tenantId,
      status: session.status,
      createdRealtime: session.createdRealtime,
      ...(session.rowId ? {
        sessionRow: {
          connect: {
            id: session.rowId
          }
        }
      } : {}),
      sessionSteps: {
        create: stepFormWizard.steps.map((step) => ({
          stepId: step.id,
        })),
      },
      matches: {
        create: session.matchingFilters.map((filter) => ({
          stepFormWizardFilterId: filter.id,
        })),
      },
    },
  });
}

export async function deleteStepFormWizard(id: string): Promise<StepFormWizard> {
  return await db.stepFormWizard.delete({
    where: { id },
  });
}
