{"version": 3, "sources": ["../../platform/platform.js"], "sourcesContent": ["/*!\n * Platform.js v1.3.6\n * Copyright 2014-2020 <PERSON>\n * Copyright 2011-2013 <PERSON><PERSON><PERSON>\n * Available under MIT license\n */\n;(function() {\n  'use strict';\n\n  /** Used to determine if values are of the language type `Object`. */\n  var objectTypes = {\n    'function': true,\n    'object': true\n  };\n\n  /** Used as a reference to the global object. */\n  var root = (objectTypes[typeof window] && window) || this;\n\n  /** Backup possible global object. */\n  var oldRoot = root;\n\n  /** Detect free variable `exports`. */\n  var freeExports = objectTypes[typeof exports] && exports;\n\n  /** Detect free variable `module`. */\n  var freeModule = objectTypes[typeof module] && module && !module.nodeType && module;\n\n  /** Detect free variable `global` from Node.js or Browserified code and use it as `root`. */\n  var freeGlobal = freeExports && freeModule && typeof global == 'object' && global;\n  if (freeGlobal && (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal || freeGlobal.self === freeGlobal)) {\n    root = freeGlobal;\n  }\n\n  /**\n   * Used as the maximum length of an array-like object.\n   * See the [ES6 spec](http://people.mozilla.org/~jorendorff/es6-draft.html#sec-tolength)\n   * for more details.\n   */\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n\n  /** Regular expression to detect Opera. */\n  var reOpera = /\\bOpera/;\n\n  /** Possible global object. */\n  var thisBinding = this;\n\n  /** Used for native method references. */\n  var objectProto = Object.prototype;\n\n  /** Used to check for own properties of an object. */\n  var hasOwnProperty = objectProto.hasOwnProperty;\n\n  /** Used to resolve the internal `[[Class]]` of values. */\n  var toString = objectProto.toString;\n\n  /*--------------------------------------------------------------------------*/\n\n  /**\n   * Capitalizes a string value.\n   *\n   * @private\n   * @param {string} string The string to capitalize.\n   * @returns {string} The capitalized string.\n   */\n  function capitalize(string) {\n    string = String(string);\n    return string.charAt(0).toUpperCase() + string.slice(1);\n  }\n\n  /**\n   * A utility function to clean up the OS name.\n   *\n   * @private\n   * @param {string} os The OS name to clean up.\n   * @param {string} [pattern] A `RegExp` pattern matching the OS name.\n   * @param {string} [label] A label for the OS.\n   */\n  function cleanupOS(os, pattern, label) {\n    // Platform tokens are defined at:\n    // http://msdn.microsoft.com/en-us/library/ms537503(VS.85).aspx\n    // http://web.archive.org/web/20081122053950/http://msdn.microsoft.com/en-us/library/ms537503(VS.85).aspx\n    var data = {\n      '10.0': '10',\n      '6.4':  '10 Technical Preview',\n      '6.3':  '8.1',\n      '6.2':  '8',\n      '6.1':  'Server 2008 R2 / 7',\n      '6.0':  'Server 2008 / Vista',\n      '5.2':  'Server 2003 / XP 64-bit',\n      '5.1':  'XP',\n      '5.01': '2000 SP1',\n      '5.0':  '2000',\n      '4.0':  'NT',\n      '4.90': 'ME'\n    };\n    // Detect Windows version from platform tokens.\n    if (pattern && label && /^Win/i.test(os) && !/^Windows Phone /i.test(os) &&\n        (data = data[/[\\d.]+$/.exec(os)])) {\n      os = 'Windows ' + data;\n    }\n    // Correct character case and cleanup string.\n    os = String(os);\n\n    if (pattern && label) {\n      os = os.replace(RegExp(pattern, 'i'), label);\n    }\n\n    os = format(\n      os.replace(/ ce$/i, ' CE')\n        .replace(/\\bhpw/i, 'web')\n        .replace(/\\bMacintosh\\b/, 'Mac OS')\n        .replace(/_PowerPC\\b/i, ' OS')\n        .replace(/\\b(OS X) [^ \\d]+/i, '$1')\n        .replace(/\\bMac (OS X)\\b/, '$1')\n        .replace(/\\/(\\d)/, ' $1')\n        .replace(/_/g, '.')\n        .replace(/(?: BePC|[ .]*fc[ \\d.]+)$/i, '')\n        .replace(/\\bx86\\.64\\b/gi, 'x86_64')\n        .replace(/\\b(Windows Phone) OS\\b/, '$1')\n        .replace(/\\b(Chrome OS \\w+) [\\d.]+\\b/, '$1')\n        .split(' on ')[0]\n    );\n\n    return os;\n  }\n\n  /**\n   * An iteration utility for arrays and objects.\n   *\n   * @private\n   * @param {Array|Object} object The object to iterate over.\n   * @param {Function} callback The function called per iteration.\n   */\n  function each(object, callback) {\n    var index = -1,\n        length = object ? object.length : 0;\n\n    if (typeof length == 'number' && length > -1 && length <= maxSafeInteger) {\n      while (++index < length) {\n        callback(object[index], index, object);\n      }\n    } else {\n      forOwn(object, callback);\n    }\n  }\n\n  /**\n   * Trim and conditionally capitalize string values.\n   *\n   * @private\n   * @param {string} string The string to format.\n   * @returns {string} The formatted string.\n   */\n  function format(string) {\n    string = trim(string);\n    return /^(?:webOS|i(?:OS|P))/.test(string)\n      ? string\n      : capitalize(string);\n  }\n\n  /**\n   * Iterates over an object's own properties, executing the `callback` for each.\n   *\n   * @private\n   * @param {Object} object The object to iterate over.\n   * @param {Function} callback The function executed per own property.\n   */\n  function forOwn(object, callback) {\n    for (var key in object) {\n      if (hasOwnProperty.call(object, key)) {\n        callback(object[key], key, object);\n      }\n    }\n  }\n\n  /**\n   * Gets the internal `[[Class]]` of a value.\n   *\n   * @private\n   * @param {*} value The value.\n   * @returns {string} The `[[Class]]`.\n   */\n  function getClassOf(value) {\n    return value == null\n      ? capitalize(value)\n      : toString.call(value).slice(8, -1);\n  }\n\n  /**\n   * Host objects can return type values that are different from their actual\n   * data type. The objects we are concerned with usually return non-primitive\n   * types of \"object\", \"function\", or \"unknown\".\n   *\n   * @private\n   * @param {*} object The owner of the property.\n   * @param {string} property The property to check.\n   * @returns {boolean} Returns `true` if the property value is a non-primitive, else `false`.\n   */\n  function isHostType(object, property) {\n    var type = object != null ? typeof object[property] : 'number';\n    return !/^(?:boolean|number|string|undefined)$/.test(type) &&\n      (type == 'object' ? !!object[property] : true);\n  }\n\n  /**\n   * Prepares a string for use in a `RegExp` by making hyphens and spaces optional.\n   *\n   * @private\n   * @param {string} string The string to qualify.\n   * @returns {string} The qualified string.\n   */\n  function qualify(string) {\n    return String(string).replace(/([ -])(?!$)/g, '$1?');\n  }\n\n  /**\n   * A bare-bones `Array#reduce` like utility function.\n   *\n   * @private\n   * @param {Array} array The array to iterate over.\n   * @param {Function} callback The function called per iteration.\n   * @returns {*} The accumulated result.\n   */\n  function reduce(array, callback) {\n    var accumulator = null;\n    each(array, function(value, index) {\n      accumulator = callback(accumulator, value, index, array);\n    });\n    return accumulator;\n  }\n\n  /**\n   * Removes leading and trailing whitespace from a string.\n   *\n   * @private\n   * @param {string} string The string to trim.\n   * @returns {string} The trimmed string.\n   */\n  function trim(string) {\n    return String(string).replace(/^ +| +$/g, '');\n  }\n\n  /*--------------------------------------------------------------------------*/\n\n  /**\n   * Creates a new platform object.\n   *\n   * @memberOf platform\n   * @param {Object|string} [ua=navigator.userAgent] The user agent string or\n   *  context object.\n   * @returns {Object} A platform object.\n   */\n  function parse(ua) {\n\n    /** The environment context object. */\n    var context = root;\n\n    /** Used to flag when a custom context is provided. */\n    var isCustomContext = ua && typeof ua == 'object' && getClassOf(ua) != 'String';\n\n    // Juggle arguments.\n    if (isCustomContext) {\n      context = ua;\n      ua = null;\n    }\n\n    /** Browser navigator object. */\n    var nav = context.navigator || {};\n\n    /** Browser user agent string. */\n    var userAgent = nav.userAgent || '';\n\n    ua || (ua = userAgent);\n\n    /** Used to flag when `thisBinding` is the [ModuleScope]. */\n    var isModuleScope = isCustomContext || thisBinding == oldRoot;\n\n    /** Used to detect if browser is like Chrome. */\n    var likeChrome = isCustomContext\n      ? !!nav.likeChrome\n      : /\\bChrome\\b/.test(ua) && !/internal|\\n/i.test(toString.toString());\n\n    /** Internal `[[Class]]` value shortcuts. */\n    var objectClass = 'Object',\n        airRuntimeClass = isCustomContext ? objectClass : 'ScriptBridgingProxyObject',\n        enviroClass = isCustomContext ? objectClass : 'Environment',\n        javaClass = (isCustomContext && context.java) ? 'JavaPackage' : getClassOf(context.java),\n        phantomClass = isCustomContext ? objectClass : 'RuntimeObject';\n\n    /** Detect Java environments. */\n    var java = /\\bJava/.test(javaClass) && context.java;\n\n    /** Detect Rhino. */\n    var rhino = java && getClassOf(context.environment) == enviroClass;\n\n    /** A character to represent alpha. */\n    var alpha = java ? 'a' : '\\u03b1';\n\n    /** A character to represent beta. */\n    var beta = java ? 'b' : '\\u03b2';\n\n    /** Browser document object. */\n    var doc = context.document || {};\n\n    /**\n     * Detect Opera browser (Presto-based).\n     * http://www.howtocreate.co.uk/operaStuff/operaObject.html\n     * http://dev.opera.com/articles/view/opera-mini-web-content-authoring-guidelines/#operamini\n     */\n    var opera = context.operamini || context.opera;\n\n    /** Opera `[[Class]]`. */\n    var operaClass = reOpera.test(operaClass = (isCustomContext && opera) ? opera['[[Class]]'] : getClassOf(opera))\n      ? operaClass\n      : (opera = null);\n\n    /*------------------------------------------------------------------------*/\n\n    /** Temporary variable used over the script's lifetime. */\n    var data;\n\n    /** The CPU architecture. */\n    var arch = ua;\n\n    /** Platform description array. */\n    var description = [];\n\n    /** Platform alpha/beta indicator. */\n    var prerelease = null;\n\n    /** A flag to indicate that environment features should be used to resolve the platform. */\n    var useFeatures = ua == userAgent;\n\n    /** The browser/environment version. */\n    var version = useFeatures && opera && typeof opera.version == 'function' && opera.version();\n\n    /** A flag to indicate if the OS ends with \"/ Version\" */\n    var isSpecialCasedOS;\n\n    /* Detectable layout engines (order is important). */\n    var layout = getLayout([\n      { 'label': 'EdgeHTML', 'pattern': 'Edge' },\n      'Trident',\n      { 'label': 'WebKit', 'pattern': 'AppleWebKit' },\n      'iCab',\n      'Presto',\n      'NetFront',\n      'Tasman',\n      'KHTML',\n      'Gecko'\n    ]);\n\n    /* Detectable browser names (order is important). */\n    var name = getName([\n      'Adobe AIR',\n      'Arora',\n      'Avant Browser',\n      'Breach',\n      'Camino',\n      'Electron',\n      'Epiphany',\n      'Fennec',\n      'Flock',\n      'Galeon',\n      'GreenBrowser',\n      'iCab',\n      'Iceweasel',\n      'K-Meleon',\n      'Konqueror',\n      'Lunascape',\n      'Maxthon',\n      { 'label': 'Microsoft Edge', 'pattern': '(?:Edge|Edg|EdgA|EdgiOS)' },\n      'Midori',\n      'Nook Browser',\n      'PaleMoon',\n      'PhantomJS',\n      'Raven',\n      'Rekonq',\n      'RockMelt',\n      { 'label': 'Samsung Internet', 'pattern': 'SamsungBrowser' },\n      'SeaMonkey',\n      { 'label': 'Silk', 'pattern': '(?:Cloud9|Silk-Accelerated)' },\n      'Sleipnir',\n      'SlimBrowser',\n      { 'label': 'SRWare Iron', 'pattern': 'Iron' },\n      'Sunrise',\n      'Swiftfox',\n      'Vivaldi',\n      'Waterfox',\n      'WebPositive',\n      { 'label': 'Yandex Browser', 'pattern': 'YaBrowser' },\n      { 'label': 'UC Browser', 'pattern': 'UCBrowser' },\n      'Opera Mini',\n      { 'label': 'Opera Mini', 'pattern': 'OPiOS' },\n      'Opera',\n      { 'label': 'Opera', 'pattern': 'OPR' },\n      'Chromium',\n      'Chrome',\n      { 'label': 'Chrome', 'pattern': '(?:HeadlessChrome)' },\n      { 'label': 'Chrome Mobile', 'pattern': '(?:CriOS|CrMo)' },\n      { 'label': 'Firefox', 'pattern': '(?:Firefox|Minefield)' },\n      { 'label': 'Firefox for iOS', 'pattern': 'FxiOS' },\n      { 'label': 'IE', 'pattern': 'IEMobile' },\n      { 'label': 'IE', 'pattern': 'MSIE' },\n      'Safari'\n    ]);\n\n    /* Detectable products (order is important). */\n    var product = getProduct([\n      { 'label': 'BlackBerry', 'pattern': 'BB10' },\n      'BlackBerry',\n      { 'label': 'Galaxy S', 'pattern': 'GT-I9000' },\n      { 'label': 'Galaxy S2', 'pattern': 'GT-I9100' },\n      { 'label': 'Galaxy S3', 'pattern': 'GT-I9300' },\n      { 'label': 'Galaxy S4', 'pattern': 'GT-I9500' },\n      { 'label': 'Galaxy S5', 'pattern': 'SM-G900' },\n      { 'label': 'Galaxy S6', 'pattern': 'SM-G920' },\n      { 'label': 'Galaxy S6 Edge', 'pattern': 'SM-G925' },\n      { 'label': 'Galaxy S7', 'pattern': 'SM-G930' },\n      { 'label': 'Galaxy S7 Edge', 'pattern': 'SM-G935' },\n      'Google TV',\n      'Lumia',\n      'iPad',\n      'iPod',\n      'iPhone',\n      'Kindle',\n      { 'label': 'Kindle Fire', 'pattern': '(?:Cloud9|Silk-Accelerated)' },\n      'Nexus',\n      'Nook',\n      'PlayBook',\n      'PlayStation Vita',\n      'PlayStation',\n      'TouchPad',\n      'Transformer',\n      { 'label': 'Wii U', 'pattern': 'WiiU' },\n      'Wii',\n      'Xbox One',\n      { 'label': 'Xbox 360', 'pattern': 'Xbox' },\n      'Xoom'\n    ]);\n\n    /* Detectable manufacturers. */\n    var manufacturer = getManufacturer({\n      'Apple': { 'iPad': 1, 'iPhone': 1, 'iPod': 1 },\n      'Alcatel': {},\n      'Archos': {},\n      'Amazon': { 'Kindle': 1, 'Kindle Fire': 1 },\n      'Asus': { 'Transformer': 1 },\n      'Barnes & Noble': { 'Nook': 1 },\n      'BlackBerry': { 'PlayBook': 1 },\n      'Google': { 'Google TV': 1, 'Nexus': 1 },\n      'HP': { 'TouchPad': 1 },\n      'HTC': {},\n      'Huawei': {},\n      'Lenovo': {},\n      'LG': {},\n      'Microsoft': { 'Xbox': 1, 'Xbox One': 1 },\n      'Motorola': { 'Xoom': 1 },\n      'Nintendo': { 'Wii U': 1,  'Wii': 1 },\n      'Nokia': { 'Lumia': 1 },\n      'Oppo': {},\n      'Samsung': { 'Galaxy S': 1, 'Galaxy S2': 1, 'Galaxy S3': 1, 'Galaxy S4': 1 },\n      'Sony': { 'PlayStation': 1, 'PlayStation Vita': 1 },\n      'Xiaomi': { 'Mi': 1, 'Redmi': 1 }\n    });\n\n    /* Detectable operating systems (order is important). */\n    var os = getOS([\n      'Windows Phone',\n      'KaiOS',\n      'Android',\n      'CentOS',\n      { 'label': 'Chrome OS', 'pattern': 'CrOS' },\n      'Debian',\n      { 'label': 'DragonFly BSD', 'pattern': 'DragonFly' },\n      'Fedora',\n      'FreeBSD',\n      'Gentoo',\n      'Haiku',\n      'Kubuntu',\n      'Linux Mint',\n      'OpenBSD',\n      'Red Hat',\n      'SuSE',\n      'Ubuntu',\n      'Xubuntu',\n      'Cygwin',\n      'Symbian OS',\n      'hpwOS',\n      'webOS ',\n      'webOS',\n      'Tablet OS',\n      'Tizen',\n      'Linux',\n      'Mac OS X',\n      'Macintosh',\n      'Mac',\n      'Windows 98;',\n      'Windows '\n    ]);\n\n    /*------------------------------------------------------------------------*/\n\n    /**\n     * Picks the layout engine from an array of guesses.\n     *\n     * @private\n     * @param {Array} guesses An array of guesses.\n     * @returns {null|string} The detected layout engine.\n     */\n    function getLayout(guesses) {\n      return reduce(guesses, function(result, guess) {\n        return result || RegExp('\\\\b' + (\n          guess.pattern || qualify(guess)\n        ) + '\\\\b', 'i').exec(ua) && (guess.label || guess);\n      });\n    }\n\n    /**\n     * Picks the manufacturer from an array of guesses.\n     *\n     * @private\n     * @param {Array} guesses An object of guesses.\n     * @returns {null|string} The detected manufacturer.\n     */\n    function getManufacturer(guesses) {\n      return reduce(guesses, function(result, value, key) {\n        // Lookup the manufacturer by product or scan the UA for the manufacturer.\n        return result || (\n          value[product] ||\n          value[/^[a-z]+(?: +[a-z]+\\b)*/i.exec(product)] ||\n          RegExp('\\\\b' + qualify(key) + '(?:\\\\b|\\\\w*\\\\d)', 'i').exec(ua)\n        ) && key;\n      });\n    }\n\n    /**\n     * Picks the browser name from an array of guesses.\n     *\n     * @private\n     * @param {Array} guesses An array of guesses.\n     * @returns {null|string} The detected browser name.\n     */\n    function getName(guesses) {\n      return reduce(guesses, function(result, guess) {\n        return result || RegExp('\\\\b' + (\n          guess.pattern || qualify(guess)\n        ) + '\\\\b', 'i').exec(ua) && (guess.label || guess);\n      });\n    }\n\n    /**\n     * Picks the OS name from an array of guesses.\n     *\n     * @private\n     * @param {Array} guesses An array of guesses.\n     * @returns {null|string} The detected OS name.\n     */\n    function getOS(guesses) {\n      return reduce(guesses, function(result, guess) {\n        var pattern = guess.pattern || qualify(guess);\n        if (!result && (result =\n              RegExp('\\\\b' + pattern + '(?:/[\\\\d.]+|[ \\\\w.]*)', 'i').exec(ua)\n            )) {\n          result = cleanupOS(result, pattern, guess.label || guess);\n        }\n        return result;\n      });\n    }\n\n    /**\n     * Picks the product name from an array of guesses.\n     *\n     * @private\n     * @param {Array} guesses An array of guesses.\n     * @returns {null|string} The detected product name.\n     */\n    function getProduct(guesses) {\n      return reduce(guesses, function(result, guess) {\n        var pattern = guess.pattern || qualify(guess);\n        if (!result && (result =\n              RegExp('\\\\b' + pattern + ' *\\\\d+[.\\\\w_]*', 'i').exec(ua) ||\n              RegExp('\\\\b' + pattern + ' *\\\\w+-[\\\\w]*', 'i').exec(ua) ||\n              RegExp('\\\\b' + pattern + '(?:; *(?:[a-z]+[_-])?[a-z]+\\\\d+|[^ ();-]*)', 'i').exec(ua)\n            )) {\n          // Split by forward slash and append product version if needed.\n          if ((result = String((guess.label && !RegExp(pattern, 'i').test(guess.label)) ? guess.label : result).split('/'))[1] && !/[\\d.]+/.test(result[0])) {\n            result[0] += ' ' + result[1];\n          }\n          // Correct character case and cleanup string.\n          guess = guess.label || guess;\n          result = format(result[0]\n            .replace(RegExp(pattern, 'i'), guess)\n            .replace(RegExp('; *(?:' + guess + '[_-])?', 'i'), ' ')\n            .replace(RegExp('(' + guess + ')[-_.]?(\\\\w)', 'i'), '$1 $2'));\n        }\n        return result;\n      });\n    }\n\n    /**\n     * Resolves the version using an array of UA patterns.\n     *\n     * @private\n     * @param {Array} patterns An array of UA patterns.\n     * @returns {null|string} The detected version.\n     */\n    function getVersion(patterns) {\n      return reduce(patterns, function(result, pattern) {\n        return result || (RegExp(pattern +\n          '(?:-[\\\\d.]+/|(?: for [\\\\w-]+)?[ /-])([\\\\d.]+[^ ();/_-]*)', 'i').exec(ua) || 0)[1] || null;\n      });\n    }\n\n    /**\n     * Returns `platform.description` when the platform object is coerced to a string.\n     *\n     * @name toString\n     * @memberOf platform\n     * @returns {string} Returns `platform.description` if available, else an empty string.\n     */\n    function toStringPlatform() {\n      return this.description || '';\n    }\n\n    /*------------------------------------------------------------------------*/\n\n    // Convert layout to an array so we can add extra details.\n    layout && (layout = [layout]);\n\n    // Detect Android products.\n    // Browsers on Android devices typically provide their product IDS after \"Android;\"\n    // up to \"Build\" or \") AppleWebKit\".\n    // Example:\n    // \"Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5) Plus) AppleWebKit/537.36\n    // (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36\"\n    if (/\\bAndroid\\b/.test(os) && !product &&\n        (data = /\\bAndroid[^;]*;(.*?)(?:Build|\\) AppleWebKit)\\b/i.exec(ua))) {\n      product = trim(data[1])\n        // Replace any language codes (eg. \"en-US\").\n        .replace(/^[a-z]{2}-[a-z]{2};\\s*/i, '')\n        || null;\n    }\n    // Detect product names that contain their manufacturer's name.\n    if (manufacturer && !product) {\n      product = getProduct([manufacturer]);\n    } else if (manufacturer && product) {\n      product = product\n        .replace(RegExp('^(' + qualify(manufacturer) + ')[-_.\\\\s]', 'i'), manufacturer + ' ')\n        .replace(RegExp('^(' + qualify(manufacturer) + ')[-_.]?(\\\\w)', 'i'), manufacturer + ' $2');\n    }\n    // Clean up Google TV.\n    if ((data = /\\bGoogle TV\\b/.exec(product))) {\n      product = data[0];\n    }\n    // Detect simulators.\n    if (/\\bSimulator\\b/i.test(ua)) {\n      product = (product ? product + ' ' : '') + 'Simulator';\n    }\n    // Detect Opera Mini 8+ running in Turbo/Uncompressed mode on iOS.\n    if (name == 'Opera Mini' && /\\bOPiOS\\b/.test(ua)) {\n      description.push('running in Turbo/Uncompressed mode');\n    }\n    // Detect IE Mobile 11.\n    if (name == 'IE' && /\\blike iPhone OS\\b/.test(ua)) {\n      data = parse(ua.replace(/like iPhone OS/, ''));\n      manufacturer = data.manufacturer;\n      product = data.product;\n    }\n    // Detect iOS.\n    else if (/^iP/.test(product)) {\n      name || (name = 'Safari');\n      os = 'iOS' + ((data = / OS ([\\d_]+)/i.exec(ua))\n        ? ' ' + data[1].replace(/_/g, '.')\n        : '');\n    }\n    // Detect Kubuntu.\n    else if (name == 'Konqueror' && /^Linux\\b/i.test(os)) {\n      os = 'Kubuntu';\n    }\n    // Detect Android browsers.\n    else if ((manufacturer && manufacturer != 'Google' &&\n        ((/Chrome/.test(name) && !/\\bMobile Safari\\b/i.test(ua)) || /\\bVita\\b/.test(product))) ||\n        (/\\bAndroid\\b/.test(os) && /^Chrome/.test(name) && /\\bVersion\\//i.test(ua))) {\n      name = 'Android Browser';\n      os = /\\bAndroid\\b/.test(os) ? os : 'Android';\n    }\n    // Detect Silk desktop/accelerated modes.\n    else if (name == 'Silk') {\n      if (!/\\bMobi/i.test(ua)) {\n        os = 'Android';\n        description.unshift('desktop mode');\n      }\n      if (/Accelerated *= *true/i.test(ua)) {\n        description.unshift('accelerated');\n      }\n    }\n    // Detect UC Browser speed mode.\n    else if (name == 'UC Browser' && /\\bUCWEB\\b/.test(ua)) {\n      description.push('speed mode');\n    }\n    // Detect PaleMoon identifying as Firefox.\n    else if (name == 'PaleMoon' && (data = /\\bFirefox\\/([\\d.]+)\\b/.exec(ua))) {\n      description.push('identifying as Firefox ' + data[1]);\n    }\n    // Detect Firefox OS and products running Firefox.\n    else if (name == 'Firefox' && (data = /\\b(Mobile|Tablet|TV)\\b/i.exec(ua))) {\n      os || (os = 'Firefox OS');\n      product || (product = data[1]);\n    }\n    // Detect false positives for Firefox/Safari.\n    else if (!name || (data = !/\\bMinefield\\b/i.test(ua) && /\\b(?:Firefox|Safari)\\b/.exec(name))) {\n      // Escape the `/` for Firefox 1.\n      if (name && !product && /[\\/,]|^[^(]+?\\)/.test(ua.slice(ua.indexOf(data + '/') + 8))) {\n        // Clear name of false positives.\n        name = null;\n      }\n      // Reassign a generic name.\n      if ((data = product || manufacturer || os) &&\n          (product || manufacturer || /\\b(?:Android|Symbian OS|Tablet OS|webOS)\\b/.test(os))) {\n        name = /[a-z]+(?: Hat)?/i.exec(/\\bAndroid\\b/.test(os) ? os : data) + ' Browser';\n      }\n    }\n    // Add Chrome version to description for Electron.\n    else if (name == 'Electron' && (data = (/\\bChrome\\/([\\d.]+)\\b/.exec(ua) || 0)[1])) {\n      description.push('Chromium ' + data);\n    }\n    // Detect non-Opera (Presto-based) versions (order is important).\n    if (!version) {\n      version = getVersion([\n        '(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\\\d.]+$)|UCBrowser|YaBrowser)',\n        'Version',\n        qualify(name),\n        '(?:Firefox|Minefield|NetFront)'\n      ]);\n    }\n    // Detect stubborn layout engines.\n    if ((data =\n          layout == 'iCab' && parseFloat(version) > 3 && 'WebKit' ||\n          /\\bOpera\\b/.test(name) && (/\\bOPR\\b/.test(ua) ? 'Blink' : 'Presto') ||\n          /\\b(?:Midori|Nook|Safari)\\b/i.test(ua) && !/^(?:Trident|EdgeHTML)$/.test(layout) && 'WebKit' ||\n          !layout && /\\bMSIE\\b/i.test(ua) && (os == 'Mac OS' ? 'Tasman' : 'Trident') ||\n          layout == 'WebKit' && /\\bPlayStation\\b(?! Vita\\b)/i.test(name) && 'NetFront'\n        )) {\n      layout = [data];\n    }\n    // Detect Windows Phone 7 desktop mode.\n    if (name == 'IE' && (data = (/; *(?:XBLWP|ZuneWP)(\\d+)/i.exec(ua) || 0)[1])) {\n      name += ' Mobile';\n      os = 'Windows Phone ' + (/\\+$/.test(data) ? data : data + '.x');\n      description.unshift('desktop mode');\n    }\n    // Detect Windows Phone 8.x desktop mode.\n    else if (/\\bWPDesktop\\b/i.test(ua)) {\n      name = 'IE Mobile';\n      os = 'Windows Phone 8.x';\n      description.unshift('desktop mode');\n      version || (version = (/\\brv:([\\d.]+)/.exec(ua) || 0)[1]);\n    }\n    // Detect IE 11 identifying as other browsers.\n    else if (name != 'IE' && layout == 'Trident' && (data = /\\brv:([\\d.]+)/.exec(ua))) {\n      if (name) {\n        description.push('identifying as ' + name + (version ? ' ' + version : ''));\n      }\n      name = 'IE';\n      version = data[1];\n    }\n    // Leverage environment features.\n    if (useFeatures) {\n      // Detect server-side environments.\n      // Rhino has a global function while others have a global object.\n      if (isHostType(context, 'global')) {\n        if (java) {\n          data = java.lang.System;\n          arch = data.getProperty('os.arch');\n          os = os || data.getProperty('os.name') + ' ' + data.getProperty('os.version');\n        }\n        if (rhino) {\n          try {\n            version = context.require('ringo/engine').version.join('.');\n            name = 'RingoJS';\n          } catch(e) {\n            if ((data = context.system) && data.global.system == context.system) {\n              name = 'Narwhal';\n              os || (os = data[0].os || null);\n            }\n          }\n          if (!name) {\n            name = 'Rhino';\n          }\n        }\n        else if (\n          typeof context.process == 'object' && !context.process.browser &&\n          (data = context.process)\n        ) {\n          if (typeof data.versions == 'object') {\n            if (typeof data.versions.electron == 'string') {\n              description.push('Node ' + data.versions.node);\n              name = 'Electron';\n              version = data.versions.electron;\n            } else if (typeof data.versions.nw == 'string') {\n              description.push('Chromium ' + version, 'Node ' + data.versions.node);\n              name = 'NW.js';\n              version = data.versions.nw;\n            }\n          }\n          if (!name) {\n            name = 'Node.js';\n            arch = data.arch;\n            os = data.platform;\n            version = /[\\d.]+/.exec(data.version);\n            version = version ? version[0] : null;\n          }\n        }\n      }\n      // Detect Adobe AIR.\n      else if (getClassOf((data = context.runtime)) == airRuntimeClass) {\n        name = 'Adobe AIR';\n        os = data.flash.system.Capabilities.os;\n      }\n      // Detect PhantomJS.\n      else if (getClassOf((data = context.phantom)) == phantomClass) {\n        name = 'PhantomJS';\n        version = (data = data.version || null) && (data.major + '.' + data.minor + '.' + data.patch);\n      }\n      // Detect IE compatibility modes.\n      else if (typeof doc.documentMode == 'number' && (data = /\\bTrident\\/(\\d+)/i.exec(ua))) {\n        // We're in compatibility mode when the Trident version + 4 doesn't\n        // equal the document mode.\n        version = [version, doc.documentMode];\n        if ((data = +data[1] + 4) != version[1]) {\n          description.push('IE ' + version[1] + ' mode');\n          layout && (layout[1] = '');\n          version[1] = data;\n        }\n        version = name == 'IE' ? String(version[1].toFixed(1)) : version[0];\n      }\n      // Detect IE 11 masking as other browsers.\n      else if (typeof doc.documentMode == 'number' && /^(?:Chrome|Firefox)\\b/.test(name)) {\n        description.push('masking as ' + name + ' ' + version);\n        name = 'IE';\n        version = '11.0';\n        layout = ['Trident'];\n        os = 'Windows';\n      }\n      os = os && format(os);\n    }\n    // Detect prerelease phases.\n    if (version && (data =\n          /(?:[ab]|dp|pre|[ab]\\d+pre)(?:\\d+\\+?)?$/i.exec(version) ||\n          /(?:alpha|beta)(?: ?\\d)?/i.exec(ua + ';' + (useFeatures && nav.appMinorVersion)) ||\n          /\\bMinefield\\b/i.test(ua) && 'a'\n        )) {\n      prerelease = /b/i.test(data) ? 'beta' : 'alpha';\n      version = version.replace(RegExp(data + '\\\\+?$'), '') +\n        (prerelease == 'beta' ? beta : alpha) + (/\\d+\\+?/.exec(data) || '');\n    }\n    // Detect Firefox Mobile.\n    if (name == 'Fennec' || name == 'Firefox' && /\\b(?:Android|Firefox OS|KaiOS)\\b/.test(os)) {\n      name = 'Firefox Mobile';\n    }\n    // Obscure Maxthon's unreliable version.\n    else if (name == 'Maxthon' && version) {\n      version = version.replace(/\\.[\\d.]+/, '.x');\n    }\n    // Detect Xbox 360 and Xbox One.\n    else if (/\\bXbox\\b/i.test(product)) {\n      if (product == 'Xbox 360') {\n        os = null;\n      }\n      if (product == 'Xbox 360' && /\\bIEMobile\\b/.test(ua)) {\n        description.unshift('mobile mode');\n      }\n    }\n    // Add mobile postfix.\n    else if ((/^(?:Chrome|IE|Opera)$/.test(name) || name && !product && !/Browser|Mobi/.test(name)) &&\n        (os == 'Windows CE' || /Mobi/i.test(ua))) {\n      name += ' Mobile';\n    }\n    // Detect IE platform preview.\n    else if (name == 'IE' && useFeatures) {\n      try {\n        if (context.external === null) {\n          description.unshift('platform preview');\n        }\n      } catch(e) {\n        description.unshift('embedded');\n      }\n    }\n    // Detect BlackBerry OS version.\n    // http://docs.blackberry.com/en/developers/deliverables/18169/HTTP_headers_sent_by_BB_Browser_1234911_11.jsp\n    else if ((/\\bBlackBerry\\b/.test(product) || /\\bBB10\\b/.test(ua)) && (data =\n          (RegExp(product.replace(/ +/g, ' *') + '/([.\\\\d]+)', 'i').exec(ua) || 0)[1] ||\n          version\n        )) {\n      data = [data, /BB10/.test(ua)];\n      os = (data[1] ? (product = null, manufacturer = 'BlackBerry') : 'Device Software') + ' ' + data[0];\n      version = null;\n    }\n    // Detect Opera identifying/masking itself as another browser.\n    // http://www.opera.com/support/kb/view/843/\n    else if (this != forOwn && product != 'Wii' && (\n          (useFeatures && opera) ||\n          (/Opera/.test(name) && /\\b(?:MSIE|Firefox)\\b/i.test(ua)) ||\n          (name == 'Firefox' && /\\bOS X (?:\\d+\\.){2,}/.test(os)) ||\n          (name == 'IE' && (\n            (os && !/^Win/.test(os) && version > 5.5) ||\n            /\\bWindows XP\\b/.test(os) && version > 8 ||\n            version == 8 && !/\\bTrident\\b/.test(ua)\n          ))\n        ) && !reOpera.test((data = parse.call(forOwn, ua.replace(reOpera, '') + ';'))) && data.name) {\n      // When \"identifying\", the UA contains both Opera and the other browser's name.\n      data = 'ing as ' + data.name + ((data = data.version) ? ' ' + data : '');\n      if (reOpera.test(name)) {\n        if (/\\bIE\\b/.test(data) && os == 'Mac OS') {\n          os = null;\n        }\n        data = 'identify' + data;\n      }\n      // When \"masking\", the UA contains only the other browser's name.\n      else {\n        data = 'mask' + data;\n        if (operaClass) {\n          name = format(operaClass.replace(/([a-z])([A-Z])/g, '$1 $2'));\n        } else {\n          name = 'Opera';\n        }\n        if (/\\bIE\\b/.test(data)) {\n          os = null;\n        }\n        if (!useFeatures) {\n          version = null;\n        }\n      }\n      layout = ['Presto'];\n      description.push(data);\n    }\n    // Detect WebKit Nightly and approximate Chrome/Safari versions.\n    if ((data = (/\\bAppleWebKit\\/([\\d.]+\\+?)/i.exec(ua) || 0)[1])) {\n      // Correct build number for numeric comparison.\n      // (e.g. \"532.5\" becomes \"532.05\")\n      data = [parseFloat(data.replace(/\\.(\\d)$/, '.0$1')), data];\n      // Nightly builds are postfixed with a \"+\".\n      if (name == 'Safari' && data[1].slice(-1) == '+') {\n        name = 'WebKit Nightly';\n        prerelease = 'alpha';\n        version = data[1].slice(0, -1);\n      }\n      // Clear incorrect browser versions.\n      else if (version == data[1] ||\n          version == (data[2] = (/\\bSafari\\/([\\d.]+\\+?)/i.exec(ua) || 0)[1])) {\n        version = null;\n      }\n      // Use the full Chrome version when available.\n      data[1] = (/\\b(?:Headless)?Chrome\\/([\\d.]+)/i.exec(ua) || 0)[1];\n      // Detect Blink layout engine.\n      if (data[0] == 537.36 && data[2] == 537.36 && parseFloat(data[1]) >= 28 && layout == 'WebKit') {\n        layout = ['Blink'];\n      }\n      // Detect JavaScriptCore.\n      // http://stackoverflow.com/questions/6768474/how-can-i-detect-which-javascript-engine-v8-or-jsc-is-used-at-runtime-in-androi\n      if (!useFeatures || (!likeChrome && !data[1])) {\n        layout && (layout[1] = 'like Safari');\n        data = (data = data[0], data < 400 ? 1 : data < 500 ? 2 : data < 526 ? 3 : data < 533 ? 4 : data < 534 ? '4+' : data < 535 ? 5 : data < 537 ? 6 : data < 538 ? 7 : data < 601 ? 8 : data < 602 ? 9 : data < 604 ? 10 : data < 606 ? 11 : data < 608 ? 12 : '12');\n      } else {\n        layout && (layout[1] = 'like Chrome');\n        data = data[1] || (data = data[0], data < 530 ? 1 : data < 532 ? 2 : data < 532.05 ? 3 : data < 533 ? 4 : data < 534.03 ? 5 : data < 534.07 ? 6 : data < 534.10 ? 7 : data < 534.13 ? 8 : data < 534.16 ? 9 : data < 534.24 ? 10 : data < 534.30 ? 11 : data < 535.01 ? 12 : data < 535.02 ? '13+' : data < 535.07 ? 15 : data < 535.11 ? 16 : data < 535.19 ? 17 : data < 536.05 ? 18 : data < 536.10 ? 19 : data < 537.01 ? 20 : data < 537.11 ? '21+' : data < 537.13 ? 23 : data < 537.18 ? 24 : data < 537.24 ? 25 : data < 537.36 ? 26 : layout != 'Blink' ? '27' : '28');\n      }\n      // Add the postfix of \".x\" or \"+\" for approximate versions.\n      layout && (layout[1] += ' ' + (data += typeof data == 'number' ? '.x' : /[.+]/.test(data) ? '' : '+'));\n      // Obscure version for some Safari 1-2 releases.\n      if (name == 'Safari' && (!version || parseInt(version) > 45)) {\n        version = data;\n      } else if (name == 'Chrome' && /\\bHeadlessChrome/i.test(ua)) {\n        description.unshift('headless');\n      }\n    }\n    // Detect Opera desktop modes.\n    if (name == 'Opera' &&  (data = /\\bzbov|zvav$/.exec(os))) {\n      name += ' ';\n      description.unshift('desktop mode');\n      if (data == 'zvav') {\n        name += 'Mini';\n        version = null;\n      } else {\n        name += 'Mobile';\n      }\n      os = os.replace(RegExp(' *' + data + '$'), '');\n    }\n    // Detect Chrome desktop mode.\n    else if (name == 'Safari' && /\\bChrome\\b/.exec(layout && layout[1])) {\n      description.unshift('desktop mode');\n      name = 'Chrome Mobile';\n      version = null;\n\n      if (/\\bOS X\\b/.test(os)) {\n        manufacturer = 'Apple';\n        os = 'iOS 4.3+';\n      } else {\n        os = null;\n      }\n    }\n    // Newer versions of SRWare Iron uses the Chrome tag to indicate its version number.\n    else if (/\\bSRWare Iron\\b/.test(name) && !version) {\n      version = getVersion('Chrome');\n    }\n    // Strip incorrect OS versions.\n    if (version && version.indexOf((data = /[\\d.]+$/.exec(os))) == 0 &&\n        ua.indexOf('/' + data + '-') > -1) {\n      os = trim(os.replace(data, ''));\n    }\n    // Ensure OS does not include the browser name.\n    if (os && os.indexOf(name) != -1 && !RegExp(name + ' OS').test(os)) {\n      os = os.replace(RegExp(' *' + qualify(name) + ' *'), '');\n    }\n    // Add layout engine.\n    if (layout && !/\\b(?:Avant|Nook)\\b/.test(name) && (\n        /Browser|Lunascape|Maxthon/.test(name) ||\n        name != 'Safari' && /^iOS/.test(os) && /\\bSafari\\b/.test(layout[1]) ||\n        /^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(name) && layout[1])) {\n      // Don't add layout details to description if they are falsey.\n      (data = layout[layout.length - 1]) && description.push(data);\n    }\n    // Combine contextual information.\n    if (description.length) {\n      description = ['(' + description.join('; ') + ')'];\n    }\n    // Append manufacturer to description.\n    if (manufacturer && product && product.indexOf(manufacturer) < 0) {\n      description.push('on ' + manufacturer);\n    }\n    // Append product to description.\n    if (product) {\n      description.push((/^on /.test(description[description.length - 1]) ? '' : 'on ') + product);\n    }\n    // Parse the OS into an object.\n    if (os) {\n      data = / ([\\d.+]+)$/.exec(os);\n      isSpecialCasedOS = data && os.charAt(os.length - data[0].length - 1) == '/';\n      os = {\n        'architecture': 32,\n        'family': (data && !isSpecialCasedOS) ? os.replace(data[0], '') : os,\n        'version': data ? data[1] : null,\n        'toString': function() {\n          var version = this.version;\n          return this.family + ((version && !isSpecialCasedOS) ? ' ' + version : '') + (this.architecture == 64 ? ' 64-bit' : '');\n        }\n      };\n    }\n    // Add browser/OS architecture.\n    if ((data = /\\b(?:AMD|IA|Win|WOW|x86_|x)64\\b/i.exec(arch)) && !/\\bi686\\b/i.test(arch)) {\n      if (os) {\n        os.architecture = 64;\n        os.family = os.family.replace(RegExp(' *' + data), '');\n      }\n      if (\n          name && (/\\bWOW64\\b/i.test(ua) ||\n          (useFeatures && /\\w(?:86|32)$/.test(nav.cpuClass || nav.platform) && !/\\bWin64; x64\\b/i.test(ua)))\n      ) {\n        description.unshift('32-bit');\n      }\n    }\n    // Chrome 39 and above on OS X is always 64-bit.\n    else if (\n        os && /^OS X/.test(os.family) &&\n        name == 'Chrome' && parseFloat(version) >= 39\n    ) {\n      os.architecture = 64;\n    }\n\n    ua || (ua = null);\n\n    /*------------------------------------------------------------------------*/\n\n    /**\n     * The platform object.\n     *\n     * @name platform\n     * @type Object\n     */\n    var platform = {};\n\n    /**\n     * The platform description.\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.description = ua;\n\n    /**\n     * The name of the browser's layout engine.\n     *\n     * The list of common layout engines include:\n     * \"Blink\", \"EdgeHTML\", \"Gecko\", \"Trident\" and \"WebKit\"\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.layout = layout && layout[0];\n\n    /**\n     * The name of the product's manufacturer.\n     *\n     * The list of manufacturers include:\n     * \"Apple\", \"Archos\", \"Amazon\", \"Asus\", \"Barnes & Noble\", \"BlackBerry\",\n     * \"Google\", \"HP\", \"HTC\", \"LG\", \"Microsoft\", \"Motorola\", \"Nintendo\",\n     * \"Nokia\", \"Samsung\" and \"Sony\"\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.manufacturer = manufacturer;\n\n    /**\n     * The name of the browser/environment.\n     *\n     * The list of common browser names include:\n     * \"Chrome\", \"Electron\", \"Firefox\", \"Firefox for iOS\", \"IE\",\n     * \"Microsoft Edge\", \"PhantomJS\", \"Safari\", \"SeaMonkey\", \"Silk\",\n     * \"Opera Mini\" and \"Opera\"\n     *\n     * Mobile versions of some browsers have \"Mobile\" appended to their name:\n     * eg. \"Chrome Mobile\", \"Firefox Mobile\", \"IE Mobile\" and \"Opera Mobile\"\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.name = name;\n\n    /**\n     * The alpha/beta release indicator.\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.prerelease = prerelease;\n\n    /**\n     * The name of the product hosting the browser.\n     *\n     * The list of common products include:\n     *\n     * \"BlackBerry\", \"Galaxy S4\", \"Lumia\", \"iPad\", \"iPod\", \"iPhone\", \"Kindle\",\n     * \"Kindle Fire\", \"Nexus\", \"Nook\", \"PlayBook\", \"TouchPad\" and \"Transformer\"\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.product = product;\n\n    /**\n     * The browser's user agent string.\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.ua = ua;\n\n    /**\n     * The browser/environment version.\n     *\n     * @memberOf platform\n     * @type string|null\n     */\n    platform.version = name && version;\n\n    /**\n     * The name of the operating system.\n     *\n     * @memberOf platform\n     * @type Object\n     */\n    platform.os = os || {\n\n      /**\n       * The CPU architecture the OS is built for.\n       *\n       * @memberOf platform.os\n       * @type number|null\n       */\n      'architecture': null,\n\n      /**\n       * The family of the OS.\n       *\n       * Common values include:\n       * \"Windows\", \"Windows Server 2008 R2 / 7\", \"Windows Server 2008 / Vista\",\n       * \"Windows XP\", \"OS X\", \"Linux\", \"Ubuntu\", \"Debian\", \"Fedora\", \"Red Hat\",\n       * \"SuSE\", \"Android\", \"iOS\" and \"Windows Phone\"\n       *\n       * @memberOf platform.os\n       * @type string|null\n       */\n      'family': null,\n\n      /**\n       * The version of the OS.\n       *\n       * @memberOf platform.os\n       * @type string|null\n       */\n      'version': null,\n\n      /**\n       * Returns the OS string.\n       *\n       * @memberOf platform.os\n       * @returns {string} The OS string.\n       */\n      'toString': function() { return 'null'; }\n    };\n\n    platform.parse = parse;\n    platform.toString = toStringPlatform;\n\n    if (platform.version) {\n      description.unshift(version);\n    }\n    if (platform.name) {\n      description.unshift(name);\n    }\n    if (os && name && !(os == String(os).split(' ')[0] && (os == name.split(' ')[0] || product))) {\n      description.push(product ? '(' + os + ')' : 'on ' + os);\n    }\n    if (description.length) {\n      platform.description = description.join(' ');\n    }\n    return platform;\n  }\n\n  /*--------------------------------------------------------------------------*/\n\n  // Export platform.\n  var platform = parse();\n\n  // Some AMD build optimizers, like r.js, check for condition patterns like the following:\n  if (typeof define == 'function' && typeof define.amd == 'object' && define.amd) {\n    // Expose platform on the global object to prevent errors when platform is\n    // loaded by a script tag in the presence of an AMD loader.\n    // See http://requirejs.org/docs/errors.html#mismatch for more details.\n    root.platform = platform;\n\n    // Define as an anonymous module so platform can be aliased through path mapping.\n    define(function() {\n      return platform;\n    });\n  }\n  // Check for `exports` after `define` in case a build optimizer adds an `exports` object.\n  else if (freeExports && freeModule) {\n    // Export for CommonJS support.\n    forOwn(platform, function(value, key) {\n      freeExports[key] = value;\n    });\n  }\n  else {\n    // Export to the global object.\n    root.platform = platform;\n  }\n}.call(this));\n"], "mappings": ";;;;;AAAA;AAAA;AAMC,KAAC,WAAW;AACX;AAGA,UAAI,cAAc;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAGA,UAAI,OAAQ,YAAY,OAAO,MAAM,KAAK,UAAW;AAGrD,UAAI,UAAU;AAGd,UAAI,cAAc,YAAY,OAAO,OAAO,KAAK;AAGjD,UAAI,aAAa,YAAY,OAAO,MAAM,KAAK,UAAU,CAAC,OAAO,YAAY;AAG7E,UAAI,aAAa,eAAe,cAAc,OAAO,UAAU,YAAY;AAC3E,UAAI,eAAe,WAAW,WAAW,cAAc,WAAW,WAAW,cAAc,WAAW,SAAS,aAAa;AAC1H,eAAO;AAAA,MACT;AAOA,UAAI,iBAAiB,KAAK,IAAI,GAAG,EAAE,IAAI;AAGvC,UAAI,UAAU;AAGd,UAAI,cAAc;AAGlB,UAAI,cAAc,OAAO;AAGzB,UAAI,iBAAiB,YAAY;AAGjC,UAAI,WAAW,YAAY;AAW3B,eAAS,WAAW,QAAQ;AAC1B,iBAAS,OAAO,MAAM;AACtB,eAAO,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AAAA,MACxD;AAUA,eAAS,UAAU,IAAI,SAAS,OAAO;AAIrC,YAAI,OAAO;AAAA,UACT,QAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAQ;AAAA,UACR,OAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAEA,YAAI,WAAW,SAAS,QAAQ,KAAK,EAAE,KAAK,CAAC,mBAAmB,KAAK,EAAE,MAClE,OAAO,KAAK,UAAU,KAAK,EAAE,CAAC,IAAI;AACrC,eAAK,aAAa;AAAA,QACpB;AAEA,aAAK,OAAO,EAAE;AAEd,YAAI,WAAW,OAAO;AACpB,eAAK,GAAG,QAAQ,OAAO,SAAS,GAAG,GAAG,KAAK;AAAA,QAC7C;AAEA,aAAK;AAAA,UACH,GAAG,QAAQ,SAAS,KAAK,EACtB,QAAQ,UAAU,KAAK,EACvB,QAAQ,iBAAiB,QAAQ,EACjC,QAAQ,eAAe,KAAK,EAC5B,QAAQ,qBAAqB,IAAI,EACjC,QAAQ,kBAAkB,IAAI,EAC9B,QAAQ,UAAU,KAAK,EACvB,QAAQ,MAAM,GAAG,EACjB,QAAQ,8BAA8B,EAAE,EACxC,QAAQ,iBAAiB,QAAQ,EACjC,QAAQ,0BAA0B,IAAI,EACtC,QAAQ,8BAA8B,IAAI,EAC1C,MAAM,MAAM,EAAE,CAAC;AAAA,QACpB;AAEA,eAAO;AAAA,MACT;AASA,eAAS,KAAK,QAAQ,UAAU;AAC9B,YAAI,QAAQ,IACR,SAAS,SAAS,OAAO,SAAS;AAEtC,YAAI,OAAO,UAAU,YAAY,SAAS,MAAM,UAAU,gBAAgB;AACxE,iBAAO,EAAE,QAAQ,QAAQ;AACvB,qBAAS,OAAO,KAAK,GAAG,OAAO,MAAM;AAAA,UACvC;AAAA,QACF,OAAO;AACL,iBAAO,QAAQ,QAAQ;AAAA,QACzB;AAAA,MACF;AASA,eAAS,OAAO,QAAQ;AACtB,iBAAS,KAAK,MAAM;AACpB,eAAO,uBAAuB,KAAK,MAAM,IACrC,SACA,WAAW,MAAM;AAAA,MACvB;AASA,eAAS,OAAO,QAAQ,UAAU;AAChC,iBAAS,OAAO,QAAQ;AACtB,cAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AACpC,qBAAS,OAAO,GAAG,GAAG,KAAK,MAAM;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AASA,eAAS,WAAW,OAAO;AACzB,eAAO,SAAS,OACZ,WAAW,KAAK,IAChB,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAAA,MACtC;AAYA,eAAS,WAAW,QAAQ,UAAU;AACpC,YAAI,OAAO,UAAU,OAAO,OAAO,OAAO,QAAQ,IAAI;AACtD,eAAO,CAAC,wCAAwC,KAAK,IAAI,MACtD,QAAQ,WAAW,CAAC,CAAC,OAAO,QAAQ,IAAI;AAAA,MAC7C;AASA,eAAS,QAAQ,QAAQ;AACvB,eAAO,OAAO,MAAM,EAAE,QAAQ,gBAAgB,KAAK;AAAA,MACrD;AAUA,eAAS,OAAO,OAAO,UAAU;AAC/B,YAAI,cAAc;AAClB,aAAK,OAAO,SAAS,OAAO,OAAO;AACjC,wBAAc,SAAS,aAAa,OAAO,OAAO,KAAK;AAAA,QACzD,CAAC;AACD,eAAO;AAAA,MACT;AASA,eAAS,KAAK,QAAQ;AACpB,eAAO,OAAO,MAAM,EAAE,QAAQ,YAAY,EAAE;AAAA,MAC9C;AAYA,eAAS,MAAM,IAAI;AAGjB,YAAI,UAAU;AAGd,YAAI,kBAAkB,MAAM,OAAO,MAAM,YAAY,WAAW,EAAE,KAAK;AAGvE,YAAI,iBAAiB;AACnB,oBAAU;AACV,eAAK;AAAA,QACP;AAGA,YAAI,MAAM,QAAQ,aAAa,CAAC;AAGhC,YAAI,YAAY,IAAI,aAAa;AAEjC,eAAO,KAAK;AAGZ,YAAI,gBAAgB,mBAAmB,eAAe;AAGtD,YAAI,aAAa,kBACb,CAAC,CAAC,IAAI,aACN,aAAa,KAAK,EAAE,KAAK,CAAC,eAAe,KAAK,SAAS,SAAS,CAAC;AAGrE,YAAI,cAAc,UACd,kBAAkB,kBAAkB,cAAc,6BAClD,cAAc,kBAAkB,cAAc,eAC9C,YAAa,mBAAmB,QAAQ,OAAQ,gBAAgB,WAAW,QAAQ,IAAI,GACvF,eAAe,kBAAkB,cAAc;AAGnD,YAAI,OAAO,SAAS,KAAK,SAAS,KAAK,QAAQ;AAG/C,YAAI,QAAQ,QAAQ,WAAW,QAAQ,WAAW,KAAK;AAGvD,YAAI,QAAQ,OAAO,MAAM;AAGzB,YAAI,OAAO,OAAO,MAAM;AAGxB,YAAI,MAAM,QAAQ,YAAY,CAAC;AAO/B,YAAI,QAAQ,QAAQ,aAAa,QAAQ;AAGzC,YAAI,aAAa,QAAQ,KAAK,aAAc,mBAAmB,QAAS,MAAM,WAAW,IAAI,WAAW,KAAK,CAAC,IAC1G,aACC,QAAQ;AAKb,YAAI;AAGJ,YAAI,OAAO;AAGX,YAAI,cAAc,CAAC;AAGnB,YAAI,aAAa;AAGjB,YAAI,cAAc,MAAM;AAGxB,YAAI,UAAU,eAAe,SAAS,OAAO,MAAM,WAAW,cAAc,MAAM,QAAQ;AAG1F,YAAI;AAGJ,YAAI,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,YAAY,WAAW,OAAO;AAAA,UACzC;AAAA,UACA,EAAE,SAAS,UAAU,WAAW,cAAc;AAAA,UAC9C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAGD,YAAI,OAAO,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,kBAAkB,WAAW,2BAA2B;AAAA,UACnE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,oBAAoB,WAAW,iBAAiB;AAAA,UAC3D;AAAA,UACA,EAAE,SAAS,QAAQ,WAAW,8BAA8B;AAAA,UAC5D;AAAA,UACA;AAAA,UACA,EAAE,SAAS,eAAe,WAAW,OAAO;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,kBAAkB,WAAW,YAAY;AAAA,UACpD,EAAE,SAAS,cAAc,WAAW,YAAY;AAAA,UAChD;AAAA,UACA,EAAE,SAAS,cAAc,WAAW,QAAQ;AAAA,UAC5C;AAAA,UACA,EAAE,SAAS,SAAS,WAAW,MAAM;AAAA,UACrC;AAAA,UACA;AAAA,UACA,EAAE,SAAS,UAAU,WAAW,qBAAqB;AAAA,UACrD,EAAE,SAAS,iBAAiB,WAAW,iBAAiB;AAAA,UACxD,EAAE,SAAS,WAAW,WAAW,wBAAwB;AAAA,UACzD,EAAE,SAAS,mBAAmB,WAAW,QAAQ;AAAA,UACjD,EAAE,SAAS,MAAM,WAAW,WAAW;AAAA,UACvC,EAAE,SAAS,MAAM,WAAW,OAAO;AAAA,UACnC;AAAA,QACF,CAAC;AAGD,YAAI,UAAU,WAAW;AAAA,UACvB,EAAE,SAAS,cAAc,WAAW,OAAO;AAAA,UAC3C;AAAA,UACA,EAAE,SAAS,YAAY,WAAW,WAAW;AAAA,UAC7C,EAAE,SAAS,aAAa,WAAW,WAAW;AAAA,UAC9C,EAAE,SAAS,aAAa,WAAW,WAAW;AAAA,UAC9C,EAAE,SAAS,aAAa,WAAW,WAAW;AAAA,UAC9C,EAAE,SAAS,aAAa,WAAW,UAAU;AAAA,UAC7C,EAAE,SAAS,aAAa,WAAW,UAAU;AAAA,UAC7C,EAAE,SAAS,kBAAkB,WAAW,UAAU;AAAA,UAClD,EAAE,SAAS,aAAa,WAAW,UAAU;AAAA,UAC7C,EAAE,SAAS,kBAAkB,WAAW,UAAU;AAAA,UAClD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,eAAe,WAAW,8BAA8B;AAAA,UACnE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,SAAS,WAAW,OAAO;AAAA,UACtC;AAAA,UACA;AAAA,UACA,EAAE,SAAS,YAAY,WAAW,OAAO;AAAA,UACzC;AAAA,QACF,CAAC;AAGD,YAAI,eAAe,gBAAgB;AAAA,UACjC,SAAS,EAAE,QAAQ,GAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,UAC7C,WAAW,CAAC;AAAA,UACZ,UAAU,CAAC;AAAA,UACX,UAAU,EAAE,UAAU,GAAG,eAAe,EAAE;AAAA,UAC1C,QAAQ,EAAE,eAAe,EAAE;AAAA,UAC3B,kBAAkB,EAAE,QAAQ,EAAE;AAAA,UAC9B,cAAc,EAAE,YAAY,EAAE;AAAA,UAC9B,UAAU,EAAE,aAAa,GAAG,SAAS,EAAE;AAAA,UACvC,MAAM,EAAE,YAAY,EAAE;AAAA,UACtB,OAAO,CAAC;AAAA,UACR,UAAU,CAAC;AAAA,UACX,UAAU,CAAC;AAAA,UACX,MAAM,CAAC;AAAA,UACP,aAAa,EAAE,QAAQ,GAAG,YAAY,EAAE;AAAA,UACxC,YAAY,EAAE,QAAQ,EAAE;AAAA,UACxB,YAAY,EAAE,SAAS,GAAI,OAAO,EAAE;AAAA,UACpC,SAAS,EAAE,SAAS,EAAE;AAAA,UACtB,QAAQ,CAAC;AAAA,UACT,WAAW,EAAE,YAAY,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,EAAE;AAAA,UAC3E,QAAQ,EAAE,eAAe,GAAG,oBAAoB,EAAE;AAAA,UAClD,UAAU,EAAE,MAAM,GAAG,SAAS,EAAE;AAAA,QAClC,CAAC;AAGD,YAAI,KAAK,MAAM;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,SAAS,aAAa,WAAW,OAAO;AAAA,UAC1C;AAAA,UACA,EAAE,SAAS,iBAAiB,WAAW,YAAY;AAAA,UACnD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAWD,iBAAS,UAAU,SAAS;AAC1B,iBAAO,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC7C,mBAAO,UAAU,OAAO,SACtB,MAAM,WAAW,QAAQ,KAAK,KAC5B,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS;AAAA,UAC9C,CAAC;AAAA,QACH;AASA,iBAAS,gBAAgB,SAAS;AAChC,iBAAO,OAAO,SAAS,SAAS,QAAQ,OAAO,KAAK;AAElD,mBAAO,WACL,MAAM,OAAO,KACb,MAAM,0BAA0B,KAAK,OAAO,CAAC,KAC7C,OAAO,QAAQ,QAAQ,GAAG,IAAI,mBAAmB,GAAG,EAAE,KAAK,EAAE,MAC1D;AAAA,UACP,CAAC;AAAA,QACH;AASA,iBAAS,QAAQ,SAAS;AACxB,iBAAO,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC7C,mBAAO,UAAU,OAAO,SACtB,MAAM,WAAW,QAAQ,KAAK,KAC5B,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS;AAAA,UAC9C,CAAC;AAAA,QACH;AASA,iBAAS,MAAM,SAAS;AACtB,iBAAO,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC7C,gBAAI,UAAU,MAAM,WAAW,QAAQ,KAAK;AAC5C,gBAAI,CAAC,WAAW,SACV,OAAO,QAAQ,UAAU,yBAAyB,GAAG,EAAE,KAAK,EAAE,IAC7D;AACL,uBAAS,UAAU,QAAQ,SAAS,MAAM,SAAS,KAAK;AAAA,YAC1D;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AASA,iBAAS,WAAW,SAAS;AAC3B,iBAAO,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC7C,gBAAI,UAAU,MAAM,WAAW,QAAQ,KAAK;AAC5C,gBAAI,CAAC,WAAW,SACV,OAAO,QAAQ,UAAU,kBAAkB,GAAG,EAAE,KAAK,EAAE,KACvD,OAAO,QAAQ,UAAU,iBAAiB,GAAG,EAAE,KAAK,EAAE,KACtD,OAAO,QAAQ,UAAU,8CAA8C,GAAG,EAAE,KAAK,EAAE,IAClF;AAEL,mBAAK,SAAS,OAAQ,MAAM,SAAS,CAAC,OAAO,SAAS,GAAG,EAAE,KAAK,MAAM,KAAK,IAAK,MAAM,QAAQ,MAAM,EAAE,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC,GAAG;AACjJ,uBAAO,CAAC,KAAK,MAAM,OAAO,CAAC;AAAA,cAC7B;AAEA,sBAAQ,MAAM,SAAS;AACvB,uBAAS,OAAO,OAAO,CAAC,EACrB,QAAQ,OAAO,SAAS,GAAG,GAAG,KAAK,EACnC,QAAQ,OAAO,WAAW,QAAQ,UAAU,GAAG,GAAG,GAAG,EACrD,QAAQ,OAAO,MAAM,QAAQ,gBAAgB,GAAG,GAAG,OAAO,CAAC;AAAA,YAChE;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AASA,iBAAS,WAAW,UAAU;AAC5B,iBAAO,OAAO,UAAU,SAAS,QAAQ,SAAS;AAChD,mBAAO,WAAW,OAAO,UACvB,4DAA4D,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,KAAK;AAAA,UAC1F,CAAC;AAAA,QACH;AASA,iBAAS,mBAAmB;AAC1B,iBAAO,KAAK,eAAe;AAAA,QAC7B;AAKA,mBAAW,SAAS,CAAC,MAAM;AAQ3B,YAAI,cAAc,KAAK,EAAE,KAAK,CAAC,YAC1B,OAAO,kDAAkD,KAAK,EAAE,IAAI;AACvE,oBAAU,KAAK,KAAK,CAAC,CAAC,EAEnB,QAAQ,2BAA2B,EAAE,KACnC;AAAA,QACP;AAEA,YAAI,gBAAgB,CAAC,SAAS;AAC5B,oBAAU,WAAW,CAAC,YAAY,CAAC;AAAA,QACrC,WAAW,gBAAgB,SAAS;AAClC,oBAAU,QACP,QAAQ,OAAO,OAAO,QAAQ,YAAY,IAAI,aAAa,GAAG,GAAG,eAAe,GAAG,EACnF,QAAQ,OAAO,OAAO,QAAQ,YAAY,IAAI,gBAAgB,GAAG,GAAG,eAAe,KAAK;AAAA,QAC7F;AAEA,YAAK,OAAO,gBAAgB,KAAK,OAAO,GAAI;AAC1C,oBAAU,KAAK,CAAC;AAAA,QAClB;AAEA,YAAI,iBAAiB,KAAK,EAAE,GAAG;AAC7B,qBAAW,UAAU,UAAU,MAAM,MAAM;AAAA,QAC7C;AAEA,YAAI,QAAQ,gBAAgB,YAAY,KAAK,EAAE,GAAG;AAChD,sBAAY,KAAK,oCAAoC;AAAA,QACvD;AAEA,YAAI,QAAQ,QAAQ,qBAAqB,KAAK,EAAE,GAAG;AACjD,iBAAO,MAAM,GAAG,QAAQ,kBAAkB,EAAE,CAAC;AAC7C,yBAAe,KAAK;AACpB,oBAAU,KAAK;AAAA,QACjB,WAES,MAAM,KAAK,OAAO,GAAG;AAC5B,mBAAS,OAAO;AAChB,eAAK,UAAU,OAAO,gBAAgB,KAAK,EAAE,KACzC,MAAM,KAAK,CAAC,EAAE,QAAQ,MAAM,GAAG,IAC/B;AAAA,QACN,WAES,QAAQ,eAAe,YAAY,KAAK,EAAE,GAAG;AACpD,eAAK;AAAA,QACP,WAEU,gBAAgB,gBAAgB,aACpC,SAAS,KAAK,IAAI,KAAK,CAAC,qBAAqB,KAAK,EAAE,KAAM,WAAW,KAAK,OAAO,MAClF,cAAc,KAAK,EAAE,KAAK,UAAU,KAAK,IAAI,KAAK,eAAe,KAAK,EAAE,GAAI;AAC/E,iBAAO;AACP,eAAK,cAAc,KAAK,EAAE,IAAI,KAAK;AAAA,QACrC,WAES,QAAQ,QAAQ;AACvB,cAAI,CAAC,UAAU,KAAK,EAAE,GAAG;AACvB,iBAAK;AACL,wBAAY,QAAQ,cAAc;AAAA,UACpC;AACA,cAAI,wBAAwB,KAAK,EAAE,GAAG;AACpC,wBAAY,QAAQ,aAAa;AAAA,UACnC;AAAA,QACF,WAES,QAAQ,gBAAgB,YAAY,KAAK,EAAE,GAAG;AACrD,sBAAY,KAAK,YAAY;AAAA,QAC/B,WAES,QAAQ,eAAe,OAAO,wBAAwB,KAAK,EAAE,IAAI;AACxE,sBAAY,KAAK,4BAA4B,KAAK,CAAC,CAAC;AAAA,QACtD,WAES,QAAQ,cAAc,OAAO,0BAA0B,KAAK,EAAE,IAAI;AACzE,iBAAO,KAAK;AACZ,sBAAY,UAAU,KAAK,CAAC;AAAA,QAC9B,WAES,CAAC,SAAS,OAAO,CAAC,iBAAiB,KAAK,EAAE,KAAK,yBAAyB,KAAK,IAAI,IAAI;AAE5F,cAAI,QAAQ,CAAC,WAAW,kBAAkB,KAAK,GAAG,MAAM,GAAG,QAAQ,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG;AAEpF,mBAAO;AAAA,UACT;AAEA,eAAK,OAAO,WAAW,gBAAgB,QAClC,WAAW,gBAAgB,6CAA6C,KAAK,EAAE,IAAI;AACtF,mBAAO,mBAAmB,KAAK,cAAc,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI;AAAA,UACvE;AAAA,QACF,WAES,QAAQ,eAAe,QAAQ,uBAAuB,KAAK,EAAE,KAAK,GAAG,CAAC,IAAI;AACjF,sBAAY,KAAK,cAAc,IAAI;AAAA,QACrC;AAEA,YAAI,CAAC,SAAS;AACZ,oBAAU,WAAW;AAAA,YACnB;AAAA,YACA;AAAA,YACA,QAAQ,IAAI;AAAA,YACZ;AAAA,UACF,CAAC;AAAA,QACH;AAEA,YAAK,OACC,UAAU,UAAU,WAAW,OAAO,IAAI,KAAK,YAC/C,YAAY,KAAK,IAAI,MAAM,UAAU,KAAK,EAAE,IAAI,UAAU,aAC1D,8BAA8B,KAAK,EAAE,KAAK,CAAC,yBAAyB,KAAK,MAAM,KAAK,YACpF,CAAC,UAAU,YAAY,KAAK,EAAE,MAAM,MAAM,WAAW,WAAW,cAChE,UAAU,YAAY,8BAA8B,KAAK,IAAI,KAAK,YACjE;AACL,mBAAS,CAAC,IAAI;AAAA,QAChB;AAEA,YAAI,QAAQ,SAAS,QAAQ,4BAA4B,KAAK,EAAE,KAAK,GAAG,CAAC,IAAI;AAC3E,kBAAQ;AACR,eAAK,oBAAoB,MAAM,KAAK,IAAI,IAAI,OAAO,OAAO;AAC1D,sBAAY,QAAQ,cAAc;AAAA,QACpC,WAES,iBAAiB,KAAK,EAAE,GAAG;AAClC,iBAAO;AACP,eAAK;AACL,sBAAY,QAAQ,cAAc;AAClC,sBAAY,WAAW,gBAAgB,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,QACzD,WAES,QAAQ,QAAQ,UAAU,cAAc,OAAO,gBAAgB,KAAK,EAAE,IAAI;AACjF,cAAI,MAAM;AACR,wBAAY,KAAK,oBAAoB,QAAQ,UAAU,MAAM,UAAU,GAAG;AAAA,UAC5E;AACA,iBAAO;AACP,oBAAU,KAAK,CAAC;AAAA,QAClB;AAEA,YAAI,aAAa;AAGf,cAAI,WAAW,SAAS,QAAQ,GAAG;AACjC,gBAAI,MAAM;AACR,qBAAO,KAAK,KAAK;AACjB,qBAAO,KAAK,YAAY,SAAS;AACjC,mBAAK,MAAM,KAAK,YAAY,SAAS,IAAI,MAAM,KAAK,YAAY,YAAY;AAAA,YAC9E;AACA,gBAAI,OAAO;AACT,kBAAI;AACF,0BAAU,QAAQ,QAAQ,cAAc,EAAE,QAAQ,KAAK,GAAG;AAC1D,uBAAO;AAAA,cACT,SAAQ,GAAG;AACT,qBAAK,OAAO,QAAQ,WAAW,KAAK,OAAO,UAAU,QAAQ,QAAQ;AACnE,yBAAO;AACP,yBAAO,KAAK,KAAK,CAAC,EAAE,MAAM;AAAA,gBAC5B;AAAA,cACF;AACA,kBAAI,CAAC,MAAM;AACT,uBAAO;AAAA,cACT;AAAA,YACF,WAEE,OAAO,QAAQ,WAAW,YAAY,CAAC,QAAQ,QAAQ,YACtD,OAAO,QAAQ,UAChB;AACA,kBAAI,OAAO,KAAK,YAAY,UAAU;AACpC,oBAAI,OAAO,KAAK,SAAS,YAAY,UAAU;AAC7C,8BAAY,KAAK,UAAU,KAAK,SAAS,IAAI;AAC7C,yBAAO;AACP,4BAAU,KAAK,SAAS;AAAA,gBAC1B,WAAW,OAAO,KAAK,SAAS,MAAM,UAAU;AAC9C,8BAAY,KAAK,cAAc,SAAS,UAAU,KAAK,SAAS,IAAI;AACpE,yBAAO;AACP,4BAAU,KAAK,SAAS;AAAA,gBAC1B;AAAA,cACF;AACA,kBAAI,CAAC,MAAM;AACT,uBAAO;AACP,uBAAO,KAAK;AACZ,qBAAK,KAAK;AACV,0BAAU,SAAS,KAAK,KAAK,OAAO;AACpC,0BAAU,UAAU,QAAQ,CAAC,IAAI;AAAA,cACnC;AAAA,YACF;AAAA,UACF,WAES,WAAY,OAAO,QAAQ,OAAQ,KAAK,iBAAiB;AAChE,mBAAO;AACP,iBAAK,KAAK,MAAM,OAAO,aAAa;AAAA,UACtC,WAES,WAAY,OAAO,QAAQ,OAAQ,KAAK,cAAc;AAC7D,mBAAO;AACP,uBAAW,OAAO,KAAK,WAAW,SAAU,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,UACzF,WAES,OAAO,IAAI,gBAAgB,aAAa,OAAO,oBAAoB,KAAK,EAAE,IAAI;AAGrF,sBAAU,CAAC,SAAS,IAAI,YAAY;AACpC,iBAAK,OAAO,CAAC,KAAK,CAAC,IAAI,MAAM,QAAQ,CAAC,GAAG;AACvC,0BAAY,KAAK,QAAQ,QAAQ,CAAC,IAAI,OAAO;AAC7C,yBAAW,OAAO,CAAC,IAAI;AACvB,sBAAQ,CAAC,IAAI;AAAA,YACf;AACA,sBAAU,QAAQ,OAAO,OAAO,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,UACpE,WAES,OAAO,IAAI,gBAAgB,YAAY,wBAAwB,KAAK,IAAI,GAAG;AAClF,wBAAY,KAAK,gBAAgB,OAAO,MAAM,OAAO;AACrD,mBAAO;AACP,sBAAU;AACV,qBAAS,CAAC,SAAS;AACnB,iBAAK;AAAA,UACP;AACA,eAAK,MAAM,OAAO,EAAE;AAAA,QACtB;AAEA,YAAI,YAAY,OACV,0CAA0C,KAAK,OAAO,KACtD,2BAA2B,KAAK,KAAK,OAAO,eAAe,IAAI,gBAAgB,KAC/E,iBAAiB,KAAK,EAAE,KAAK,MAC5B;AACL,uBAAa,KAAK,KAAK,IAAI,IAAI,SAAS;AACxC,oBAAU,QAAQ,QAAQ,OAAO,OAAO,OAAO,GAAG,EAAE,KACjD,cAAc,SAAS,OAAO,UAAU,SAAS,KAAK,IAAI,KAAK;AAAA,QACpE;AAEA,YAAI,QAAQ,YAAY,QAAQ,aAAa,mCAAmC,KAAK,EAAE,GAAG;AACxF,iBAAO;AAAA,QACT,WAES,QAAQ,aAAa,SAAS;AACrC,oBAAU,QAAQ,QAAQ,YAAY,IAAI;AAAA,QAC5C,WAES,YAAY,KAAK,OAAO,GAAG;AAClC,cAAI,WAAW,YAAY;AACzB,iBAAK;AAAA,UACP;AACA,cAAI,WAAW,cAAc,eAAe,KAAK,EAAE,GAAG;AACpD,wBAAY,QAAQ,aAAa;AAAA,UACnC;AAAA,QACF,YAEU,wBAAwB,KAAK,IAAI,KAAK,QAAQ,CAAC,WAAW,CAAC,eAAe,KAAK,IAAI,OACxF,MAAM,gBAAgB,QAAQ,KAAK,EAAE,IAAI;AAC5C,kBAAQ;AAAA,QACV,WAES,QAAQ,QAAQ,aAAa;AACpC,cAAI;AACF,gBAAI,QAAQ,aAAa,MAAM;AAC7B,0BAAY,QAAQ,kBAAkB;AAAA,YACxC;AAAA,UACF,SAAQ,GAAG;AACT,wBAAY,QAAQ,UAAU;AAAA,UAChC;AAAA,QACF,YAGU,iBAAiB,KAAK,OAAO,KAAK,WAAW,KAAK,EAAE,OAAO,QAC9D,OAAO,QAAQ,QAAQ,OAAO,IAAI,IAAI,cAAc,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,KAC1E,UACC;AACL,iBAAO,CAAC,MAAM,OAAO,KAAK,EAAE,CAAC;AAC7B,gBAAM,KAAK,CAAC,KAAK,UAAU,MAAM,eAAe,gBAAgB,qBAAqB,MAAM,KAAK,CAAC;AACjG,oBAAU;AAAA,QACZ,WAGS,QAAQ,UAAU,WAAW,UAC/B,eAAe,SACf,QAAQ,KAAK,IAAI,KAAK,wBAAwB,KAAK,EAAE,KACrD,QAAQ,aAAa,uBAAuB,KAAK,EAAE,KACnD,QAAQ,SACN,MAAM,CAAC,OAAO,KAAK,EAAE,KAAK,UAAU,OACrC,iBAAiB,KAAK,EAAE,KAAK,UAAU,KACvC,WAAW,KAAK,CAAC,cAAc,KAAK,EAAE,OAErC,CAAC,QAAQ,KAAM,OAAO,MAAM,KAAK,QAAQ,GAAG,QAAQ,SAAS,EAAE,IAAI,GAAG,CAAE,KAAK,KAAK,MAAM;AAE/F,iBAAO,YAAY,KAAK,SAAS,OAAO,KAAK,WAAW,MAAM,OAAO;AACrE,cAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,gBAAI,SAAS,KAAK,IAAI,KAAK,MAAM,UAAU;AACzC,mBAAK;AAAA,YACP;AACA,mBAAO,aAAa;AAAA,UACtB,OAEK;AACH,mBAAO,SAAS;AAChB,gBAAI,YAAY;AACd,qBAAO,OAAO,WAAW,QAAQ,mBAAmB,OAAO,CAAC;AAAA,YAC9D,OAAO;AACL,qBAAO;AAAA,YACT;AACA,gBAAI,SAAS,KAAK,IAAI,GAAG;AACvB,mBAAK;AAAA,YACP;AACA,gBAAI,CAAC,aAAa;AAChB,wBAAU;AAAA,YACZ;AAAA,UACF;AACA,mBAAS,CAAC,QAAQ;AAClB,sBAAY,KAAK,IAAI;AAAA,QACvB;AAEA,YAAK,QAAQ,8BAA8B,KAAK,EAAE,KAAK,GAAG,CAAC,GAAI;AAG7D,iBAAO,CAAC,WAAW,KAAK,QAAQ,WAAW,MAAM,CAAC,GAAG,IAAI;AAEzD,cAAI,QAAQ,YAAY,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,KAAK;AAChD,mBAAO;AACP,yBAAa;AACb,sBAAU,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,UAC/B,WAES,WAAW,KAAK,CAAC,KACtB,YAAY,KAAK,CAAC,KAAK,yBAAyB,KAAK,EAAE,KAAK,GAAG,CAAC,IAAI;AACtE,sBAAU;AAAA,UACZ;AAEA,eAAK,CAAC,KAAK,mCAAmC,KAAK,EAAE,KAAK,GAAG,CAAC;AAE9D,cAAI,KAAK,CAAC,KAAK,UAAU,KAAK,CAAC,KAAK,UAAU,WAAW,KAAK,CAAC,CAAC,KAAK,MAAM,UAAU,UAAU;AAC7F,qBAAS,CAAC,OAAO;AAAA,UACnB;AAGA,cAAI,CAAC,eAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,GAAI;AAC7C,uBAAW,OAAO,CAAC,IAAI;AACvB,oBAAQ,OAAO,KAAK,CAAC,GAAG,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,OAAO,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK;AAAA,UAC7P,OAAO;AACL,uBAAW,OAAO,CAAC,IAAI;AACvB,mBAAO,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,GAAG,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,IAAI,OAAO,QAAS,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,OAAO,QAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,QAAQ,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,QAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,QAAQ,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,UAAU,UAAU,OAAO;AAAA,UAC5iB;AAEA,qBAAW,OAAO,CAAC,KAAK,OAAO,QAAQ,OAAO,QAAQ,WAAW,OAAO,OAAO,KAAK,IAAI,IAAI,KAAK;AAEjG,cAAI,QAAQ,aAAa,CAAC,WAAW,SAAS,OAAO,IAAI,KAAK;AAC5D,sBAAU;AAAA,UACZ,WAAW,QAAQ,YAAY,oBAAoB,KAAK,EAAE,GAAG;AAC3D,wBAAY,QAAQ,UAAU;AAAA,UAChC;AAAA,QACF;AAEA,YAAI,QAAQ,YAAa,OAAO,eAAe,KAAK,EAAE,IAAI;AACxD,kBAAQ;AACR,sBAAY,QAAQ,cAAc;AAClC,cAAI,QAAQ,QAAQ;AAClB,oBAAQ;AACR,sBAAU;AAAA,UACZ,OAAO;AACL,oBAAQ;AAAA,UACV;AACA,eAAK,GAAG,QAAQ,OAAO,OAAO,OAAO,GAAG,GAAG,EAAE;AAAA,QAC/C,WAES,QAAQ,YAAY,aAAa,KAAK,UAAU,OAAO,CAAC,CAAC,GAAG;AACnE,sBAAY,QAAQ,cAAc;AAClC,iBAAO;AACP,oBAAU;AAEV,cAAI,WAAW,KAAK,EAAE,GAAG;AACvB,2BAAe;AACf,iBAAK;AAAA,UACP,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF,WAES,kBAAkB,KAAK,IAAI,KAAK,CAAC,SAAS;AACjD,oBAAU,WAAW,QAAQ;AAAA,QAC/B;AAEA,YAAI,WAAW,QAAQ,QAAS,OAAO,UAAU,KAAK,EAAE,CAAE,KAAK,KAC3D,GAAG,QAAQ,MAAM,OAAO,GAAG,IAAI,IAAI;AACrC,eAAK,KAAK,GAAG,QAAQ,MAAM,EAAE,CAAC;AAAA,QAChC;AAEA,YAAI,MAAM,GAAG,QAAQ,IAAI,KAAK,MAAM,CAAC,OAAO,OAAO,KAAK,EAAE,KAAK,EAAE,GAAG;AAClE,eAAK,GAAG,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI,IAAI,GAAG,EAAE;AAAA,QACzD;AAEA,YAAI,UAAU,CAAC,qBAAqB,KAAK,IAAI,MACzC,4BAA4B,KAAK,IAAI,KACrC,QAAQ,YAAY,OAAO,KAAK,EAAE,KAAK,aAAa,KAAK,OAAO,CAAC,CAAC,KAClE,6GAA6G,KAAK,IAAI,KAAK,OAAO,CAAC,IAAI;AAEzI,WAAC,OAAO,OAAO,OAAO,SAAS,CAAC,MAAM,YAAY,KAAK,IAAI;AAAA,QAC7D;AAEA,YAAI,YAAY,QAAQ;AACtB,wBAAc,CAAC,MAAM,YAAY,KAAK,IAAI,IAAI,GAAG;AAAA,QACnD;AAEA,YAAI,gBAAgB,WAAW,QAAQ,QAAQ,YAAY,IAAI,GAAG;AAChE,sBAAY,KAAK,QAAQ,YAAY;AAAA,QACvC;AAEA,YAAI,SAAS;AACX,sBAAY,MAAM,OAAO,KAAK,YAAY,YAAY,SAAS,CAAC,CAAC,IAAI,KAAK,SAAS,OAAO;AAAA,QAC5F;AAEA,YAAI,IAAI;AACN,iBAAO,cAAc,KAAK,EAAE;AAC5B,6BAAmB,QAAQ,GAAG,OAAO,GAAG,SAAS,KAAK,CAAC,EAAE,SAAS,CAAC,KAAK;AACxE,eAAK;AAAA,YACH,gBAAgB;AAAA,YAChB,UAAW,QAAQ,CAAC,mBAAoB,GAAG,QAAQ,KAAK,CAAC,GAAG,EAAE,IAAI;AAAA,YAClE,WAAW,OAAO,KAAK,CAAC,IAAI;AAAA,YAC5B,YAAY,WAAW;AACrB,kBAAIA,WAAU,KAAK;AACnB,qBAAO,KAAK,UAAWA,YAAW,CAAC,mBAAoB,MAAMA,WAAU,OAAO,KAAK,gBAAgB,KAAK,YAAY;AAAA,YACtH;AAAA,UACF;AAAA,QACF;AAEA,aAAK,OAAO,mCAAmC,KAAK,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,GAAG;AACrF,cAAI,IAAI;AACN,eAAG,eAAe;AAClB,eAAG,SAAS,GAAG,OAAO,QAAQ,OAAO,OAAO,IAAI,GAAG,EAAE;AAAA,UACvD;AACA,cACI,SAAS,aAAa,KAAK,EAAE,KAC5B,eAAe,eAAe,KAAK,IAAI,YAAY,IAAI,QAAQ,KAAK,CAAC,kBAAkB,KAAK,EAAE,IACjG;AACA,wBAAY,QAAQ,QAAQ;AAAA,UAC9B;AAAA,QACF,WAGI,MAAM,QAAQ,KAAK,GAAG,MAAM,KAC5B,QAAQ,YAAY,WAAW,OAAO,KAAK,IAC7C;AACA,aAAG,eAAe;AAAA,QACpB;AAEA,eAAO,KAAK;AAUZ,YAAIC,YAAW,CAAC;AAQhB,QAAAA,UAAS,cAAc;AAWvB,QAAAA,UAAS,SAAS,UAAU,OAAO,CAAC;AAapC,QAAAA,UAAS,eAAe;AAgBxB,QAAAA,UAAS,OAAO;AAQhB,QAAAA,UAAS,aAAa;AAatB,QAAAA,UAAS,UAAU;AAQnB,QAAAA,UAAS,KAAK;AAQd,QAAAA,UAAS,UAAU,QAAQ;AAQ3B,QAAAA,UAAS,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQlB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAahB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQX,YAAY,WAAW;AAAE,mBAAO;AAAA,UAAQ;AAAA,QAC1C;AAEA,QAAAA,UAAS,QAAQ;AACjB,QAAAA,UAAS,WAAW;AAEpB,YAAIA,UAAS,SAAS;AACpB,sBAAY,QAAQ,OAAO;AAAA,QAC7B;AACA,YAAIA,UAAS,MAAM;AACjB,sBAAY,QAAQ,IAAI;AAAA,QAC1B;AACA,YAAI,MAAM,QAAQ,EAAE,MAAM,OAAO,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,WAAW;AAC5F,sBAAY,KAAK,UAAU,MAAM,KAAK,MAAM,QAAQ,EAAE;AAAA,QACxD;AACA,YAAI,YAAY,QAAQ;AACtB,UAAAA,UAAS,cAAc,YAAY,KAAK,GAAG;AAAA,QAC7C;AACA,eAAOA;AAAA,MACT;AAKA,UAAI,WAAW,MAAM;AAGrB,UAAI,OAAO,UAAU,cAAc,OAAO,OAAO,OAAO,YAAY,OAAO,KAAK;AAI9E,aAAK,WAAW;AAGhB,eAAO,WAAW;AAChB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,WAES,eAAe,YAAY;AAElC,eAAO,UAAU,SAAS,OAAO,KAAK;AACpC,sBAAY,GAAG,IAAI;AAAA,QACrB,CAAC;AAAA,MACH,OACK;AAEH,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,GAAE,KAAK,OAAI;AAAA;AAAA;", "names": ["version", "platform"]}