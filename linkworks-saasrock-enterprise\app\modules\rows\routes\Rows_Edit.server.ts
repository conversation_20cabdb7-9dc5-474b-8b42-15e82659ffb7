import { ActionFunction, LoaderFunctionArgs, redirect } from "react-router";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { createMetrics } from "~/modules/metrics/services/.server/MetricTracker";
import NotificationService from "~/modules/notifications/services/.server/NotificationService";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import UrlUtils from "~/utils/app/UrlUtils";
import { EntityWithDetails, getAllEntities } from "~/utils/db/entities/entities.db.server";
import { getUser } from "~/utils/db/users.db.server";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { getEntityPermission } from "~/utils/helpers/PermissionsHelper";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";
import RowHelper from "~/utils/helpers/RowHelper";
import RowsRequestUtils from "../utils/RowsRequestUtils";
import FormulaService from "~/modules/formulas/services/.server/FormulaService";
import EventsService from "~/modules/events/services/.server/EventsService";
import { RowDeletedDto } from "~/modules/events/dtos/RowDeletedDto";
import ApiHelper from "~/utils/helpers/ApiHelper";
import { RowUpdatedDto } from "~/modules/events/dtos/RowUpdatedDto";
import { db } from "~/utils/db.server";
import StepFormWizardSessionService from "~/custom/modules/stepFormWizard/services/StepFormWizardSessionService";
export namespace Rows_Edit {
  export type LoaderData = {
    meta: MetaTagsDto;
    rowData: RowsApi.GetRowData;
    routes: EntitiesApi.Routes;
    allEntities: EntityWithDetails[];
    relationshipRows: RowsApi.GetRelationshipRowsData;
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    const { time, getServerTimingHeader } = await createMetrics({ request, params }, `[Rows_Edit] ${params.entity}`);
    const { t, userId, tenantId, entity } = await RowsRequestUtils.getLoader({ request, params });
    const user = await time(getUser(userId), "getUser");
    await time(verifyUserHasPermission(request, getEntityPermission(entity, "update"), tenantId), "verifyUserHasPermission");
    if (!entity.isAutogenerated || entity.type === "system") {
      throw redirect(tenantId ? UrlUtils.currentTenantUrl(params, "404") : "/404?entity=" + params.entity);
    }
    const rowData = await time(
      RowsApi.get(params.id!, {
        entity,
        tenantId,
        userId,
      }),
      "RowsApi.get"
    );
    if (!rowData.rowPermissions.canUpdate && !user?.admin) {
      throw Error(t("shared.unauthorized"));
    }
    const data: LoaderData = {
      meta: [
        {
          title: `${t("shared.edit")} | ${RowHelper.getTextDescription({ entity, item: rowData.item, t })} | ${t(entity.titlePlural)} | ${process.env.APP_NAME
            }`,
        },
      ],
      rowData,
      routes: EntitiesApi.getNoCodeRoutes({ request, params }),
      allEntities: await time(getAllEntities({ tenantId }), "getAllEntities"),
      relationshipRows: await time(RowsApi.getRelationshipRows({ entity, tenantId, userId }), "RowsApi.getRelationshipRows"),
    };
    return Response.json(data, { headers: getServerTimingHeader() });
  };

  export const action: ActionFunction = async ({ request, params }) => {
    const { time, getServerTimingHeader } = await createMetrics({ request, params }, `[Rows_Edit] ${params.entity}`);
    const { t, userId, tenantId, entity, form } = await RowsRequestUtils.getAction({ request, params });
    const user = await getUser(userId);
    console.log("formm",form)
    const { item } = await time(
      RowsApi.get(params.id!, {
        entity,
        tenantId,
        userId,
      }),
      "RowsApi.get"
    );
    const action = form.get("action");
    const steps = entity.stepFormWizard?.steps ?? [];
    const currentStepIndex = parseInt((form?.get('currentStepIndex') ?? '0') as string);
    const routes = EntityHelper.getRoutes({ routes: EntitiesApi.getNoCodeRoutes({ request, params }), entity, item });
    let rowValues: any = {};
    if (action === "edit") {
      try {
        rowValues = RowHelper.getRowPropertiesFromForm({ t, entity, form, existing: item });
        const entityName = entity?.name;
        const article = /^[aeiouAEIOU]/.test(entityName) ? "An" : "A";
        console.log("rowValues", rowValues);

        // Check for required fields
        const requiredFields = Object.entries(entity.properties)
          .filter(([key, value]) => value.isRequired)
          .map(([key, value]) => ({ ...value }));
        
        const fieldErrors: Record<string, string> = {};
        const missingRequiredFields = [];
        for (const field of requiredFields) {
          const { id, name: fieldName } = field;
          const formField = rowValues.dynamicProperties.find((item: any) => item.propertyId === id);

          let isEmpty = false;
          if (field.type === 0) {
            isEmpty = formField?.numberValue === undefined || formField?.numberValue === null;
          } else if (field.type === 1) {
            isEmpty = !formField?.textValue || formField?.textValue.trim() === "";
          } else if (field.type === 2 || field.type === 3 || field.type===4 ) {
            isEmpty = !formField?.dateValue;
          } else if (field.type === 5) {
            isEmpty = !formField?.range?.dateMin || !formField?.range?.dateMax;
          } else {
            isEmpty = true;
          }

          if (isEmpty) {
            missingRequiredFields.push(fieldName);
            fieldErrors[fieldName] = `${fieldName} is required`;
          }
        }

        if (missingRequiredFields.length > 0) {
          return Response.json(
            {
              error: "Validation failed",
              message: `The following required fields are missing: ${missingRequiredFields.join(", ")}`,
              fieldErrors,
              missingFields: missingRequiredFields,
              status: 400,
            },
            { status: 400, headers: getServerTimingHeader() }
          );
        }

        const uniqueFields = Object.entries(entity.properties)
          .filter(([key, value]) => value.isUnique)
          .map(([key, value]) => ({ ...value }));

        if (uniqueFields.length > 0) {
          for (const field of uniqueFields) {
            const { type, id, name: fieldName } = field;
            const formField = rowValues.dynamicProperties.find((item: any) => item.propertyId === id);

            let fieldValue;
            let whereCondition: any = { propertyId: id };

            if (type === 0) {
              fieldValue = formField?.numberValue;
              whereCondition = { ...whereCondition, numberValue: fieldValue };
            } else if (type === 1) {
              fieldValue = formField?.textValue;
              whereCondition = { ...whereCondition, textValue: fieldValue };
            } else if (type === 2) {
              fieldValue = formField?.dateValue;
              whereCondition = { ...whereCondition, dateValue: fieldValue };
            }

            if (fieldValue !== undefined) {
              const existingRows = await db.rowValue.findMany({
                where: {
                  AND: [
                    whereCondition,
                    {
                      rowId: { not: item.values[0].rowId },
                    },
                  ],
                },
              });

              if (existingRows.length > 0) {
                return Response.json(
                  {
                    error: `${article} ${entityName} with the ${fieldName} '${fieldValue}' already exists. Please use a different ${fieldName} or modify the existing entry.`,
                  },
                  { status: 400 }
                );
              }
            }
          }
        }

        const updatedRow = await time(
          RowsApi.update(params.id!, {
            entity,
            tenantId,
            userId,
            rowValues,
          }),
          "RowsApi.update"
        );
        if (entity.isStepFormWizard) {
          // trigger session webhook
          await StepFormWizardSessionService.triggerRowSessionWebHook(entity, updatedRow.id, currentStepIndex);
        }
        if (form?.get('saveAndExit') === 'true') {
          return redirect(`${routes?.overview ?? ""}`, { headers: getServerTimingHeader() });
        }
        await time(
          FormulaService.trigger({ trigger: "AFTER_UPDATED", rows: [updatedRow], entity: entity, session: { tenantId, userId }, t }),
          "FormulaService.trigger.AFTER_UPDATED"
        );
      } catch (error: any) {
        return Response.json({ error: error.message }, { status: 400, headers: getServerTimingHeader() });
      }
      const updatedRow = await RowsApi.get(params.id!, { entity });
      if (item.createdByUser) {
        // eslint-disable-next-line no-console
        console.log("Sending notification");
        await NotificationService.send({
          channel: "my-rows",
          to: item.createdByUser,
          notification: {
            from: { user },
            message: `${user?.email} updated ${RowHelper.getTextDescription({ entity, item })}`,
            action: {
              title: t("shared.view"),
              url: EntityHelper.getRoutes({ routes: EntitiesApi.getNoCodeRoutes({ request, params }), entity, item })?.overview ?? "",
            },
          },
        });
        await EventsService.create({
          request,
          event: "row.updated",
          tenantId,
          userId,
          data: {
            id: item.id,
            title: RowHelper.getTextDescription({ entity, item, t }),
            entity: { id: entity.id, name: entity.name, slug: entity.slug, title: t(entity.title) },
            row: RowHelper.getDiff({ entity, before: item, after: updatedRow.item }),
            user: { id: user?.id ?? "", email: user?.email ?? "" },
          } satisfies RowUpdatedDto,
        });
      }
      if (entity.isStepFormWizard) {

        if (currentStepIndex < steps.length - 1) {
          const url = new URL(request.url);
          const editStepFormWizard = url.searchParams.get("editStepFormWizard") === "true" ? "true" : "";
          const nextStep = currentStepIndex + 1;
          return redirect(`${routes?.edit ?? ""}?step=${nextStep}${editStepFormWizard.length > 0 ? `&editStepFormWizard=${editStepFormWizard}` : ""}`, { headers: getServerTimingHeader() });
        } else {
          return redirect(`${routes?.overview ?? ""}`, { headers: getServerTimingHeader() });
        }
      } else {
        const redirectTo = form.get("redirect")?.toString() || new URL(request.url).searchParams.get("redirect")?.toString();
        if (redirectTo) {
          return redirect(redirectTo, { headers: getServerTimingHeader() });
        }
      }
      return Response.json({ updatedRow }, { headers: getServerTimingHeader() });
    } else if (action === "delete") {
      try {
        await time(verifyUserHasPermission(request, getEntityPermission(entity, "delete"), tenantId), "verifyUserHasPermission");
        await RowsApi.del(params.id!, {
          entity,
          userId,
          checkPermissions: !user?.admin,
        });
        await EventsService.create({
          request,
          event: "row.deleted",
          tenantId,
          userId,
          data: {
            id: item.id,
            title: RowHelper.getTextDescription({ entity, item, t }),
            row: ApiHelper.getApiFormat(entity, item),
            entity: { id: entity.id, name: entity.name, slug: entity.slug, title: t(entity.title) },
            user: { id: user?.id ?? "", email: user?.email ?? "" },
          } satisfies RowDeletedDto,
        });
      } catch (error: any) {
        return Response.json({ error: error.message }, { status: 400, headers: getServerTimingHeader() });
      }
      const redirectTo = form.get("redirect")?.toString() || new URL(request.url).searchParams.get("redirect")?.toString();
      if (redirectTo) {
        return redirect(redirectTo, { headers: getServerTimingHeader() });
      }
      return Response.json({ deleted: true }, { headers: getServerTimingHeader() });
    } else {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400, headers: getServerTimingHeader() });
    }
  };
}
