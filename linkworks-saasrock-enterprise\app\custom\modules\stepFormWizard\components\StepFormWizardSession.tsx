import { useEffect, useState } from "react";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import StepFormWizardBlock from "../blocks/StepFormWizardBlock";
import { StepFormWizardBlockDto, StepFormWizardBlockStyle, StepFormWizardHeightDto, StepFormWizardStepBlockDto } from "../blocks/StepFormWizardBlockUtils";

export default function StepFormWizardSession({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void }) {
  const appOrAdminData = useAppOrAdminData();
  const [stepFormWizardBlock, setStepFormWizardBlock] = useState<StepFormWizardBlockDto>();

  useEffect(() => {
    if (appOrAdminData.stepFormWizardSession && !stepFormWizardBlock) {
      const stepFormWizard = appOrAdminData.stepFormWizardSession.stepFormWizard;
      setStepFormWizardBlock({
        style: stepFormWizard.type as StepFormWizardBlockStyle,
        title: stepFormWizard.title,
        canBeDismissed: stepFormWizard.canBeDismissed,
        height: (stepFormWizard.height ?? "md") as StepFormWizardHeightDto,
        steps: appOrAdminData.stepFormWizardSession.sessionSteps.map((f) => {
          const block = JSON.parse(f.step.block) as StepFormWizardStepBlockDto;
          block.completedAt = f.completedAt;
          return block;
        }),
      });
      if (stepFormWizard.type === "modal") {
        setOpen(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appOrAdminData.stepFormWizardSession]);
  return (
    <>{stepFormWizardBlock && <StepFormWizardBlock session={appOrAdminData.stepFormWizardSession} item={stepFormWizardBlock} open={open} onClose={() => setOpen(false)} />}</>
  );
}
