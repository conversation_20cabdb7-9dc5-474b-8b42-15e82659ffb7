import { LoaderFunctionArgs, MetaFunction } from "react-router";
import { Outlet } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import IncreaseIcon from "~/components/ui/icons/crm/IncreaseIcon";
import IncreaseIconFilled from "~/components/ui/icons/crm/IncreaseIconFilled";
import GoalIcon from "~/components/ui/icons/stepFormWizards/GoalIcon";
import GoalIconFilled from "~/components/ui/icons/stepFormWizards/GoalIconFilled";
import JourneyIcon from "~/components/ui/icons/stepFormWizards/JourneyIcon";
import JourneyIconFilled from "~/components/ui/icons/stepFormWizards/JourneyIconFilled";
import SidebarIconsLayout from "~/components/ui/layouts/SidebarIconsLayout";
import { getTranslations } from "~/locale/i18next.server";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

type LoaderData = {
  title: string;
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { t } = await getTranslations(request);
  await verifyUserHasPermission(request, "admin.stepFormWizard.view");
  const data: LoaderData = {
    title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}`,
  };
  return data;
};

export const meta: MetaFunction<typeof loader> = ({ data }) => [{ title: data?.title }];

export default () => {
  return (
    <SidebarIconsLayout
      label={{ align: "right" }}
      items={[
        {
          name: "Overview",
          href: "/admin/step-form-wizard",
          exact: true,
          icon: <IncreaseIcon className="h-5 w-5" />,
          iconSelected: <IncreaseIconFilled className="h-5 w-5" />,
        },
        {
          name: "Step form wizards",
          href: "/admin/step-form-wizard/step-form-wizards",
          icon: <GoalIcon className="h-5 w-5" />,
          iconSelected: <GoalIconFilled className="h-5 w-5" />,
        },
        {
          name: "Sessions",
          href: "/admin/step-form-wizard/sessions",
          icon: <JourneyIcon className="h-5 w-5" />,
          iconSelected: <JourneyIconFilled className="h-5 w-5" />,
        },
      ]}
    >
      <Outlet />
    </SidebarIconsLayout>
  );
};

export function ErrorBoundary() {
  return <ServerError />;
}
