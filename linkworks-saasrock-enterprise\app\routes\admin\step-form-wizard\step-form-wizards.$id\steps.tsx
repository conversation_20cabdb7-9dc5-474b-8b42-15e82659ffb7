import { ActionFunction, LoaderFunctionArgs, MetaFunction, redirect } from "react-router";
import { useLoaderData, useActionData, Form, useSubmit } from "react-router";
import { FormEvent, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import ServerError from "~/components/ui/errors/ServerError";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import { getTranslations } from "~/locale/i18next.server";
import { StepFormWizardWithDetails, getStepFormWizard } from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import WarningBanner from "~/components/ui/banners/WarningBanner";
import StepFormWizardBlock from "~/custom/modules/stepFormWizard/blocks/StepFormWizardBlock";
import StepFormWizardBlockForm from "~/custom/modules/stepFormWizard/blocks/StepFormWizardBlockForm";
import {
  StepFormWizardBlockDto,
  StepFormWizardBlockProgressBar,
  StepFormWizardHeightDto,
  StepFormWizardStepBlockDto,
} from "~/custom/modules/stepFormWizard/blocks/StepFormWizardBlockUtils";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import ErrorBanner from "~/components/ui/banners/ErrorBanner";
import StepFormWizardStepsService from "~/custom/modules/stepFormWizard/services/StepFormWizardStepsService";
import { getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";
import { EntityWithDetails, getAllEntities } from "~/utils/db/entities/entities.db.server";
export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
type LoaderData = {
  meta: MetaTagsDto;
  item: StepFormWizardWithDetails;
  entities: EntityWithDetails[];
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const item = await getStepFormWizard(params.id!);
  const entities = (await getAllEntities({ tenantId: null })).filter((o) => !!o.isStepFormWizard);
  if (!item) {
    return redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  const data: LoaderData = {
    meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
    item,
    entities: entities,
  };
  return data;
};

type ActionData = {
  error?: string;
  success?: string;
};
export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (action === "set-steps") {
    try {
      await StepFormWizardStepsService.setSteps({ item, form, t });
      return Response.json({ success: "Step form wizard steps updated" });
    } catch (e: any) {
      return Response.json({ error: e.message }, { status: 500 });
    }
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();
  const appOrAdminData = useAppOrAdminData();

  const [showPreview, setShowPreview] = useState(false);
  const [selectedEntitypropertiesMap, setSelectedEntitypropertiesMap] = useState<{ [key: string]: number }>({});

  const [stepFormWizardBlock, setStepFormWizardBlock] = useState<StepFormWizardBlockDto>({
    style: data.item.type as any,
    title: data.item.title,
    canBeDismissed: data.item.canBeDismissed,
    height: (data.item.height ?? "md") as StepFormWizardHeightDto,
    entity: data.item.entity || "",
    progressBar: (data.item.progressBar ?? "tabs") as StepFormWizardBlockProgressBar,
    steps: data.item.steps.map((f) => {
      const block = JSON.parse(f.block) as StepFormWizardStepBlockDto;
      return block;
    }),
  });

  const modalConfirm = useRef<RefConfirmModal>(null);

  function onUpdateStepFormWizardBlock(item: StepFormWizardBlockDto) {
    setStepFormWizardBlock(item);
  }

  function onSave(e: FormEvent<HTMLFormElement>) {
    e.stopPropagation();
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    if (data.item.sessions.length > 0) {
      modalConfirm.current?.setValue(formData);
      modalConfirm.current?.show(
        t("stepFormWizard.prompts.updateSteps.title", { 0: data.item.sessions.length }),
        t("shared.confirm"),
        t("shared.back"),
        t("stepFormWizard.prompts.updateSteps.description")
      );
    } else {
      onConfirmSave(formData);
    }
  }

  function onConfirmSave(form: FormData) {
    submit(form, {
      method: "post",
    });
  }

  return (
    <Form method="post" onSubmit={onSave} className="mx-auto flex-1 space-y-5 overflow-x-auto px-2 py-2 xl:overflow-y-auto">
      <input type="hidden" name="action" value="set-steps" hidden readOnly />
      <input type="hidden" name="block" value={JSON.stringify(stepFormWizardBlock)} hidden readOnly />

      {data.item.active ? (
        <ErrorBanner title={t("shared.warning")} text="You cannot edit an active step form wizard." />
      ) : data.item.sessions.length > 0 ? (
        <WarningBanner
          title={t("stepFormWizard.prompts.updateSteps.title", { 0: data.item.sessions.length })}
          text={t("stepFormWizard.prompts.updateSteps.description")}
        />
      ) : null}

      <div>
        <StepFormWizardBlock open={showPreview} onClose={() => setShowPreview(false)} item={stepFormWizardBlock} />
        <StepFormWizardBlockForm
          item={stepFormWizardBlock}
          onUpdate={onUpdateStepFormWizardBlock}
          entities={data.entities}
          selectedEntitypropertiesMap={selectedEntitypropertiesMap}
          setSelectedEntitypropertiesMap={setSelectedEntitypropertiesMap}
        />
      </div>

      <div className="space-y-3">
        <button
          type="button"
          onClick={() => setShowPreview(true)}
          className="hover:bg-secondary border-border text-muted-foreground hover:text-foreground bg-background flex w-full justify-center rounded-lg border-2 border-dashed p-4 shadow-lg hover:border-dashed hover:border-gray-600 focus:outline-hidden"
        >
          {t("shared.clickHereTo", { 0: t("shared.preview").toLowerCase() })}
        </button>
      </div>

      <div className="border-border flex justify-between border-t pt-4">
        <div></div>
        <div>
          <LoadingButton disabled={data.item.active || !getUserHasPermission(appOrAdminData, "admin.stepFormWizard.update")} type="submit">
            {t("shared.save")}
          </LoadingButton>
        </div>
      </div>

      <ConfirmModal ref={modalConfirm} onYes={onConfirmSave} />
      <ActionResultModal actionData={actionData} />
    </Form>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
