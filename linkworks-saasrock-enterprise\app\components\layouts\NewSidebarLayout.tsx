import { Fragment, ReactNode, useEffect, useState } from "react";
import { useParams, useLocation } from "react-router";
import { useKBar } from "kbar";
import NewSidebarMenu from "./NewSidebarMenu";
import OnboardingSession from "~/modules/onboarding/components/OnboardingSession";

interface Props {
  layout: "app" | "admin" | "docs";
  children: ReactNode;
  className?: string;
}

export default function NewSidebarLayout({ layout, children }: Props) {
  const { query } = useKBar();
  const params = useParams();
  const location = useLocation();
  const [onboardingModalOpen, setOnboardingModalOpen] = useState(false);
  const isStepFormWizardPage = location.pathname.includes("/stepFormWizardpage");

  useEffect(() => {
    try {
      // @ts-ignore
      $crisp?.push(["do", "chat:hide"]);
    } catch (e) {
      // ignore
    }
  }, []);

  function onOpenCommandPalette() {
    query.toggle();
  }

  return (
    <Fragment>
      <OnboardingSession open={onboardingModalOpen} setOpen={setOnboardingModalOpen} />
      {isStepFormWizardPage ? (
        <>{children}</>
      ) : (
        <NewSidebarMenu key={params.tenant} layout={layout} onOpenCommandPalette={onOpenCommandPalette}>
          {children}
        </NewSidebarMenu>
      )}
    </Fragment>
  );
}
