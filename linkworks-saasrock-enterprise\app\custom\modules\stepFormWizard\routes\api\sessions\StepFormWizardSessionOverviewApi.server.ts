import { ActionFunction, LoaderFunctionArgs, redirect } from "react-router";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { getTranslations } from "~/locale/i18next.server";
import {
  deleteStepFormWizardSession,
  getStepFormWizardSession,
  StepFormWizardSessionWithDetails,
  updateStepFormWizardSession,
} from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { StepFormWizardSessionStatus } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardSessionStatus";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export namespace StepFormWizardSessionOverviewApi {
  export type LoaderData = {
    meta: MetaTagsDto;
    item: StepFormWizardSessionWithDetails;
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.view");
    const { t } = await getTranslations(request);
    const item = await getStepFormWizardSession(params.id!);
    if (!item) {
      throw redirect("/step-form-wizard/sessions");
    }
    const data: LoaderData = {
      meta: [{ title: `${t("stepFormWizard.session.object")} | ${process.env.APP_NAME}` }],
      item,
    };
    return data;
  };

  export type ActionData = {
    error?: string;
  };
  export const action: ActionFunction = async ({ request, params }) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.update");
    const { t } = await getTranslations(request);
    const form = await request.formData();
    const action = form.get("action");
    const item = await getStepFormWizardSession(params.id!);
    if (!item) {
      return redirect("/step-form-wizard/sessions");
    }
    if (action === "update") {
      const status = form.get("status")?.toString();
      const startedAt = form.get("startedAt")?.toString();
      const completedAt = form.get("completedAt")?.toString();
      const dismissedAt = form.get("dismissedAt")?.toString();
      await updateStepFormWizardSession(item.id, {
        status: status !== undefined ? (status as StepFormWizardSessionStatus) : undefined,
        startedAt: startedAt !== undefined ? new Date(startedAt) : undefined,
        completedAt: completedAt !== undefined ? new Date(completedAt) : undefined,
        dismissedAt: dismissedAt !== undefined ? new Date(dismissedAt) : undefined,
      });
      return Response.json({ success: true });
    } else if (action === "delete") {
      await verifyUserHasPermission(request, "admin.stepFormWizard.delete");
      await deleteStepFormWizardSession(item.id);
      return redirect("/step-form-wizard/sessions");
    } else {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
    }
  };
}
