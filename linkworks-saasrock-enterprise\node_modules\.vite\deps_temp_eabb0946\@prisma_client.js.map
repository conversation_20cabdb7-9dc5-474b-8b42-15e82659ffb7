{"version": 3, "sources": ["../../@prisma/client/src/runtime/index-browser.ts", "../../@prisma/client/src/runtime/core/public/index.ts", "../../@prisma/client/src/runtime/core/public/validator.ts", "../../@prisma/client/src/runtime/core/types/exported/ObjectEnums.ts", "../../@prisma/client/src/runtime/strictEnum.ts", "../../@prisma/client/src/runtime/utils/getRuntime.ts", "../../node_modules/.pnpm/decimal.js@10.5.0/node_modules/decimal.js/decimal.mjs", "../../.prisma/client/index-browser.js", "../../@prisma/client/index-browser.js"], "sourcesContent": ["import * as Public from './core/public'\n\nexport { objectEnumValues } from './core/types/exported/ObjectEnums'\nexport { makeStrictEnum } from './strictEnum'\nexport { getRuntime } from './utils/getRuntime'\nexport { default as Decimal } from 'decimal.js'\n\nexport { Public }\n", "import { validator } from './validator'\n\n/*\n * /!\\ These exports are exposed to the user. Proceed with caution.\n *\n * TODO: Move more hardcoded utils from generation into here\n */\n\nexport { validator }\n", "import { Args, Operation } from '../types/exported/Public'\nimport { Exact } from '../types/exported/Utils'\n\nexport function validator<V>(): <S>(select: Exact<S, V>) => S\nexport function validator<C, M extends Exclude<keyof C, `$${string}`>, O extends keyof C[M] & Operation>(\n  client: C,\n  model: M,\n  operation: O,\n): <S>(select: Exact<S, Args<C[M], O>>) => S\nexport function validator<\n  C,\n  M extends Exclude<keyof C, `$${string}`>,\n  O extends keyof C[M] & Operation,\n  P extends keyof Args<C[M], O>,\n>(client: C, model: M, operation: O, prop: P): <S>(select: Exact<S, Args<C[M], O>[P]>) => S\nexport function validator(..._args: any[]) {\n  return (args: any) => args\n}\n", "/**\n * Module-private symbol used to distinguish between instances of\n * `ObjectEnumValue` created inside and outside this module.\n */\nconst secret = Symbol()\n\n/**\n * Emulate a private property via a WeakMap manually. Using native private\n * properties is a breaking change for downstream users with minimal TypeScript\n * configs, because TypeScript uses ES3 as the default target.\n *\n * TODO: replace this with a `#representation` private property in the\n * `ObjectEnumValue` class and document minimal required `target` for TypeScript.\n */\nconst representations = new WeakMap<ObjectEnumValue, string>()\n\n/**\n * Base class for unique values of object-valued enums.\n */\nexport abstract class ObjectEnumValue {\n  constructor(arg?: symbol) {\n    if (arg === secret) {\n      representations.set(this, `Prisma.${this._getName()}`)\n    } else {\n      representations.set(this, `new Prisma.${this._getNamespace()}.${this._getName()}()`)\n    }\n  }\n\n  abstract _getNamespace(): string\n\n  _getName() {\n    return this.constructor.name\n  }\n\n  toString() {\n    return representations.get(this)!\n  }\n}\n\nclass NullTypesEnumValue extends ObjectEnumValue {\n  override _getNamespace() {\n    return 'NullTypes'\n  }\n}\n\nclass DbNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_DbNull!: void\n}\nsetClassName(DbNull, 'DbNull')\n\nclass JsonNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_JsonNull!: void\n}\nsetClassName(JsonNull, 'JsonNull')\n\nclass AnyNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_AnyNull!: void\n}\nsetClassName(AnyNull, 'AnyNull')\n\nexport const objectEnumValues = {\n  classes: {\n    DbNull,\n    JsonNull,\n    AnyNull,\n  },\n  instances: {\n    DbNull: new DbNull(secret),\n    JsonNull: new JsonNull(secret),\n    AnyNull: new AnyNull(secret),\n  },\n}\n\n/**\n * See helper in @internals package. Can not be used here\n * because importing internal breaks browser build.\n *\n * @param classObject\n * @param name\n */\nfunction setClassName(classObject: Function, name: string) {\n  Object.defineProperty(classObject, 'name', {\n    value: name,\n    configurable: true,\n  })\n}\n", "/**\n * List of properties that won't throw exception on access and return undefined instead\n */\nconst allowList = new Set([\n  'toJSON', // used by JSON.stringify\n  '$$typeof', // used by old React tooling\n  'asymmetricMatch', // used by Jest\n  Symbol.iterator, // used by various JS constructs/methods\n  Symbol.toStringTag, // Used by .toString()\n  Symbol.isConcatSpreadable, // Used by Array#concat,\n  Symbol.toPrimitive, // Used when getting converted to primitive values\n])\n/**\n * Generates more strict variant of an enum which, unlike regular enum,\n * throws on non-existing property access. This can be useful in following situations:\n * - we have an API, that accepts both `undefined` and `SomeEnumType` as an input\n * - enum values are generated dynamically from DMMF.\n *\n * In that case, if using normal enums and no compile-time typechecking, using non-existing property\n * will result in `undefined` value being used, which will be accepted. Using strict enum\n * in this case will help to have a runtime exception, telling you that you are probably doing something wrong.\n *\n * Note: if you need to check for existence of a value in the enum you can still use either\n * `in` operator or `hasOwnProperty` function.\n *\n * @param definition\n * @returns\n */\nexport function makeStrictEnum<T extends Record<PropertyKey, string | number>>(definition: T): T {\n  return new Proxy(definition, {\n    get(target, property) {\n      if (property in target) {\n        return target[property]\n      }\n      if (allowList.has(property)) {\n        return undefined\n      }\n      throw new TypeError(`Invalid enum value: ${String(property)}`)\n    },\n  })\n}\n", "// https://runtime-keys.proposal.wintercg.org/\nexport type RuntimeName = 'workerd' | 'deno' | 'netlify' | 'node' | 'bun' | 'edge-light' | '' /* unknown */\n\n/**\n * Indicates if running in Node.js or a Node.js compatible runtime.\n *\n * **Note:** When running code in Bun and Deno with Node.js compatibility mode, `isNode` flag will be also `true`, indicating running in a Node.js compatible runtime.\n */\nconst isNode = () => globalThis.process?.release?.name === 'node'\n\n/**\n * Indicates if running in Bun runtime.\n */\nconst isBun = () => !!globalThis.Bun || !!globalThis.process?.versions?.bun\n\n/**\n * Indicates if running in Deno runtime.\n */\nconst isDeno = () => !!globalThis.Deno\n\n/**\n * Indicates if running in Netlify runtime.\n */\nconst isNetlify = () => typeof globalThis.Netlify === 'object'\n\n/**\n * Indicates if running in EdgeLight (Vercel Edge) runtime.\n */\nconst isEdgeLight = () => typeof globalThis.EdgeRuntime === 'object'\n\n/**\n * Indicates if running in Cloudflare Workers runtime.\n * See: https://developers.cloudflare.com/workers/runtime-apis/web-standards/#navigatoruseragent\n */\nconst isWorkerd = () => globalThis.navigator?.userAgent === 'Cloudflare-Workers'\n\nfunction detectRuntime(): RuntimeName {\n  // Note: we're currently not taking 'fastly' into account. Why?\n  const runtimeChecks = [\n    [isNetlify, 'netlify'],\n    [isEdgeLight, 'edge-light'],\n    [isWorkerd, 'workerd'],\n    [isDeno, 'deno'],\n    [isBun, 'bun'],\n    [isNode, 'node'],\n  ] as const\n\n  const detectedRuntime =\n    runtimeChecks\n      // TODO: Transforming destructuring to the configured target environment ('chrome58', 'edge16', 'firefox57', 'safari11') is not supported yet,\n      // so we can't write the following code yet:\n      // ```\n      // .flatMap(([isCurrentRuntime, runtime]) => isCurrentRuntime() ? [runtime] : [])\n      // ```\n      .flatMap((check) => (check[0]() ? [check[1]] : []))\n      .at(0) ?? ''\n\n  return detectedRuntime\n}\n\nconst runtimesPrettyNames = {\n  node: 'Node.js',\n  workerd: 'Cloudflare Workers',\n  deno: 'Deno and Deno Deploy',\n  netlify: 'Netlify Edge Functions',\n  'edge-light':\n    'Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)',\n} as const\n\ntype GetRuntimeOutput = {\n  id: RuntimeName\n  prettyName: string\n  isEdge: boolean\n}\n\nexport function getRuntime(): GetRuntimeOutput {\n  const runtimeId = detectRuntime()\n\n  return {\n    id: runtimeId,\n    // Fallback to the runtimeId if the runtime is not in the list\n    prettyName: runtimesPrettyNames[runtimeId] || runtimeId,\n    isEdge: ['workerd', 'deno', 'netlify', 'edge-light'].includes(runtimeId),\n  }\n}\n", "/*!\r\n *  decimal.js v10.5.0\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js\r\n *  Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *  MIT Licence\r\n */\r\n\r\n\r\n// -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n  // The maximum exponent magnitude.\r\n  // The limit on the value of `toExpNeg`, `toExpPos`, `minE` and `maxE`.\r\nvar EXP_LIMIT = 9e15,                      // 0 to 9e15\r\n\r\n  // The limit on the value of `precision`, and on the value of the first argument to\r\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n  // Base conversion alphabet.\r\n  NUMERALS = '0123456789abcdef',\r\n\r\n  // The natural logarithm of 10 (1025 digits).\r\n  LN10 = '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058',\r\n\r\n  // Pi (1025 digits).\r\n  PI = '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789',\r\n\r\n\r\n  // The initial configuration properties of the Decimal constructor.\r\n  DEFAULTS = {\r\n\r\n    // These values must be integers within the stated ranges (inclusive).\r\n    // Most of these values can be changed at run-time using the `Decimal.config` method.\r\n\r\n    // The maximum number of significant digits of the result of a calculation or base conversion.\r\n    // E.g. `Decimal.config({ precision: 20 });`\r\n    precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n    // The rounding mode used when rounding to `precision`.\r\n    //\r\n    // ROUND_UP         0 Away from zero.\r\n    // ROUND_DOWN       1 Towards zero.\r\n    // ROUND_CEIL       2 Towards +Infinity.\r\n    // ROUND_FLOOR      3 Towards -Infinity.\r\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    //\r\n    // E.g.\r\n    // `Decimal.rounding = 4;`\r\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n    rounding: 4,                           // 0 to 8\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP         0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN       1 The remainder has the same sign as the dividend (JavaScript %).\r\n    // FLOOR      3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN  6 The IEEE 754 remainder function.\r\n    // EUCLID     9 Euclidian division. q = sign(n) * floor(a / abs(n)). Always positive.\r\n    //\r\n    // Truncated division (1), floored division (3), the IEEE 754 remainder (6), and Euclidian\r\n    // division (9) are commonly used for the modulus operation. The other rounding modes can also\r\n    // be used, but they may not give useful results.\r\n    modulo: 1,                             // 0 to 9\r\n\r\n    // The exponent value at and beneath which `toString` returns exponential notation.\r\n    // JavaScript numbers: -7\r\n    toExpNeg: -7,                          // 0 to -EXP_LIMIT\r\n\r\n    // The exponent value at and above which `toString` returns exponential notation.\r\n    // JavaScript numbers: 21\r\n    toExpPos:  21,                         // 0 to EXP_LIMIT\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // JavaScript numbers: -324  (5e-324)\r\n    minE: -EXP_LIMIT,                      // -1 to -EXP_LIMIT\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // JavaScript numbers: 308  (1.7976931348623157e+308)\r\n    maxE: EXP_LIMIT,                       // 1 to EXP_LIMIT\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    crypto: false                          // true/false\r\n  },\r\n\r\n\r\n// ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n  inexact, quadrant,\r\n  external = true,\r\n\r\n  decimalError = '[DecimalError] ',\r\n  invalidArgument = decimalError + 'Invalid argument: ',\r\n  precisionLimitExceeded = decimalError + 'Precision limit exceeded',\r\n  cryptoUnavailable = decimalError + 'crypto unavailable',\r\n  tag = '[object Decimal]',\r\n\r\n  mathfloor = Math.floor,\r\n  mathpow = Math.pow,\r\n\r\n  isBinary = /^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,\r\n  isHex = /^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,\r\n  isOctal = /^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,\r\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n  BASE = 1e7,\r\n  LOG_BASE = 7,\r\n  MAX_SAFE_INTEGER = 9007199254740991,\r\n\r\n  LN10_PRECISION = LN10.length - 1,\r\n  PI_PRECISION = PI.length - 1,\r\n\r\n  // Decimal.prototype object\r\n  P = { toStringTag: tag };\r\n\r\n\r\n// Decimal prototype methods\r\n\r\n\r\n/*\r\n *  absoluteValue             abs\r\n *  ceil\r\n *  clampedTo                 clamp\r\n *  comparedTo                cmp\r\n *  cosine                    cos\r\n *  cubeRoot                  cbrt\r\n *  decimalPlaces             dp\r\n *  dividedBy                 div\r\n *  dividedToIntegerBy        divToInt\r\n *  equals                    eq\r\n *  floor\r\n *  greaterThan               gt\r\n *  greaterThanOrEqualTo      gte\r\n *  hyperbolicCosine          cosh\r\n *  hyperbolicSine            sinh\r\n *  hyperbolicTangent         tanh\r\n *  inverseCosine             acos\r\n *  inverseHyperbolicCosine   acosh\r\n *  inverseHyperbolicSine     asinh\r\n *  inverseHyperbolicTangent  atanh\r\n *  inverseSine               asin\r\n *  inverseTangent            atan\r\n *  isFinite\r\n *  isInteger                 isInt\r\n *  isNaN\r\n *  isNegative                isNeg\r\n *  isPositive                isPos\r\n *  isZero\r\n *  lessThan                  lt\r\n *  lessThanOrEqualTo         lte\r\n *  logarithm                 log\r\n *  [maximum]                 [max]\r\n *  [minimum]                 [min]\r\n *  minus                     sub\r\n *  modulo                    mod\r\n *  naturalExponential        exp\r\n *  naturalLogarithm          ln\r\n *  negated                   neg\r\n *  plus                      add\r\n *  precision                 sd\r\n *  round\r\n *  sine                      sin\r\n *  squareRoot                sqrt\r\n *  tangent                   tan\r\n *  times                     mul\r\n *  toBinary\r\n *  toDecimalPlaces           toDP\r\n *  toExponential\r\n *  toFixed\r\n *  toFraction\r\n *  toHexadecimal             toHex\r\n *  toNearest\r\n *  toNumber\r\n *  toOctal\r\n *  toPower                   pow\r\n *  toPrecision\r\n *  toSignificantDigits       toSD\r\n *  toString\r\n *  truncated                 trunc\r\n *  valueOf                   toJSON\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\r\nP.absoluteValue = P.abs = function () {\r\n  var x = new this.constructor(this);\r\n  if (x.s < 0) x.s = 1;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of positive Infinity.\r\n *\r\n */\r\nP.ceil = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal clamped to the range\r\n * delineated by `min` and `max`.\r\n *\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nP.clampedTo = P.clamp = function (min, max) {\r\n  var k,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n  min = new Ctor(min);\r\n  max = new Ctor(max);\r\n  if (!min.s || !max.s) return new Ctor(NaN);\r\n  if (min.gt(max)) throw Error(invalidArgument + max);\r\n  k = x.cmp(min);\r\n  return k < 0 ? min : x.cmp(max) > 0 ? max : new Ctor(x);\r\n};\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value,\r\n *   NaN  if the value of either Decimal is NaN.\r\n *\r\n */\r\nP.comparedTo = P.cmp = function (y) {\r\n  var i, j, xdL, ydL,\r\n    x = this,\r\n    xd = x.d,\r\n    yd = (y = new x.constructor(y)).d,\r\n    xs = x.s,\r\n    ys = y.s;\r\n\r\n  // Either NaN or ±Infinity?\r\n  if (!xd || !yd) {\r\n    return !xs || !ys ? NaN : xs !== ys ? xs : xd === yd ? 0 : !xd ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Either zero?\r\n  if (!xd[0] || !yd[0]) return xd[0] ? xs : yd[0] ? -ys : 0;\r\n\r\n  // Signs differ?\r\n  if (xs !== ys) return xs;\r\n\r\n  // Compare exponents.\r\n  if (x.e !== y.e) return x.e > y.e ^ xs < 0 ? 1 : -1;\r\n\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n    if (xd[i] !== yd[i]) return xd[i] > yd[i] ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return xdL === ydL ? 0 : xdL > ydL ^ xs < 0 ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * cos(0)         = 1\r\n * cos(-0)        = 1\r\n * cos(Infinity)  = NaN\r\n * cos(-Infinity) = NaN\r\n * cos(NaN)       = NaN\r\n *\r\n */\r\nP.cosine = P.cos = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.d) return new Ctor(NaN);\r\n\r\n  // cos(0) = cos(-0) = 1\r\n  if (!x.d[0]) return new Ctor(1);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = cosine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 3 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *\r\n * Return a new Decimal whose value is the cube root of the value of this Decimal, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n *  cbrt(0)  =  0\r\n *  cbrt(-0) = -0\r\n *  cbrt(1)  =  1\r\n *  cbrt(-1) = -1\r\n *  cbrt(N)  =  N\r\n *  cbrt(-I) = -I\r\n *  cbrt(I)  =  I\r\n *\r\n * Math.cbrt(x) = (x < 0 ? -Math.pow(-x, 1/3) : Math.pow(x, 1/3))\r\n *\r\n */\r\nP.cubeRoot = P.cbrt = function () {\r\n  var e, m, n, r, rep, s, sd, t, t3, t3plusx,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = x.s * mathpow(x.s * x, 1 / 3);\r\n\r\n   // Math.cbrt underflow/overflow?\r\n   // Pass x to Math.pow as integer, then adjust the exponent of the result.\r\n  if (!s || Math.abs(s) == 1 / 0) {\r\n    n = digitsToString(x.d);\r\n    e = x.e;\r\n\r\n    // Adjust n exponent so it is a multiple of 3 away from x exponent.\r\n    if (s = (e - n.length + 1) % 3) n += (s == 1 || s == -2 ? '0' : '00');\r\n    s = mathpow(n, 1 / 3);\r\n\r\n    // Rarely, e may be one less than the result exponent value.\r\n    e = mathfloor((e + 1) / 3) - (e % 3 == (e < 0 ? -1 : 2));\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n    r.s = x.s;\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Halley's method.\r\n  // TODO? Compare Newton's method.\r\n  for (;;) {\r\n    t = r;\r\n    t3 = t.times(t).times(t);\r\n    t3plusx = t3.plus(x);\r\n    r = divide(t3plusx.plus(x).times(t), t3plusx.plus(t3), sd + 2, 1);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or 4999\r\n      // , i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\r\nP.decimalPlaces = P.dp = function () {\r\n  var w,\r\n    d = this.d,\r\n    n = NaN;\r\n\r\n  if (d) {\r\n    w = d.length - 1;\r\n    n = (w - mathfloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) n--;\r\n    if (n < 0) n = 0;\r\n  }\r\n\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n *  n / 0 = I\r\n *  n / N = N\r\n *  n / I = 0\r\n *  0 / n = 0\r\n *  0 / 0 = N\r\n *  0 / N = N\r\n *  0 / I = 0\r\n *  N / n = N\r\n *  N / 0 = N\r\n *  N / N = N\r\n *  N / I = N\r\n *  I / n = I\r\n *  I / 0 = I\r\n *  I / N = N\r\n *  I / I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedBy = P.div = function (y) {\r\n  return divide(this, new this.constructor(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedToIntegerBy = P.divToInt = function (y) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return finalise(divide(x, new Ctor(y), 0, 1, 1), Ctor.precision, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\r\nP.equals = P.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of negative Infinity.\r\n *\r\n */\r\nP.floor = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 3);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\r\nP.greaterThan = P.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\r\nP.greaterThanOrEqualTo = P.gte = function (y) {\r\n  var k = this.cmp(y);\r\n  return k == 1 || k === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [1, Infinity]\r\n *\r\n * cosh(x) = 1 + x^2/2! + x^4/4! + x^6/6! + ...\r\n *\r\n * cosh(0)         = 1\r\n * cosh(-0)        = 1\r\n * cosh(Infinity)  = Infinity\r\n * cosh(-Infinity) = Infinity\r\n * cosh(NaN)       = NaN\r\n *\r\n *  x        time taken (ms)   result\r\n * 1000      9                 9.8503555700852349694e+433\r\n * 10000     25                4.4034091128314607936e+4342\r\n * 100000    171               1.4033316802130615897e+43429\r\n * 1000000   3817              1.5166076984010437725e+434294\r\n * 10000000  abandoned after 2 minute wait\r\n *\r\n * TODO? Compare performance of cosh(x) = 0.5 * (exp(x) + exp(-x))\r\n *\r\n */\r\nP.hyperbolicCosine = P.cosh = function () {\r\n  var k, n, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    one = new Ctor(1);\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s ? 1 / 0 : NaN);\r\n  if (x.isZero()) return one;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  // Argument reduction: cos(4x) = 1 - 8cos^2(x) + 8cos^4(x) + 1\r\n  // i.e. cos(x) = 1 - cos^2(x/4)(8 - 8cos^2(x/4))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  // TODO? Estimation reused from cosine() and may not be optimal here.\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    n = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    n = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(n), new Ctor(1), true);\r\n\r\n  // Reverse argument reduction\r\n  var cosh2_x,\r\n    i = k,\r\n    d8 = new Ctor(8);\r\n  for (; i--;) {\r\n    cosh2_x = x.times(x);\r\n    x = one.minus(cosh2_x.times(d8.minus(cosh2_x.times(d8))));\r\n  }\r\n\r\n  return finalise(x, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * sinh(x) = x + x^3/3! + x^5/5! + x^7/7! + ...\r\n *\r\n * sinh(0)         = 0\r\n * sinh(-0)        = -0\r\n * sinh(Infinity)  = Infinity\r\n * sinh(-Infinity) = -Infinity\r\n * sinh(NaN)       = NaN\r\n *\r\n * x        time taken (ms)\r\n * 10       2 ms\r\n * 100      5 ms\r\n * 1000     14 ms\r\n * 10000    82 ms\r\n * 100000   886 ms            1.4033316802130615897e+43429\r\n * 200000   2613 ms\r\n * 300000   5407 ms\r\n * 400000   8824 ms\r\n * 500000   13026 ms          8.7080643612718084129e+217146\r\n * 1000000  48543 ms\r\n *\r\n * TODO? Compare performance of sinh(x) = 0.5 * (exp(x) - exp(-x))\r\n *\r\n */\r\nP.hyperbolicSine = P.sinh = function () {\r\n  var k, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n  } else {\r\n\r\n    // Alternative argument reduction: sinh(3x) = sinh(x)(3 + 4sinh^2(x))\r\n    // i.e. sinh(x) = sinh(x/3)(3 + 4sinh^2(x/3))\r\n    // 3 multiplications and 1 addition\r\n\r\n    // Argument reduction: sinh(5x) = sinh(x)(5 + sinh^2(x)(20 + 16sinh^2(x)))\r\n    // i.e. sinh(x) = sinh(x/5)(5 + sinh^2(x/5)(20 + 16sinh^2(x/5)))\r\n    // 4 multiplications and 2 additions\r\n\r\n    // Estimate the optimum number of times to use the argument reduction.\r\n    k = 1.4 * Math.sqrt(len);\r\n    k = k > 16 ? 16 : k | 0;\r\n\r\n    x = x.times(1 / tinyPow(5, k));\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n\r\n    // Reverse argument reduction\r\n    var sinh2_x,\r\n      d5 = new Ctor(5),\r\n      d16 = new Ctor(16),\r\n      d20 = new Ctor(20);\r\n    for (; k--;) {\r\n      sinh2_x = x.times(x);\r\n      x = x.times(d5.plus(sinh2_x.times(d16.times(sinh2_x).plus(d20))));\r\n    }\r\n  }\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * tanh(x) = sinh(x) / cosh(x)\r\n *\r\n * tanh(0)         = 0\r\n * tanh(-0)        = -0\r\n * tanh(Infinity)  = 1\r\n * tanh(-Infinity) = -1\r\n * tanh(NaN)       = NaN\r\n *\r\n */\r\nP.hyperbolicTangent = P.tanh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 7;\r\n  Ctor.rounding = 1;\r\n\r\n  return divide(x.sinh(), x.cosh(), Ctor.precision = pr, Ctor.rounding = rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine (inverse cosine) in radians of the value of\r\n * this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [0, pi]\r\n *\r\n * acos(x) = pi/2 - asin(x)\r\n *\r\n * acos(0)       = pi/2\r\n * acos(-0)      = pi/2\r\n * acos(1)       = 0\r\n * acos(-1)      = pi\r\n * acos(1/2)     = pi/3\r\n * acos(-1/2)    = 2*pi/3\r\n * acos(|x| > 1) = NaN\r\n * acos(NaN)     = NaN\r\n *\r\n */\r\nP.inverseCosine = P.acos = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    k = x.abs().cmp(1),\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n    return k === 0\r\n      // |x| is 1\r\n      ? x.isNeg() ? getPi(Ctor, pr, rm) : new Ctor(0)\r\n      // |x| > 1 or x is NaN\r\n      : new Ctor(NaN);\r\n  }\r\n\r\n  if (x.isZero()) return getPi(Ctor, pr + 4, rm).times(0.5);\r\n\r\n  // TODO? Special case acos(0.5) = pi/3 and acos(-0.5) = 2*pi/3\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  // See https://github.com/MikeMcl/decimal.js/pull/217\r\n  x = new Ctor(1).minus(x).div(x.plus(1)).sqrt().atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [1, Infinity]\r\n * Range: [0, Infinity]\r\n *\r\n * acosh(x) = ln(x + sqrt(x^2 - 1))\r\n *\r\n * acosh(x < 1)     = NaN\r\n * acosh(NaN)       = NaN\r\n * acosh(Infinity)  = Infinity\r\n * acosh(-Infinity) = NaN\r\n * acosh(0)         = NaN\r\n * acosh(-0)        = NaN\r\n * acosh(1)         = 0\r\n * acosh(-1)        = NaN\r\n *\r\n */\r\nP.inverseHyperbolicCosine = P.acosh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.lte(1)) return new Ctor(x.eq(1) ? 0 : NaN);\r\n  if (!x.isFinite()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(Math.abs(x.e), x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).minus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * asinh(x) = ln(x + sqrt(x^2 + 1))\r\n *\r\n * asinh(NaN)       = NaN\r\n * asinh(Infinity)  = Infinity\r\n * asinh(-Infinity) = -Infinity\r\n * asinh(0)         = 0\r\n * asinh(-0)        = -0\r\n *\r\n */\r\nP.inverseHyperbolicSine = P.asinh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 2 * Math.max(Math.abs(x.e), x.sd()) + 6;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).plus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * atanh(x) = 0.5 * ln((1 + x) / (1 - x))\r\n *\r\n * atanh(|x| > 1)   = NaN\r\n * atanh(NaN)       = NaN\r\n * atanh(Infinity)  = NaN\r\n * atanh(-Infinity) = NaN\r\n * atanh(0)         = 0\r\n * atanh(-0)        = -0\r\n * atanh(1)         = Infinity\r\n * atanh(-1)        = -Infinity\r\n *\r\n */\r\nP.inverseHyperbolicTangent = P.atanh = function () {\r\n  var pr, rm, wpr, xsd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.e >= 0) return new Ctor(x.abs().eq(1) ? x.s / 0 : x.isZero() ? x : NaN);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  xsd = x.sd();\r\n\r\n  if (Math.max(xsd, pr) < 2 * -x.e - 1) return finalise(new Ctor(x), pr, rm, true);\r\n\r\n  Ctor.precision = wpr = xsd - x.e;\r\n\r\n  x = divide(x.plus(1), new Ctor(1).minus(x), wpr + pr, 1);\r\n\r\n  Ctor.precision = pr + 4;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.ln();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(0.5);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine (inverse sine) in radians of the value of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * asin(x) = 2*atan(x/(1 + sqrt(1 - x^2)))\r\n *\r\n * asin(0)       = 0\r\n * asin(-0)      = -0\r\n * asin(1/2)     = pi/6\r\n * asin(-1/2)    = -pi/6\r\n * asin(1)       = pi/2\r\n * asin(-1)      = -pi/2\r\n * asin(|x| > 1) = NaN\r\n * asin(NaN)     = NaN\r\n *\r\n * TODO? Compare performance of Taylor series.\r\n *\r\n */\r\nP.inverseSine = P.asin = function () {\r\n  var halfPi, k,\r\n    pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  k = x.abs().cmp(1);\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n\r\n    // |x| is 1\r\n    if (k === 0) {\r\n      halfPi = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      halfPi.s = x.s;\r\n      return halfPi;\r\n    }\r\n\r\n    // |x| > 1 or x is NaN\r\n    return new Ctor(NaN);\r\n  }\r\n\r\n  // TODO? Special case asin(1/2) = pi/6 and asin(-1/2) = -pi/6\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.div(new Ctor(1).minus(x.times(x)).sqrt().plus(1)).atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent (inverse tangent) in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n *\r\n * atan(0)         = 0\r\n * atan(-0)        = -0\r\n * atan(1)         = pi/4\r\n * atan(-1)        = -pi/4\r\n * atan(Infinity)  = pi/2\r\n * atan(-Infinity) = -pi/2\r\n * atan(NaN)       = NaN\r\n *\r\n */\r\nP.inverseTangent = P.atan = function () {\r\n  var i, j, k, n, px, t, r, wpr, x2,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (!x.isFinite()) {\r\n    if (!x.s) return new Ctor(NaN);\r\n    if (pr + 4 <= PI_PRECISION) {\r\n      r = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      r.s = x.s;\r\n      return r;\r\n    }\r\n  } else if (x.isZero()) {\r\n    return new Ctor(x);\r\n  } else if (x.abs().eq(1) && pr + 4 <= PI_PRECISION) {\r\n    r = getPi(Ctor, pr + 4, rm).times(0.25);\r\n    r.s = x.s;\r\n    return r;\r\n  }\r\n\r\n  Ctor.precision = wpr = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  // TODO? if (x >= 1 && pr <= PI_PRECISION) atan(x) = halfPi * x.s - atan(1 / x);\r\n\r\n  // Argument reduction\r\n  // Ensure |x| < 0.42\r\n  // atan(x) = 2 * atan(x / (1 + sqrt(1 + x^2)))\r\n\r\n  k = Math.min(28, wpr / LOG_BASE + 2 | 0);\r\n\r\n  for (i = k; i; --i) x = x.div(x.times(x).plus(1).sqrt().plus(1));\r\n\r\n  external = false;\r\n\r\n  j = Math.ceil(wpr / LOG_BASE);\r\n  n = 1;\r\n  x2 = x.times(x);\r\n  r = new Ctor(x);\r\n  px = x;\r\n\r\n  // atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n  for (; i !== -1;) {\r\n    px = px.times(x2);\r\n    t = r.minus(px.div(n += 2));\r\n\r\n    px = px.times(x2);\r\n    r = t.plus(px.div(n += 2));\r\n\r\n    if (r.d[j] !== void 0) for (i = j; r.d[i] === t.d[i] && i--;);\r\n  }\r\n\r\n  if (k) r = r.times(2 << (k - 1));\r\n\r\n  external = true;\r\n\r\n  return finalise(r, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is a finite number, otherwise return false.\r\n *\r\n */\r\nP.isFinite = function () {\r\n  return !!this.d;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\r\nP.isInteger = P.isInt = function () {\r\n  return !!this.d && mathfloor(this.e / LOG_BASE) > this.d.length - 2;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is NaN, otherwise return false.\r\n *\r\n */\r\nP.isNaN = function () {\r\n  return !this.s;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\r\nP.isNegative = P.isNeg = function () {\r\n  return this.s < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\r\nP.isPositive = P.isPos = function () {\r\n  return this.s > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is 0 or -0, otherwise return false.\r\n *\r\n */\r\nP.isZero = function () {\r\n  return !!this.d && this.d[0] === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThan = P.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThanOrEqualTo = P.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * If no base is specified, return log[10](arg).\r\n *\r\n * log[base](arg) = ln(arg) / ln(base)\r\n *\r\n * The result will always be correctly rounded if the base of the log is 10, and 'almost always'\r\n * otherwise:\r\n *\r\n * Depending on the rounding mode, the result may be incorrectly rounded if the first fifteen\r\n * rounding digits are [49]99999999999999 or [50]00000000000000. In that case, the maximum error\r\n * between the result and the correctly rounded result will be one ulp (unit in the last place).\r\n *\r\n * log[-b](a)       = NaN\r\n * log[0](a)        = NaN\r\n * log[1](a)        = NaN\r\n * log[NaN](a)      = NaN\r\n * log[Infinity](a) = NaN\r\n * log[b](0)        = -Infinity\r\n * log[b](-0)       = -Infinity\r\n * log[b](-a)       = NaN\r\n * log[b](1)        = 0\r\n * log[b](Infinity) = Infinity\r\n * log[b](NaN)      = NaN\r\n *\r\n * [base] {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nP.logarithm = P.log = function (base) {\r\n  var isBase10, d, denominator, k, inf, num, sd, r,\r\n    arg = this,\r\n    Ctor = arg.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding,\r\n    guard = 5;\r\n\r\n  // Default base is 10.\r\n  if (base == null) {\r\n    base = new Ctor(10);\r\n    isBase10 = true;\r\n  } else {\r\n    base = new Ctor(base);\r\n    d = base.d;\r\n\r\n    // Return NaN if base is negative, or non-finite, or is 0 or 1.\r\n    if (base.s < 0 || !d || !d[0] || base.eq(1)) return new Ctor(NaN);\r\n\r\n    isBase10 = base.eq(10);\r\n  }\r\n\r\n  d = arg.d;\r\n\r\n  // Is arg negative, non-finite, 0 or 1?\r\n  if (arg.s < 0 || !d || !d[0] || arg.eq(1)) {\r\n    return new Ctor(d && !d[0] ? -1 / 0 : arg.s != 1 ? NaN : d ? 0 : 1 / 0);\r\n  }\r\n\r\n  // The result will have a non-terminating decimal expansion if base is 10 and arg is not an\r\n  // integer power of 10.\r\n  if (isBase10) {\r\n    if (d.length > 1) {\r\n      inf = true;\r\n    } else {\r\n      for (k = d[0]; k % 10 === 0;) k /= 10;\r\n      inf = k !== 1;\r\n    }\r\n  }\r\n\r\n  external = false;\r\n  sd = pr + guard;\r\n  num = naturalLogarithm(arg, sd);\r\n  denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n\r\n  // The result will have 5 rounding digits.\r\n  r = divide(num, denominator, sd, 1);\r\n\r\n  // If at a rounding boundary, i.e. the result's rounding digits are [49]9999 or [50]0000,\r\n  // calculate 10 further digits.\r\n  //\r\n  // If the result is known to have an infinite decimal expansion, repeat this until it is clear\r\n  // that the result is above or below the boundary. Otherwise, if after calculating the 10\r\n  // further digits, the last 14 are nines, round up and assume the result is exact.\r\n  // Also assume the result is exact if the last 14 are zero.\r\n  //\r\n  // Example of a result that will be incorrectly rounded:\r\n  // log[1048576](4503599627370502) = 2.60000000000000009610279511444746...\r\n  // The above result correctly rounded using ROUND_CEIL to 1 decimal place should be 2.7, but it\r\n  // will be given as 2.6 as there are 15 zeros immediately after the requested decimal place, so\r\n  // the exact result would be assumed to be 2.6, which rounded using ROUND_CEIL to 1 decimal\r\n  // place is still 2.6.\r\n  if (checkRoundingDigits(r.d, k = pr, rm)) {\r\n\r\n    do {\r\n      sd += 10;\r\n      num = naturalLogarithm(arg, sd);\r\n      denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n      r = divide(num, denominator, sd, 1);\r\n\r\n      if (!inf) {\r\n\r\n        // Check for 14 nines from the 2nd rounding digit, as the first may be 4.\r\n        if (+digitsToString(r.d).slice(k + 1, k + 15) + 1 == 1e14) {\r\n          r = finalise(r, pr + 1, 0);\r\n        }\r\n\r\n        break;\r\n      }\r\n    } while (checkRoundingDigits(r.d, k += 10, rm));\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.max = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, -1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.min = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, 1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n *  n - 0 = n\r\n *  n - N = N\r\n *  n - I = -I\r\n *  0 - n = -n\r\n *  0 - 0 = 0\r\n *  0 - N = N\r\n *  0 - I = -I\r\n *  N - n = N\r\n *  N - 0 = N\r\n *  N - N = N\r\n *  N - I = N\r\n *  I - n = I\r\n *  I - 0 = I\r\n *  I - N = N\r\n *  I - I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var d, e, i, j, k, len, pr, rm, xd, xe, xLTy, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return y negated if x is finite and y is ±Infinity.\r\n    else if (x.d) y.s = -y.s;\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with different signs.\r\n    // Return NaN if both are ±Infinity with the same sign.\r\n    else y = new Ctor(y.d || x.s !== y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n  // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.plus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return y negated if x is zero and y is non-zero.\r\n    if (yd[0]) y.s = -y.s;\r\n\r\n    // Return x if y is zero and x is non-zero.\r\n    else if (xd[0]) y = new Ctor(x);\r\n\r\n    // Return zero if both are zero.\r\n    // From IEEE 754 (2008) 6.3: 0 - 0 = -0 - -0 = -0 when rounding to -Infinity.\r\n    else return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  e = mathfloor(y.e / LOG_BASE);\r\n  xe = mathfloor(x.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  k = xe - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (k) {\r\n    xLTy = k < 0;\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      k = -k;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = xe;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Numbers with massively different exponents would result in a very high number of\r\n    // zeros needing to be prepended, but this can be avoided while still ensuring correct\r\n    // rounding by limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n    if (k > i) {\r\n      k = i;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents.\r\n    d.reverse();\r\n    for (i = k; i--;) d.push(0);\r\n    d.reverse();\r\n\r\n  // Base 1e7 exponents equal.\r\n  } else {\r\n\r\n    // Check digits to determine which is the bigger number.\r\n\r\n    i = xd.length;\r\n    len = yd.length;\r\n    xLTy = i < len;\r\n    if (xLTy) len = i;\r\n\r\n    for (i = 0; i < len; i++) {\r\n      if (xd[i] != yd[i]) {\r\n        xLTy = xd[i] < yd[i];\r\n        break;\r\n      }\r\n    }\r\n\r\n    k = 0;\r\n  }\r\n\r\n  if (xLTy) {\r\n    d = xd;\r\n    xd = yd;\r\n    yd = d;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  len = xd.length;\r\n\r\n  // Append zeros to `xd` if shorter.\r\n  // Don't add zeros to `yd` if shorter as subtraction only needs to start at `yd` length.\r\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n  // Subtract yd from xd.\r\n  for (i = yd.length; i > k;) {\r\n\r\n    if (xd[--i] < yd[i]) {\r\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n      --xd[j];\r\n      xd[i] += BASE;\r\n    }\r\n\r\n    xd[i] -= yd[i];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xd[--len] === 0;) xd.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n  // Zero?\r\n  if (!xd[0]) return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n *   n % 0 =  N\r\n *   n % N =  N\r\n *   n % I =  n\r\n *   0 % n =  0\r\n *  -0 % n = -0\r\n *   0 % 0 =  N\r\n *   0 % N =  N\r\n *   0 % I =  0\r\n *   N % n =  N\r\n *   N % 0 =  N\r\n *   N % N =  N\r\n *   N % I =  N\r\n *   I % n =  N\r\n *   I % 0 =  N\r\n *   I % N =  N\r\n *   I % I =  N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * The result depends on the modulo mode.\r\n *\r\n */\r\nP.modulo = P.mod = function (y) {\r\n  var q,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // Return NaN if x is ±Infinity or NaN, or y is NaN or ±0.\r\n  if (!x.d || !y.s || y.d && !y.d[0]) return new Ctor(NaN);\r\n\r\n  // Return x if y is ±Infinity or x is ±0.\r\n  if (!y.d || x.d && !x.d[0]) {\r\n    return finalise(new Ctor(x), Ctor.precision, Ctor.rounding);\r\n  }\r\n\r\n  // Prevent rounding of intermediate calculations.\r\n  external = false;\r\n\r\n  if (Ctor.modulo == 9) {\r\n\r\n    // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n    // result = x - q * y    where  0 <= result < abs(y)\r\n    q = divide(x, y.abs(), 0, 3, 1);\r\n    q.s *= y.s;\r\n  } else {\r\n    q = divide(x, y, 0, Ctor.modulo, 1);\r\n  }\r\n\r\n  q = q.times(y);\r\n\r\n  external = true;\r\n\r\n  return x.minus(q);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalExponential = P.exp = function () {\r\n  return naturalExponential(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalLogarithm = P.ln = function () {\r\n  return naturalLogarithm(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\r\nP.negated = P.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n *  n + 0 = n\r\n *  n + N = N\r\n *  n + I = I\r\n *  0 + n = n\r\n *  0 + 0 = 0\r\n *  0 + N = N\r\n *  0 + I = I\r\n *  N + n = N\r\n *  N + 0 = N\r\n *  N + N = N\r\n *  N + I = N\r\n *  I + n = I\r\n *  I + 0 = I\r\n *  I + N = N\r\n *  I + I = I\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.plus = P.add = function (y) {\r\n  var carry, d, e, i, k, len, pr, rm, xd, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with the same sign.\r\n    // Return NaN if both are ±Infinity with different signs.\r\n    // Return y if x is finite and y is ±Infinity.\r\n    else if (!x.d) y = new Ctor(y.d || x.s === y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n   // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return x if y is zero.\r\n    // Return y if y is non-zero.\r\n    if (!yd[0]) y = new Ctor(x);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  k = mathfloor(x.e / LOG_BASE);\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  i = k - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (i) {\r\n\r\n    if (i < 0) {\r\n      d = xd;\r\n      i = -i;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = k;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n    k = Math.ceil(pr / LOG_BASE);\r\n    len = k > len ? k + 1 : len + 1;\r\n\r\n    if (i > len) {\r\n      i = len;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n    d.reverse();\r\n    for (; i--;) d.push(0);\r\n    d.reverse();\r\n  }\r\n\r\n  len = xd.length;\r\n  i = yd.length;\r\n\r\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n  if (len - i < 0) {\r\n    i = len;\r\n    d = yd;\r\n    yd = xd;\r\n    xd = d;\r\n  }\r\n\r\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n  for (carry = 0; i;) {\r\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n    xd[i] %= BASE;\r\n  }\r\n\r\n  if (carry) {\r\n    xd.unshift(carry);\r\n    ++e;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\r\nP.precision = P.sd = function (z) {\r\n  var k,\r\n    x = this;\r\n\r\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n  if (x.d) {\r\n    k = getPrecision(x.d);\r\n    if (z && x.e + 1 > k) k = x.e + 1;\r\n  } else {\r\n    k = NaN;\r\n  }\r\n\r\n  return k;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\r\nP.round = function () {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  return finalise(new Ctor(x), x.e + 1, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n *\r\n * sin(0)         = 0\r\n * sin(-0)        = -0\r\n * sin(Infinity)  = NaN\r\n * sin(-Infinity) = NaN\r\n * sin(NaN)       = NaN\r\n *\r\n */\r\nP.sine = P.sin = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = sine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant > 2 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n *  sqrt(-n) =  N\r\n *  sqrt(N)  =  N\r\n *  sqrt(-I) =  N\r\n *  sqrt(I)  =  I\r\n *  sqrt(0)  =  0\r\n *  sqrt(-0) = -0\r\n *\r\n */\r\nP.squareRoot = P.sqrt = function () {\r\n  var m, n, sd, r, rep, t,\r\n    x = this,\r\n    d = x.d,\r\n    e = x.e,\r\n    s = x.s,\r\n    Ctor = x.constructor;\r\n\r\n  // Negative/NaN/Infinity/zero?\r\n  if (s !== 1 || !d || !d[0]) {\r\n    return new Ctor(!s || s < 0 && (!d || d[0]) ? NaN : d ? x : 1 / 0);\r\n  }\r\n\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = Math.sqrt(+x);\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n  if (s == 0 || s == 1 / 0) {\r\n    n = digitsToString(d);\r\n\r\n    if ((n.length + e) % 2 == 0) n += '0';\r\n    s = Math.sqrt(n);\r\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Newton-Raphson iteration.\r\n  for (;;) {\r\n    t = r;\r\n    r = t.plus(divide(x, t, sd + 2, 1)).times(0.5);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * tan(0)         = 0\r\n * tan(-0)        = -0\r\n * tan(Infinity)  = NaN\r\n * tan(-Infinity) = NaN\r\n * tan(NaN)       = NaN\r\n *\r\n */\r\nP.tangent = P.tan = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.sin();\r\n  x.s = 1;\r\n  x = divide(x, new Ctor(1).minus(x.times(x)).sqrt(), pr + 10, 0);\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 4 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *  n * 0 = 0\r\n *  n * N = N\r\n *  n * I = I\r\n *  0 * n = 0\r\n *  0 * 0 = 0\r\n *  0 * N = N\r\n *  0 * I = N\r\n *  N * n = N\r\n *  N * 0 = N\r\n *  N * N = N\r\n *  N * I = N\r\n *  I * n = I\r\n *  I * 0 = N\r\n *  I * N = N\r\n *  I * I = I\r\n *\r\n * Return a new Decimal whose value is this Decimal times `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.times = P.mul = function (y) {\r\n  var carry, e, i, k, r, rL, t, xdL, ydL,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    xd = x.d,\r\n    yd = (y = new Ctor(y)).d;\r\n\r\n  y.s *= x.s;\r\n\r\n   // If either is NaN, ±Infinity or ±0...\r\n  if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n    return new Ctor(!y.s || xd && !xd[0] && !yd || yd && !yd[0] && !xd\r\n\r\n      // Return NaN if either is NaN.\r\n      // Return NaN if x is ±0 and y is ±Infinity, or y is ±0 and x is ±Infinity.\r\n      ? NaN\r\n\r\n      // Return ±Infinity if either is ±Infinity.\r\n      // Return ±0 if either is ±0.\r\n      : !xd || !yd ? y.s / 0 : y.s * 0);\r\n  }\r\n\r\n  e = mathfloor(x.e / LOG_BASE) + mathfloor(y.e / LOG_BASE);\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Ensure xd points to the longer array.\r\n  if (xdL < ydL) {\r\n    r = xd;\r\n    xd = yd;\r\n    yd = r;\r\n    rL = xdL;\r\n    xdL = ydL;\r\n    ydL = rL;\r\n  }\r\n\r\n  // Initialise the result array with zeros.\r\n  r = [];\r\n  rL = xdL + ydL;\r\n  for (i = rL; i--;) r.push(0);\r\n\r\n  // Multiply!\r\n  for (i = ydL; --i >= 0;) {\r\n    carry = 0;\r\n    for (k = xdL + i; k > i;) {\r\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n      r[k--] = t % BASE | 0;\r\n      carry = t / BASE | 0;\r\n    }\r\n\r\n    r[k] = (r[k] + carry) % BASE | 0;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; !r[--rL];) r.pop();\r\n\r\n  if (carry) ++e;\r\n  else r.shift();\r\n\r\n  y.d = r;\r\n  y.e = getBase10Exponent(r, e);\r\n\r\n  return external ? finalise(y, Ctor.precision, Ctor.rounding) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 2, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toBinary = function (sd, rm) {\r\n  return toStringBinary(this, 2, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toDecimalPlaces = P.toDP = function (dp, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n  if (dp === void 0) return x;\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  return finalise(x, dp + x.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x, true);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), dp + 1, rm);\r\n    str = finiteToString(x, true, dp + 1);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var str, y,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = finalise(new Ctor(x), dp + x.e + 1, rm);\r\n    str = finiteToString(y, false, dp + y.e + 1);\r\n  }\r\n\r\n  // To determine whether to add the minus sign look at the value before it was rounded,\r\n  // i.e. look at `x` rather than `y`.\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return an array representing the value of this Decimal as a simple fraction with an integer\r\n * numerator and an integer denominator.\r\n *\r\n * The denominator will be a positive non-zero value less than or equal to the specified maximum\r\n * denominator. If a maximum denominator is not specified, the denominator will be the lowest\r\n * value necessary to represent the number exactly.\r\n *\r\n * [maxD] {number|string|bigint|Decimal} Maximum denominator. Integer >= 1 and < Infinity.\r\n *\r\n */\r\nP.toFraction = function (maxD) {\r\n  var d, d0, d1, d2, e, k, n, n0, n1, pr, q, r,\r\n    x = this,\r\n    xd = x.d,\r\n    Ctor = x.constructor;\r\n\r\n  if (!xd) return new Ctor(x);\r\n\r\n  n1 = d0 = new Ctor(1);\r\n  d1 = n0 = new Ctor(0);\r\n\r\n  d = new Ctor(d1);\r\n  e = d.e = getPrecision(xd) - x.e - 1;\r\n  k = e % LOG_BASE;\r\n  d.d[0] = mathpow(10, k < 0 ? LOG_BASE + k : k);\r\n\r\n  if (maxD == null) {\r\n\r\n    // d is 10**e, the minimum max-denominator needed.\r\n    maxD = e > 0 ? d : n1;\r\n  } else {\r\n    n = new Ctor(maxD);\r\n    if (!n.isInt() || n.lt(n1)) throw Error(invalidArgument + n);\r\n    maxD = n.gt(d) ? (e > 0 ? d : n1) : n;\r\n  }\r\n\r\n  external = false;\r\n  n = new Ctor(digitsToString(xd));\r\n  pr = Ctor.precision;\r\n  Ctor.precision = e = xd.length * LOG_BASE * 2;\r\n\r\n  for (;;)  {\r\n    q = divide(n, d, 0, 1, 1);\r\n    d2 = d0.plus(q.times(d1));\r\n    if (d2.cmp(maxD) == 1) break;\r\n    d0 = d1;\r\n    d1 = d2;\r\n    d2 = n1;\r\n    n1 = n0.plus(q.times(d2));\r\n    n0 = d2;\r\n    d2 = d;\r\n    d = n.minus(q.times(d2));\r\n    n = d2;\r\n  }\r\n\r\n  d2 = divide(maxD.minus(d0), d1, 0, 1, 1);\r\n  n0 = n0.plus(d2.times(n1));\r\n  d0 = d0.plus(d2.times(d1));\r\n  n0.s = n1.s = x.s;\r\n\r\n  // Determine which fraction is closer to x, n0/d0 or n1/d1?\r\n  r = divide(n1, d1, e, 1).minus(x).abs().cmp(divide(n0, d0, e, 1).minus(x).abs()) < 1\r\n      ? [n1, d1] : [n0, d0];\r\n\r\n  Ctor.precision = pr;\r\n  external = true;\r\n\r\n  return r;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 16, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toHexadecimal = P.toHex = function (sd, rm) {\r\n  return toStringBinary(this, 16, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Returns a new Decimal whose value is the nearest multiple of `y` in the direction of rounding\r\n * mode `rm`, or `Decimal.rounding` if `rm` is omitted, to the value of this Decimal.\r\n *\r\n * The return value will always have the same sign as this Decimal, unless either this Decimal\r\n * or `y` is NaN, in which case the return value will be also be NaN.\r\n *\r\n * The return value is not affected by the value of `precision`.\r\n *\r\n * y {number|string|bigint|Decimal} The magnitude to round to a multiple of.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toNearest() rounding mode not an integer: {rm}'\r\n * 'toNearest() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toNearest = function (y, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (y == null) {\r\n\r\n    // If x is not finite, return x.\r\n    if (!x.d) return x;\r\n\r\n    y = new Ctor(1);\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    y = new Ctor(y);\r\n    if (rm === void 0) {\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    // If x is not finite, return x if y is not NaN, else NaN.\r\n    if (!x.d) return y.s ? x : y;\r\n\r\n    // If y is not finite, return Infinity with the sign of x if y is Infinity, else NaN.\r\n    if (!y.d) {\r\n      if (y.s) y.s = x.s;\r\n      return y;\r\n    }\r\n  }\r\n\r\n  // If y is not zero, calculate the nearest multiple of y to x.\r\n  if (y.d[0]) {\r\n    external = false;\r\n    x = divide(x, y, 0, rm, 1).times(y);\r\n    external = true;\r\n    finalise(x);\r\n\r\n  // If y is zero, return zero with the sign of x.\r\n  } else {\r\n    y.s = x.s;\r\n    x = y;\r\n  }\r\n\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n * Zero keeps its sign.\r\n *\r\n */\r\nP.toNumber = function () {\r\n  return +this;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 8, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toOctal = function (sd, rm) {\r\n  return toStringBinary(this, 8, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`, rounded\r\n * to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * ECMAScript compliant.\r\n *\r\n *   pow(x, NaN)                           = NaN\r\n *   pow(x, ±0)                            = 1\r\n\r\n *   pow(NaN, non-zero)                    = NaN\r\n *   pow(abs(x) > 1, +Infinity)            = +Infinity\r\n *   pow(abs(x) > 1, -Infinity)            = +0\r\n *   pow(abs(x) == 1, ±Infinity)           = NaN\r\n *   pow(abs(x) < 1, +Infinity)            = +0\r\n *   pow(abs(x) < 1, -Infinity)            = +Infinity\r\n *   pow(+Infinity, y > 0)                 = +Infinity\r\n *   pow(+Infinity, y < 0)                 = +0\r\n *   pow(-Infinity, odd integer > 0)       = -Infinity\r\n *   pow(-Infinity, even integer > 0)      = +Infinity\r\n *   pow(-Infinity, odd integer < 0)       = -0\r\n *   pow(-Infinity, even integer < 0)      = +0\r\n *   pow(+0, y > 0)                        = +0\r\n *   pow(+0, y < 0)                        = +Infinity\r\n *   pow(-0, odd integer > 0)              = -0\r\n *   pow(-0, even integer > 0)             = +0\r\n *   pow(-0, odd integer < 0)              = -Infinity\r\n *   pow(-0, even integer < 0)             = +Infinity\r\n *   pow(finite x < 0, finite non-integer) = NaN\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * Assuming the first 15 rounding digits are each equally likely to be any digit 0-9, the\r\n * probability of an incorrectly rounded result\r\n * P([49]9{14} | [50]0{14}) = 2 * 0.2 * 10^-14 = 4e-15 = 1/2.5e+14\r\n * i.e. 1 in 250,000,000,000,000\r\n *\r\n * If a result is incorrectly rounded the maximum error will be 1 ulp (unit in last place).\r\n *\r\n * y {number|string|bigint|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\r\nP.toPower = P.pow = function (y) {\r\n  var e, k, pr, r, rm, s,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    yn = +(y = new Ctor(y));\r\n\r\n  // Either ±Infinity, NaN or ±0?\r\n  if (!x.d || !y.d || !x.d[0] || !y.d[0]) return new Ctor(mathpow(+x, yn));\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (x.eq(1)) return x;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (y.eq(1)) return finalise(x, pr, rm);\r\n\r\n  // y exponent\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n  if (e >= y.d.length - 1 && (k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n    r = intPow(Ctor, x, k, pr);\r\n    return y.s < 0 ? new Ctor(1).div(r) : finalise(r, pr, rm);\r\n  }\r\n\r\n  s = x.s;\r\n\r\n  // if x is negative\r\n  if (s < 0) {\r\n\r\n    // if y is not an integer\r\n    if (e < y.d.length - 1) return new Ctor(NaN);\r\n\r\n    // Result is positive if x is negative and the last digit of integer y is even.\r\n    if ((y.d[e] & 1) == 0) s = 1;\r\n\r\n    // if x.eq(-1)\r\n    if (x.e == 0 && x.d[0] == 1 && x.d.length == 1) {\r\n      x.s = s;\r\n      return x;\r\n    }\r\n  }\r\n\r\n  // Estimate result exponent.\r\n  // x^y = 10^e,  where e = y * log10(x)\r\n  // log10(x) = log10(x_significand) + x_exponent\r\n  // log10(x_significand) = ln(x_significand) / ln(10)\r\n  k = mathpow(+x, yn);\r\n  e = k == 0 || !isFinite(k)\r\n    ? mathfloor(yn * (Math.log('0.' + digitsToString(x.d)) / Math.LN10 + x.e + 1))\r\n    : new Ctor(k + '').e;\r\n\r\n  // Exponent estimate may be incorrect e.g. x: 0.999999999999999999, y: 2.29, e: 0, r.e: -1.\r\n\r\n  // Overflow/underflow?\r\n  if (e > Ctor.maxE + 1 || e < Ctor.minE - 1) return new Ctor(e > 0 ? s / 0 : 0);\r\n\r\n  external = false;\r\n  Ctor.rounding = x.s = 1;\r\n\r\n  // Estimate the extra guard digits needed to ensure five correct rounding digits from\r\n  // naturalLogarithm(x). Example of failure without these extra digits (precision: 10):\r\n  // new Decimal(2.32456).pow('2087987436534566.46411')\r\n  // should be 1.162377823e+764914905173815, but is 1.162355823e+764914905173815\r\n  k = Math.min(12, (e + '').length);\r\n\r\n  // r = x^y = exp(y*ln(x))\r\n  r = naturalExponential(y.times(naturalLogarithm(x, pr + k)), pr);\r\n\r\n  // r may be Infinity, e.g. (0.9999999999999999).pow(-1e+40)\r\n  if (r.d) {\r\n\r\n    // Truncate to the required precision plus five rounding digits.\r\n    r = finalise(r, pr + 5, 1);\r\n\r\n    // If the rounding digits are [49]9999 or [50]0000 increase the precision by 10 and recalculate\r\n    // the result.\r\n    if (checkRoundingDigits(r.d, pr, rm)) {\r\n      e = pr + 10;\r\n\r\n      // Truncate to the increased precision plus five rounding digits.\r\n      r = finalise(naturalExponential(y.times(naturalLogarithm(x, e + k)), e), e + 5, 1);\r\n\r\n      // Check for 14 nines from the 2nd rounding digit (the first rounding digit may be 4 or 9).\r\n      if (+digitsToString(r.d).slice(pr + 1, pr + 15) + 1 == 1e14) {\r\n        r = finalise(r, pr + 1, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  r.s = s;\r\n  external = true;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), sd, rm);\r\n    str = finiteToString(x, sd <= x.e || x.e <= Ctor.toExpNeg, sd);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toSD() digits out of range: {sd}'\r\n * 'toSD() digits not an integer: {sd}'\r\n * 'toSD() rounding mode not an integer: {rm}'\r\n * 'toSD() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toSignificantDigits = P.toSD = function (sd, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  }\r\n\r\n  return finalise(new Ctor(x), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\r\nP.toString = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal truncated to a whole number.\r\n *\r\n */\r\nP.truncated = P.trunc = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 1);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n * Unlike `toString`, negative zero will include the minus sign.\r\n *\r\n */\r\nP.valueOf = P.toJSON = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() ? '-' + str : str;\r\n};\r\n\r\n\r\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n/*\r\n *  digitsToString           P.cubeRoot, P.logarithm, P.squareRoot, P.toFraction, P.toPower,\r\n *                           finiteToString, naturalExponential, naturalLogarithm\r\n *  checkInt32               P.toDecimalPlaces, P.toExponential, P.toFixed, P.toNearest,\r\n *                           P.toPrecision, P.toSignificantDigits, toStringBinary, random\r\n *  checkRoundingDigits      P.logarithm, P.toPower, naturalExponential, naturalLogarithm\r\n *  convertBase              toStringBinary, parseOther\r\n *  cos                      P.cos\r\n *  divide                   P.atanh, P.cubeRoot, P.dividedBy, P.dividedToIntegerBy,\r\n *                           P.logarithm, P.modulo, P.squareRoot, P.tan, P.tanh, P.toFraction,\r\n *                           P.toNearest, toStringBinary, naturalExponential, naturalLogarithm,\r\n *                           taylorSeries, atan2, parseOther\r\n *  finalise                 P.absoluteValue, P.atan, P.atanh, P.ceil, P.cos, P.cosh,\r\n *                           P.cubeRoot, P.dividedToIntegerBy, P.floor, P.logarithm, P.minus,\r\n *                           P.modulo, P.negated, P.plus, P.round, P.sin, P.sinh, P.squareRoot,\r\n *                           P.tan, P.times, P.toDecimalPlaces, P.toExponential, P.toFixed,\r\n *                           P.toNearest, P.toPower, P.toPrecision, P.toSignificantDigits,\r\n *                           P.truncated, divide, getLn10, getPi, naturalExponential,\r\n *                           naturalLogarithm, ceil, floor, round, trunc\r\n *  finiteToString           P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf,\r\n *                           toStringBinary\r\n *  getBase10Exponent        P.minus, P.plus, P.times, parseOther\r\n *  getLn10                  P.logarithm, naturalLogarithm\r\n *  getPi                    P.acos, P.asin, P.atan, toLessThanHalfPi, atan2\r\n *  getPrecision             P.precision, P.toFraction\r\n *  getZeroString            digitsToString, finiteToString\r\n *  intPow                   P.toPower, parseOther\r\n *  isOdd                    toLessThanHalfPi\r\n *  maxOrMin                 max, min\r\n *  naturalExponential       P.naturalExponential, P.toPower\r\n *  naturalLogarithm         P.acosh, P.asinh, P.atanh, P.logarithm, P.naturalLogarithm,\r\n *                           P.toPower, naturalExponential\r\n *  nonFiniteToString        finiteToString, toStringBinary\r\n *  parseDecimal             Decimal\r\n *  parseOther               Decimal\r\n *  sin                      P.sin\r\n *  taylorSeries             P.cosh, P.sinh, cos, sin\r\n *  toLessThanHalfPi         P.cos, P.sin\r\n *  toStringBinary           P.toBinary, P.toHexadecimal, P.toOctal\r\n *  truncate                 intPow\r\n *\r\n *  Throws:                  P.logarithm, P.precision, P.toFraction, checkInt32, getLn10, getPi,\r\n *                           naturalLogarithm, config, parseOther, random, Decimal\r\n */\r\n\r\n\r\nfunction digitsToString(d) {\r\n  var i, k, ws,\r\n    indexOfLastWord = d.length - 1,\r\n    str = '',\r\n    w = d[0];\r\n\r\n  if (indexOfLastWord > 0) {\r\n    str += w;\r\n    for (i = 1; i < indexOfLastWord; i++) {\r\n      ws = d[i] + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n      str += ws;\r\n    }\r\n\r\n    w = d[i];\r\n    ws = w + '';\r\n    k = LOG_BASE - ws.length;\r\n    if (k) str += getZeroString(k);\r\n  } else if (w === 0) {\r\n    return '0';\r\n  }\r\n\r\n  // Remove trailing zeros of last w.\r\n  for (; w % 10 === 0;) w /= 10;\r\n\r\n  return str + w;\r\n}\r\n\r\n\r\nfunction checkInt32(i, min, max) {\r\n  if (i !== ~~i || i < min || i > max) {\r\n    throw Error(invalidArgument + i);\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Check 5 rounding digits if `repeating` is null, 4 otherwise.\r\n * `repeating == null` if caller is `log` or `pow`,\r\n * `repeating != null` if caller is `naturalLogarithm` or `naturalExponential`.\r\n */\r\nfunction checkRoundingDigits(d, i, rm, repeating) {\r\n  var di, k, r, rd;\r\n\r\n  // Get the length of the first word of the array d.\r\n  for (k = d[0]; k >= 10; k /= 10) --i;\r\n\r\n  // Is the rounding digit in the first word of d?\r\n  if (--i < 0) {\r\n    i += LOG_BASE;\r\n    di = 0;\r\n  } else {\r\n    di = Math.ceil((i + 1) / LOG_BASE);\r\n    i %= LOG_BASE;\r\n  }\r\n\r\n  // i is the index (0 - 6) of the rounding digit.\r\n  // E.g. if within the word 3487563 the first rounding digit is 5,\r\n  // then i = 4, k = 1000, rd = 3487563 % 1000 = 563\r\n  k = mathpow(10, LOG_BASE - i);\r\n  rd = d[di] % k | 0;\r\n\r\n  if (repeating == null) {\r\n    if (i < 3) {\r\n      if (i == 0) rd = rd / 100 | 0;\r\n      else if (i == 1) rd = rd / 10 | 0;\r\n      r = rm < 4 && rd == 99999 || rm > 3 && rd == 49999 || rd == 50000 || rd == 0;\r\n    } else {\r\n      r = (rm < 4 && rd + 1 == k || rm > 3 && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 100 | 0) == mathpow(10, i - 2) - 1 ||\r\n          (rd == k / 2 || rd == 0) && (d[di + 1] / k / 100 | 0) == 0;\r\n    }\r\n  } else {\r\n    if (i < 4) {\r\n      if (i == 0) rd = rd / 1000 | 0;\r\n      else if (i == 1) rd = rd / 100 | 0;\r\n      else if (i == 2) rd = rd / 10 | 0;\r\n      r = (repeating || rm < 4) && rd == 9999 || !repeating && rm > 3 && rd == 4999;\r\n    } else {\r\n      r = ((repeating || rm < 4) && rd + 1 == k ||\r\n      (!repeating && rm > 3) && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 1000 | 0) == mathpow(10, i - 3) - 1;\r\n    }\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n// Convert string of `baseIn` to an array of numbers of `baseOut`.\r\n// Eg. convertBase('255', 10, 16) returns [15, 15].\r\n// Eg. convertBase('ff', 16, 10) returns [2, 5, 5].\r\nfunction convertBase(str, baseIn, baseOut) {\r\n  var j,\r\n    arr = [0],\r\n    arrL,\r\n    i = 0,\r\n    strL = str.length;\r\n\r\n  for (; i < strL;) {\r\n    for (arrL = arr.length; arrL--;) arr[arrL] *= baseIn;\r\n    arr[0] += NUMERALS.indexOf(str.charAt(i++));\r\n    for (j = 0; j < arr.length; j++) {\r\n      if (arr[j] > baseOut - 1) {\r\n        if (arr[j + 1] === void 0) arr[j + 1] = 0;\r\n        arr[j + 1] += arr[j] / baseOut | 0;\r\n        arr[j] %= baseOut;\r\n      }\r\n    }\r\n  }\r\n\r\n  return arr.reverse();\r\n}\r\n\r\n\r\n/*\r\n * cos(x) = 1 - x^2/2! + x^4/4! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction cosine(Ctor, x) {\r\n  var k, len, y;\r\n\r\n  if (x.isZero()) return x;\r\n\r\n  // Argument reduction: cos(4x) = 8*(cos^4(x) - cos^2(x)) + 1\r\n  // i.e. cos(x) = 8*(cos^4(x/4) - cos^2(x/4)) + 1\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  len = x.d.length;\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    y = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    y = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  Ctor.precision += k;\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(y), new Ctor(1));\r\n\r\n  // Reverse argument reduction\r\n  for (var i = k; i--;) {\r\n    var cos2x = x.times(x);\r\n    x = cos2x.times(cos2x).minus(cos2x).times(8).plus(1);\r\n  }\r\n\r\n  Ctor.precision -= k;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Perform division in the specified base.\r\n */\r\nvar divide = (function () {\r\n\r\n  // Assumes non-zero x and k, and hence non-zero result.\r\n  function multiplyInteger(x, k, base) {\r\n    var temp,\r\n      carry = 0,\r\n      i = x.length;\r\n\r\n    for (x = x.slice(); i--;) {\r\n      temp = x[i] * k + carry;\r\n      x[i] = temp % base | 0;\r\n      carry = temp / base | 0;\r\n    }\r\n\r\n    if (carry) x.unshift(carry);\r\n\r\n    return x;\r\n  }\r\n\r\n  function compare(a, b, aL, bL) {\r\n    var i, r;\r\n\r\n    if (aL != bL) {\r\n      r = aL > bL ? 1 : -1;\r\n    } else {\r\n      for (i = r = 0; i < aL; i++) {\r\n        if (a[i] != b[i]) {\r\n          r = a[i] > b[i] ? 1 : -1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return r;\r\n  }\r\n\r\n  function subtract(a, b, aL, base) {\r\n    var i = 0;\r\n\r\n    // Subtract b from a.\r\n    for (; aL--;) {\r\n      a[aL] -= i;\r\n      i = a[aL] < b[aL] ? 1 : 0;\r\n      a[aL] = i * base + a[aL] - b[aL];\r\n    }\r\n\r\n    // Remove leading zeros.\r\n    for (; !a[0] && a.length > 1;) a.shift();\r\n  }\r\n\r\n  return function (x, y, pr, rm, dp, base) {\r\n    var cmp, e, i, k, logBase, more, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0,\r\n      yL, yz,\r\n      Ctor = x.constructor,\r\n      sign = x.s == y.s ? 1 : -1,\r\n      xd = x.d,\r\n      yd = y.d;\r\n\r\n    // Either NaN, Infinity or 0?\r\n    if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n      return new Ctor(// Return NaN if either NaN, or both Infinity or 0.\r\n        !x.s || !y.s || (xd ? yd && xd[0] == yd[0] : !yd) ? NaN :\r\n\r\n        // Return ±0 if x is 0 or y is ±Infinity, or return ±Infinity as y is 0.\r\n        xd && xd[0] == 0 || !yd ? sign * 0 : sign / 0);\r\n    }\r\n\r\n    if (base) {\r\n      logBase = 1;\r\n      e = x.e - y.e;\r\n    } else {\r\n      base = BASE;\r\n      logBase = LOG_BASE;\r\n      e = mathfloor(x.e / logBase) - mathfloor(y.e / logBase);\r\n    }\r\n\r\n    yL = yd.length;\r\n    xL = xd.length;\r\n    q = new Ctor(sign);\r\n    qd = q.d = [];\r\n\r\n    // Result exponent may be one less than e.\r\n    // The digit array of a Decimal from toStringBinary may have trailing zeros.\r\n    for (i = 0; yd[i] == (xd[i] || 0); i++);\r\n\r\n    if (yd[i] > (xd[i] || 0)) e--;\r\n\r\n    if (pr == null) {\r\n      sd = pr = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else if (dp) {\r\n      sd = pr + (x.e - y.e) + 1;\r\n    } else {\r\n      sd = pr;\r\n    }\r\n\r\n    if (sd < 0) {\r\n      qd.push(1);\r\n      more = true;\r\n    } else {\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / logBase + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * base + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n        more = k || i < xL;\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= base/2\r\n        k = base / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k, base);\r\n          xd = multiplyInteger(xd, k, base);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= base / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= base) k = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL, base);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL, base);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n\r\n        more = rem[0] !== void 0;\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n    }\r\n\r\n    // logBase is 1 when divide is being used for base conversion.\r\n    if (logBase == 1) {\r\n      q.e = e;\r\n      inexact = more;\r\n    } else {\r\n\r\n      // To calculate q.e, first get the number of digits of qd[0].\r\n      for (i = 1, k = qd[0]; k >= 10; k /= 10) i++;\r\n      q.e = i + e * logBase - 1;\r\n\r\n      finalise(q, dp ? pr + q.e + 1 : pr, rm, more);\r\n    }\r\n\r\n    return q;\r\n  };\r\n})();\r\n\r\n\r\n/*\r\n * Round `x` to `sd` significant digits using rounding mode `rm`.\r\n * Check for over/under-flow.\r\n */\r\n function finalise(x, sd, rm, isTruncated) {\r\n  var digits, i, j, k, rd, roundUp, w, xd, xdi,\r\n    Ctor = x.constructor;\r\n\r\n  // Don't round if sd is null or undefined.\r\n  out: if (sd != null) {\r\n    xd = x.d;\r\n\r\n    // Infinity/NaN.\r\n    if (!xd) return x;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd containing rd, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // digits: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (digits = 1, k = xd[0]; k >= 10; k /= 10) digits++;\r\n    i = sd - digits;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / mathpow(10, digits - j - 1) % 10 | 0;\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) {\r\n        if (isTruncated) {\r\n\r\n          // Needed by `naturalExponential`, `naturalLogarithm` and `squareRoot`.\r\n          for (; k++ <= xdi;) xd.push(0);\r\n          w = rd = 0;\r\n          digits = 1;\r\n          i %= LOG_BASE;\r\n          j = i - LOG_BASE + 1;\r\n        } else {\r\n          break out;\r\n        }\r\n      } else {\r\n        w = k = xd[xdi];\r\n\r\n        // Get the number of digits of w.\r\n        for (digits = 1; k >= 10; k /= 10) digits++;\r\n\r\n        // Get the index of rd within w.\r\n        i %= LOG_BASE;\r\n\r\n        // Get the index of rd within w, adjusted for leading zeros.\r\n        // The number of leading zeros of w is given by LOG_BASE - digits.\r\n        j = i - LOG_BASE + digits;\r\n\r\n        // Get the rounding digit at index j of w.\r\n        rd = j < 0 ? 0 : w / mathpow(10, digits - j - 1) % 10 | 0;\r\n      }\r\n    }\r\n\r\n    // Are there any non-zero digits after the rounding digit?\r\n    isTruncated = isTruncated || sd < 0 ||\r\n      xd[xdi + 1] !== void 0 || (j < 0 ? w : w % mathpow(10, digits - j - 1));\r\n\r\n    // The expression `w % mathpow(10, digits - j - 1)` returns all the digits of w to the right\r\n    // of the digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression\r\n    // will give 714.\r\n\r\n    roundUp = rm < 4\r\n      ? (rd || isTruncated) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n      : rd > 5 || rd == 5 && (rm == 4 || isTruncated || rm == 6 &&\r\n\r\n        // Check whether the digit to the left of the rounding digit is odd.\r\n        ((i > 0 ? j > 0 ? w / mathpow(10, digits - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n          rm == (x.s < 0 ? 8 : 7));\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      xd.length = 0;\r\n      if (roundUp) {\r\n\r\n        // Convert sd to decimal places.\r\n        sd -= x.e + 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = -sd || 0;\r\n      } else {\r\n\r\n        // Zero.\r\n        xd[0] = x.e = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, digits - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (roundUp) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n\r\n          // i will be the length of xd[0] before k is added.\r\n          for (i = 1, j = xd[0]; j >= 10; j /= 10) i++;\r\n          j = xd[0] += k;\r\n          for (k = 1; j >= 10; j /= 10) k++;\r\n\r\n          // if i != k the length has increased.\r\n          if (i != k) {\r\n            x.e++;\r\n            if (xd[0] == BASE) xd[0] = 1;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n  }\r\n\r\n  if (external) {\r\n\r\n    // Overflow?\r\n    if (x.e > Ctor.maxE) {\r\n\r\n      // Infinity.\r\n      x.d = null;\r\n      x.e = NaN;\r\n\r\n    // Underflow?\r\n    } else if (x.e < Ctor.minE) {\r\n\r\n      // Zero.\r\n      x.e = 0;\r\n      x.d = [0];\r\n      // Ctor.underflow = true;\r\n    } // else Ctor.underflow = false;\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\nfunction finiteToString(x, isExp, sd) {\r\n  if (!x.isFinite()) return nonFiniteToString(x);\r\n  var k,\r\n    e = x.e,\r\n    str = digitsToString(x.d),\r\n    len = str.length;\r\n\r\n  if (isExp) {\r\n    if (sd && (k = sd - len) > 0) {\r\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n    } else if (len > 1) {\r\n      str = str.charAt(0) + '.' + str.slice(1);\r\n    }\r\n\r\n    str = str + (x.e < 0 ? 'e' : 'e+') + x.e;\r\n  } else if (e < 0) {\r\n    str = '0.' + getZeroString(-e - 1) + str;\r\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n  } else if (e >= len) {\r\n    str += getZeroString(e + 1 - len);\r\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n  } else {\r\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n    if (sd && (k = sd - len) > 0) {\r\n      if (e + 1 === len) str += '.';\r\n      str += getZeroString(k);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// Calculate the base 10 exponent from the base 1e7 exponent.\r\nfunction getBase10Exponent(digits, e) {\r\n  var w = digits[0];\r\n\r\n  // Add the number of digits of the first word of the digits array.\r\n  for ( e *= LOG_BASE; w >= 10; w /= 10) e++;\r\n  return e;\r\n}\r\n\r\n\r\nfunction getLn10(Ctor, sd, pr) {\r\n  if (sd > LN10_PRECISION) {\r\n\r\n    // Reset global state in case the exception is caught.\r\n    external = true;\r\n    if (pr) Ctor.precision = pr;\r\n    throw Error(precisionLimitExceeded);\r\n  }\r\n  return finalise(new Ctor(LN10), sd, 1, true);\r\n}\r\n\r\n\r\nfunction getPi(Ctor, sd, rm) {\r\n  if (sd > PI_PRECISION) throw Error(precisionLimitExceeded);\r\n  return finalise(new Ctor(PI), sd, rm, true);\r\n}\r\n\r\n\r\nfunction getPrecision(digits) {\r\n  var w = digits.length - 1,\r\n    len = w * LOG_BASE + 1;\r\n\r\n  w = digits[w];\r\n\r\n  // If non-zero...\r\n  if (w) {\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    for (; w % 10 == 0; w /= 10) len--;\r\n\r\n    // Add the number of digits of the first word.\r\n    for (w = digits[0]; w >= 10; w /= 10) len++;\r\n  }\r\n\r\n  return len;\r\n}\r\n\r\n\r\nfunction getZeroString(k) {\r\n  var zs = '';\r\n  for (; k--;) zs += '0';\r\n  return zs;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of Decimal `x` to the power `n`, where `n` is an\r\n * integer of type number.\r\n *\r\n * Implements 'exponentiation by squaring'. Called by `pow` and `parseOther`.\r\n *\r\n */\r\nfunction intPow(Ctor, x, n, pr) {\r\n  var isTruncated,\r\n    r = new Ctor(1),\r\n\r\n    // Max n of 9007199254740991 takes 53 loop iterations.\r\n    // Maximum digits array length; leaves [28, 34] guard digits.\r\n    k = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n  external = false;\r\n\r\n  for (;;) {\r\n    if (n % 2) {\r\n      r = r.times(x);\r\n      if (truncate(r.d, k)) isTruncated = true;\r\n    }\r\n\r\n    n = mathfloor(n / 2);\r\n    if (n === 0) {\r\n\r\n      // To ensure correct rounding when r.d is truncated, increment the last word if it is zero.\r\n      n = r.d.length - 1;\r\n      if (isTruncated && r.d[n] === 0) ++r.d[n];\r\n      break;\r\n    }\r\n\r\n    x = x.times(x);\r\n    truncate(x.d, k);\r\n  }\r\n\r\n  external = true;\r\n\r\n  return r;\r\n}\r\n\r\n\r\nfunction isOdd(n) {\r\n  return n.d[n.d.length - 1] & 1;\r\n}\r\n\r\n\r\n/*\r\n * Handle `max` (`n` is -1) and `min` (`n` is 1).\r\n */\r\nfunction maxOrMin(Ctor, args, n) {\r\n  var k, y,\r\n    x = new Ctor(args[0]),\r\n    i = 0;\r\n\r\n  for (; ++i < args.length;) {\r\n    y = new Ctor(args[i]);\r\n\r\n    // NaN?\r\n    if (!y.s) {\r\n      x = y;\r\n      break;\r\n    }\r\n\r\n    k = x.cmp(y);\r\n\r\n    if (k === n || k === 0 && x.s === n) {\r\n      x = y;\r\n    }\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * Max integer argument: exp('20723265836946413') = 6.3e+9000000000000000\r\n * Min integer argument: exp('-20723265836946411') = 1.2e-9000000000000000\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(Infinity)  = Infinity\r\n *  exp(-Infinity) = 0\r\n *  exp(NaN)       = NaN\r\n *  exp(±0)        = 1\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n *  The result will always be correctly rounded.\r\n *\r\n */\r\nfunction naturalExponential(x, sd) {\r\n  var denominator, guard, j, pow, sum, t, wpr,\r\n    rep = 0,\r\n    i = 0,\r\n    k = 0,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // 0/NaN/Infinity?\r\n  if (!x.d || !x.d[0] || x.e > 17) {\r\n\r\n    return new Ctor(x.d\r\n      ? !x.d[0] ? 1 : x.s < 0 ? 0 : 1 / 0\r\n      : x.s ? x.s < 0 ? 0 : x : 0 / 0);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  t = new Ctor(0.03125);\r\n\r\n  // while abs(x) >= 0.1\r\n  while (x.e > -2) {\r\n\r\n    // x = x / 2^5\r\n    x = x.times(t);\r\n    k += 5;\r\n  }\r\n\r\n  // Use 2 * log10(2^k) + 5 (empirically derived) to estimate the increase in precision\r\n  // necessary to ensure the first 4 rounding digits are correct.\r\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n  wpr += guard;\r\n  denominator = pow = sum = new Ctor(1);\r\n  Ctor.precision = wpr;\r\n\r\n  for (;;) {\r\n    pow = finalise(pow.times(x), wpr, 1);\r\n    denominator = denominator.times(++i);\r\n    t = sum.plus(divide(pow, denominator, wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      j = k;\r\n      while (j--) sum = finalise(sum.times(sum), wpr, 1);\r\n\r\n      // Check to see if the first 4 rounding digits are [49]999.\r\n      // If so, repeat the summation with a higher precision, otherwise\r\n      // e.g. with precision: 18, rounding: 1\r\n      // exp(18.404272462595034083567793919843761) = 98372560.1229999999 (should be 98372560.123)\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n\r\n        if (rep < 3 && checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += 10;\r\n          denominator = pow = t = new Ctor(1);\r\n          i = 0;\r\n          rep++;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n *  ln(-n)        = NaN\r\n *  ln(0)         = -Infinity\r\n *  ln(-0)        = -Infinity\r\n *  ln(1)         = 0\r\n *  ln(Infinity)  = Infinity\r\n *  ln(-Infinity) = NaN\r\n *  ln(NaN)       = NaN\r\n *\r\n *  ln(n) (n != 1) is non-terminating.\r\n *\r\n */\r\nfunction naturalLogarithm(y, sd) {\r\n  var c, c0, denominator, e, numerator, rep, sum, t, wpr, x1, x2,\r\n    n = 1,\r\n    guard = 10,\r\n    x = y,\r\n    xd = x.d,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // Is x negative or Infinity, NaN, 0 or 1?\r\n  if (x.s < 0 || !xd || !xd[0] || !x.e && xd[0] == 1 && xd.length == 1) {\r\n    return new Ctor(xd && !xd[0] ? -1 / 0 : x.s != 1 ? NaN : xd ? 0 : x);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  Ctor.precision = wpr += guard;\r\n  c = digitsToString(xd);\r\n  c0 = c.charAt(0);\r\n\r\n  if (Math.abs(e = x.e) < 1.5e15) {\r\n\r\n    // Argument reduction.\r\n    // The series converges faster the closer the argument is to 1, so using\r\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n    // later be divided by this number, then separate out the power of 10 using\r\n    // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n    // max n is 6 (gives 0.7 - 1.3)\r\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n      x = x.times(y);\r\n      c = digitsToString(x.d);\r\n      c0 = c.charAt(0);\r\n      n++;\r\n    }\r\n\r\n    e = x.e;\r\n\r\n    if (c0 > 1) {\r\n      x = new Ctor('0.' + c);\r\n      e++;\r\n    } else {\r\n      x = new Ctor(c0 + '.' + c.slice(1));\r\n    }\r\n  } else {\r\n\r\n    // The argument reduction method above may result in overflow if the argument y is a massive\r\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n    x = naturalLogarithm(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n    Ctor.precision = pr;\r\n\r\n    return sd == null ? finalise(x, pr, rm, external = true) : x;\r\n  }\r\n\r\n  // x1 is x reduced to a value near 1.\r\n  x1 = x;\r\n\r\n  // Taylor series.\r\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n  sum = numerator = x = divide(x.minus(1), x.plus(1), wpr, 1);\r\n  x2 = finalise(x.times(x), wpr, 1);\r\n  denominator = 3;\r\n\r\n  for (;;) {\r\n    numerator = finalise(numerator.times(x2), wpr, 1);\r\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      sum = sum.times(2);\r\n\r\n      // Reverse the argument reduction. Check that e is not 0 because, besides preventing an\r\n      // unnecessary calculation, -0 + 0 = +0 and to ensure correct rounding -0 needs to stay -0.\r\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n      sum = divide(sum, new Ctor(n), wpr, 1);\r\n\r\n      // Is rm > 3 and the first 4 rounding digits 4999, or rm < 4 (or the summation has\r\n      // been repeated previously) and the first 4 rounding digits 9999?\r\n      // If so, restart the summation with a higher precision, otherwise\r\n      // e.g. with precision: 12, rounding: 1\r\n      // ln(135520028.6126091714265381533) = 18.7246299999 when it should be 18.72463.\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n        if (checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += guard;\r\n          t = numerator = x = divide(x1.minus(1), x1.plus(1), wpr, 1);\r\n          x2 = finalise(x.times(x), wpr, 1);\r\n          denominator = rep = 1;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n    denominator += 2;\r\n  }\r\n}\r\n\r\n\r\n// ±Infinity, NaN.\r\nfunction nonFiniteToString(x) {\r\n  // Unsigned.\r\n  return String(x.s * x.s / 0);\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\r\nfunction parseDecimal(x, str) {\r\n  var e, i, len;\r\n\r\n  // TODO BigInt str: no need to check for decimal point, exponential form or leading zeros.\r\n  // Decimal point?\r\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = str.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +str.slice(i + 1);\r\n    str = str.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = str.length;\r\n  }\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n  // Determine trailing zeros.\r\n  for (len = str.length; str.charCodeAt(len - 1) === 48; --len);\r\n  str = str.slice(i, len);\r\n\r\n  if (str) {\r\n    len -= i;\r\n    x.e = e = e - i - 1;\r\n    x.d = [];\r\n\r\n    // Transform base\r\n\r\n    // e is the base 10 exponent.\r\n    // i is where to slice str to get the first word of the digits array.\r\n    i = (e + 1) % LOG_BASE;\r\n    if (e < 0) i += LOG_BASE;\r\n\r\n    if (i < len) {\r\n      if (i) x.d.push(+str.slice(0, i));\r\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n      str = str.slice(i);\r\n      i = LOG_BASE - str.length;\r\n    } else {\r\n      i -= len;\r\n    }\r\n\r\n    for (; i--;) str += '0';\r\n    x.d.push(+str);\r\n\r\n    if (external) {\r\n\r\n      // Overflow?\r\n      if (x.e > x.constructor.maxE) {\r\n\r\n        // Infinity.\r\n        x.d = null;\r\n        x.e = NaN;\r\n\r\n      // Underflow?\r\n      } else if (x.e < x.constructor.minE) {\r\n\r\n        // Zero.\r\n        x.e = 0;\r\n        x.d = [0];\r\n        // x.constructor.underflow = true;\r\n      } // else x.constructor.underflow = false;\r\n    }\r\n  } else {\r\n\r\n    // Zero.\r\n    x.e = 0;\r\n    x.d = [0];\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from a string `str`, which is not a decimal value.\r\n */\r\nfunction parseOther(x, str) {\r\n  var base, Ctor, divisor, i, isFloat, len, p, xd, xe;\r\n\r\n  if (str.indexOf('_') > -1) {\r\n    str = str.replace(/(\\d)_(?=\\d)/g, '$1');\r\n    if (isDecimal.test(str)) return parseDecimal(x, str);\r\n  } else if (str === 'Infinity' || str === 'NaN') {\r\n    if (!+str) x.s = NaN;\r\n    x.e = NaN;\r\n    x.d = null;\r\n    return x;\r\n  }\r\n\r\n  if (isHex.test(str))  {\r\n    base = 16;\r\n    str = str.toLowerCase();\r\n  } else if (isBinary.test(str))  {\r\n    base = 2;\r\n  } else if (isOctal.test(str))  {\r\n    base = 8;\r\n  } else {\r\n    throw Error(invalidArgument + str);\r\n  }\r\n\r\n  // Is there a binary exponent part?\r\n  i = str.search(/p/i);\r\n\r\n  if (i > 0) {\r\n    p = +str.slice(i + 1);\r\n    str = str.substring(2, i);\r\n  } else {\r\n    str = str.slice(2);\r\n  }\r\n\r\n  // Convert `str` as an integer then divide the result by `base` raised to a power such that the\r\n  // fraction part will be restored.\r\n  i = str.indexOf('.');\r\n  isFloat = i >= 0;\r\n  Ctor = x.constructor;\r\n\r\n  if (isFloat) {\r\n    str = str.replace('.', '');\r\n    len = str.length;\r\n    i = len - i;\r\n\r\n    // log[10](16) = 1.2041... , log[10](88) = 1.9444....\r\n    divisor = intPow(Ctor, new Ctor(base), i, i * 2);\r\n  }\r\n\r\n  xd = convertBase(str, base, BASE);\r\n  xe = xd.length - 1;\r\n\r\n  // Remove trailing zeros.\r\n  for (i = xe; xd[i] === 0; --i) xd.pop();\r\n  if (i < 0) return new Ctor(x.s * 0);\r\n  x.e = getBase10Exponent(xd, xe);\r\n  x.d = xd;\r\n  external = false;\r\n\r\n  // At what precision to perform the division to ensure exact conversion?\r\n  // maxDecimalIntegerPartDigitCount = ceil(log[10](b) * otherBaseIntegerPartDigitCount)\r\n  // log[10](2) = 0.30103, log[10](8) = 0.90309, log[10](16) = 1.20412\r\n  // E.g. ceil(1.2 * 3) = 4, so up to 4 decimal digits are needed to represent 3 hex int digits.\r\n  // maxDecimalFractionPartDigitCount = {Hex:4|Oct:3|Bin:1} * otherBaseFractionPartDigitCount\r\n  // Therefore using 4 * the number of digits of str will always be enough.\r\n  if (isFloat) x = divide(x, divisor, len * 4);\r\n\r\n  // Multiply by the binary exponent part if present.\r\n  if (p) x = x.times(Math.abs(p) < 54 ? mathpow(2, p) : Decimal.pow(2, p));\r\n  external = true;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction sine(Ctor, x) {\r\n  var k,\r\n    len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    return x.isZero() ? x : taylorSeries(Ctor, 2, x, x);\r\n  }\r\n\r\n  // Argument reduction: sin(5x) = 16*sin^5(x) - 20*sin^3(x) + 5*sin(x)\r\n  // i.e. sin(x) = 16*sin^5(x/5) - 20*sin^3(x/5) + 5*sin(x/5)\r\n  // and  sin(x) = sin(x/5)(5 + sin^2(x/5)(16sin^2(x/5) - 20))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  k = 1.4 * Math.sqrt(len);\r\n  k = k > 16 ? 16 : k | 0;\r\n\r\n  x = x.times(1 / tinyPow(5, k));\r\n  x = taylorSeries(Ctor, 2, x, x);\r\n\r\n  // Reverse argument reduction\r\n  var sin2_x,\r\n    d5 = new Ctor(5),\r\n    d16 = new Ctor(16),\r\n    d20 = new Ctor(20);\r\n  for (; k--;) {\r\n    sin2_x = x.times(x);\r\n    x = x.times(d5.plus(sin2_x.times(d16.times(sin2_x).minus(d20))));\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n// Calculate Taylor series for `cos`, `cosh`, `sin` and `sinh`.\r\nfunction taylorSeries(Ctor, n, x, y, isHyperbolic) {\r\n  var j, t, u, x2,\r\n    i = 1,\r\n    pr = Ctor.precision,\r\n    k = Math.ceil(pr / LOG_BASE);\r\n\r\n  external = false;\r\n  x2 = x.times(x);\r\n  u = new Ctor(y);\r\n\r\n  for (;;) {\r\n    t = divide(u.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    u = isHyperbolic ? y.plus(t) : y.minus(t);\r\n    y = divide(t.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    t = u.plus(y);\r\n\r\n    if (t.d[k] !== void 0) {\r\n      for (j = k; t.d[j] === u.d[j] && j--;);\r\n      if (j == -1) break;\r\n    }\r\n\r\n    j = u;\r\n    u = y;\r\n    y = t;\r\n    t = j;\r\n    i++;\r\n  }\r\n\r\n  external = true;\r\n  t.d.length = k + 1;\r\n\r\n  return t;\r\n}\r\n\r\n\r\n// Exponent e must be positive and non-zero.\r\nfunction tinyPow(b, e) {\r\n  var n = b;\r\n  while (--e) n *= b;\r\n  return n;\r\n}\r\n\r\n\r\n// Return the absolute value of `x` reduced to less than or equal to half pi.\r\nfunction toLessThanHalfPi(Ctor, x) {\r\n  var t,\r\n    isNeg = x.s < 0,\r\n    pi = getPi(Ctor, Ctor.precision, 1),\r\n    halfPi = pi.times(0.5);\r\n\r\n  x = x.abs();\r\n\r\n  if (x.lte(halfPi)) {\r\n    quadrant = isNeg ? 4 : 1;\r\n    return x;\r\n  }\r\n\r\n  t = x.divToInt(pi);\r\n\r\n  if (t.isZero()) {\r\n    quadrant = isNeg ? 3 : 2;\r\n  } else {\r\n    x = x.minus(t.times(pi));\r\n\r\n    // 0 <= x < pi\r\n    if (x.lte(halfPi)) {\r\n      quadrant = isOdd(t) ? (isNeg ? 2 : 3) : (isNeg ? 4 : 1);\r\n      return x;\r\n    }\r\n\r\n    quadrant = isOdd(t) ? (isNeg ? 1 : 4) : (isNeg ? 3 : 2);\r\n  }\r\n\r\n  return x.minus(pi).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return the value of Decimal `x` as a string in base `baseOut`.\r\n *\r\n * If the optional `sd` argument is present include a binary exponent suffix.\r\n */\r\nfunction toStringBinary(x, baseOut, sd, rm) {\r\n  var base, e, i, k, len, roundUp, str, xd, y,\r\n    Ctor = x.constructor,\r\n    isExp = sd !== void 0;\r\n\r\n  if (isExp) {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  } else {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  }\r\n\r\n  if (!x.isFinite()) {\r\n    str = nonFiniteToString(x);\r\n  } else {\r\n    str = finiteToString(x);\r\n    i = str.indexOf('.');\r\n\r\n    // Use exponential notation according to `toExpPos` and `toExpNeg`? No, but if required:\r\n    // maxBinaryExponent = floor((decimalExponent + 1) * log[2](10))\r\n    // minBinaryExponent = floor(decimalExponent * log[2](10))\r\n    // log[2](10) = 3.321928094887362347870319429489390175864\r\n\r\n    if (isExp) {\r\n      base = 2;\r\n      if (baseOut == 16) {\r\n        sd = sd * 4 - 3;\r\n      } else if (baseOut == 8) {\r\n        sd = sd * 3 - 2;\r\n      }\r\n    } else {\r\n      base = baseOut;\r\n    }\r\n\r\n    // Convert the number as an integer then divide the result by its base raised to a power such\r\n    // that the fraction part will be restored.\r\n\r\n    // Non-integer.\r\n    if (i >= 0) {\r\n      str = str.replace('.', '');\r\n      y = new Ctor(1);\r\n      y.e = str.length - i;\r\n      y.d = convertBase(finiteToString(y), 10, base);\r\n      y.e = y.d.length;\r\n    }\r\n\r\n    xd = convertBase(str, 10, base);\r\n    e = len = xd.length;\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] == 0;) xd.pop();\r\n\r\n    if (!xd[0]) {\r\n      str = isExp ? '0p+0' : '0';\r\n    } else {\r\n      if (i < 0) {\r\n        e--;\r\n      } else {\r\n        x = new Ctor(x);\r\n        x.d = xd;\r\n        x.e = e;\r\n        x = divide(x, y, sd, rm, 0, base);\r\n        xd = x.d;\r\n        e = x.e;\r\n        roundUp = inexact;\r\n      }\r\n\r\n      // The rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      i = xd[sd];\r\n      k = base / 2;\r\n      roundUp = roundUp || xd[sd + 1] !== void 0;\r\n\r\n      roundUp = rm < 4\r\n        ? (i !== void 0 || roundUp) && (rm === 0 || rm === (x.s < 0 ? 3 : 2))\r\n        : i > k || i === k && (rm === 4 || roundUp || rm === 6 && xd[sd - 1] & 1 ||\r\n          rm === (x.s < 0 ? 8 : 7));\r\n\r\n      xd.length = sd;\r\n\r\n      if (roundUp) {\r\n\r\n        // Rounding up may mean the previous digit has to be rounded up and so on.\r\n        for (; ++xd[--sd] > base - 1;) {\r\n          xd[sd] = 0;\r\n          if (!sd) {\r\n            ++e;\r\n            xd.unshift(1);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Determine trailing zeros.\r\n      for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n      // E.g. [4, 11, 15] becomes 4bf.\r\n      for (i = 0, str = ''; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n\r\n      // Add binary exponent suffix?\r\n      if (isExp) {\r\n        if (len > 1) {\r\n          if (baseOut == 16 || baseOut == 8) {\r\n            i = baseOut == 16 ? 4 : 3;\r\n            for (--len; len % i; len++) str += '0';\r\n            xd = convertBase(str, base, baseOut);\r\n            for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n            // xd[0] will always be be 1\r\n            for (i = 1, str = '1.'; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n          } else {\r\n            str = str.charAt(0) + '.' + str.slice(1);\r\n          }\r\n        }\r\n\r\n        str =  str + (e < 0 ? 'p' : 'p+') + e;\r\n      } else if (e < 0) {\r\n        for (; ++e;) str = '0' + str;\r\n        str = '0.' + str;\r\n      } else {\r\n        if (++e > len) for (e -= len; e-- ;) str += '0';\r\n        else if (e < len) str = str.slice(0, e) + '.' + str.slice(e);\r\n      }\r\n    }\r\n\r\n    str = (baseOut == 16 ? '0x' : baseOut == 2 ? '0b' : baseOut == 8 ? '0o' : '') + str;\r\n  }\r\n\r\n  return x.s < 0 ? '-' + str : str;\r\n}\r\n\r\n\r\n// Does not strip trailing zeros.\r\nfunction truncate(arr, len) {\r\n  if (arr.length > len) {\r\n    arr.length = len;\r\n    return true;\r\n  }\r\n}\r\n\r\n\r\n// Decimal methods\r\n\r\n\r\n/*\r\n *  abs\r\n *  acos\r\n *  acosh\r\n *  add\r\n *  asin\r\n *  asinh\r\n *  atan\r\n *  atanh\r\n *  atan2\r\n *  cbrt\r\n *  ceil\r\n *  clamp\r\n *  clone\r\n *  config\r\n *  cos\r\n *  cosh\r\n *  div\r\n *  exp\r\n *  floor\r\n *  hypot\r\n *  ln\r\n *  log\r\n *  log2\r\n *  log10\r\n *  max\r\n *  min\r\n *  mod\r\n *  mul\r\n *  pow\r\n *  random\r\n *  round\r\n *  set\r\n *  sign\r\n *  sin\r\n *  sinh\r\n *  sqrt\r\n *  sub\r\n *  sum\r\n *  tan\r\n *  tanh\r\n *  trunc\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction abs(x) {\r\n  return new this(x).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine in radians of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction acos(x) {\r\n  return new this(x).acos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction acosh(x) {\r\n  return new this(x).acosh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of `x` and `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction add(x, y) {\r\n  return new this(x).plus(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction asin(x) {\r\n  return new this(x).asin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction asinh(x) {\r\n  return new this(x).asinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction atan(x) {\r\n  return new this(x).atan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction atanh(x) {\r\n  return new this(x).atanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `y/x` in the range -pi to pi\r\n * (inclusive), rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi, pi]\r\n *\r\n * y {number|string|bigint|Decimal} The y-coordinate.\r\n * x {number|string|bigint|Decimal} The x-coordinate.\r\n *\r\n * atan2(±0, -0)               = ±pi\r\n * atan2(±0, +0)               = ±0\r\n * atan2(±0, -x)               = ±pi for x > 0\r\n * atan2(±0, x)                = ±0 for x > 0\r\n * atan2(-y, ±0)               = -pi/2 for y > 0\r\n * atan2(y, ±0)                = pi/2 for y > 0\r\n * atan2(±y, -Infinity)        = ±pi for finite y > 0\r\n * atan2(±y, +Infinity)        = ±0 for finite y > 0\r\n * atan2(±Infinity, x)         = ±pi/2 for finite x\r\n * atan2(±Infinity, -Infinity) = ±3*pi/4\r\n * atan2(±Infinity, +Infinity) = ±pi/4\r\n * atan2(NaN, x) = NaN\r\n * atan2(y, NaN) = NaN\r\n *\r\n */\r\nfunction atan2(y, x) {\r\n  y = new this(y);\r\n  x = new this(x);\r\n  var r,\r\n    pr = this.precision,\r\n    rm = this.rounding,\r\n    wpr = pr + 4;\r\n\r\n  // Either NaN\r\n  if (!y.s || !x.s) {\r\n    r = new this(NaN);\r\n\r\n  // Both ±Infinity\r\n  } else if (!y.d && !x.d) {\r\n    r = getPi(this, wpr, 1).times(x.s > 0 ? 0.25 : 0.75);\r\n    r.s = y.s;\r\n\r\n  // x is ±Infinity or y is ±0\r\n  } else if (!x.d || y.isZero()) {\r\n    r = x.s < 0 ? getPi(this, pr, rm) : new this(0);\r\n    r.s = y.s;\r\n\r\n  // y is ±Infinity or x is ±0\r\n  } else if (!y.d || x.isZero()) {\r\n    r = getPi(this, wpr, 1).times(0.5);\r\n    r.s = y.s;\r\n\r\n  // Both non-zero and finite\r\n  } else if (x.s < 0) {\r\n    this.precision = wpr;\r\n    this.rounding = 1;\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n    x = getPi(this, wpr, 1);\r\n    this.precision = pr;\r\n    this.rounding = rm;\r\n    r = y.s < 0 ? r.minus(x) : r.plus(x);\r\n  } else {\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cube root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction cbrt(x) {\r\n  return new this(x).cbrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using `ROUND_CEIL`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ceil(x) {\r\n  return finalise(x = new this(x), x.e + 1, 2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` clamped to the range delineated by `min` and `max`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction clamp(x, min, max) {\r\n  return new this(x).clamp(min, max);\r\n}\r\n\r\n\r\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *   maxE       {number}\r\n *   minE       {number}\r\n *   modulo     {number}\r\n *   crypto     {boolean|number}\r\n *   defaults   {true}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\r\nfunction config(obj) {\r\n  if (!obj || typeof obj !== 'object') throw Error(decimalError + 'Object expected');\r\n  var i, p, v,\r\n    useDefaults = obj.defaults === true,\r\n    ps = [\r\n      'precision', 1, MAX_DIGITS,\r\n      'rounding', 0, 8,\r\n      'toExpNeg', -EXP_LIMIT, 0,\r\n      'toExpPos', 0, EXP_LIMIT,\r\n      'maxE', 0, EXP_LIMIT,\r\n      'minE', -EXP_LIMIT, 0,\r\n      'modulo', 0, 9\r\n    ];\r\n\r\n  for (i = 0; i < ps.length; i += 3) {\r\n    if (p = ps[i], useDefaults) this[p] = DEFAULTS[p];\r\n    if ((v = obj[p]) !== void 0) {\r\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  if (p = 'crypto', useDefaults) this[p] = DEFAULTS[p];\r\n  if ((v = obj[p]) !== void 0) {\r\n    if (v === true || v === false || v === 0 || v === 1) {\r\n      if (v) {\r\n        if (typeof crypto != 'undefined' && crypto &&\r\n          (crypto.getRandomValues || crypto.randomBytes)) {\r\n          this[p] = true;\r\n        } else {\r\n          throw Error(cryptoUnavailable);\r\n        }\r\n      } else {\r\n        this[p] = false;\r\n      }\r\n    } else {\r\n      throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  return this;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cos(x) {\r\n  return new this(x).cos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of `x`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cosh(x) {\r\n  return new this(x).cosh();\r\n}\r\n\r\n\r\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\r\nfunction clone(obj) {\r\n  var i, p, ps;\r\n\r\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * v {number|string|bigint|Decimal} A numeric value.\r\n   *\r\n   */\r\n  function Decimal(v) {\r\n    var e, i, t,\r\n      x = this;\r\n\r\n    // Decimal called without new.\r\n    if (!(x instanceof Decimal)) return new Decimal(v);\r\n\r\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n    // which points to Object.\r\n    x.constructor = Decimal;\r\n\r\n    if (isDecimalInstance(v)) {\r\n      x.s = v.s;\r\n\r\n      if (external) {\r\n        if (!v.d || v.e > Decimal.maxE) {\r\n\r\n          // Infinity.\r\n          x.e = NaN;\r\n          x.d = null;\r\n        } else if (v.e < Decimal.minE) {\r\n\r\n          // Zero.\r\n          x.e = 0;\r\n          x.d = [0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.d = v.d.slice();\r\n        }\r\n      } else {\r\n        x.e = v.e;\r\n        x.d = v.d ? v.d.slice() : v.d;\r\n      }\r\n\r\n      return;\r\n    }\r\n\r\n    t = typeof v;\r\n\r\n    if (t === 'number') {\r\n      if (v === 0) {\r\n        x.s = 1 / v < 0 ? -1 : 1;\r\n        x.e = 0;\r\n        x.d = [0];\r\n        return;\r\n      }\r\n\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      // Fast path for small integers.\r\n      if (v === ~~v && v < 1e7) {\r\n        for (e = 0, i = v; i >= 10; i /= 10) e++;\r\n\r\n        if (external) {\r\n          if (e > Decimal.maxE) {\r\n            x.e = NaN;\r\n            x.d = null;\r\n          } else if (e < Decimal.minE) {\r\n            x.e = 0;\r\n            x.d = [0];\r\n          } else {\r\n            x.e = e;\r\n            x.d = [v];\r\n          }\r\n        } else {\r\n          x.e = e;\r\n          x.d = [v];\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      // Infinity or NaN?\r\n      if (v * 0 !== 0) {\r\n        if (!v) x.s = NaN;\r\n        x.e = NaN;\r\n        x.d = null;\r\n        return;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    if (t === 'string') {\r\n      if ((i = v.charCodeAt(0)) === 45) {  // minus sign\r\n        v = v.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        if (i === 43) v = v.slice(1);  // plus sign\r\n        x.s = 1;\r\n      }\r\n\r\n      return isDecimal.test(v) ? parseDecimal(x, v) : parseOther(x, v);\r\n    }\r\n\r\n    if (t === 'bigint') {\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    throw Error(invalidArgument + v);\r\n  }\r\n\r\n  Decimal.prototype = P;\r\n\r\n  Decimal.ROUND_UP = 0;\r\n  Decimal.ROUND_DOWN = 1;\r\n  Decimal.ROUND_CEIL = 2;\r\n  Decimal.ROUND_FLOOR = 3;\r\n  Decimal.ROUND_HALF_UP = 4;\r\n  Decimal.ROUND_HALF_DOWN = 5;\r\n  Decimal.ROUND_HALF_EVEN = 6;\r\n  Decimal.ROUND_HALF_CEIL = 7;\r\n  Decimal.ROUND_HALF_FLOOR = 8;\r\n  Decimal.EUCLID = 9;\r\n\r\n  Decimal.config = Decimal.set = config;\r\n  Decimal.clone = clone;\r\n  Decimal.isDecimal = isDecimalInstance;\r\n\r\n  Decimal.abs = abs;\r\n  Decimal.acos = acos;\r\n  Decimal.acosh = acosh;        // ES6\r\n  Decimal.add = add;\r\n  Decimal.asin = asin;\r\n  Decimal.asinh = asinh;        // ES6\r\n  Decimal.atan = atan;\r\n  Decimal.atanh = atanh;        // ES6\r\n  Decimal.atan2 = atan2;\r\n  Decimal.cbrt = cbrt;          // ES6\r\n  Decimal.ceil = ceil;\r\n  Decimal.clamp = clamp;\r\n  Decimal.cos = cos;\r\n  Decimal.cosh = cosh;          // ES6\r\n  Decimal.div = div;\r\n  Decimal.exp = exp;\r\n  Decimal.floor = floor;\r\n  Decimal.hypot = hypot;        // ES6\r\n  Decimal.ln = ln;\r\n  Decimal.log = log;\r\n  Decimal.log10 = log10;        // ES6\r\n  Decimal.log2 = log2;          // ES6\r\n  Decimal.max = max;\r\n  Decimal.min = min;\r\n  Decimal.mod = mod;\r\n  Decimal.mul = mul;\r\n  Decimal.pow = pow;\r\n  Decimal.random = random;\r\n  Decimal.round = round;\r\n  Decimal.sign = sign;          // ES6\r\n  Decimal.sin = sin;\r\n  Decimal.sinh = sinh;          // ES6\r\n  Decimal.sqrt = sqrt;\r\n  Decimal.sub = sub;\r\n  Decimal.sum = sum;\r\n  Decimal.tan = tan;\r\n  Decimal.tanh = tanh;          // ES6\r\n  Decimal.trunc = trunc;        // ES6\r\n\r\n  if (obj === void 0) obj = {};\r\n  if (obj) {\r\n    if (obj.defaults !== true) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'maxE', 'minE', 'modulo', 'crypto'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n  }\r\n\r\n  Decimal.config(obj);\r\n\r\n  return Decimal;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` divided by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction div(x, y) {\r\n  return new this(x).div(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The power to which to raise the base of the natural log.\r\n *\r\n */\r\nfunction exp(x) {\r\n  return new this(x).exp();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` round to an integer using `ROUND_FLOOR`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction floor(x) {\r\n  return finalise(x = new this(x), x.e + 1, 3);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of the sum of the squares of the arguments,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * hypot(a, b, ...) = sqrt(a^2 + b^2 + ...)\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction hypot() {\r\n  var i, n,\r\n    t = new this(0);\r\n\r\n  external = false;\r\n\r\n  for (i = 0; i < arguments.length;) {\r\n    n = new this(arguments[i++]);\r\n    if (!n.d) {\r\n      if (n.s) {\r\n        external = true;\r\n        return new this(1 / 0);\r\n      }\r\n      t = n;\r\n    } else if (t.d) {\r\n      t = t.plus(n.times(n));\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return t.sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return true if object is a Decimal instance (where Decimal is any Decimal constructor),\r\n * otherwise return false.\r\n *\r\n */\r\nfunction isDecimalInstance(obj) {\r\n  return obj instanceof Decimal || obj && obj.toStringTag === tag || false;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ln(x) {\r\n  return new this(x).ln();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the log of `x` to the base `y`, or to base 10 if no base\r\n * is specified, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * log[y](x)\r\n *\r\n * x {number|string|bigint|Decimal} The argument of the logarithm.\r\n * y {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nfunction log(x, y) {\r\n  return new this(x).log(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 2 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log2(x) {\r\n  return new this(x).log(2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 10 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log10(x) {\r\n  return new this(x).log(10);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction max() {\r\n  return maxOrMin(this, arguments, -1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction min() {\r\n  return maxOrMin(this, arguments, 1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` modulo `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mod(x, y) {\r\n  return new this(x).mod(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` multiplied by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mul(x, y) {\r\n  return new this(x).mul(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` raised to the power `y`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The base.\r\n * y {number|string|bigint|Decimal} The exponent.\r\n *\r\n */\r\nfunction pow(x, y) {\r\n  return new this(x).pow(y);\r\n}\r\n\r\n\r\n/*\r\n * Returns a new Decimal with a random value equal to or greater than 0 and less than 1, and with\r\n * `sd`, or `Decimal.precision` if `sd` is omitted, significant digits (or less if trailing zeros\r\n * are produced).\r\n *\r\n * [sd] {number} Significant digits. Integer, 0 to MAX_DIGITS inclusive.\r\n *\r\n */\r\nfunction random(sd) {\r\n  var d, e, k, n,\r\n    i = 0,\r\n    r = new this(1),\r\n    rd = [];\r\n\r\n  if (sd === void 0) sd = this.precision;\r\n  else checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n  k = Math.ceil(sd / LOG_BASE);\r\n\r\n  if (!this.crypto) {\r\n    for (; i < k;) rd[i++] = Math.random() * 1e7 | 0;\r\n\r\n  // Browsers supporting crypto.getRandomValues.\r\n  } else if (crypto.getRandomValues) {\r\n    d = crypto.getRandomValues(new Uint32Array(k));\r\n\r\n    for (; i < k;) {\r\n      n = d[i];\r\n\r\n      // 0 <= n < 4294967296\r\n      // Probability n >= 4.29e9, is 4967296 / 4294967296 = 0.00116 (1 in 865).\r\n      if (n >= 4.29e9) {\r\n        d[i] = crypto.getRandomValues(new Uint32Array(1))[0];\r\n      } else {\r\n\r\n        // 0 <= n <= 4289999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd[i++] = n % 1e7;\r\n      }\r\n    }\r\n\r\n  // Node.js supporting crypto.randomBytes.\r\n  } else if (crypto.randomBytes) {\r\n\r\n    // buffer\r\n    d = crypto.randomBytes(k *= 4);\r\n\r\n    for (; i < k;) {\r\n\r\n      // 0 <= n < 2147483648\r\n      n = d[i] + (d[i + 1] << 8) + (d[i + 2] << 16) + ((d[i + 3] & 0x7f) << 24);\r\n\r\n      // Probability n >= 2.14e9, is 7483648 / 2147483648 = 0.0035 (1 in 286).\r\n      if (n >= 2.14e9) {\r\n        crypto.randomBytes(4).copy(d, i);\r\n      } else {\r\n\r\n        // 0 <= n <= 2139999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd.push(n % 1e7);\r\n        i += 4;\r\n      }\r\n    }\r\n\r\n    i = k / 4;\r\n  } else {\r\n    throw Error(cryptoUnavailable);\r\n  }\r\n\r\n  k = rd[--i];\r\n  sd %= LOG_BASE;\r\n\r\n  // Convert trailing digits to zeros according to sd.\r\n  if (k && sd) {\r\n    n = mathpow(10, LOG_BASE - sd);\r\n    rd[i] = (k / n | 0) * n;\r\n  }\r\n\r\n  // Remove trailing words which are zero.\r\n  for (; rd[i] === 0; i--) rd.pop();\r\n\r\n  // Zero?\r\n  if (i < 0) {\r\n    e = 0;\r\n    rd = [0];\r\n  } else {\r\n    e = -1;\r\n\r\n    // Remove leading words which are zero and adjust exponent accordingly.\r\n    for (; rd[0] === 0; e -= LOG_BASE) rd.shift();\r\n\r\n    // Count the digits of the first word of rd to determine leading zeros.\r\n    for (k = 1, n = rd[0]; n >= 10; n /= 10) k++;\r\n\r\n    // Adjust the exponent for leading zeros of the first word of rd.\r\n    if (k < LOG_BASE) e -= LOG_BASE - k;\r\n  }\r\n\r\n  r.e = e;\r\n  r.d = rd;\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using rounding mode `rounding`.\r\n *\r\n * To emulate `Math.round`, set rounding to 7 (ROUND_HALF_CEIL).\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction round(x) {\r\n  return finalise(x = new this(x), x.e + 1, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if x > 0,\r\n *  -1    if x < 0,\r\n *   0    if x is 0,\r\n *  -0    if x is -0,\r\n *   NaN  otherwise\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sign(x) {\r\n  x = new this(x);\r\n  return x.d ? (x.d[0] ? x.s : 0 * x.s) : x.s || NaN;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of `x`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sin(x) {\r\n  return new this(x).sin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sinh(x) {\r\n  return new this(x).sinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sqrt(x) {\r\n  return new this(x).sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` minus `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sub(x, y) {\r\n  return new this(x).sub(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of the arguments, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * Only the result is rounded, not the intermediate calculations.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sum() {\r\n  var i = 0,\r\n    args = arguments,\r\n    x = new this(args[i]);\r\n\r\n  external = false;\r\n  for (; x.s && ++i < args.length;) x = x.plus(args[i]);\r\n  external = true;\r\n\r\n  return finalise(x, this.precision, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tan(x) {\r\n  return new this(x).tan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tanh(x) {\r\n  return new this(x).tanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` truncated to an integer.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction trunc(x) {\r\n  return finalise(x = new this(x), x.e + 1, 1);\r\n}\r\n\r\n\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toString;\r\nP[Symbol.toStringTag] = 'Decimal';\r\n\r\n// Create and configure initial Decimal constructor.\r\nexport var Decimal = P.constructor = clone(DEFAULTS);\r\n\r\n// Create the internal constants from their string values.\r\nLN10 = new Decimal(LN10);\r\nPI = new Decimal(PI);\r\n\r\nexport default Decimal;\r\n", "\n/* !!! This is code generated by Prisma. Do not edit directly. !!!\n/* eslint-disable */\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\n\nconst {\n  Decimal,\n  objectEnumValues,\n  makeStrictEnum,\n  Public,\n  getRuntime,\n  skip\n} = require('@prisma/client/runtime/index-browser.js')\n\n\nconst Prisma = {}\n\nexports.Prisma = Prisma\nexports.$Enums = {}\n\n/**\n * Prisma Client JS version: 6.9.0\n * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e\n */\nPrisma.prismaVersion = {\n  client: \"6.9.0\",\n  engine: \"81e4af48011447c3cc503a190e86995b66d2a28e\"\n}\n\nPrisma.PrismaClientKnownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)};\nPrisma.PrismaClientUnknownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientRustPanicError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientInitializationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientValidationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.Decimal = Decimal\n\n/**\n * Re-export of sql-template-tag\n */\nPrisma.sql = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.empty = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.join = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.raw = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.validator = Public.validator\n\n/**\n* Extensions\n*/\nPrisma.getExtensionContext = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.defineExtension = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\n\n/**\n * Shorthand utilities for JSON filtering\n */\nPrisma.DbNull = objectEnumValues.instances.DbNull\nPrisma.JsonNull = objectEnumValues.instances.JsonNull\nPrisma.AnyNull = objectEnumValues.instances.AnyNull\n\nPrisma.NullTypes = {\n  DbNull: objectEnumValues.classes.DbNull,\n  JsonNull: objectEnumValues.classes.JsonNull,\n  AnyNull: objectEnumValues.classes.AnyNull\n}\n\n\n\n/**\n * Enums\n */\n\nexports.Prisma.TransactionIsolationLevel = makeStrictEnum({\n  ReadUncommitted: 'ReadUncommitted',\n  ReadCommitted: 'ReadCommitted',\n  RepeatableRead: 'RepeatableRead',\n  Serializable: 'Serializable'\n});\n\nexports.Prisma.UserScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  email: 'email',\n  passwordHash: 'passwordHash',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  avatar: 'avatar',\n  phone: 'phone',\n  defaultTenantId: 'defaultTenantId',\n  verifyToken: 'verifyToken',\n  githubId: 'githubId',\n  azureId: 'azureId',\n  googleId: 'googleId',\n  locale: 'locale',\n  active: 'active'\n};\n\nexports.Prisma.AdminUserScalarFieldEnum = {\n  userId: 'userId'\n};\n\nexports.Prisma.TenantScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  slug: 'slug',\n  name: 'name',\n  icon: 'icon',\n  theme: 'theme',\n  subscriptionId: 'subscriptionId',\n  active: 'active',\n  deactivatedReason: 'deactivatedReason'\n};\n\nexports.Prisma.TenantUserScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  type: 'type',\n  joined: 'joined',\n  status: 'status'\n};\n\nexports.Prisma.TenantUserInvitationScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  email: 'email',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  type: 'type',\n  pending: 'pending',\n  createdUserId: 'createdUserId',\n  fromUserId: 'fromUserId'\n};\n\nexports.Prisma.RegistrationScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  email: 'email',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  slug: 'slug',\n  token: 'token',\n  ipAddress: 'ipAddress',\n  company: 'company',\n  selectedSubscriptionPriceId: 'selectedSubscriptionPriceId',\n  createdTenantId: 'createdTenantId'\n};\n\nexports.Prisma.BlacklistScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  type: 'type',\n  value: 'value',\n  active: 'active',\n  registerAttempts: 'registerAttempts'\n};\n\nexports.Prisma.TenantIpAddressScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  apiKeyId: 'apiKeyId',\n  ip: 'ip',\n  fromUrl: 'fromUrl',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.TenantTypeScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  title: 'title',\n  titlePlural: 'titlePlural',\n  description: 'description',\n  isDefault: 'isDefault'\n};\n\nexports.Prisma.CustomThemeScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  value: 'value',\n  colors: 'colors',\n  isActive: 'isActive'\n};\n\nexports.Prisma.LoyalityScalarFieldEnum = {\n  id: 'id',\n  name: 'name',\n  mail: 'mail',\n  team: 'team'\n};\n\nexports.Prisma.MilestoneScalarFieldEnum = {\n  id: 'id',\n  title: 'title',\n  threshold: 'threshold',\n  order: 'order',\n  rewardId: 'rewardId'\n};\n\nexports.Prisma.RewardScalarFieldEnum = {\n  id: 'id',\n  title: 'title',\n  cost: 'cost',\n  order: 'order',\n  link: 'link',\n  couponCode: 'couponCode'\n};\n\nexports.Prisma.RowMilestoneScalarFieldEnum = {\n  loyalityId: 'loyalityId',\n  milestoneId: 'milestoneId',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.RowRewardScalarFieldEnum = {\n  loyalityId: 'loyalityId',\n  rewardId: 'rewardId',\n  status: 'status',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.RowCoinsScalarFieldEnum = {\n  id: 'id',\n  loyalityId: 'loyalityId',\n  coins: 'coins',\n  message: 'message',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId'\n};\n\nexports.Prisma.AnalyticsSettingsScalarFieldEnum = {\n  id: 'id',\n  public: 'public',\n  ignorePages: 'ignorePages',\n  onlyPages: 'onlyPages'\n};\n\nexports.Prisma.AnalyticsUniqueVisitorScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  cookie: 'cookie',\n  via: 'via',\n  httpReferrer: 'httpReferrer',\n  browser: 'browser',\n  browserVersion: 'browserVersion',\n  os: 'os',\n  osVersion: 'osVersion',\n  device: 'device',\n  source: 'source',\n  medium: 'medium',\n  campaign: 'campaign',\n  content: 'content',\n  term: 'term',\n  country: 'country',\n  city: 'city',\n  fromUrl: 'fromUrl',\n  fromRoute: 'fromRoute',\n  userId: 'userId',\n  portalId: 'portalId',\n  portalUserId: 'portalUserId'\n};\n\nexports.Prisma.AnalyticsPageViewScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  uniqueVisitorId: 'uniqueVisitorId',\n  url: 'url',\n  route: 'route',\n  portalId: 'portalId',\n  portalUserId: 'portalUserId'\n};\n\nexports.Prisma.AnalyticsEventScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  uniqueVisitorId: 'uniqueVisitorId',\n  action: 'action',\n  category: 'category',\n  label: 'label',\n  value: 'value',\n  url: 'url',\n  route: 'route',\n  featureFlagId: 'featureFlagId',\n  portalId: 'portalId',\n  portalUserId: 'portalUserId'\n};\n\nexports.Prisma.BlogCategoryScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  color: 'color'\n};\n\nexports.Prisma.BlogTagScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  color: 'color'\n};\n\nexports.Prisma.BlogPostTagScalarFieldEnum = {\n  id: 'id',\n  postId: 'postId',\n  tagId: 'tagId'\n};\n\nexports.Prisma.BlogPostScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  slug: 'slug',\n  title: 'title',\n  description: 'description',\n  date: 'date',\n  image: 'image',\n  content: 'content',\n  readingTime: 'readingTime',\n  published: 'published',\n  authorId: 'authorId',\n  categoryId: 'categoryId',\n  contentType: 'contentType'\n};\n\nexports.Prisma.AppConfigurationScalarFieldEnum = {\n  id: 'id',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  url: 'url',\n  theme: 'theme',\n  authRequireEmailVerification: 'authRequireEmailVerification',\n  authRequireOrganization: 'authRequireOrganization',\n  authRequireName: 'authRequireName',\n  authRecaptchaSiteKey: 'authRecaptchaSiteKey',\n  analyticsEnabled: 'analyticsEnabled',\n  analyticsSimpleAnalytics: 'analyticsSimpleAnalytics',\n  analyticsPlausibleAnalytics: 'analyticsPlausibleAnalytics',\n  analyticsGoogleAnalyticsTrackingId: 'analyticsGoogleAnalyticsTrackingId',\n  subscriptionRequired: 'subscriptionRequired',\n  subscriptionAllowSubscribeBeforeSignUp: 'subscriptionAllowSubscribeBeforeSignUp',\n  subscriptionAllowSignUpBeforeSubscribe: 'subscriptionAllowSignUpBeforeSubscribe',\n  cookiesEnabled: 'cookiesEnabled',\n  metricsEnabled: 'metricsEnabled',\n  metricsLogToConsole: 'metricsLogToConsole',\n  metricsSaveToDatabase: 'metricsSaveToDatabase',\n  metricsIgnoreUrls: 'metricsIgnoreUrls',\n  brandingLogo: 'brandingLogo',\n  brandingLogoDarkMode: 'brandingLogoDarkMode',\n  brandingIcon: 'brandingIcon',\n  brandingIconDarkMode: 'brandingIconDarkMode',\n  brandingFavicon: 'brandingFavicon',\n  headScripts: 'headScripts',\n  bodyScripts: 'bodyScripts',\n  emailProvider: 'emailProvider',\n  emailFromEmail: 'emailFromEmail',\n  emailFromName: 'emailFromName',\n  emailSupportEmail: 'emailSupportEmail'\n};\n\nexports.Prisma.LogScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  apiKeyId: 'apiKeyId',\n  rowId: 'rowId',\n  url: 'url',\n  action: 'action',\n  details: 'details',\n  commentId: 'commentId'\n};\n\nexports.Prisma.ApiKeyScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId',\n  tenantId: 'tenantId',\n  key: 'key',\n  alias: 'alias',\n  expires: 'expires',\n  active: 'active'\n};\n\nexports.Prisma.ApiKeyLogScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  apiKeyId: 'apiKeyId',\n  tenantId: 'tenantId',\n  ip: 'ip',\n  endpoint: 'endpoint',\n  method: 'method',\n  params: 'params',\n  status: 'status',\n  duration: 'duration',\n  error: 'error',\n  type: 'type'\n};\n\nexports.Prisma.EventScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  name: 'name',\n  data: 'data',\n  resource: 'resource',\n  description: 'description'\n};\n\nexports.Prisma.EventWebhookAttemptScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  startedAt: 'startedAt',\n  finishedAt: 'finishedAt',\n  eventId: 'eventId',\n  endpoint: 'endpoint',\n  success: 'success',\n  status: 'status',\n  message: 'message',\n  body: 'body'\n};\n\nexports.Prisma.MetricLogScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  env: 'env',\n  type: 'type',\n  route: 'route',\n  url: 'url',\n  function: 'function',\n  duration: 'duration',\n  userId: 'userId',\n  tenantId: 'tenantId'\n};\n\nexports.Prisma.FileUploadProgressScalarFieldEnum = {\n  id: 'id',\n  fileName: 'fileName',\n  progressServer: 'progressServer',\n  progressStorage: 'progressStorage',\n  url: 'url',\n  error: 'error'\n};\n\nexports.Prisma.FileChunkScalarFieldEnum = {\n  id: 'id',\n  fileUploadId: 'fileUploadId',\n  index: 'index',\n  data: 'data'\n};\n\nexports.Prisma.IpAddressScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  ip: 'ip',\n  provider: 'provider',\n  type: 'type',\n  countryCode: 'countryCode',\n  countryName: 'countryName',\n  regionCode: 'regionCode',\n  regionName: 'regionName',\n  city: 'city',\n  zipCode: 'zipCode',\n  latitude: 'latitude',\n  longitude: 'longitude',\n  metadata: 'metadata'\n};\n\nexports.Prisma.IpAddressLogScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  ip: 'ip',\n  url: 'url',\n  action: 'action',\n  description: 'description',\n  success: 'success',\n  error: 'error',\n  metadata: 'metadata',\n  ipAddressId: 'ipAddressId'\n};\n\nexports.Prisma.WidgetScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  appearance: 'appearance',\n  metadata: 'metadata'\n};\n\nexports.Prisma.CredentialScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  value: 'value'\n};\n\nexports.Prisma.TenantInboundAddressScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  address: 'address'\n};\n\nexports.Prisma.EmailScalarFieldEnum = {\n  id: 'id',\n  tenantInboundAddressId: 'tenantInboundAddressId',\n  messageId: 'messageId',\n  type: 'type',\n  date: 'date',\n  subject: 'subject',\n  fromEmail: 'fromEmail',\n  fromName: 'fromName',\n  toEmail: 'toEmail',\n  toName: 'toName',\n  textBody: 'textBody',\n  htmlBody: 'htmlBody'\n};\n\nexports.Prisma.EmailReadScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  emailId: 'emailId',\n  userId: 'userId'\n};\n\nexports.Prisma.EmailCcScalarFieldEnum = {\n  id: 'id',\n  emailId: 'emailId',\n  toEmail: 'toEmail',\n  toName: 'toName'\n};\n\nexports.Prisma.EmailAttachmentScalarFieldEnum = {\n  id: 'id',\n  emailId: 'emailId',\n  name: 'name',\n  type: 'type',\n  length: 'length',\n  content: 'content',\n  publicUrl: 'publicUrl',\n  storageBucket: 'storageBucket',\n  storageProvider: 'storageProvider'\n};\n\nexports.Prisma.EmailSenderScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  provider: 'provider',\n  stream: 'stream',\n  apiKey: 'apiKey',\n  fromEmail: 'fromEmail',\n  fromName: 'fromName',\n  replyToEmail: 'replyToEmail'\n};\n\nexports.Prisma.CampaignScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  emailSenderId: 'emailSenderId',\n  name: 'name',\n  subject: 'subject',\n  htmlBody: 'htmlBody',\n  textBody: 'textBody',\n  status: 'status',\n  track: 'track',\n  sentAt: 'sentAt'\n};\n\nexports.Prisma.OutboundEmailScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  campaignId: 'campaignId',\n  contactRowId: 'contactRowId',\n  email: 'email',\n  fromSenderId: 'fromSenderId',\n  isPreview: 'isPreview',\n  error: 'error',\n  sentAt: 'sentAt',\n  deliveredAt: 'deliveredAt',\n  bouncedAt: 'bouncedAt',\n  spamComplainedAt: 'spamComplainedAt',\n  unsubscribedAt: 'unsubscribedAt'\n};\n\nexports.Prisma.OutboundEmailOpenScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  firstOpen: 'firstOpen',\n  outboundEmailId: 'outboundEmailId'\n};\n\nexports.Prisma.OutboundEmailClickScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  link: 'link',\n  outboundEmailId: 'outboundEmailId'\n};\n\nexports.Prisma.EntityScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  moduleId: 'moduleId',\n  name: 'name',\n  slug: 'slug',\n  order: 'order',\n  prefix: 'prefix',\n  type: 'type',\n  title: 'title',\n  titlePlural: 'titlePlural',\n  isAutogenerated: 'isAutogenerated',\n  isStepFormWizard: 'isStepFormWizard',\n  hasApi: 'hasApi',\n  icon: 'icon',\n  active: 'active',\n  showInSidebar: 'showInSidebar',\n  hasTags: 'hasTags',\n  hasComments: 'hasComments',\n  hasTasks: 'hasTasks',\n  hasActivity: 'hasActivity',\n  hasBulkDelete: 'hasBulkDelete',\n  hasViews: 'hasViews',\n  defaultVisibility: 'defaultVisibility',\n  onCreated: 'onCreated',\n  onEdit: 'onEdit',\n  promptFlowGroupId: 'promptFlowGroupId',\n  stepFormWizardId: 'stepFormWizardId'\n};\n\nexports.Prisma.PropertyScalarFieldEnum = {\n  id: 'id',\n  entityId: 'entityId',\n  order: 'order',\n  name: 'name',\n  title: 'title',\n  type: 'type',\n  subtype: 'subtype',\n  isDefault: 'isDefault',\n  isRequired: 'isRequired',\n  isHidden: 'isHidden',\n  isDisplay: 'isDisplay',\n  isUnique: 'isUnique',\n  isReadOnly: 'isReadOnly',\n  isSortable: 'isSortable',\n  isSearchable: 'isSearchable',\n  isFilterable: 'isFilterable',\n  showInCreate: 'showInCreate',\n  canUpdate: 'canUpdate',\n  isOverviewHeaderProperty: 'isOverviewHeaderProperty',\n  isOverviewSecondaryHeaderProperty: 'isOverviewSecondaryHeaderProperty',\n  isMetaProperty: 'isMetaProperty',\n  formulaId: 'formulaId',\n  tenantId: 'tenantId'\n};\n\nexports.Prisma.EntityViewScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  createdByUserId: 'createdByUserId',\n  entityId: 'entityId',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  layout: 'layout',\n  order: 'order',\n  name: 'name',\n  title: 'title',\n  pageSize: 'pageSize',\n  isDefault: 'isDefault',\n  isSystem: 'isSystem',\n  gridColumns: 'gridColumns',\n  gridColumnsSm: 'gridColumnsSm',\n  gridColumnsMd: 'gridColumnsMd',\n  gridColumnsLg: 'gridColumnsLg',\n  gridColumnsXl: 'gridColumnsXl',\n  gridColumns2xl: 'gridColumns2xl',\n  gridGap: 'gridGap',\n  groupByPropertyId: 'groupByPropertyId'\n};\n\nexports.Prisma.EntityViewPropertyScalarFieldEnum = {\n  id: 'id',\n  entityViewId: 'entityViewId',\n  propertyId: 'propertyId',\n  name: 'name',\n  order: 'order'\n};\n\nexports.Prisma.EntityViewFilterScalarFieldEnum = {\n  id: 'id',\n  entityViewId: 'entityViewId',\n  match: 'match',\n  name: 'name',\n  condition: 'condition',\n  value: 'value'\n};\n\nexports.Prisma.EntityViewSortScalarFieldEnum = {\n  id: 'id',\n  entityViewId: 'entityViewId',\n  name: 'name',\n  asc: 'asc',\n  order: 'order'\n};\n\nexports.Prisma.PropertyAttributeScalarFieldEnum = {\n  id: 'id',\n  propertyId: 'propertyId',\n  name: 'name',\n  value: 'value'\n};\n\nexports.Prisma.PropertyOptionScalarFieldEnum = {\n  id: 'id',\n  propertyId: 'propertyId',\n  order: 'order',\n  value: 'value',\n  name: 'name',\n  color: 'color'\n};\n\nexports.Prisma.EntityTagScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  entityId: 'entityId',\n  value: 'value',\n  color: 'color'\n};\n\nexports.Prisma.EntityTenantUserPermissionScalarFieldEnum = {\n  id: 'id',\n  entityId: 'entityId',\n  level: 'level'\n};\n\nexports.Prisma.EntityWebhookScalarFieldEnum = {\n  id: 'id',\n  entityId: 'entityId',\n  action: 'action',\n  method: 'method',\n  endpoint: 'endpoint'\n};\n\nexports.Prisma.EntityWebhookLogScalarFieldEnum = {\n  id: 'id',\n  webhookId: 'webhookId',\n  logId: 'logId',\n  status: 'status',\n  error: 'error'\n};\n\nexports.Prisma.EntityRelationshipScalarFieldEnum = {\n  id: 'id',\n  parentId: 'parentId',\n  childId: 'childId',\n  order: 'order',\n  title: 'title',\n  type: 'type',\n  required: 'required',\n  cascade: 'cascade',\n  distinct: 'distinct',\n  readOnly: 'readOnly',\n  hiddenIfEmpty: 'hiddenIfEmpty',\n  childEntityViewId: 'childEntityViewId',\n  parentEntityViewId: 'parentEntityViewId'\n};\n\nexports.Prisma.SampleCustomEntityScalarFieldEnum = {\n  rowId: 'rowId',\n  customText: 'customText',\n  customNumber: 'customNumber',\n  customDate: 'customDate',\n  customBoolean: 'customBoolean',\n  customSelect: 'customSelect'\n};\n\nexports.Prisma.RowRelationshipScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  relationshipId: 'relationshipId',\n  parentId: 'parentId',\n  childId: 'childId',\n  metadata: 'metadata'\n};\n\nexports.Prisma.RowScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  deletedAt: 'deletedAt',\n  entityId: 'entityId',\n  tenantId: 'tenantId',\n  folio: 'folio',\n  createdByUserId: 'createdByUserId',\n  createdByApiKeyId: 'createdByApiKeyId',\n  order: 'order',\n  stepFormWizardSessionId: 'stepFormWizardSessionId',\n  stepFormWizardId: 'stepFormWizardId',\n  loyalityId: 'loyalityId'\n};\n\nexports.Prisma.RowValueScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  rowId: 'rowId',\n  propertyId: 'propertyId',\n  textValue: 'textValue',\n  numberValue: 'numberValue',\n  dateValue: 'dateValue',\n  booleanValue: 'booleanValue'\n};\n\nexports.Prisma.RowValueMultipleScalarFieldEnum = {\n  id: 'id',\n  rowValueId: 'rowValueId',\n  order: 'order',\n  value: 'value'\n};\n\nexports.Prisma.RowValueRangeScalarFieldEnum = {\n  rowValueId: 'rowValueId',\n  numberMin: 'numberMin',\n  numberMax: 'numberMax',\n  dateMin: 'dateMin',\n  dateMax: 'dateMax'\n};\n\nexports.Prisma.RowPermissionScalarFieldEnum = {\n  id: 'id',\n  rowId: 'rowId',\n  tenantId: 'tenantId',\n  roleId: 'roleId',\n  groupId: 'groupId',\n  userId: 'userId',\n  public: 'public',\n  access: 'access'\n};\n\nexports.Prisma.RowMediaScalarFieldEnum = {\n  id: 'id',\n  rowValueId: 'rowValueId',\n  title: 'title',\n  name: 'name',\n  file: 'file',\n  type: 'type',\n  publicUrl: 'publicUrl',\n  storageBucket: 'storageBucket',\n  storageProvider: 'storageProvider'\n};\n\nexports.Prisma.RowTagScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  rowId: 'rowId',\n  tagId: 'tagId'\n};\n\nexports.Prisma.RowCommentScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId',\n  rowId: 'rowId',\n  value: 'value',\n  isDeleted: 'isDeleted'\n};\n\nexports.Prisma.RowCommentReactionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId',\n  rowCommentId: 'rowCommentId',\n  reaction: 'reaction'\n};\n\nexports.Prisma.RowTaskScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId',\n  rowId: 'rowId',\n  title: 'title',\n  description: 'description',\n  completed: 'completed',\n  completedAt: 'completedAt',\n  completedByUserId: 'completedByUserId',\n  priority: 'priority',\n  assignedToUserId: 'assignedToUserId',\n  deadline: 'deadline'\n};\n\nexports.Prisma.EntityTemplateScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  entityId: 'entityId',\n  title: 'title',\n  config: 'config'\n};\n\nexports.Prisma.EntityGroupScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  order: 'order',\n  slug: 'slug',\n  title: 'title',\n  icon: 'icon',\n  collapsible: 'collapsible',\n  section: 'section'\n};\n\nexports.Prisma.EntityGroupEntityScalarFieldEnum = {\n  id: 'id',\n  entityGroupId: 'entityGroupId',\n  entityId: 'entityId',\n  allViewId: 'allViewId',\n  selectMin: 'selectMin',\n  selectMax: 'selectMax'\n};\n\nexports.Prisma.FormulaScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  name: 'name',\n  description: 'description',\n  resultAs: 'resultAs',\n  calculationTrigger: 'calculationTrigger',\n  withLogs: 'withLogs'\n};\n\nexports.Prisma.FormulaComponentScalarFieldEnum = {\n  id: 'id',\n  formulaId: 'formulaId',\n  order: 'order',\n  type: 'type',\n  value: 'value'\n};\n\nexports.Prisma.FormulaLogScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  formulaId: 'formulaId',\n  userId: 'userId',\n  tenantId: 'tenantId',\n  originalTrigger: 'originalTrigger',\n  triggeredBy: 'triggeredBy',\n  expression: 'expression',\n  result: 'result',\n  duration: 'duration',\n  error: 'error',\n  rowValueId: 'rowValueId'\n};\n\nexports.Prisma.FormulaComponentLogScalarFieldEnum = {\n  id: 'id',\n  order: 'order',\n  type: 'type',\n  value: 'value',\n  rowId: 'rowId',\n  formulaLogId: 'formulaLogId'\n};\n\nexports.Prisma.EntityGroupConfigurationScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  entityGroupId: 'entityGroupId',\n  title: 'title'\n};\n\nexports.Prisma.EntityGroupConfigurationRowScalarFieldEnum = {\n  id: 'id',\n  entityGroupConfigurationId: 'entityGroupConfigurationId',\n  rowId: 'rowId'\n};\n\nexports.Prisma.ApiKeyEntityScalarFieldEnum = {\n  id: 'id',\n  apiKeyId: 'apiKeyId',\n  entityId: 'entityId',\n  create: 'create',\n  read: 'read',\n  update: 'update',\n  delete: 'delete'\n};\n\nexports.Prisma.TenantSettingsRowScalarFieldEnum = {\n  tenantId: 'tenantId',\n  rowId: 'rowId'\n};\n\nexports.Prisma.FeatureFlagScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  description: 'description',\n  enabled: 'enabled'\n};\n\nexports.Prisma.FeatureFlagFilterScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  featureFlagId: 'featureFlagId',\n  type: 'type',\n  value: 'value',\n  action: 'action'\n};\n\nexports.Prisma.FeedbackScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  message: 'message',\n  fromUrl: 'fromUrl'\n};\n\nexports.Prisma.SurveyScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  title: 'title',\n  slug: 'slug',\n  description: 'description',\n  isEnabled: 'isEnabled',\n  isPublic: 'isPublic',\n  minSubmissions: 'minSubmissions',\n  image: 'image'\n};\n\nexports.Prisma.SurveyItemScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  surveyId: 'surveyId',\n  title: 'title',\n  description: 'description',\n  shortName: 'shortName',\n  type: 'type',\n  order: 'order',\n  categories: 'categories',\n  href: 'href',\n  color: 'color',\n  options: 'options',\n  style: 'style'\n};\n\nexports.Prisma.SurveySubmissionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  surveyId: 'surveyId',\n  userAnalyticsId: 'userAnalyticsId',\n  ipAddress: 'ipAddress'\n};\n\nexports.Prisma.SurveySubmissionResultScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  surveySubmissionId: 'surveySubmissionId',\n  surveItemTitle: 'surveItemTitle',\n  surveItemType: 'surveItemType',\n  value: 'value',\n  other: 'other'\n};\n\nexports.Prisma.KnowledgeBaseScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  basePath: 'basePath',\n  slug: 'slug',\n  title: 'title',\n  description: 'description',\n  defaultLanguage: 'defaultLanguage',\n  layout: 'layout',\n  color: 'color',\n  enabled: 'enabled',\n  languages: 'languages',\n  links: 'links',\n  logo: 'logo',\n  seoImage: 'seoImage'\n};\n\nexports.Prisma.KnowledgeBaseCategoryScalarFieldEnum = {\n  id: 'id',\n  knowledgeBaseId: 'knowledgeBaseId',\n  slug: 'slug',\n  order: 'order',\n  title: 'title',\n  description: 'description',\n  icon: 'icon',\n  language: 'language',\n  seoImage: 'seoImage'\n};\n\nexports.Prisma.KnowledgeBaseCategorySectionScalarFieldEnum = {\n  id: 'id',\n  categoryId: 'categoryId',\n  order: 'order',\n  title: 'title',\n  description: 'description'\n};\n\nexports.Prisma.KnowledgeBaseArticleScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  knowledgeBaseId: 'knowledgeBaseId',\n  categoryId: 'categoryId',\n  sectionId: 'sectionId',\n  slug: 'slug',\n  title: 'title',\n  description: 'description',\n  order: 'order',\n  contentDraft: 'contentDraft',\n  contentPublished: 'contentPublished',\n  contentPublishedAsText: 'contentPublishedAsText',\n  contentType: 'contentType',\n  language: 'language',\n  featuredOrder: 'featuredOrder',\n  seoImage: 'seoImage',\n  publishedAt: 'publishedAt',\n  relatedInArticleId: 'relatedInArticleId',\n  createdByUserId: 'createdByUserId'\n};\n\nexports.Prisma.KnowledgeBaseRelatedArticleScalarFieldEnum = {\n  id: 'id',\n  articleId: 'articleId',\n  relatedArticleId: 'relatedArticleId'\n};\n\nexports.Prisma.KnowledgeBaseViewsScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  knowledgeBaseId: 'knowledgeBaseId',\n  userAnalyticsId: 'userAnalyticsId'\n};\n\nexports.Prisma.KnowledgeBaseArticleViewsScalarFieldEnum = {\n  knowledgeBaseArticleId: 'knowledgeBaseArticleId',\n  userAnalyticsId: 'userAnalyticsId'\n};\n\nexports.Prisma.KnowledgeBaseArticleUpvotesScalarFieldEnum = {\n  createdAt: 'createdAt',\n  knowledgeBaseArticleId: 'knowledgeBaseArticleId',\n  userAnalyticsId: 'userAnalyticsId'\n};\n\nexports.Prisma.KnowledgeBaseArticleDownvotesScalarFieldEnum = {\n  createdAt: 'createdAt',\n  knowledgeBaseArticleId: 'knowledgeBaseArticleId',\n  userAnalyticsId: 'userAnalyticsId'\n};\n\nexports.Prisma.OnboardingScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  title: 'title',\n  type: 'type',\n  realtime: 'realtime',\n  active: 'active',\n  canBeDismissed: 'canBeDismissed',\n  height: 'height'\n};\n\nexports.Prisma.OnboardingFilterScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  onboardingId: 'onboardingId',\n  type: 'type',\n  value: 'value'\n};\n\nexports.Prisma.OnboardingStepScalarFieldEnum = {\n  id: 'id',\n  onboardingId: 'onboardingId',\n  order: 'order',\n  block: 'block'\n};\n\nexports.Prisma.OnboardingSessionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  onboardingId: 'onboardingId',\n  userId: 'userId',\n  tenantId: 'tenantId',\n  status: 'status',\n  startedAt: 'startedAt',\n  completedAt: 'completedAt',\n  dismissedAt: 'dismissedAt',\n  createdRealtime: 'createdRealtime'\n};\n\nexports.Prisma.OnboardingSessionActionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  onboardingSessionId: 'onboardingSessionId',\n  type: 'type',\n  name: 'name',\n  value: 'value'\n};\n\nexports.Prisma.OnboardingSessionFilterMatchScalarFieldEnum = {\n  id: 'id',\n  onboardingFilterId: 'onboardingFilterId',\n  onboardingSessionId: 'onboardingSessionId'\n};\n\nexports.Prisma.OnboardingSessionStepScalarFieldEnum = {\n  id: 'id',\n  onboardingSessionId: 'onboardingSessionId',\n  stepId: 'stepId',\n  seenAt: 'seenAt',\n  completedAt: 'completedAt'\n};\n\nexports.Prisma.PageScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  slug: 'slug',\n  isPublished: 'isPublished',\n  isPublic: 'isPublic'\n};\n\nexports.Prisma.PageMetaTagScalarFieldEnum = {\n  id: 'id',\n  pageId: 'pageId',\n  order: 'order',\n  name: 'name',\n  value: 'value'\n};\n\nexports.Prisma.PageBlockScalarFieldEnum = {\n  id: 'id',\n  pageId: 'pageId',\n  order: 'order',\n  type: 'type',\n  value: 'value'\n};\n\nexports.Prisma.RoleScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  description: 'description',\n  type: 'type',\n  assignToNewUsers: 'assignToNewUsers',\n  isDefault: 'isDefault',\n  order: 'order'\n};\n\nexports.Prisma.PermissionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  description: 'description',\n  type: 'type',\n  isDefault: 'isDefault',\n  order: 'order',\n  entityId: 'entityId'\n};\n\nexports.Prisma.RolePermissionScalarFieldEnum = {\n  id: 'id',\n  roleId: 'roleId',\n  permissionId: 'permissionId'\n};\n\nexports.Prisma.UserRoleScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  userId: 'userId',\n  roleId: 'roleId',\n  tenantId: 'tenantId'\n};\n\nexports.Prisma.GroupScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  createdByUserId: 'createdByUserId',\n  tenantId: 'tenantId',\n  name: 'name',\n  description: 'description',\n  color: 'color'\n};\n\nexports.Prisma.GroupUserScalarFieldEnum = {\n  id: 'id',\n  groupId: 'groupId',\n  userId: 'userId'\n};\n\nexports.Prisma.PortalScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  createdByUserId: 'createdByUserId',\n  subdomain: 'subdomain',\n  domain: 'domain',\n  title: 'title',\n  isPublished: 'isPublished',\n  stripeAccountId: 'stripeAccountId',\n  themeColor: 'themeColor',\n  themeScheme: 'themeScheme',\n  seoTitle: 'seoTitle',\n  seoDescription: 'seoDescription',\n  seoImage: 'seoImage',\n  seoThumbnail: 'seoThumbnail',\n  seoTwitterCreator: 'seoTwitterCreator',\n  seoTwitterSite: 'seoTwitterSite',\n  seoKeywords: 'seoKeywords',\n  authRequireEmailVerification: 'authRequireEmailVerification',\n  authRequireOrganization: 'authRequireOrganization',\n  authRequireName: 'authRequireName',\n  analyticsSimpleAnalytics: 'analyticsSimpleAnalytics',\n  analyticsPlausibleAnalytics: 'analyticsPlausibleAnalytics',\n  analyticsGoogleAnalyticsTrackingId: 'analyticsGoogleAnalyticsTrackingId',\n  brandingLogo: 'brandingLogo',\n  brandingLogoDarkMode: 'brandingLogoDarkMode',\n  brandingIcon: 'brandingIcon',\n  brandingIconDarkMode: 'brandingIconDarkMode',\n  brandingFavicon: 'brandingFavicon',\n  affiliatesRewardfulApiKey: 'affiliatesRewardfulApiKey',\n  affiliatesRewardfulUrl: 'affiliatesRewardfulUrl',\n  metadata: 'metadata'\n};\n\nexports.Prisma.PortalUserScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  portalId: 'portalId',\n  email: 'email',\n  passwordHash: 'passwordHash',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  avatar: 'avatar',\n  phone: 'phone',\n  verifyToken: 'verifyToken',\n  githubId: 'githubId',\n  googleId: 'googleId',\n  locale: 'locale'\n};\n\nexports.Prisma.PortalPageScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  portalId: 'portalId',\n  name: 'name',\n  attributes: 'attributes'\n};\n\nexports.Prisma.PortalUserRegistrationScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  portalId: 'portalId',\n  email: 'email',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  slug: 'slug',\n  token: 'token',\n  ipAddress: 'ipAddress',\n  company: 'company',\n  selectedSubscriptionPriceId: 'selectedSubscriptionPriceId',\n  createdPortalUserId: 'createdPortalUserId'\n};\n\nexports.Prisma.PortalSubscriptionProductScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  stripeId: 'stripeId',\n  order: 'order',\n  title: 'title',\n  active: 'active',\n  model: 'model',\n  public: 'public',\n  groupTitle: 'groupTitle',\n  groupDescription: 'groupDescription',\n  description: 'description',\n  badge: 'badge',\n  billingAddressCollection: 'billingAddressCollection',\n  hasQuantity: 'hasQuantity',\n  canBuyAgain: 'canBuyAgain'\n};\n\nexports.Prisma.PortalSubscriptionPriceScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  subscriptionProductId: 'subscriptionProductId',\n  stripeId: 'stripeId',\n  type: 'type',\n  billingPeriod: 'billingPeriod',\n  price: 'price',\n  currency: 'currency',\n  trialDays: 'trialDays',\n  active: 'active'\n};\n\nexports.Prisma.PortalSubscriptionFeatureScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  subscriptionProductId: 'subscriptionProductId',\n  order: 'order',\n  title: 'title',\n  name: 'name',\n  type: 'type',\n  value: 'value',\n  href: 'href',\n  badge: 'badge',\n  accumulate: 'accumulate'\n};\n\nexports.Prisma.PortalUserSubscriptionScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  portalUserId: 'portalUserId',\n  stripeCustomerId: 'stripeCustomerId'\n};\n\nexports.Prisma.PortalUserSubscriptionProductScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  createdAt: 'createdAt',\n  portalUserSubscriptionId: 'portalUserSubscriptionId',\n  subscriptionProductId: 'subscriptionProductId',\n  cancelledAt: 'cancelledAt',\n  endsAt: 'endsAt',\n  stripeSubscriptionId: 'stripeSubscriptionId',\n  quantity: 'quantity',\n  fromCheckoutSessionId: 'fromCheckoutSessionId',\n  currentPeriodStart: 'currentPeriodStart',\n  currentPeriodEnd: 'currentPeriodEnd'\n};\n\nexports.Prisma.PortalUserSubscriptionProductPriceScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  portalUserSubscriptionProductId: 'portalUserSubscriptionProductId',\n  subscriptionPriceId: 'subscriptionPriceId'\n};\n\nexports.Prisma.PortalCheckoutSessionStatusScalarFieldEnum = {\n  id: 'id',\n  portalId: 'portalId',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  pending: 'pending',\n  email: 'email',\n  fromUrl: 'fromUrl',\n  fromUserId: 'fromUserId',\n  createdUserId: 'createdUserId'\n};\n\nexports.Prisma.PromptFlowGroupScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  order: 'order',\n  title: 'title',\n  description: 'description'\n};\n\nexports.Prisma.PromptFlowGroupTemplateScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  order: 'order',\n  title: 'title',\n  promptFlowGroupId: 'promptFlowGroupId'\n};\n\nexports.Prisma.PromptFlowGroupEntityScalarFieldEnum = {\n  entityId: 'entityId',\n  promptFlowGroupId: 'promptFlowGroupId'\n};\n\nexports.Prisma.PromptFlowScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  model: 'model',\n  title: 'title',\n  description: 'description',\n  actionTitle: 'actionTitle',\n  executionType: 'executionType',\n  promptFlowGroupId: 'promptFlowGroupId',\n  stream: 'stream',\n  public: 'public',\n  inputEntityId: 'inputEntityId'\n};\n\nexports.Prisma.PromptFlowInputVariableScalarFieldEnum = {\n  id: 'id',\n  promptFlowId: 'promptFlowId',\n  type: 'type',\n  name: 'name',\n  title: 'title',\n  isRequired: 'isRequired'\n};\n\nexports.Prisma.PromptTemplateScalarFieldEnum = {\n  id: 'id',\n  flowId: 'flowId',\n  order: 'order',\n  title: 'title',\n  template: 'template',\n  temperature: 'temperature',\n  maxTokens: 'maxTokens',\n  generations: 'generations'\n};\n\nexports.Prisma.PromptFlowOutputScalarFieldEnum = {\n  id: 'id',\n  promptFlowId: 'promptFlowId',\n  type: 'type',\n  entityId: 'entityId'\n};\n\nexports.Prisma.PromptFlowOutputMappingScalarFieldEnum = {\n  id: 'id',\n  promptFlowOutputId: 'promptFlowOutputId',\n  promptTemplateId: 'promptTemplateId',\n  propertyId: 'propertyId'\n};\n\nexports.Prisma.PromptFlowExecutionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  flowId: 'flowId',\n  model: 'model',\n  userId: 'userId',\n  tenantId: 'tenantId',\n  status: 'status',\n  error: 'error',\n  startedAt: 'startedAt',\n  completedAt: 'completedAt',\n  duration: 'duration'\n};\n\nexports.Prisma.PromptTemplateResultScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  flowExecutionId: 'flowExecutionId',\n  templateId: 'templateId',\n  order: 'order',\n  status: 'status',\n  prompt: 'prompt',\n  response: 'response',\n  error: 'error',\n  startedAt: 'startedAt',\n  completedAt: 'completedAt'\n};\n\nexports.Prisma.StepFormWizardScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  title: 'title',\n  type: 'type',\n  realtime: 'realtime',\n  active: 'active',\n  canBeDismissed: 'canBeDismissed',\n  height: 'height',\n  progressBar: 'progressBar',\n  entity: 'entity'\n};\n\nexports.Prisma.StepFormWizardFilterScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  stepFormWizardId: 'stepFormWizardId',\n  type: 'type',\n  value: 'value'\n};\n\nexports.Prisma.StepFormWizardStepScalarFieldEnum = {\n  id: 'id',\n  stepFormWizardId: 'stepFormWizardId',\n  order: 'order',\n  block: 'block'\n};\n\nexports.Prisma.StepFormWizardSessionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  stepFormWizardId: 'stepFormWizardId',\n  userId: 'userId',\n  tenantId: 'tenantId',\n  status: 'status',\n  startedAt: 'startedAt',\n  completedAt: 'completedAt',\n  dismissedAt: 'dismissedAt',\n  createdRealtime: 'createdRealtime',\n  logicAppRunId: 'logicAppRunId',\n  callBackURL: 'callBackURL',\n  currentStepIndex: 'currentStepIndex',\n  error: 'error'\n};\n\nexports.Prisma.StepFormWizardSessionActionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  stepFormWizardSessionId: 'stepFormWizardSessionId',\n  type: 'type',\n  name: 'name',\n  value: 'value'\n};\n\nexports.Prisma.StepFormWizardSessionFilterMatchScalarFieldEnum = {\n  id: 'id',\n  stepFormWizardFilterId: 'stepFormWizardFilterId',\n  stepFormWizardSessionId: 'stepFormWizardSessionId'\n};\n\nexports.Prisma.StepFormWizardSessionStepScalarFieldEnum = {\n  id: 'id',\n  stepFormWizardSessionId: 'stepFormWizardSessionId',\n  stepId: 'stepId',\n  seenAt: 'seenAt',\n  completedAt: 'completedAt',\n  status: 'status',\n  webHookTriggeredAt: 'webHookTriggeredAt',\n  error: 'error'\n};\n\nexports.Prisma.SubscriptionProductScalarFieldEnum = {\n  id: 'id',\n  stripeId: 'stripeId',\n  order: 'order',\n  title: 'title',\n  active: 'active',\n  model: 'model',\n  public: 'public',\n  groupTitle: 'groupTitle',\n  groupDescription: 'groupDescription',\n  description: 'description',\n  badge: 'badge',\n  billingAddressCollection: 'billingAddressCollection',\n  hasQuantity: 'hasQuantity',\n  canBuyAgain: 'canBuyAgain'\n};\n\nexports.Prisma.SubscriptionPriceScalarFieldEnum = {\n  id: 'id',\n  subscriptionProductId: 'subscriptionProductId',\n  stripeId: 'stripeId',\n  type: 'type',\n  billingPeriod: 'billingPeriod',\n  price: 'price',\n  currency: 'currency',\n  trialDays: 'trialDays',\n  active: 'active'\n};\n\nexports.Prisma.SubscriptionUsageBasedPriceScalarFieldEnum = {\n  id: 'id',\n  subscriptionProductId: 'subscriptionProductId',\n  stripeId: 'stripeId',\n  billingPeriod: 'billingPeriod',\n  currency: 'currency',\n  unit: 'unit',\n  unitTitle: 'unitTitle',\n  unitTitlePlural: 'unitTitlePlural',\n  usageType: 'usageType',\n  aggregateUsage: 'aggregateUsage',\n  tiersMode: 'tiersMode',\n  billingScheme: 'billingScheme'\n};\n\nexports.Prisma.SubscriptionUsageBasedTierScalarFieldEnum = {\n  id: 'id',\n  subscriptionUsageBasedPriceId: 'subscriptionUsageBasedPriceId',\n  from: 'from',\n  to: 'to',\n  perUnitPrice: 'perUnitPrice',\n  flatFeePrice: 'flatFeePrice'\n};\n\nexports.Prisma.SubscriptionFeatureScalarFieldEnum = {\n  id: 'id',\n  subscriptionProductId: 'subscriptionProductId',\n  order: 'order',\n  title: 'title',\n  name: 'name',\n  type: 'type',\n  value: 'value',\n  href: 'href',\n  badge: 'badge',\n  accumulate: 'accumulate'\n};\n\nexports.Prisma.TenantSubscriptionScalarFieldEnum = {\n  id: 'id',\n  tenantId: 'tenantId',\n  stripeCustomerId: 'stripeCustomerId'\n};\n\nexports.Prisma.TenantSubscriptionProductScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantSubscriptionId: 'tenantSubscriptionId',\n  subscriptionProductId: 'subscriptionProductId',\n  cancelledAt: 'cancelledAt',\n  endsAt: 'endsAt',\n  stripeSubscriptionId: 'stripeSubscriptionId',\n  quantity: 'quantity',\n  fromCheckoutSessionId: 'fromCheckoutSessionId',\n  currentPeriodStart: 'currentPeriodStart',\n  currentPeriodEnd: 'currentPeriodEnd'\n};\n\nexports.Prisma.TenantSubscriptionProductPriceScalarFieldEnum = {\n  id: 'id',\n  tenantSubscriptionProductId: 'tenantSubscriptionProductId',\n  subscriptionPriceId: 'subscriptionPriceId',\n  subscriptionUsageBasedPriceId: 'subscriptionUsageBasedPriceId'\n};\n\nexports.Prisma.TenantSubscriptionUsageRecordScalarFieldEnum = {\n  id: 'id',\n  tenantSubscriptionProductPriceId: 'tenantSubscriptionProductPriceId',\n  timestamp: 'timestamp',\n  quantity: 'quantity',\n  stripeSubscriptionItemId: 'stripeSubscriptionItemId'\n};\n\nexports.Prisma.CheckoutSessionStatusScalarFieldEnum = {\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  id: 'id',\n  pending: 'pending',\n  email: 'email',\n  fromUrl: 'fromUrl',\n  fromUserId: 'fromUserId',\n  fromTenantId: 'fromTenantId',\n  createdUserId: 'createdUserId',\n  createdTenantId: 'createdTenantId'\n};\n\nexports.Prisma.CreditScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  tenantId: 'tenantId',\n  userId: 'userId',\n  amount: 'amount',\n  type: 'type',\n  objectId: 'objectId'\n};\n\nexports.Prisma.WorkflowScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  name: 'name',\n  description: 'description',\n  status: 'status',\n  tenantId: 'tenantId',\n  createdByUserId: 'createdByUserId',\n  appliesToAllTenants: 'appliesToAllTenants'\n};\n\nexports.Prisma.WorkflowBlockScalarFieldEnum = {\n  id: 'id',\n  workflowId: 'workflowId',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  type: 'type',\n  description: 'description',\n  isTrigger: 'isTrigger',\n  isBlock: 'isBlock',\n  input: 'input'\n};\n\nexports.Prisma.WorkflowBlockConditionGroupScalarFieldEnum = {\n  id: 'id',\n  workflowBlockId: 'workflowBlockId',\n  index: 'index',\n  type: 'type'\n};\n\nexports.Prisma.WorkflowBlockConditionScalarFieldEnum = {\n  id: 'id',\n  workflowBlockConditionGroupId: 'workflowBlockConditionGroupId',\n  index: 'index',\n  variable: 'variable',\n  operator: 'operator',\n  value: 'value'\n};\n\nexports.Prisma.WorkflowBlockToBlockScalarFieldEnum = {\n  id: 'id',\n  fromBlockId: 'fromBlockId',\n  toBlockId: 'toBlockId',\n  condition: 'condition'\n};\n\nexports.Prisma.WorkflowExecutionScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  workflowId: 'workflowId',\n  tenantId: 'tenantId',\n  type: 'type',\n  status: 'status',\n  input: 'input',\n  output: 'output',\n  duration: 'duration',\n  endedAt: 'endedAt',\n  error: 'error',\n  waitingBlockId: 'waitingBlockId',\n  createdByUserId: 'createdByUserId',\n  appliesToAllTenants: 'appliesToAllTenants'\n};\n\nexports.Prisma.WorkflowInputExampleScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  workflowId: 'workflowId',\n  title: 'title',\n  input: 'input'\n};\n\nexports.Prisma.WorkflowBlockExecutionScalarFieldEnum = {\n  id: 'id',\n  workflowExecutionId: 'workflowExecutionId',\n  workflowBlockId: 'workflowBlockId',\n  fromWorkflowBlockId: 'fromWorkflowBlockId',\n  status: 'status',\n  startedAt: 'startedAt',\n  input: 'input',\n  output: 'output',\n  duration: 'duration',\n  endedAt: 'endedAt',\n  error: 'error'\n};\n\nexports.Prisma.WorkflowVariableScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  value: 'value',\n  createdByUserId: 'createdByUserId',\n  userId: 'userId'\n};\n\nexports.Prisma.WorkflowCredentialScalarFieldEnum = {\n  id: 'id',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  tenantId: 'tenantId',\n  name: 'name',\n  value: 'value',\n  createdByUserId: 'createdByUserId',\n  userId: 'userId'\n};\n\nexports.Prisma.SortOrder = {\n  asc: 'asc',\n  desc: 'desc'\n};\n\nexports.Prisma.JsonNullValueInput = {\n  JsonNull: Prisma.JsonNull\n};\n\nexports.Prisma.NullableJsonNullValueInput = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull\n};\n\nexports.Prisma.QueryMode = {\n  default: 'default',\n  insensitive: 'insensitive'\n};\n\nexports.Prisma.NullsOrder = {\n  first: 'first',\n  last: 'last'\n};\n\nexports.Prisma.JsonNullValueFilter = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull,\n  AnyNull: Prisma.AnyNull\n};\nexports.LogicAppStatus = exports.$Enums.LogicAppStatus = {\n  PENDING: 'PENDING',\n  SUCCESS: 'SUCCESS',\n  FAILED: 'FAILED'\n};\n\nexports.Prisma.ModelName = {\n  User: 'User',\n  AdminUser: 'AdminUser',\n  Tenant: 'Tenant',\n  TenantUser: 'TenantUser',\n  TenantUserInvitation: 'TenantUserInvitation',\n  Registration: 'Registration',\n  Blacklist: 'Blacklist',\n  TenantIpAddress: 'TenantIpAddress',\n  TenantType: 'TenantType',\n  CustomTheme: 'CustomTheme',\n  Loyality: 'Loyality',\n  Milestone: 'Milestone',\n  Reward: 'Reward',\n  RowMilestone: 'RowMilestone',\n  RowReward: 'RowReward',\n  RowCoins: 'RowCoins',\n  AnalyticsSettings: 'AnalyticsSettings',\n  AnalyticsUniqueVisitor: 'AnalyticsUniqueVisitor',\n  AnalyticsPageView: 'AnalyticsPageView',\n  AnalyticsEvent: 'AnalyticsEvent',\n  BlogCategory: 'BlogCategory',\n  BlogTag: 'BlogTag',\n  BlogPostTag: 'BlogPostTag',\n  BlogPost: 'BlogPost',\n  AppConfiguration: 'AppConfiguration',\n  Log: 'Log',\n  ApiKey: 'ApiKey',\n  ApiKeyLog: 'ApiKeyLog',\n  Event: 'Event',\n  EventWebhookAttempt: 'EventWebhookAttempt',\n  MetricLog: 'MetricLog',\n  FileUploadProgress: 'FileUploadProgress',\n  FileChunk: 'FileChunk',\n  IpAddress: 'IpAddress',\n  IpAddressLog: 'IpAddressLog',\n  Widget: 'Widget',\n  Credential: 'Credential',\n  TenantInboundAddress: 'TenantInboundAddress',\n  Email: 'Email',\n  EmailRead: 'EmailRead',\n  EmailCc: 'EmailCc',\n  EmailAttachment: 'EmailAttachment',\n  EmailSender: 'EmailSender',\n  Campaign: 'Campaign',\n  OutboundEmail: 'OutboundEmail',\n  OutboundEmailOpen: 'OutboundEmailOpen',\n  OutboundEmailClick: 'OutboundEmailClick',\n  Entity: 'Entity',\n  Property: 'Property',\n  EntityView: 'EntityView',\n  EntityViewProperty: 'EntityViewProperty',\n  EntityViewFilter: 'EntityViewFilter',\n  EntityViewSort: 'EntityViewSort',\n  PropertyAttribute: 'PropertyAttribute',\n  PropertyOption: 'PropertyOption',\n  EntityTag: 'EntityTag',\n  EntityTenantUserPermission: 'EntityTenantUserPermission',\n  EntityWebhook: 'EntityWebhook',\n  EntityWebhookLog: 'EntityWebhookLog',\n  EntityRelationship: 'EntityRelationship',\n  SampleCustomEntity: 'SampleCustomEntity',\n  RowRelationship: 'RowRelationship',\n  Row: 'Row',\n  RowValue: 'RowValue',\n  RowValueMultiple: 'RowValueMultiple',\n  RowValueRange: 'RowValueRange',\n  RowPermission: 'RowPermission',\n  RowMedia: 'RowMedia',\n  RowTag: 'RowTag',\n  RowComment: 'RowComment',\n  RowCommentReaction: 'RowCommentReaction',\n  RowTask: 'RowTask',\n  EntityTemplate: 'EntityTemplate',\n  EntityGroup: 'EntityGroup',\n  EntityGroupEntity: 'EntityGroupEntity',\n  Formula: 'Formula',\n  FormulaComponent: 'FormulaComponent',\n  FormulaLog: 'FormulaLog',\n  FormulaComponentLog: 'FormulaComponentLog',\n  EntityGroupConfiguration: 'EntityGroupConfiguration',\n  EntityGroupConfigurationRow: 'EntityGroupConfigurationRow',\n  ApiKeyEntity: 'ApiKeyEntity',\n  TenantSettingsRow: 'TenantSettingsRow',\n  FeatureFlag: 'FeatureFlag',\n  FeatureFlagFilter: 'FeatureFlagFilter',\n  Feedback: 'Feedback',\n  Survey: 'Survey',\n  SurveyItem: 'SurveyItem',\n  SurveySubmission: 'SurveySubmission',\n  SurveySubmissionResult: 'SurveySubmissionResult',\n  KnowledgeBase: 'KnowledgeBase',\n  KnowledgeBaseCategory: 'KnowledgeBaseCategory',\n  KnowledgeBaseCategorySection: 'KnowledgeBaseCategorySection',\n  KnowledgeBaseArticle: 'KnowledgeBaseArticle',\n  KnowledgeBaseRelatedArticle: 'KnowledgeBaseRelatedArticle',\n  KnowledgeBaseViews: 'KnowledgeBaseViews',\n  KnowledgeBaseArticleViews: 'KnowledgeBaseArticleViews',\n  KnowledgeBaseArticleUpvotes: 'KnowledgeBaseArticleUpvotes',\n  KnowledgeBaseArticleDownvotes: 'KnowledgeBaseArticleDownvotes',\n  Onboarding: 'Onboarding',\n  OnboardingFilter: 'OnboardingFilter',\n  OnboardingStep: 'OnboardingStep',\n  OnboardingSession: 'OnboardingSession',\n  OnboardingSessionAction: 'OnboardingSessionAction',\n  OnboardingSessionFilterMatch: 'OnboardingSessionFilterMatch',\n  OnboardingSessionStep: 'OnboardingSessionStep',\n  Page: 'Page',\n  PageMetaTag: 'PageMetaTag',\n  PageBlock: 'PageBlock',\n  Role: 'Role',\n  Permission: 'Permission',\n  RolePermission: 'RolePermission',\n  UserRole: 'UserRole',\n  Group: 'Group',\n  GroupUser: 'GroupUser',\n  Portal: 'Portal',\n  PortalUser: 'PortalUser',\n  PortalPage: 'PortalPage',\n  PortalUserRegistration: 'PortalUserRegistration',\n  PortalSubscriptionProduct: 'PortalSubscriptionProduct',\n  PortalSubscriptionPrice: 'PortalSubscriptionPrice',\n  PortalSubscriptionFeature: 'PortalSubscriptionFeature',\n  PortalUserSubscription: 'PortalUserSubscription',\n  PortalUserSubscriptionProduct: 'PortalUserSubscriptionProduct',\n  PortalUserSubscriptionProductPrice: 'PortalUserSubscriptionProductPrice',\n  PortalCheckoutSessionStatus: 'PortalCheckoutSessionStatus',\n  PromptFlowGroup: 'PromptFlowGroup',\n  PromptFlowGroupTemplate: 'PromptFlowGroupTemplate',\n  PromptFlowGroupEntity: 'PromptFlowGroupEntity',\n  PromptFlow: 'PromptFlow',\n  PromptFlowInputVariable: 'PromptFlowInputVariable',\n  PromptTemplate: 'PromptTemplate',\n  PromptFlowOutput: 'PromptFlowOutput',\n  PromptFlowOutputMapping: 'PromptFlowOutputMapping',\n  PromptFlowExecution: 'PromptFlowExecution',\n  PromptTemplateResult: 'PromptTemplateResult',\n  StepFormWizard: 'StepFormWizard',\n  StepFormWizardFilter: 'StepFormWizardFilter',\n  StepFormWizardStep: 'StepFormWizardStep',\n  StepFormWizardSession: 'StepFormWizardSession',\n  StepFormWizardSessionAction: 'StepFormWizardSessionAction',\n  StepFormWizardSessionFilterMatch: 'StepFormWizardSessionFilterMatch',\n  StepFormWizardSessionStep: 'StepFormWizardSessionStep',\n  SubscriptionProduct: 'SubscriptionProduct',\n  SubscriptionPrice: 'SubscriptionPrice',\n  SubscriptionUsageBasedPrice: 'SubscriptionUsageBasedPrice',\n  SubscriptionUsageBasedTier: 'SubscriptionUsageBasedTier',\n  SubscriptionFeature: 'SubscriptionFeature',\n  TenantSubscription: 'TenantSubscription',\n  TenantSubscriptionProduct: 'TenantSubscriptionProduct',\n  TenantSubscriptionProductPrice: 'TenantSubscriptionProductPrice',\n  TenantSubscriptionUsageRecord: 'TenantSubscriptionUsageRecord',\n  CheckoutSessionStatus: 'CheckoutSessionStatus',\n  Credit: 'Credit',\n  Workflow: 'Workflow',\n  WorkflowBlock: 'WorkflowBlock',\n  WorkflowBlockConditionGroup: 'WorkflowBlockConditionGroup',\n  WorkflowBlockCondition: 'WorkflowBlockCondition',\n  WorkflowBlockToBlock: 'WorkflowBlockToBlock',\n  WorkflowExecution: 'WorkflowExecution',\n  WorkflowInputExample: 'WorkflowInputExample',\n  WorkflowBlockExecution: 'WorkflowBlockExecution',\n  WorkflowVariable: 'WorkflowVariable',\n  WorkflowCredential: 'WorkflowCredential'\n};\n\n/**\n * This is a stub Prisma Client that will error at runtime if called.\n */\nclass PrismaClient {\n  constructor() {\n    return new Proxy(this, {\n      get(target, prop) {\n        let message\n        const runtime = getRuntime()\n        if (runtime.isEdge) {\n          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:\n- Use Prisma Accelerate: https://pris.ly/d/accelerate\n- Use Driver Adapters: https://pris.ly/d/driver-adapters\n`;\n        } else {\n          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'\n        }\n\n        message += `\nIf this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`\n\n        throw new Error(message)\n      }\n    })\n  }\n}\n\nexports.PrismaClient = PrismaClient\n\nObject.assign(exports, Prisma)\n", "const prisma = require('.prisma/client/index-browser')\n\nmodule.exports = prisma\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,QAAAA,KAAA,CAAA;AAAAC,OAAAD,IAAA,EAAA,SAAA,MAAAE,IAAA,QAAA,MAAAC,IAAA,YAAA,MAAAC,IAAA,gBAAA,MAAAC,IAAA,kBAAA,MAAAC,GAAAA,CAAAA;AAAA,WAAA,UAAAC,GAAAP,EAAAA;ACAA,QAAAG,KAAA,CAAA;AAAAF,OAAAE,IAAA,EAAA,WAAA,MAAAK,GAAAA,CAAAA;ACeO,aAASA,MAAaC,GAAc;AACzC,aAAQC,OAAcA;IACxB;ACbA,QAAMC,KAAS,OAAO;AAAtB,QAUMC,KAAkB,oBAAI;AAV5B,QAesBC,KAAf,MAA+B;MACpC,YAAYC,GAAc;AACpBA,cAAQH,KACVC,GAAgB,IAAI,MAAM,UAAU,OAAA,KAAK,SAAS,CAAA,CAAG,IAErDA,GAAgB,IAAI,MAAM,cAAc,OAAA,KAAK,cAAc,GAAC,GAAA,EAAI,OAAA,KAAK,SAAS,GAAC,IAAA,CAAI;MAEvF;MAIA,WAAW;AACT,eAAO,KAAK,YAAY;MAC1B;MAEA,WAAW;AACT,eAAOA,GAAgB,IAAI,IAAI;MACjC;IACF;AAjCA,QAmCMG,IAAN,cAAiCF,GAAgB;MACtC,gBAAgB;AACvB,eAAO;MACT;IACF;AAvCA,QAJAG;AAIA,QAyCMC,IAAN,cAAqBF,EAAmB;MAAxC,cAAA;AAAA,cAAA,GAAA,SAAA;AAGEG,WAAA,MAASF,EAAAA;MAAAA;IACX;AADWA,SAAA,oBAAA;AAEXG,OAAaF,GAAQ,QAAQ;AAlD7B,QAAAG;AAAA,QAoDMC,IAAN,cAAuBN,EAAmB;MAA1C,cAAA;AAAA,cAAA,GAAA,SAAA;AAGEG,WAAA,MAASE,EAAAA;MAAAA;IACX;AADWA,SAAA,oBAAA;AAEXD,OAAaE,GAAU,UAAU;AAzDjC,QAAAC;AAAA,QA2DMC,IAAN,cAAsBR,EAAmB;MAAzC,cAAA;AAAA,cAAA,GAAA,SAAA;AAGEG,WAAA,MAASI,EAAAA;MAAAA;IACX;AADWA,SAAA,oBAAA;AAEXH,OAAaI,GAAS,SAAS;AAExB,QAAMjB,KAAmB,EAC9B,SAAS,EACP,QAAAW,GACA,UAAAI,GACA,SAAAE,EACF,GACA,WAAW,EACT,QAAQ,IAAIN,EAAON,EAAM,GACzB,UAAU,IAAIU,EAASV,EAAM,GAC7B,SAAS,IAAIY,EAAQZ,EAAM,EAC7B,EACF;AASA,aAASQ,GAAaK,GAAuBC,GAAc;AACzD,aAAO,eAAeD,GAAa,QAAQ,EACzC,OAAOC,GACP,cAAc,KAChB,CAAC;IACH;ACxFA,QAAMC,KAAY,oBAAI,IAAI,CACxB,UACA,YACA,mBACA,OAAO,UACP,OAAO,aACP,OAAO,oBACP,OAAO,WACT,CAAC;AAiBM,aAASrB,GAA+DsB,GAAkB;AAC/F,aAAO,IAAI,MAAMA,GAAY,EAC3B,IAAIC,GAAQC,GAAU;AACpB,YAAIA,KAAYD,EACd,QAAOA,EAAOC,CAAQ;AAExB,YAAI,CAAAH,GAAU,IAAIG,CAAQ,EAG1B,OAAM,IAAI,UAAU,uBAAuB,OAAA,OAAOA,CAAQ,CAAA,CAAG;MAC/D,EACF,CAAC;IACH;AChCA,QAAMC,KAAS,MAAG;AARlB,UAAAC,GAAAC;AAQqB,eAAAA,KAAAD,IAAA,WAAW,YAAX,OAAA,SAAAA,EAAoB,YAApB,OAAA,SAAAC,EAA6B,UAAS;IAAA;AAA3D,QAKMC,KAAQ,MAAG;AAbjB,UAAAF,GAAAC;AAaoB,aAAA,CAAC,CAAC,WAAW,OAAO,CAAC,GAACA,KAAAD,IAAA,WAAW,YAAX,OAAA,SAAAA,EAAoB,aAApB,QAAAC,EAA8B;IAAA;AALxE,QAUME,KAAS,MAAM,CAAC,CAAC,WAAW;AAVlC,QAeMC,KAAY,MAAM,OAAO,WAAW,WAAY;AAftD,QAoBMC,KAAc,MAAM,OAAO,WAAW,eAAgB;AApB5D,QA0BMC,KAAY,MAAG;AAlCrB,UAAAN;AAkCwB,eAAAA,IAAA,WAAW,cAAX,OAAA,SAAAA,EAAsB,eAAc;IAAA;AAE5D,aAASO,KAA6B;AApCtC,UAAAP;AAyDE,cATEA,IAVoB,CACpB,CAACI,IAAW,SAAS,GACrB,CAACC,IAAa,YAAY,GAC1B,CAACC,IAAW,SAAS,GACrB,CAACH,IAAQ,MAAM,GACf,CAACD,IAAO,KAAK,GACb,CAACH,IAAQ,MAAM,CACjB,EASK,QAASS,OAAWA,EAAM,CAAC,EAAE,IAAI,CAACA,EAAM,CAAC,CAAC,IAAI,CAAC,CAAE,EACjD,GAAG,CAAC,MAPP,OAAAR,IAOY;IAGhB;AAEA,QAAMS,KAAsB,EAC1B,MAAM,WACN,SAAS,sBACT,MAAM,wBACN,SAAS,0BACT,cACE,uKACJ;AAQO,aAASpC,KAA+B;AAC7C,UAAMqC,IAAYH,GAAc;AAEhC,aAAO,EACL,IAAIG,GAEJ,YAAYD,GAAoBC,CAAS,KAAKA,GAC9C,QAAQ,CAAC,WAAW,QAAQ,WAAW,YAAY,EAAE,SAASA,CAAS,EACzE;IACF;ACtEA,QAAIC,IAAY;AAAhB,QAIEC,IAAa;AAJf,QAOEC,KAAW;AAPb,QAUEC,KAAO;AAVT,QAaEC,KAAK;AAbP,QAiBEC,KAAW,EAOT,WAAW,IAiBX,UAAU,GAeV,QAAQ,GAIR,UAAU,IAIV,UAAW,IAIX,MAAM,CAACL,GAIP,MAAMA,GAGN,QAAQ,MACV;AA5EF,QAkFEM;AAlFF,QAkFWC;AAlFX,QAmFEC,IAAW;AAnFb,QAqFEC,KAAe;AArFjB,QAsFEC,IAAkBD,KAAe;AAtFnC,QAuFEE,KAAyBF,KAAe;AAvF1C,QAwFEG,KAAoBH,KAAe;AAxFrC,QAyFEI,KAAM;AAzFR,QA2FEC,IAAY,KAAK;AA3FnB,QA4FEC,IAAU,KAAK;AA5FjB,QA8FEC,KAAW;AA9Fb,QA+FEC,KAAQ;AA/FV,QAgGEC,KAAU;AAhGZ,QAiGEC,KAAY;AAjGd,QAmGEC,IAAO;AAnGT,QAoGEC,IAAW;AApGb,QAqGEC,KAAmB;AArGrB,QAuGEC,KAAiBpB,GAAK,SAAS;AAvGjC,QAwGEqB,KAAepB,GAAG,SAAS;AAxG7B,QA2GEqB,IAAI,EAAE,aAAaZ,GAAI;AA0EzBY,MAAE,gBAAgBA,EAAE,MAAM,WAAY;AACpC,UAAIC,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,aAAIA,EAAE,IAAI,MAAGA,EAAE,IAAI,IACZC,EAASD,CAAC;IACnB;AAQAD,MAAE,OAAO,WAAY;AACnB,aAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;IAC3D;AAWAF,MAAE,YAAYA,EAAE,QAAQ,SAAUG,GAAKC,GAAK;AAC1C,UAAIC,GACFJ,IAAI,MACJK,IAAOL,EAAE;AAGX,UAFAE,IAAM,IAAIG,EAAKH,CAAG,GAClBC,IAAM,IAAIE,EAAKF,CAAG,GACd,CAACD,EAAI,KAAK,CAACC,EAAI,EAAG,QAAO,IAAIE,EAAK,GAAG;AACzC,UAAIH,EAAI,GAAGC,CAAG,EAAG,OAAM,MAAMnB,IAAkBmB,CAAG;AAClD,aAAAC,IAAIJ,EAAE,IAAIE,CAAG,GACNE,IAAI,IAAIF,IAAMF,EAAE,IAAIG,CAAG,IAAI,IAAIA,IAAM,IAAIE,EAAKL,CAAC;IACxD;AAWAD,MAAE,aAAaA,EAAE,MAAM,SAAUO,GAAG;AAClC,UAAIC,GAAGC,GAAGC,GAAKC,GACbV,IAAI,MACJW,IAAKX,EAAE,GACPY,KAAMN,IAAI,IAAIN,EAAE,YAAYM,CAAC,GAAG,GAChCO,IAAKb,EAAE,GACPc,IAAKR,EAAE;AAGT,UAAI,CAACK,KAAM,CAACC,EACV,QAAO,CAACC,KAAM,CAACC,IAAK,MAAMD,MAAOC,IAAKD,IAAKF,MAAOC,IAAK,IAAI,CAACD,IAAKE,IAAK,IAAI,IAAI;AAIhF,UAAI,CAACF,EAAG,CAAC,KAAK,CAACC,EAAG,CAAC,EAAG,QAAOD,EAAG,CAAC,IAAIE,IAAKD,EAAG,CAAC,IAAI,CAACE,IAAK;AAGxD,UAAID,MAAOC,EAAI,QAAOD;AAGtB,UAAIb,EAAE,MAAMM,EAAE,EAAG,QAAON,EAAE,IAAIM,EAAE,IAAIO,IAAK,IAAI,IAAI;AAMjD,WAJAJ,IAAME,EAAG,QACTD,IAAME,EAAG,QAGJL,IAAI,GAAGC,IAAIC,IAAMC,IAAMD,IAAMC,GAAKH,IAAIC,GAAG,EAAED,EAC9C,KAAII,EAAGJ,CAAC,MAAMK,EAAGL,CAAC,EAAG,QAAOI,EAAGJ,CAAC,IAAIK,EAAGL,CAAC,IAAIM,IAAK,IAAI,IAAI;AAI3D,aAAOJ,MAAQC,IAAM,IAAID,IAAMC,IAAMG,IAAK,IAAI,IAAI;IACpD;AAgBAd,MAAE,SAASA,EAAE,MAAM,WAAY;AAC7B,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAKA,EAAE,IAGFA,EAAE,EAAE,CAAC,KAEVe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,KAAK,IAAIf,EAAE,GAAGA,EAAE,GAAG,CAAC,IAAIL,GAC9CU,EAAK,WAAW,GAEhBL,IAAIiB,GAAOZ,GAAMa,GAAiBb,GAAML,CAAC,CAAC,GAE1CK,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAETf,EAASpB,KAAY,KAAKA,KAAY,IAAImB,EAAE,IAAI,IAAIA,GAAGe,GAAIC,GAAI,IAAI,KAZtD,IAAIX,EAAK,CAAC,IAHb,IAAIA,EAAK,GAAG;IAgB/B;AAmBAN,MAAE,WAAWA,EAAE,OAAO,WAAY;AAChC,UAAI,GAAGoB,GAAGC,GAAGC,GAAGC,GAAK,GAAGC,GAAIC,GAAGC,GAAIC,GACjC1B,IAAI,MACJK,IAAOL,EAAE;AAEX,UAAI,CAACA,EAAE,SAAS,KAAKA,EAAE,OAAO,EAAG,QAAO,IAAIK,EAAKL,CAAC;AAoClD,WAnCAlB,IAAW,OAGX,IAAIkB,EAAE,IAAIX,EAAQW,EAAE,IAAIA,GAAG,IAAI,CAAC,GAI5B,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAA,KACvBoB,IAAIO,EAAe3B,EAAE,CAAC,GACtB,IAAIA,EAAE,IAGF,KAAK,IAAIoB,EAAE,SAAS,KAAK,OAAGA,KAAM,KAAK,KAAK,KAAK,KAAK,MAAM,OAChE,IAAI/B,EAAQ+B,GAAG,IAAI,CAAC,GAGpB,IAAIhC,GAAW,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,KAEjD,KAAK,IAAA,IACPgC,IAAI,OAAO,KAEXA,IAAI,EAAE,cAAc,GACpBA,IAAIA,EAAE,MAAM,GAAGA,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI,IAGvCC,IAAI,IAAIhB,EAAKe,CAAC,GACdC,EAAE,IAAIrB,EAAE,KAERqB,IAAI,IAAIhB,EAAK,EAAE,SAAS,CAAC,GAG3BkB,KAAM,IAAIlB,EAAK,aAAa,MAW1B,KANAmB,IAAIH,GACJI,IAAKD,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,GACvBE,IAAUD,EAAG,KAAKzB,CAAC,GACnBqB,IAAIO,EAAOF,EAAQ,KAAK1B,CAAC,EAAE,MAAMwB,CAAC,GAAGE,EAAQ,KAAKD,CAAE,GAAGF,IAAK,GAAG,CAAC,GAG5DI,EAAeH,EAAE,CAAC,EAAE,MAAM,GAAGD,CAAE,OAAOH,IAAIO,EAAeN,EAAE,CAAC,GAAG,MAAM,GAAGE,CAAE,EAK5E,KAJAH,IAAIA,EAAE,MAAMG,IAAK,GAAGA,IAAK,CAAC,GAItBH,KAAK,UAAU,CAACE,KAAOF,KAAK,QAAQ;AAItC,YAAI,CAACE,MACHrB,EAASuB,GAAG,IAAI,GAAG,CAAC,GAEhBA,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,EAAE,GAAGxB,CAAC,IAAG;AAC7BqB,cAAIG;AACJ;QACF;AAGFD,aAAM,GACND,IAAM;MACR,OAAO;AAAA,SAID,CAAC,CAACF,KAAK,CAAC,CAACA,EAAE,MAAM,CAAC,KAAKA,EAAE,OAAO,CAAC,KAAK,SAGxCnB,EAASoB,GAAG,IAAI,GAAG,CAAC,GACpBF,IAAI,CAACE,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,EAAE,GAAGrB,CAAC;AAG/B;MACF;AAIJ,aAAAlB,IAAW,MAEJmB,EAASoB,GAAG,GAAGhB,EAAK,UAAUc,CAAC;IACxC;AAOApB,MAAE,gBAAgBA,EAAE,KAAK,WAAY;AACnC,UAAI8B,GACFC,IAAI,KAAK,GACTV,IAAI;AAEN,UAAIU,GAAG;AAML,YALAD,IAAIC,EAAE,SAAS,GACfV,KAAKS,IAAIzC,EAAU,KAAK,IAAIO,CAAQ,KAAKA,GAGzCkC,IAAIC,EAAED,CAAC,GACHA,EAAG,QAAOA,IAAI,MAAM,GAAGA,KAAK,GAAIT;AAChCA,YAAI,MAAGA,IAAI;MACjB;AAEA,aAAOA;IACT;AAwBArB,MAAE,YAAYA,EAAE,MAAM,SAAUO,GAAG;AACjC,aAAOsB,EAAO,MAAM,IAAI,KAAK,YAAYtB,CAAC,CAAC;IAC7C;AAQAP,MAAE,qBAAqBA,EAAE,WAAW,SAAUO,GAAG;AAC/C,UAAIN,IAAI,MACNK,IAAOL,EAAE;AACX,aAAOC,EAAS2B,EAAO5B,GAAG,IAAIK,EAAKC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGD,EAAK,WAAWA,EAAK,QAAQ;IAChF;AAOAN,MAAE,SAASA,EAAE,KAAK,SAAUO,GAAG;AAC7B,aAAO,KAAK,IAAIA,CAAC,MAAM;IACzB;AAQAP,MAAE,QAAQ,WAAY;AACpB,aAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;IAC3D;AAQAF,MAAE,cAAcA,EAAE,KAAK,SAAUO,GAAG;AAClC,aAAO,KAAK,IAAIA,CAAC,IAAI;IACvB;AAQAP,MAAE,uBAAuBA,EAAE,MAAM,SAAUO,GAAG;AAC5C,UAAIF,IAAI,KAAK,IAAIE,CAAC;AAClB,aAAOF,KAAK,KAAKA,MAAM;IACzB;AA4BAL,MAAE,mBAAmBA,EAAE,OAAO,WAAY;AACxC,UAAIK,GAAG,GAAGW,GAAIC,GAAIe,GAChB/B,IAAI,MACJK,IAAOL,EAAE,aACTgC,IAAM,IAAI3B,EAAK,CAAC;AAElB,UAAI,CAACL,EAAE,SAAS,EAAG,QAAO,IAAIK,EAAKL,EAAE,IAAI,IAAA,IAAQ,GAAG;AACpD,UAAIA,EAAE,OAAO,EAAG,QAAOgC;AAEvBjB,UAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,KAAK,IAAIf,EAAE,GAAGA,EAAE,GAAG,CAAC,IAAI,GAC9CK,EAAK,WAAW,GAChB0B,IAAM/B,EAAE,EAAE,QAON+B,IAAM,MACR3B,IAAI,KAAK,KAAK2B,IAAM,CAAC,GACrB,KAAK,IAAIE,GAAQ,GAAG7B,CAAC,GAAG,SAAS,MAEjCA,IAAI,IACJ,IAAI,iCAGNJ,IAAIkC,EAAa7B,GAAM,GAAGL,EAAE,MAAM,CAAC,GAAG,IAAIK,EAAK,CAAC,GAAG,IAAI;AAMvD,eAHI8B,GACF5B,IAAIH,GACJgC,IAAK,IAAI/B,EAAK,CAAC,GACVE,MACL4B,KAAUnC,EAAE,MAAMA,CAAC,GACnBA,IAAIgC,EAAI,MAAMG,EAAQ,MAAMC,EAAG,MAAMD,EAAQ,MAAMC,CAAE,CAAC,CAAC,CAAC;AAG1D,aAAOnC,EAASD,GAAGK,EAAK,YAAYU,GAAIV,EAAK,WAAWW,GAAI,IAAI;IAClE;AAiCAjB,MAAE,iBAAiBA,EAAE,OAAO,WAAY;AACtC,UAAIK,GAAGW,GAAIC,GAAIe,GACb/B,IAAI,MACJK,IAAOL,EAAE;AAEX,UAAI,CAACA,EAAE,SAAS,KAAKA,EAAE,OAAO,EAAG,QAAO,IAAIK,EAAKL,CAAC;AAQlD,UANAe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,KAAK,IAAIf,EAAE,GAAGA,EAAE,GAAG,CAAC,IAAI,GAC9CK,EAAK,WAAW,GAChB0B,IAAM/B,EAAE,EAAE,QAEN+B,IAAM,EACR/B,KAAIkC,EAAa7B,GAAM,GAAGL,GAAGA,GAAG,IAAI;WAC/B;AAWLI,YAAI,MAAM,KAAK,KAAK2B,CAAG,GACvB3B,IAAIA,IAAI,KAAK,KAAKA,IAAI,GAEtBJ,IAAIA,EAAE,MAAM,IAAIiC,GAAQ,GAAG7B,CAAC,CAAC,GAC7BJ,IAAIkC,EAAa7B,GAAM,GAAGL,GAAGA,GAAG,IAAI;AAOpC,iBAJIqC,GACFC,IAAK,IAAIjC,EAAK,CAAC,GACfkC,IAAM,IAAIlC,EAAK,EAAE,GACjBmC,IAAM,IAAInC,EAAK,EAAE,GACZD,MACLiC,KAAUrC,EAAE,MAAMA,CAAC,GACnBA,IAAIA,EAAE,MAAMsC,EAAG,KAAKD,EAAQ,MAAME,EAAI,MAAMF,CAAO,EAAE,KAAKG,CAAG,CAAC,CAAC,CAAC;MAEpE;AAEA,aAAAnC,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAETf,EAASD,GAAGe,GAAIC,GAAI,IAAI;IACjC;AAmBAjB,MAAE,oBAAoBA,EAAE,OAAO,WAAY;AACzC,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAKA,EAAE,SAAS,IACZA,EAAE,OAAO,IAAU,IAAIK,EAAKL,CAAC,KAEjCe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,GACtBV,EAAK,WAAW,GAETuB,EAAO5B,EAAE,KAAK,GAAGA,EAAE,KAAK,GAAGK,EAAK,YAAYU,GAAIV,EAAK,WAAWW,CAAE,KAR/C,IAAIX,EAAKL,EAAE,CAAC;IASxC;AAsBAD,MAAE,gBAAgBA,EAAE,OAAO,WAAY;AACrC,UAAIC,IAAI,MACNK,IAAOL,EAAE,aACTI,IAAIJ,EAAE,IAAI,EAAE,IAAI,CAAC,GACjBe,IAAKV,EAAK,WACVW,IAAKX,EAAK;AAEZ,aAAID,MAAM,KACDA,MAAM,IAETJ,EAAE,MAAM,IAAIyC,EAAMpC,GAAMU,GAAIC,CAAE,IAAI,IAAIX,EAAK,CAAC,IAE5C,IAAIA,EAAK,GAAG,IAGdL,EAAE,OAAO,IAAUyC,EAAMpC,GAAMU,IAAK,GAAGC,CAAE,EAAE,MAAM,GAAG,KAIxDX,EAAK,YAAYU,IAAK,GACtBV,EAAK,WAAW,GAGhBL,IAAI,IAAIK,EAAK,CAAC,EAAE,MAAML,CAAC,EAAE,IAAIA,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,GAEpDK,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAEThB,EAAE,MAAM,CAAC;IAClB;AAsBAD,MAAE,0BAA0BA,EAAE,QAAQ,WAAY;AAChD,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAIA,EAAE,IAAI,CAAC,IAAU,IAAIK,EAAKL,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,IAC1CA,EAAE,SAAS,KAEhBe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,KAAK,IAAI,KAAK,IAAIf,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,GACxDK,EAAK,WAAW,GAChBvB,IAAW,OAEXkB,IAAIA,EAAE,MAAMA,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAKA,CAAC,GAErClB,IAAW,MACXuB,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAEThB,EAAE,GAAG,KAdc,IAAIK,EAAKL,CAAC;IAetC;AAmBAD,MAAE,wBAAwBA,EAAE,QAAQ,WAAY;AAC9C,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAI,CAACA,EAAE,SAAS,KAAKA,EAAE,OAAO,IAAU,IAAIK,EAAKL,CAAC,KAElDe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,IAAI,KAAK,IAAI,KAAK,IAAIf,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,GAC5DK,EAAK,WAAW,GAChBvB,IAAW,OAEXkB,IAAIA,EAAE,MAAMA,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAKA,CAAC,GAEpClB,IAAW,MACXuB,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAEThB,EAAE,GAAG;IACd;AAsBAD,MAAE,2BAA2BA,EAAE,QAAQ,WAAY;AACjD,UAAIgB,GAAIC,GAAI0B,GAAKC,GACf3C,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAKA,EAAE,SAAS,IACZA,EAAE,KAAK,IAAU,IAAIK,EAAKL,EAAE,IAAI,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,IAAIA,EAAE,OAAO,IAAIA,IAAI,GAAG,KAE5Ee,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVsC,IAAM3C,EAAE,GAAG,GAEP,KAAK,IAAI2C,GAAK5B,CAAE,IAAI,IAAI,CAACf,EAAE,IAAI,IAAUC,EAAS,IAAII,EAAKL,CAAC,GAAGe,GAAIC,GAAI,IAAI,KAE/EX,EAAK,YAAYqC,IAAMC,IAAM3C,EAAE,GAE/BA,IAAI4B,EAAO5B,EAAE,KAAK,CAAC,GAAG,IAAIK,EAAK,CAAC,EAAE,MAAML,CAAC,GAAG0C,IAAM3B,GAAI,CAAC,GAEvDV,EAAK,YAAYU,IAAK,GACtBV,EAAK,WAAW,GAEhBL,IAAIA,EAAE,GAAG,GAETK,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAEThB,EAAE,MAAM,GAAG,MArBQ,IAAIK,EAAK,GAAG;IAsBxC;AAwBAN,MAAE,cAAcA,EAAE,OAAO,WAAY;AACnC,UAAI6C,GAAQxC,GACVW,GAAIC,GACJhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAIA,EAAE,OAAO,IAAU,IAAIK,EAAKL,CAAC,KAEjCI,IAAIJ,EAAE,IAAI,EAAE,IAAI,CAAC,GACjBe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UAEND,MAAM,KAGJA,MAAM,KACRwC,IAASH,EAAMpC,GAAMU,IAAK,GAAGC,CAAE,EAAE,MAAM,GAAG,GAC1C4B,EAAO,IAAI5C,EAAE,GACN4C,KAIF,IAAIvC,EAAK,GAAG,KAKrBA,EAAK,YAAYU,IAAK,GACtBV,EAAK,WAAW,GAEhBL,IAAIA,EAAE,IAAI,IAAIK,EAAK,CAAC,EAAE,MAAML,EAAE,MAAMA,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,GAE7DK,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAEThB,EAAE,MAAM,CAAC;IAClB;AAqBAD,MAAE,iBAAiBA,EAAE,OAAO,WAAY;AACtC,UAAIQ,GAAGC,GAAGJ,GAAGgB,GAAGyB,GAAIrB,GAAGH,GAAGqB,GAAKI,GAC7B9C,IAAI,MACJK,IAAOL,EAAE,aACTe,IAAKV,EAAK,WACVW,IAAKX,EAAK;AAEZ,UAAKL,EAAE,SAAS,GAOT;AAAA,YAAIA,EAAE,OAAO,EAClB,QAAO,IAAIK,EAAKL,CAAC;AACZ,YAAIA,EAAE,IAAI,EAAE,GAAG,CAAC,KAAKe,IAAK,KAAKjB,GACpC,QAAAuB,IAAIoB,EAAMpC,GAAMU,IAAK,GAAGC,CAAE,EAAE,MAAM,IAAI,GACtCK,EAAE,IAAIrB,EAAE,GACDqB;MAAAA,OAZU;AACjB,YAAI,CAACrB,EAAE,EAAG,QAAO,IAAIK,EAAK,GAAG;AAC7B,YAAIU,IAAK,KAAKjB,GACZ,QAAAuB,IAAIoB,EAAMpC,GAAMU,IAAK,GAAGC,CAAE,EAAE,MAAM,GAAG,GACrCK,EAAE,IAAIrB,EAAE,GACDqB;MAEX;AAmBA,WAXAhB,EAAK,YAAYqC,IAAM3B,IAAK,IAC5BV,EAAK,WAAW,GAQhBD,IAAI,KAAK,IAAI,IAAIsC,IAAM/C,IAAW,IAAI,CAAC,GAElCY,IAAIH,GAAGG,GAAG,EAAEA,EAAGP,KAAIA,EAAE,IAAIA,EAAE,MAAMA,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAW/D,WATAlB,IAAW,OAEX0B,IAAI,KAAK,KAAKkC,IAAM/C,CAAQ,GAC5ByB,IAAI,GACJ0B,IAAK9C,EAAE,MAAMA,CAAC,GACdqB,IAAI,IAAIhB,EAAKL,CAAC,GACd6C,IAAK7C,GAGEO,MAAM,KAOX,KANAsC,IAAKA,EAAG,MAAMC,CAAE,GAChBtB,IAAIH,EAAE,MAAMwB,EAAG,IAAIzB,KAAK,CAAC,CAAC,GAE1ByB,IAAKA,EAAG,MAAMC,CAAE,GAChBzB,IAAIG,EAAE,KAAKqB,EAAG,IAAIzB,KAAK,CAAC,CAAC,GAErBC,EAAE,EAAEb,CAAC,MAAM,OAAQ,MAAKD,IAAIC,GAAGa,EAAE,EAAEd,CAAC,MAAMiB,EAAE,EAAEjB,CAAC,KAAKA,MAAK;AAG/D,aAAIH,MAAGiB,IAAIA,EAAE,MAAM,KAAMjB,IAAI,CAAE,IAE/BtB,IAAW,MAEJmB,EAASoB,GAAGhB,EAAK,YAAYU,GAAIV,EAAK,WAAWW,GAAI,IAAI;IAClE;AAOAjB,MAAE,WAAW,WAAY;AACvB,aAAO,CAAC,CAAC,KAAK;IAChB;AAOAA,MAAE,YAAYA,EAAE,QAAQ,WAAY;AAClC,aAAO,CAAC,CAAC,KAAK,KAAKX,EAAU,KAAK,IAAIO,CAAQ,IAAI,KAAK,EAAE,SAAS;IACpE;AAOAI,MAAE,QAAQ,WAAY;AACpB,aAAO,CAAC,KAAK;IACf;AAOAA,MAAE,aAAaA,EAAE,QAAQ,WAAY;AACnC,aAAO,KAAK,IAAI;IAClB;AAOAA,MAAE,aAAaA,EAAE,QAAQ,WAAY;AACnC,aAAO,KAAK,IAAI;IAClB;AAOAA,MAAE,SAAS,WAAY;AACrB,aAAO,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,MAAM;IACnC;AAOAA,MAAE,WAAWA,EAAE,KAAK,SAAUO,GAAG;AAC/B,aAAO,KAAK,IAAIA,CAAC,IAAI;IACvB;AAOAP,MAAE,oBAAoBA,EAAE,MAAM,SAAUO,GAAG;AACzC,aAAO,KAAK,IAAIA,CAAC,IAAI;IACvB;AAiCAP,MAAE,YAAYA,EAAE,MAAM,SAAUgD,GAAM;AACpC,UAAIC,GAAUlB,GAAGmB,GAAa7C,GAAG8C,GAAKC,GAAK5B,GAAIF,GAC7C3E,IAAM,MACN2D,IAAO3D,EAAI,aACXqE,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACV+C,IAAQ;AAGV,UAAIL,KAAQ,KACVA,KAAO,IAAI1C,EAAK,EAAE,GAClB2C,IAAW;WACN;AAKL,YAJAD,IAAO,IAAI1C,EAAK0C,CAAI,GACpBjB,IAAIiB,EAAK,GAGLA,EAAK,IAAI,KAAK,CAACjB,KAAK,CAACA,EAAE,CAAC,KAAKiB,EAAK,GAAG,CAAC,EAAG,QAAO,IAAI1C,EAAK,GAAG;AAEhE2C,YAAWD,EAAK,GAAG,EAAE;MACvB;AAKA,UAHAjB,IAAIpF,EAAI,GAGJA,EAAI,IAAI,KAAK,CAACoF,KAAK,CAACA,EAAE,CAAC,KAAKpF,EAAI,GAAG,CAAC,EACtC,QAAO,IAAI2D,EAAKyB,KAAK,CAACA,EAAE,CAAC,IAAI,KAAA,IAASpF,EAAI,KAAK,IAAI,MAAMoF,IAAI,IAAI,IAAA,CAAK;AAKxE,UAAIkB,EACF,KAAIlB,EAAE,SAAS,EACboB,KAAM;WACD;AACL,aAAK9C,IAAI0B,EAAE,CAAC,GAAG1B,IAAI,OAAO,IAAIA,MAAK;AACnC8C,YAAM9C,MAAM;MACd;AAyBF,UAtBAtB,IAAW,OACXyC,IAAKR,IAAKqC,GACVD,IAAME,EAAiB3G,GAAK6E,CAAE,GAC9B0B,IAAcD,IAAWM,GAAQjD,GAAMkB,IAAK,EAAE,IAAI8B,EAAiBN,GAAMxB,CAAE,GAG3EF,IAAIO,EAAOuB,GAAKF,GAAa1B,GAAI,CAAC,GAgB9BgC,EAAoBlC,EAAE,GAAGjB,IAAIW,GAAIC,CAAE,EAErC;AAME,YALAO,KAAM,IACN4B,IAAME,EAAiB3G,GAAK6E,CAAE,GAC9B0B,IAAcD,IAAWM,GAAQjD,GAAMkB,IAAK,EAAE,IAAI8B,EAAiBN,GAAMxB,CAAE,GAC3EF,IAAIO,EAAOuB,GAAKF,GAAa1B,GAAI,CAAC,GAE9B,CAAC2B,GAAK;AAGJ,WAACvB,EAAeN,EAAE,CAAC,EAAE,MAAMjB,IAAI,GAAGA,IAAI,EAAE,IAAI,KAAK,SACnDiB,IAAIpB,EAASoB,GAAGN,IAAK,GAAG,CAAC;AAG3B;QACF;aACOwC,EAAoBlC,EAAE,GAAGjB,KAAK,IAAIY,CAAE;AAG/C,aAAAlC,IAAW,MAEJmB,EAASoB,GAAGN,GAAIC,CAAE;IAC3B;AAgDAjB,MAAE,QAAQA,EAAE,MAAM,SAAUO,GAAG;AAC7B,UAAIwB,GAAG0B,GAAGjD,GAAGC,GAAGJ,GAAG2B,GAAKhB,GAAIC,GAAIL,GAAI8C,GAAIC,GAAM9C,GAC5CZ,IAAI,MACJK,IAAOL,EAAE;AAKX,UAHAM,IAAI,IAAID,EAAKC,CAAC,GAGV,CAACN,EAAE,KAAK,CAACM,EAAE,EAGb,QAAI,CAACN,EAAE,KAAK,CAACM,EAAE,IAAGA,IAAI,IAAID,EAAK,GAAG,IAGzBL,EAAE,IAAGM,EAAE,IAAI,CAACA,EAAE,IAKlBA,IAAI,IAAID,EAAKC,EAAE,KAAKN,EAAE,MAAMM,EAAE,IAAIN,IAAI,GAAG,GAEvCM;AAIT,UAAIN,EAAE,KAAKM,EAAE,EACX,QAAAA,EAAE,IAAI,CAACA,EAAE,GACFN,EAAE,KAAKM,CAAC;AASjB,UANAK,IAAKX,EAAE,GACPY,IAAKN,EAAE,GACPS,IAAKV,EAAK,WACVW,IAAKX,EAAK,UAGN,CAACM,EAAG,CAAC,KAAK,CAACC,EAAG,CAAC,GAAG;AAGpB,YAAIA,EAAG,CAAC,EAAGN,GAAE,IAAI,CAACA,EAAE;iBAGXK,EAAG,CAAC,EAAGL,KAAI,IAAID,EAAKL,CAAC;YAIzB,QAAO,IAAIK,EAAKW,MAAO,IAAI,KAAK,CAAC;AAEtC,eAAOlC,IAAWmB,EAASK,GAAGS,GAAIC,CAAE,IAAIV;MAC1C;AAYA,UAPAkD,IAAIpE,EAAUkB,EAAE,IAAIX,CAAQ,GAC5B8D,IAAKrE,EAAUY,EAAE,IAAIL,CAAQ,GAE7BgB,IAAKA,EAAG,MAAM,GACdP,IAAIqD,IAAKD,GAGLpD,GAAG;AAyBL,aAxBAsD,IAAOtD,IAAI,GAEPsD,KACF5B,IAAInB,GACJP,IAAI,CAACA,GACL2B,IAAMnB,EAAG,WAETkB,IAAIlB,GACJ4C,IAAIC,GACJ1B,IAAMpB,EAAG,SAMXJ,IAAI,KAAK,IAAI,KAAK,KAAKQ,IAAKpB,CAAQ,GAAGoC,CAAG,IAAI,GAE1C3B,IAAIG,MACNH,IAAIG,GACJuB,EAAE,SAAS,IAIbA,EAAE,QAAQ,GACLvB,IAAIH,GAAGG,MAAMuB,GAAE,KAAK,CAAC;AAC1BA,UAAE,QAAQ;MAGZ,OAAO;AASL,aALAvB,IAAII,EAAG,QACPoB,IAAMnB,EAAG,QACT8C,IAAOnD,IAAIwB,GACP2B,MAAM3B,IAAMxB,IAEXA,IAAI,GAAGA,IAAIwB,GAAKxB,IACnB,KAAII,EAAGJ,CAAC,KAAKK,EAAGL,CAAC,GAAG;AAClBmD,cAAO/C,EAAGJ,CAAC,IAAIK,EAAGL,CAAC;AACnB;QACF;AAGFH,YAAI;MACN;AAaA,WAXIsD,MACF5B,IAAInB,GACJA,IAAKC,GACLA,IAAKkB,GACLxB,EAAE,IAAI,CAACA,EAAE,IAGXyB,IAAMpB,EAAG,QAIJJ,IAAIK,EAAG,SAASmB,GAAKxB,IAAI,GAAG,EAAEA,EAAGI,GAAGoB,GAAK,IAAI;AAGlD,WAAKxB,IAAIK,EAAG,QAAQL,IAAIH,KAAI;AAE1B,YAAIO,EAAG,EAAEJ,CAAC,IAAIK,EAAGL,CAAC,GAAG;AACnB,eAAKC,IAAID,GAAGC,KAAKG,EAAG,EAAEH,CAAC,MAAM,IAAIG,GAAGH,CAAC,IAAId,IAAO;AAChD,YAAEiB,EAAGH,CAAC,GACNG,EAAGJ,CAAC,KAAKb;QACX;AAEAiB,UAAGJ,CAAC,KAAKK,EAAGL,CAAC;MACf;AAGA,aAAOI,EAAG,EAAEoB,CAAG,MAAM,IAAIpB,GAAG,IAAI;AAGhC,aAAOA,EAAG,CAAC,MAAM,GAAGA,EAAG,MAAM,EAAG,GAAE6C;AAGlC,aAAK7C,EAAG,CAAC,KAETL,EAAE,IAAIK,GACNL,EAAE,IAAIqD,GAAkBhD,GAAI6C,CAAC,GAEtB1E,IAAWmB,EAASK,GAAGS,GAAIC,CAAE,IAAIV,KALrB,IAAID,EAAKW,MAAO,IAAI,KAAK,CAAC;IAM/C;AA2BAjB,MAAE,SAASA,EAAE,MAAM,SAAUO,GAAG;AAC9B,UAAIsD,GACF5D,IAAI,MACJK,IAAOL,EAAE;AAKX,aAHAM,IAAI,IAAID,EAAKC,CAAC,GAGV,CAACN,EAAE,KAAK,CAACM,EAAE,KAAKA,EAAE,KAAK,CAACA,EAAE,EAAE,CAAC,IAAU,IAAID,EAAK,GAAG,IAGnD,CAACC,EAAE,KAAKN,EAAE,KAAK,CAACA,EAAE,EAAE,CAAC,IAChBC,EAAS,IAAII,EAAKL,CAAC,GAAGK,EAAK,WAAWA,EAAK,QAAQ,KAI5DvB,IAAW,OAEPuB,EAAK,UAAU,KAIjBuD,IAAIhC,EAAO5B,GAAGM,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAC9BsD,EAAE,KAAKtD,EAAE,KAETsD,IAAIhC,EAAO5B,GAAGM,GAAG,GAAGD,EAAK,QAAQ,CAAC,GAGpCuD,IAAIA,EAAE,MAAMtD,CAAC,GAEbxB,IAAW,MAEJkB,EAAE,MAAM4D,CAAC;IAClB;AASA7D,MAAE,qBAAqBA,EAAE,MAAM,WAAY;AACzC,aAAO8D,GAAmB,IAAI;IAChC;AAQA9D,MAAE,mBAAmBA,EAAE,KAAK,WAAY;AACtC,aAAOsD,EAAiB,IAAI;IAC9B;AAQAtD,MAAE,UAAUA,EAAE,MAAM,WAAY;AAC9B,UAAIC,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,aAAAA,EAAE,IAAI,CAACA,EAAE,GACFC,EAASD,CAAC;IACnB;AAwBAD,MAAE,OAAOA,EAAE,MAAM,SAAUO,GAAG;AAC5B,UAAIwD,GAAOhC,GAAG0B,GAAGjD,GAAGH,GAAG2B,GAAKhB,GAAIC,GAAIL,GAAIC,GACtCZ,IAAI,MACJK,IAAOL,EAAE;AAKX,UAHAM,IAAI,IAAID,EAAKC,CAAC,GAGV,CAACN,EAAE,KAAK,CAACM,EAAE,EAGb,QAAI,CAACN,EAAE,KAAK,CAACM,EAAE,IAAGA,IAAI,IAAID,EAAK,GAAG,IAMxBL,EAAE,MAAGM,IAAI,IAAID,EAAKC,EAAE,KAAKN,EAAE,MAAMM,EAAE,IAAIN,IAAI,GAAG,IAEjDM;AAIT,UAAIN,EAAE,KAAKM,EAAE,EACX,QAAAA,EAAE,IAAI,CAACA,EAAE,GACFN,EAAE,MAAMM,CAAC;AASlB,UANAK,IAAKX,EAAE,GACPY,IAAKN,EAAE,GACPS,IAAKV,EAAK,WACVW,IAAKX,EAAK,UAGN,CAACM,EAAG,CAAC,KAAK,CAACC,EAAG,CAAC,EAIjB,QAAKA,EAAG,CAAC,MAAGN,IAAI,IAAID,EAAKL,CAAC,IAEnBlB,IAAWmB,EAASK,GAAGS,GAAIC,CAAE,IAAIV;AAa1C,UAPAF,IAAIhB,EAAUY,EAAE,IAAIL,CAAQ,GAC5B6D,IAAIpE,EAAUkB,EAAE,IAAIX,CAAQ,GAE5BgB,IAAKA,EAAG,MAAM,GACdJ,IAAIH,IAAIoD,GAGJjD,GAAG;AAuBL,aArBIA,IAAI,KACNuB,IAAInB,GACJJ,IAAI,CAACA,GACLwB,IAAMnB,EAAG,WAETkB,IAAIlB,GACJ4C,IAAIpD,GACJ2B,IAAMpB,EAAG,SAIXP,IAAI,KAAK,KAAKW,IAAKpB,CAAQ,GAC3BoC,IAAM3B,IAAI2B,IAAM3B,IAAI,IAAI2B,IAAM,GAE1BxB,IAAIwB,MACNxB,IAAIwB,GACJD,EAAE,SAAS,IAIbA,EAAE,QAAQ,GACHvB,MAAMuB,GAAE,KAAK,CAAC;AACrBA,UAAE,QAAQ;MACZ;AAcA,WAZAC,IAAMpB,EAAG,QACTJ,IAAIK,EAAG,QAGHmB,IAAMxB,IAAI,MACZA,IAAIwB,GACJD,IAAIlB,GACJA,IAAKD,GACLA,IAAKmB,IAIFgC,IAAQ,GAAGvD,IACduD,MAASnD,EAAG,EAAEJ,CAAC,IAAII,EAAGJ,CAAC,IAAIK,EAAGL,CAAC,IAAIuD,KAASpE,IAAO,GACnDiB,EAAGJ,CAAC,KAAKb;AAUX,WAPIoE,MACFnD,EAAG,QAAQmD,CAAK,GAChB,EAAEN,IAKCzB,IAAMpB,EAAG,QAAQA,EAAG,EAAEoB,CAAG,KAAK,IAAIpB,GAAG,IAAI;AAE9C,aAAAL,EAAE,IAAIK,GACNL,EAAE,IAAIqD,GAAkBhD,GAAI6C,CAAC,GAEtB1E,IAAWmB,EAASK,GAAGS,GAAIC,CAAE,IAAIV;IAC1C;AASAP,MAAE,YAAYA,EAAE,KAAK,SAAUgE,GAAG;AAChC,UAAI3D,GACFJ,IAAI;AAEN,UAAI+D,MAAM,UAAUA,MAAM,CAAC,CAACA,KAAKA,MAAM,KAAKA,MAAM,EAAG,OAAM,MAAM/E,IAAkB+E,CAAC;AAEpF,aAAI/D,EAAE,KACJI,IAAI4D,GAAahE,EAAE,CAAC,GAChB+D,KAAK/D,EAAE,IAAI,IAAII,MAAGA,IAAIJ,EAAE,IAAI,MAEhCI,IAAI,KAGCA;IACT;AAQAL,MAAE,QAAQ,WAAY;AACpB,UAAIC,IAAI,MACNK,IAAOL,EAAE;AAEX,aAAOC,EAAS,IAAII,EAAKL,CAAC,GAAGA,EAAE,IAAI,GAAGK,EAAK,QAAQ;IACrD;AAkBAN,MAAE,OAAOA,EAAE,MAAM,WAAY;AAC3B,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAKA,EAAE,SAAS,IACZA,EAAE,OAAO,IAAU,IAAIK,EAAKL,CAAC,KAEjCe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,KAAK,IAAIf,EAAE,GAAGA,EAAE,GAAG,CAAC,IAAIL,GAC9CU,EAAK,WAAW,GAEhBL,IAAIiE,GAAK5D,GAAMa,GAAiBb,GAAML,CAAC,CAAC,GAExCK,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAETf,EAASpB,IAAW,IAAImB,EAAE,IAAI,IAAIA,GAAGe,GAAIC,GAAI,IAAI,KAb9B,IAAIX,EAAK,GAAG;IAcxC;AAeAN,MAAE,aAAaA,EAAE,OAAO,WAAY;AAClC,UAAIoB,GAAG,GAAGI,GAAIF,GAAGC,GAAKE,GACpBxB,IAAI,MACJ8B,IAAI9B,EAAE,GACNwD,IAAIxD,EAAE,GACNkE,IAAIlE,EAAE,GACNK,IAAOL,EAAE;AAGX,UAAIkE,MAAM,KAAK,CAACpC,KAAK,CAACA,EAAE,CAAC,EACvB,QAAO,IAAIzB,EAAK,CAAC6D,KAAKA,IAAI,MAAM,CAACpC,KAAKA,EAAE,CAAC,KAAK,MAAMA,IAAI9B,IAAI,IAAA,CAAK;AAgCnE,WA7BAlB,IAAW,OAGXoF,IAAI,KAAK,KAAK,CAAClE,CAAC,GAIZkE,KAAK,KAAKA,KAAK,IAAA,KACjB,IAAIvC,EAAeG,CAAC,IAEf,EAAE,SAAS0B,KAAK,KAAK,MAAG,KAAK,MAClCU,IAAI,KAAK,KAAK,CAAC,GACfV,IAAIpE,GAAWoE,IAAI,KAAK,CAAC,KAAKA,IAAI,KAAKA,IAAI,IAEvCU,KAAK,IAAA,IACP,IAAI,OAAOV,KAEX,IAAIU,EAAE,cAAc,GACpB,IAAI,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAIV,IAGvCnC,IAAI,IAAIhB,EAAK,CAAC,KAEdgB,IAAI,IAAIhB,EAAK6D,EAAE,SAAS,CAAC,GAG3B3C,KAAMiC,IAAInD,EAAK,aAAa,MAQ1B,KAJAmB,IAAIH,GACJA,IAAIG,EAAE,KAAKI,EAAO5B,GAAGwB,GAAGD,IAAK,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,GAGzCI,EAAeH,EAAE,CAAC,EAAE,MAAM,GAAGD,CAAE,OAAO,IAAII,EAAeN,EAAE,CAAC,GAAG,MAAM,GAAGE,CAAE,EAK5E,KAJA,IAAI,EAAE,MAAMA,IAAK,GAAGA,IAAK,CAAC,GAItB,KAAK,UAAU,CAACD,KAAO,KAAK,QAAQ;AAItC,YAAI,CAACA,MACHrB,EAASuB,GAAGgC,IAAI,GAAG,CAAC,GAEhBhC,EAAE,MAAMA,CAAC,EAAE,GAAGxB,CAAC,IAAG;AACpBqB,cAAIG;AACJ;QACF;AAGFD,aAAM,GACND,IAAM;MACR,OAAO;AAAA,SAID,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,SAGxCrB,EAASoB,GAAGmC,IAAI,GAAG,CAAC,GACpBrC,IAAI,CAACE,EAAE,MAAMA,CAAC,EAAE,GAAGrB,CAAC;AAGtB;MACF;AAIJ,aAAAlB,IAAW,MAEJmB,EAASoB,GAAGmC,GAAGnD,EAAK,UAAUc,CAAC;IACxC;AAgBApB,MAAE,UAAUA,EAAE,MAAM,WAAY;AAC9B,UAAIgB,GAAIC,GACNhB,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAKA,EAAE,SAAS,IACZA,EAAE,OAAO,IAAU,IAAIK,EAAKL,CAAC,KAEjCe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UACVA,EAAK,YAAYU,IAAK,IACtBV,EAAK,WAAW,GAEhBL,IAAIA,EAAE,IAAI,GACVA,EAAE,IAAI,GACNA,IAAI4B,EAAO5B,GAAG,IAAIK,EAAK,CAAC,EAAE,MAAML,EAAE,MAAMA,CAAC,CAAC,EAAE,KAAK,GAAGe,IAAK,IAAI,CAAC,GAE9DV,EAAK,YAAYU,GACjBV,EAAK,WAAWW,GAETf,EAASpB,KAAY,KAAKA,KAAY,IAAImB,EAAE,IAAI,IAAIA,GAAGe,GAAIC,GAAI,IAAI,KAfhD,IAAIX,EAAK,GAAG;IAgBxC;AAwBAN,MAAE,QAAQA,EAAE,MAAM,SAAUO,GAAG;AAC7B,UAAIwD,GAAON,GAAGjD,GAAGH,GAAGiB,GAAG8C,GAAI3C,GAAGf,GAAKC,GACjCV,IAAI,MACJK,IAAOL,EAAE,aACTW,IAAKX,EAAE,GACPY,KAAMN,IAAI,IAAID,EAAKC,CAAC,GAAG;AAKzB,UAHAA,EAAE,KAAKN,EAAE,GAGL,CAACW,KAAM,CAACA,EAAG,CAAC,KAAK,CAACC,KAAM,CAACA,EAAG,CAAC,EAE/B,QAAO,IAAIP,EAAK,CAACC,EAAE,KAAKK,KAAM,CAACA,EAAG,CAAC,KAAK,CAACC,KAAMA,KAAM,CAACA,EAAG,CAAC,KAAK,CAACD,IAI5D,MAIA,CAACA,KAAM,CAACC,IAAKN,EAAE,IAAI,IAAIA,EAAE,IAAI,CAAC;AAoBpC,WAjBAkD,IAAIpE,EAAUY,EAAE,IAAIL,CAAQ,IAAIP,EAAUkB,EAAE,IAAIX,CAAQ,GACxDc,IAAME,EAAG,QACTD,IAAME,EAAG,QAGLH,IAAMC,MACRW,IAAIV,GACJA,IAAKC,GACLA,IAAKS,GACL8C,IAAK1D,GACLA,IAAMC,GACNA,IAAMyD,IAIR9C,IAAI,CAAC,GACL8C,IAAK1D,IAAMC,GACNH,IAAI4D,GAAI5D,MAAMc,GAAE,KAAK,CAAC;AAG3B,WAAKd,IAAIG,GAAK,EAAEH,KAAK,KAAI;AAEvB,aADAuD,IAAQ,GACH1D,IAAIK,IAAMF,GAAGH,IAAIG,IACpBiB,KAAIH,EAAEjB,CAAC,IAAIQ,EAAGL,CAAC,IAAII,EAAGP,IAAIG,IAAI,CAAC,IAAIuD,GACnCzC,EAAEjB,GAAG,IAAIoB,IAAI9B,IAAO,GACpBoE,IAAQtC,IAAI9B,IAAO;AAGrB2B,UAAEjB,CAAC,KAAKiB,EAAEjB,CAAC,IAAI0D,KAASpE,IAAO;MACjC;AAGA,aAAO,CAAC2B,EAAE,EAAE8C,CAAE,IAAI9C,GAAE,IAAI;AAExB,aAAIyC,IAAO,EAAEN,IACRnC,EAAE,MAAM,GAEbf,EAAE,IAAIe,GACNf,EAAE,IAAIqD,GAAkBtC,GAAGmC,CAAC,GAErB1E,IAAWmB,EAASK,GAAGD,EAAK,WAAWA,EAAK,QAAQ,IAAIC;IACjE;AAaAP,MAAE,WAAW,SAAUwB,GAAIP,GAAI;AAC7B,aAAOoD,GAAe,MAAM,GAAG7C,GAAIP,CAAE;IACvC;AAaAjB,MAAE,kBAAkBA,EAAE,OAAO,SAAUsE,GAAIrD,GAAI;AAC7C,UAAIhB,IAAI,MACNK,IAAOL,EAAE;AAGX,aADAA,IAAI,IAAIK,EAAKL,CAAC,GACVqE,MAAO,SAAerE,KAE1BsE,EAAWD,GAAI,GAAG9F,CAAU,GAExByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,GAEjBf,EAASD,GAAGqE,IAAKrE,EAAE,IAAI,GAAGgB,CAAE;IACrC;AAWAjB,MAAE,gBAAgB,SAAUsE,GAAIrD,GAAI;AAClC,UAAIuD,GACFvE,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAIqE,MAAO,SACTE,IAAMC,EAAexE,GAAG,IAAI,KAE5BsE,EAAWD,GAAI,GAAG9F,CAAU,GAExByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,GAExBhB,IAAIC,EAAS,IAAII,EAAKL,CAAC,GAAGqE,IAAK,GAAGrD,CAAE,GACpCuD,IAAMC,EAAexE,GAAG,MAAMqE,IAAK,CAAC,IAG/BrE,EAAE,MAAM,KAAK,CAACA,EAAE,OAAO,IAAI,MAAMuE,IAAMA;IAChD;AAmBAxE,MAAE,UAAU,SAAUsE,GAAIrD,GAAI;AAC5B,UAAIuD,GAAKjE,GACPN,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAIqE,MAAO,SACTE,IAAMC,EAAexE,CAAC,KAEtBsE,EAAWD,GAAI,GAAG9F,CAAU,GAExByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,GAExBV,IAAIL,EAAS,IAAII,EAAKL,CAAC,GAAGqE,IAAKrE,EAAE,IAAI,GAAGgB,CAAE,GAC1CuD,IAAMC,EAAelE,GAAG,OAAO+D,IAAK/D,EAAE,IAAI,CAAC,IAKtCN,EAAE,MAAM,KAAK,CAACA,EAAE,OAAO,IAAI,MAAMuE,IAAMA;IAChD;AAcAxE,MAAE,aAAa,SAAU0E,GAAM;AAC7B,UAAI3C,GAAG4C,GAAIC,GAAIC,GAAIpB,GAAGpD,GAAGgB,GAAGyD,GAAIC,GAAI/D,GAAI6C,GAAGvC,GACzCrB,IAAI,MACJW,IAAKX,EAAE,GACPK,IAAOL,EAAE;AAEX,UAAI,CAACW,EAAI,QAAO,IAAIN,EAAKL,CAAC;AAU1B,UARA8E,IAAKJ,IAAK,IAAIrE,EAAK,CAAC,GACpBsE,IAAKE,IAAK,IAAIxE,EAAK,CAAC,GAEpByB,IAAI,IAAIzB,EAAKsE,CAAE,GACfnB,IAAI1B,EAAE,IAAIkC,GAAarD,CAAE,IAAIX,EAAE,IAAI,GACnCI,IAAIoD,IAAI7D,GACRmC,EAAE,EAAE,CAAC,IAAIzC,EAAQ,IAAIe,IAAI,IAAIT,IAAWS,IAAIA,CAAC,GAEzCqE,KAAQ,KAGVA,KAAOjB,IAAI,IAAI1B,IAAIgD;WACd;AAEL,YADA1D,IAAI,IAAIf,EAAKoE,CAAI,GACb,CAACrD,EAAE,MAAM,KAAKA,EAAE,GAAG0D,CAAE,EAAG,OAAM,MAAM9F,IAAkBoC,CAAC;AAC3DqD,YAAOrD,EAAE,GAAGU,CAAC,IAAK0B,IAAI,IAAI1B,IAAIgD,IAAM1D;MACtC;AAOA,WALAtC,IAAW,OACXsC,IAAI,IAAIf,EAAKsB,EAAehB,CAAE,CAAC,GAC/BI,IAAKV,EAAK,WACVA,EAAK,YAAYmD,IAAI7C,EAAG,SAAShB,IAAW,GAG1CiE,IAAIhC,EAAOR,GAAGU,GAAG,GAAG,GAAG,CAAC,GACxB8C,IAAKF,EAAG,KAAKd,EAAE,MAAMe,CAAE,CAAC,GACpBC,EAAG,IAAIH,CAAI,KAAK,IACpBC,KAAKC,GACLA,IAAKC,GACLA,IAAKE,GACLA,IAAKD,EAAG,KAAKjB,EAAE,MAAMgB,CAAE,CAAC,GACxBC,IAAKD,GACLA,IAAK9C,GACLA,IAAIV,EAAE,MAAMwC,EAAE,MAAMgB,CAAE,CAAC,GACvBxD,IAAIwD;AAGN,aAAAA,IAAKhD,EAAO6C,EAAK,MAAMC,CAAE,GAAGC,GAAI,GAAG,GAAG,CAAC,GACvCE,IAAKA,EAAG,KAAKD,EAAG,MAAME,CAAE,CAAC,GACzBJ,IAAKA,EAAG,KAAKE,EAAG,MAAMD,CAAE,CAAC,GACzBE,EAAG,IAAIC,EAAG,IAAI9E,EAAE,GAGhBqB,IAAIO,EAAOkD,GAAIH,GAAInB,GAAG,CAAC,EAAE,MAAMxD,CAAC,EAAE,IAAI,EAAE,IAAI4B,EAAOiD,GAAIH,GAAIlB,GAAG,CAAC,EAAE,MAAMxD,CAAC,EAAE,IAAI,CAAC,IAAI,IAC7E,CAAC8E,GAAIH,CAAE,IAAI,CAACE,GAAIH,CAAE,GAExBrE,EAAK,YAAYU,GACjBjC,IAAW,MAEJuC;IACT;AAaAtB,MAAE,gBAAgBA,EAAE,QAAQ,SAAUwB,GAAIP,GAAI;AAC5C,aAAOoD,GAAe,MAAM,IAAI7C,GAAIP,CAAE;IACxC;AAmBAjB,MAAE,YAAY,SAAUO,GAAGU,GAAI;AAC7B,UAAIhB,IAAI,MACNK,IAAOL,EAAE;AAIX,UAFAA,IAAI,IAAIK,EAAKL,CAAC,GAEVM,KAAK,MAAM;AAGb,YAAI,CAACN,EAAE,EAAG,QAAOA;AAEjBM,YAAI,IAAID,EAAK,CAAC,GACdW,IAAKX,EAAK;MACZ,OAAO;AASL,YARAC,IAAI,IAAID,EAAKC,CAAC,GACVU,MAAO,SACTA,IAAKX,EAAK,WAEViE,EAAWtD,GAAI,GAAG,CAAC,GAIjB,CAAChB,EAAE,EAAG,QAAOM,EAAE,IAAIN,IAAIM;AAG3B,YAAI,CAACA,EAAE,EACL,QAAIA,EAAE,MAAGA,EAAE,IAAIN,EAAE,IACVM;MAEX;AAGA,aAAIA,EAAE,EAAE,CAAC,KACPxB,IAAW,OACXkB,IAAI4B,EAAO5B,GAAGM,GAAG,GAAGU,GAAI,CAAC,EAAE,MAAMV,CAAC,GAClCxB,IAAW,MACXmB,EAASD,CAAC,MAIVM,EAAE,IAAIN,EAAE,GACRA,IAAIM,IAGCN;IACT;AAQAD,MAAE,WAAW,WAAY;AACvB,aAAO,CAAC;IACV;AAaAA,MAAE,UAAU,SAAUwB,GAAIP,GAAI;AAC5B,aAAOoD,GAAe,MAAM,GAAG7C,GAAIP,CAAE;IACvC;AA8CAjB,MAAE,UAAUA,EAAE,MAAM,SAAUO,GAAG;AAC/B,UAAIkD,GAAGpD,GAAGW,GAAI,GAAGC,GAAIkD,GACnBlE,IAAI,MACJK,IAAOL,EAAE,aACT+E,IAAK,EAAEzE,IAAI,IAAID,EAAKC,CAAC;AAGvB,UAAI,CAACN,EAAE,KAAK,CAACM,EAAE,KAAK,CAACN,EAAE,EAAE,CAAC,KAAK,CAACM,EAAE,EAAE,CAAC,EAAG,QAAO,IAAID,EAAKhB,EAAQ,CAACW,GAAG+E,CAAE,CAAC;AAIvE,UAFA/E,IAAI,IAAIK,EAAKL,CAAC,GAEVA,EAAE,GAAG,CAAC,EAAG,QAAOA;AAKpB,UAHAe,IAAKV,EAAK,WACVW,IAAKX,EAAK,UAENC,EAAE,GAAG,CAAC,EAAG,QAAOL,EAASD,GAAGe,GAAIC,CAAE;AAMtC,UAHAwC,IAAIpE,EAAUkB,EAAE,IAAIX,CAAQ,GAGxB6D,KAAKlD,EAAE,EAAE,SAAS,MAAMF,IAAI2E,IAAK,IAAI,CAACA,IAAKA,MAAOnF,GACpD,QAAA,IAAIoF,GAAO3E,GAAML,GAAGI,GAAGW,CAAE,GAClBT,EAAE,IAAI,IAAI,IAAID,EAAK,CAAC,EAAE,IAAI,CAAC,IAAIJ,EAAS,GAAGc,GAAIC,CAAE;AAM1D,UAHAkD,IAAIlE,EAAE,GAGFkE,IAAI,GAAG;AAGT,YAAIV,IAAIlD,EAAE,EAAE,SAAS,EAAG,QAAO,IAAID,EAAK,GAAG;AAM3C,aAHKC,EAAE,EAAEkD,CAAC,IAAI,MAAM,MAAGU,IAAI,IAGvBlE,EAAE,KAAK,KAAKA,EAAE,EAAE,CAAC,KAAK,KAAKA,EAAE,EAAE,UAAU,EAC3C,QAAAA,EAAE,IAAIkE,GACClE;MAEX;AAcA,aARAI,IAAIf,EAAQ,CAACW,GAAG+E,CAAE,GAClBvB,IAAIpD,KAAK,KAAK,CAAC,SAASA,CAAC,IACrBhB,EAAU2F,KAAM,KAAK,IAAI,OAAOpD,EAAe3B,EAAE,CAAC,CAAC,IAAI,KAAK,OAAOA,EAAE,IAAI,EAAE,IAC3E,IAAIK,EAAKD,IAAI,EAAE,EAAE,GAKjBoD,IAAInD,EAAK,OAAO,KAAKmD,IAAInD,EAAK,OAAO,IAAU,IAAIA,EAAKmD,IAAI,IAAIU,IAAI,IAAI,CAAC,KAE7EpF,IAAW,OACXuB,EAAK,WAAWL,EAAE,IAAI,GAMtBI,IAAI,KAAK,IAAI,KAAKoD,IAAI,IAAI,MAAM,GAGhC,IAAIK,GAAmBvD,EAAE,MAAM+C,EAAiBrD,GAAGe,IAAKX,CAAC,CAAC,GAAGW,CAAE,GAG3D,EAAE,MAGJ,IAAId,EAAS,GAAGc,IAAK,GAAG,CAAC,GAIrBwC,EAAoB,EAAE,GAAGxC,GAAIC,CAAE,MACjCwC,IAAIzC,IAAK,IAGT,IAAId,EAAS4D,GAAmBvD,EAAE,MAAM+C,EAAiBrD,GAAGwD,IAAIpD,CAAC,CAAC,GAAGoD,CAAC,GAAGA,IAAI,GAAG,CAAC,GAG7E,CAAC7B,EAAe,EAAE,CAAC,EAAE,MAAMZ,IAAK,GAAGA,IAAK,EAAE,IAAI,KAAK,SACrD,IAAId,EAAS,GAAGc,IAAK,GAAG,CAAC,MAK/B,EAAE,IAAImD,GACNpF,IAAW,MACXuB,EAAK,WAAWW,GAETf,EAAS,GAAGc,GAAIC,CAAE;IAC3B;AAcAjB,MAAE,cAAc,SAAUwB,GAAIP,GAAI;AAChC,UAAIuD,GACFvE,IAAI,MACJK,IAAOL,EAAE;AAEX,aAAIuB,MAAO,SACTgD,IAAMC,EAAexE,GAAGA,EAAE,KAAKK,EAAK,YAAYL,EAAE,KAAKK,EAAK,QAAQ,KAEpEiE,EAAW/C,GAAI,GAAGhD,CAAU,GAExByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,GAExBhB,IAAIC,EAAS,IAAII,EAAKL,CAAC,GAAGuB,GAAIP,CAAE,GAChCuD,IAAMC,EAAexE,GAAGuB,KAAMvB,EAAE,KAAKA,EAAE,KAAKK,EAAK,UAAUkB,CAAE,IAGxDvB,EAAE,MAAM,KAAK,CAACA,EAAE,OAAO,IAAI,MAAMuE,IAAMA;IAChD;AAiBAxE,MAAE,sBAAsBA,EAAE,OAAO,SAAUwB,GAAIP,GAAI;AACjD,UAAIhB,IAAI,MACNK,IAAOL,EAAE;AAEX,aAAIuB,MAAO,UACTA,IAAKlB,EAAK,WACVW,IAAKX,EAAK,aAEViE,EAAW/C,GAAI,GAAGhD,CAAU,GAExByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,IAGnBf,EAAS,IAAII,EAAKL,CAAC,GAAGuB,GAAIP,CAAE;IACrC;AAUAjB,MAAE,WAAW,WAAY;AACvB,UAAIC,IAAI,MACNK,IAAOL,EAAE,aACTuE,IAAMC,EAAexE,GAAGA,EAAE,KAAKK,EAAK,YAAYL,EAAE,KAAKK,EAAK,QAAQ;AAEtE,aAAOL,EAAE,MAAM,KAAK,CAACA,EAAE,OAAO,IAAI,MAAMuE,IAAMA;IAChD;AAOAxE,MAAE,YAAYA,EAAE,QAAQ,WAAY;AAClC,aAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;IAC3D;AAQAF,MAAE,UAAUA,EAAE,SAAS,WAAY;AACjC,UAAIC,IAAI,MACNK,IAAOL,EAAE,aACTuE,IAAMC,EAAexE,GAAGA,EAAE,KAAKK,EAAK,YAAYL,EAAE,KAAKK,EAAK,QAAQ;AAEtE,aAAOL,EAAE,MAAM,IAAI,MAAMuE,IAAMA;IACjC;AAoDA,aAAS5C,EAAeG,GAAG;AACzB,UAAIvB,GAAGH,GAAG6E,GACRC,IAAkBpD,EAAE,SAAS,GAC7ByC,IAAM,IACN1C,IAAIC,EAAE,CAAC;AAET,UAAIoD,IAAkB,GAAG;AAEvB,aADAX,KAAO1C,GACFtB,IAAI,GAAGA,IAAI2E,GAAiB3E,IAC/B0E,KAAKnD,EAAEvB,CAAC,IAAI,IACZH,IAAIT,IAAWsF,EAAG,QACd7E,MAAGmE,KAAOY,EAAc/E,CAAC,IAC7BmE,KAAOU;AAGTpD,YAAIC,EAAEvB,CAAC,GACP0E,IAAKpD,IAAI,IACTzB,IAAIT,IAAWsF,EAAG,QACd7E,MAAGmE,KAAOY,EAAc/E,CAAC;MAC/B,WAAWyB,MAAM,EACf,QAAO;AAIT,aAAOA,IAAI,OAAO,IAAIA,MAAK;AAE3B,aAAO0C,IAAM1C;IACf;AAGA,aAASyC,EAAW/D,GAAGL,GAAKC,GAAK;AAC/B,UAAII,MAAM,CAAC,CAACA,KAAKA,IAAIL,KAAOK,IAAIJ,EAC9B,OAAM,MAAMnB,IAAkBuB,CAAC;IAEnC;AAQA,aAASgD,EAAoBzB,GAAGvB,GAAGS,GAAIoE,GAAW;AAChD,UAAIC,GAAIjF,GAAGiB,GAAGiE;AAGd,WAAKlF,IAAI0B,EAAE,CAAC,GAAG1B,KAAK,IAAIA,KAAK,GAAI,GAAEG;AAGnC,aAAI,EAAEA,IAAI,KACRA,KAAKZ,GACL0F,IAAK,MAELA,IAAK,KAAK,MAAM9E,IAAI,KAAKZ,CAAQ,GACjCY,KAAKZ,IAMPS,IAAIf,EAAQ,IAAIM,IAAWY,CAAC,GAC5B+E,IAAKxD,EAAEuD,CAAE,IAAIjF,IAAI,GAEbgF,KAAa,OACX7E,IAAI,KACFA,KAAK,IAAG+E,IAAKA,IAAK,MAAM,IACnB/E,KAAK,MAAG+E,IAAKA,IAAK,KAAK,IAChCjE,IAAIL,IAAK,KAAKsE,KAAM,SAAStE,IAAK,KAAKsE,KAAM,SAASA,KAAM,OAASA,KAAM,KAE3EjE,KAAKL,IAAK,KAAKsE,IAAK,KAAKlF,KAAKY,IAAK,KAAKsE,IAAK,KAAKlF,IAAI,OACnD0B,EAAEuD,IAAK,CAAC,IAAIjF,IAAI,MAAM,MAAMf,EAAQ,IAAIkB,IAAI,CAAC,IAAI,MAC/C+E,KAAMlF,IAAI,KAAKkF,KAAM,OAAOxD,EAAEuD,IAAK,CAAC,IAAIjF,IAAI,MAAM,MAAM,IAG3DG,IAAI,KACFA,KAAK,IAAG+E,IAAKA,IAAK,MAAO,IACpB/E,KAAK,IAAG+E,IAAKA,IAAK,MAAM,IACxB/E,KAAK,MAAG+E,IAAKA,IAAK,KAAK,IAChCjE,KAAK+D,KAAapE,IAAK,MAAMsE,KAAM,QAAQ,CAACF,KAAapE,IAAK,KAAKsE,KAAM,QAEzEjE,MAAM+D,KAAapE,IAAK,MAAMsE,IAAK,KAAKlF,KACvC,CAACgF,KAAapE,IAAK,KAAMsE,IAAK,KAAKlF,IAAI,OACrC0B,EAAEuD,IAAK,CAAC,IAAIjF,IAAI,MAAO,MAAMf,EAAQ,IAAIkB,IAAI,CAAC,IAAI,GAIlDc;IACT;AAMA,aAASkE,GAAYhB,GAAKiB,GAAQC,GAAS;AAOzC,eANIjF,GACFkF,IAAM,CAAC,CAAC,GACRC,GACApF,IAAI,GACJqF,IAAOrB,EAAI,QAENhE,IAAIqF,KAAO;AAChB,aAAKD,IAAOD,EAAI,QAAQC,MAASD,GAAIC,CAAI,KAAKH;AAE9C,aADAE,EAAI,CAAC,KAAKlH,GAAS,QAAQ+F,EAAI,OAAOhE,GAAG,CAAC,GACrCC,IAAI,GAAGA,IAAIkF,EAAI,QAAQlF,IACtBkF,GAAIlF,CAAC,IAAIiF,IAAU,MACjBC,EAAIlF,IAAI,CAAC,MAAM,WAAQkF,EAAIlF,IAAI,CAAC,IAAI,IACxCkF,EAAIlF,IAAI,CAAC,KAAKkF,EAAIlF,CAAC,IAAIiF,IAAU,GACjCC,EAAIlF,CAAC,KAAKiF;MAGhB;AAEA,aAAOC,EAAI,QAAQ;IACrB;AAQA,aAASzE,GAAOZ,GAAML,GAAG;AACvB,UAAII,GAAG2B,GAAKzB;AAEZ,UAAIN,EAAE,OAAO,EAAG,QAAOA;AAMvB+B,UAAM/B,EAAE,EAAE,QACN+B,IAAM,MACR3B,IAAI,KAAK,KAAK2B,IAAM,CAAC,GACrBzB,KAAK,IAAI2B,GAAQ,GAAG7B,CAAC,GAAG,SAAS,MAEjCA,IAAI,IACJE,IAAI,iCAGND,EAAK,aAAaD,GAElBJ,IAAIkC,EAAa7B,GAAM,GAAGL,EAAE,MAAMM,CAAC,GAAG,IAAID,EAAK,CAAC,CAAC;AAGjD,eAASE,IAAIH,GAAGG,OAAM;AACpB,YAAIsF,IAAQ7F,EAAE,MAAMA,CAAC;AACrBA,YAAI6F,EAAM,MAAMA,CAAK,EAAE,MAAMA,CAAK,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC;MACrD;AAEA,aAAAxF,EAAK,aAAaD,GAEXJ;IACT;AAMA,QAAI4B,IAAU,2BAAY;AAGxB,eAASkE,EAAgB9F,GAAGI,GAAG2C,GAAM;AACnC,YAAIgD,GACFjC,IAAQ,GACRvD,IAAIP,EAAE;AAER,aAAKA,IAAIA,EAAE,MAAM,GAAGO,MAClBwF,KAAO/F,EAAEO,CAAC,IAAIH,IAAI0D,GAClB9D,EAAEO,CAAC,IAAIwF,IAAOhD,IAAO,GACrBe,IAAQiC,IAAOhD,IAAO;AAGxB,eAAIe,KAAO9D,EAAE,QAAQ8D,CAAK,GAEnB9D;MACT;AAEA,eAASgG,EAAQC,GAAGC,GAAGC,GAAIC,GAAI;AAC7B,YAAI7F,GAAGc;AAEP,YAAI8E,KAAMC,EACR/E,KAAI8E,IAAKC,IAAK,IAAI;YAElB,MAAK7F,IAAIc,IAAI,GAAGd,IAAI4F,GAAI5F,IACtB,KAAI0F,EAAE1F,CAAC,KAAK2F,EAAE3F,CAAC,GAAG;AAChBc,cAAI4E,EAAE1F,CAAC,IAAI2F,EAAE3F,CAAC,IAAI,IAAI;AACtB;QACF;AAIJ,eAAOc;MACT;AAEA,eAASgF,EAASJ,GAAGC,GAAGC,GAAIpD,GAAM;AAIhC,iBAHIxC,IAAI,GAGD4F,MACLF,GAAEE,CAAE,KAAK5F,GACTA,IAAI0F,EAAEE,CAAE,IAAID,EAAEC,CAAE,IAAI,IAAI,GACxBF,EAAEE,CAAE,IAAI5F,IAAIwC,IAAOkD,EAAEE,CAAE,IAAID,EAAEC,CAAE;AAIjC,eAAO,CAACF,EAAE,CAAC,KAAKA,EAAE,SAAS,IAAIA,GAAE,MAAM;MACzC;AAEA,aAAO,SAAUjG,GAAGM,GAAGS,GAAIC,GAAIqD,GAAItB,GAAM;AACvC,YAAIuD,GAAK9C,GAAGjD,GAAGH,GAAGmG,GAASC,GAAMC,GAAMC,GAAO9C,GAAG+C,GAAIC,GAAKC,GAAMC,GAAMvF,GAAIC,IAAGuF,GAAIC,GAAIC,IACnFC,GAAIC,GACJ9G,KAAOL,EAAE,aACToH,KAAOpH,EAAE,KAAKM,EAAE,IAAI,IAAI,IACxBK,IAAKX,EAAE,GACPY,IAAKN,EAAE;AAGT,YAAI,CAACK,KAAM,CAACA,EAAG,CAAC,KAAK,CAACC,KAAM,CAACA,EAAG,CAAC,EAE/B,QAAO,IAAIP,GACT,CAACL,EAAE,KAAK,CAACM,EAAE,MAAMK,IAAKC,KAAMD,EAAG,CAAC,KAAKC,EAAG,CAAC,IAAI,CAACA,KAAM,MAGpDD,KAAMA,EAAG,CAAC,KAAK,KAAK,CAACC,IAAKwG,KAAO,IAAIA,KAAO,CAAC;AAmBjD,aAhBIrE,KACFwD,IAAU,GACV/C,IAAIxD,EAAE,IAAIM,EAAE,MAEZyC,IAAOrD,GACP6G,IAAU5G,GACV6D,IAAIpE,EAAUY,EAAE,IAAIuG,CAAO,IAAInH,EAAUkB,EAAE,IAAIiG,CAAO,IAGxDW,IAAKtG,EAAG,QACRoG,IAAKrG,EAAG,QACRiD,IAAI,IAAIvD,GAAK+G,EAAI,GACjBT,IAAK/C,EAAE,IAAI,CAAC,GAIPrD,IAAI,GAAGK,EAAGL,CAAC,MAAMI,EAAGJ,CAAC,KAAK,IAAIA,IAAI;AAavC,YAXIK,EAAGL,CAAC,KAAKI,EAAGJ,CAAC,KAAK,MAAIiD,KAEtBzC,KAAM,QACRQ,IAAKR,IAAKV,GAAK,WACfW,IAAKX,GAAK,YACDgE,IACT9C,IAAKR,KAAMf,EAAE,IAAIM,EAAE,KAAK,IAExBiB,IAAKR,GAGHQ,IAAK,EACPoF,GAAG,KAAK,CAAC,GACTH,IAAO;aACF;AAOL,cAJAjF,IAAKA,IAAKgF,IAAU,IAAI,GACxBhG,IAAI,GAGA2G,KAAM,GAAG;AAMX,iBALA9G,IAAI,GACJQ,IAAKA,EAAG,CAAC,GACTW,MAGQhB,IAAIyG,KAAM5G,MAAMmB,KAAMhB,IAC5BiB,MAAIpB,IAAI2C,KAAQpC,EAAGJ,CAAC,KAAK,IACzBoG,EAAGpG,CAAC,IAAIiB,KAAIZ,IAAK,GACjBR,IAAIoB,KAAIZ,IAAK;AAGf4F,gBAAOpG,KAAKG,IAAIyG;UAGlB,OAAO;AAiBL,iBAdA5G,IAAI2C,KAAQnC,EAAG,CAAC,IAAI,KAAK,GAErBR,IAAI,MACNQ,IAAKkF,EAAgBlF,GAAIR,GAAG2C,CAAI,GAChCpC,IAAKmF,EAAgBnF,GAAIP,GAAG2C,CAAI,GAChCmE,IAAKtG,EAAG,QACRoG,IAAKrG,EAAG,SAGVoG,IAAKG,GACLN,IAAMjG,EAAG,MAAM,GAAGuG,CAAE,GACpBL,IAAOD,EAAI,QAGJC,IAAOK,IAAKN,GAAIC,GAAM,IAAI;AAEjCM,gBAAKvG,EAAG,MAAM,GACduG,EAAG,QAAQ,CAAC,GACZF,KAAMrG,EAAG,CAAC,GAENA,EAAG,CAAC,KAAKmC,IAAO,KAAG,EAAEkE;AAEzB;AACE7G,kBAAI,GAGJkG,IAAMN,EAAQpF,GAAIgG,GAAKM,GAAIL,CAAI,GAG3BP,IAAM,KAGRQ,IAAOF,EAAI,CAAC,GACRM,KAAML,MAAMC,IAAOA,IAAO/D,KAAQ6D,EAAI,CAAC,KAAK,KAGhDxG,IAAI0G,IAAOG,KAAM,GAUb7G,IAAI,KACFA,KAAK2C,MAAM3C,IAAI2C,IAAO,IAG1B0D,IAAOX,EAAgBlF,GAAIR,GAAG2C,CAAI,GAClC2D,IAAQD,EAAK,QACbI,IAAOD,EAAI,QAGXN,IAAMN,EAAQS,GAAMG,GAAKF,GAAOG,CAAI,GAGhCP,KAAO,MACTlG,KAGAiG,EAASI,GAAMS,IAAKR,IAAQS,IAAKvG,GAAI8F,GAAO3D,CAAI,OAO9C3C,KAAK,MAAGkG,IAAMlG,IAAI,IACtBqG,IAAO7F,EAAG,MAAM,IAGlB8F,IAAQD,EAAK,QACTC,IAAQG,KAAMJ,EAAK,QAAQ,CAAC,GAGhCJ,EAASO,GAAKH,GAAMI,GAAM9D,CAAI,GAG1BuD,KAAO,OACTO,IAAOD,EAAI,QAGXN,IAAMN,EAAQpF,GAAIgG,GAAKM,GAAIL,CAAI,GAG3BP,IAAM,MACRlG,KAGAiG,EAASO,GAAKM,IAAKL,IAAOM,IAAKvG,GAAIiG,GAAM9D,CAAI,KAIjD8D,IAAOD,EAAI,UACFN,MAAQ,MACjBlG,KACAwG,IAAM,CAAC,CAAC,IAIVD,EAAGpG,GAAG,IAAIH,GAGNkG,KAAOM,EAAI,CAAC,IACdA,EAAIC,GAAM,IAAIlG,EAAGoG,CAAE,KAAK,KAExBH,IAAM,CAACjG,EAAGoG,CAAE,CAAC,GACbF,IAAO;oBAGDE,MAAOC,KAAMJ,EAAI,CAAC,MAAM,WAAWrF;AAE7CiF,gBAAOI,EAAI,CAAC,MAAM;UACpB;AAGKD,YAAG,CAAC,KAAGA,EAAG,MAAM;QACvB;AAGA,YAAIJ,KAAW,EACb3C,GAAE,IAAIJ,GACN5E,KAAU4H;aACL;AAGL,eAAKjG,IAAI,GAAGH,IAAIuG,EAAG,CAAC,GAAGvG,KAAK,IAAIA,KAAK,GAAIG;AACzCqD,YAAE,IAAIrD,IAAIiD,IAAI+C,IAAU,GAExBtG,EAAS2D,GAAGS,IAAKtD,IAAK6C,EAAE,IAAI,IAAI7C,GAAIC,GAAIwF,CAAI;QAC9C;AAEA,eAAO5C;MACT;IACF,EAAG;AAOF,aAAS3D,EAASD,GAAGuB,GAAIP,GAAIqG,GAAa;AACzC,UAAIC,GAAQ/G,GAAGC,GAAGJ,GAAGkF,GAAIiC,GAAS1F,GAAGlB,GAAI6G,GACvCnH,IAAOL,EAAE;AAGXyH,QAAK,KAAIlG,KAAM,MAAM;AAInB,YAHAZ,IAAKX,EAAE,GAGH,CAACW,EAAI,QAAOX;AAWhB,aAAKsH,IAAS,GAAGlH,IAAIO,EAAG,CAAC,GAAGP,KAAK,IAAIA,KAAK,GAAIkH;AAI9C,YAHA/G,IAAIgB,IAAK+F,GAGL/G,IAAI,EACNA,MAAKZ,GACLa,IAAIe,GACJM,IAAIlB,EAAG6G,IAAM,CAAC,GAGdlC,IAAKzD,IAAIxC,EAAQ,IAAIiI,IAAS9G,IAAI,CAAC,IAAI,KAAK;iBAE5CgH,IAAM,KAAK,MAAMjH,IAAI,KAAKZ,CAAQ,GAClCS,IAAIO,EAAG,QACH6G,KAAOpH,EACT,KAAIiH,GAAa;AAGf,iBAAOjH,OAAOoH,IAAM7G,GAAG,KAAK,CAAC;AAC7BkB,cAAIyD,IAAK,GACTgC,IAAS,GACT/G,KAAKZ,GACLa,IAAID,IAAIZ,IAAW;QACrB,MACE,OAAM8H;aAEH;AAIL,eAHA5F,IAAIzB,IAAIO,EAAG6G,CAAG,GAGTF,IAAS,GAAGlH,KAAK,IAAIA,KAAK,GAAIkH;AAGnC/G,eAAKZ,GAILa,IAAID,IAAIZ,IAAW2H,GAGnBhC,IAAK9E,IAAI,IAAI,IAAIqB,IAAIxC,EAAQ,IAAIiI,IAAS9G,IAAI,CAAC,IAAI,KAAK;QAC1D;AAmBF,YAfA6G,IAAcA,KAAe9F,IAAK,KAChCZ,EAAG6G,IAAM,CAAC,MAAM,WAAWhH,IAAI,IAAIqB,IAAIA,IAAIxC,EAAQ,IAAIiI,IAAS9G,IAAI,CAAC,IAMvE+G,IAAUvG,IAAK,KACVsE,KAAM+B,OAAiBrG,KAAM,KAAKA,MAAOhB,EAAE,IAAI,IAAI,IAAI,MACxDsF,IAAK,KAAKA,KAAM,MAAMtE,KAAM,KAAKqG,KAAerG,KAAM,MAGpDT,IAAI,IAAIC,IAAI,IAAIqB,IAAIxC,EAAQ,IAAIiI,IAAS9G,CAAC,IAAI,IAAIG,EAAG6G,IAAM,CAAC,KAAK,KAAM,KACvExG,MAAOhB,EAAE,IAAI,IAAI,IAAI,KAEvBuB,IAAK,KAAK,CAACZ,EAAG,CAAC,EACjB,QAAAA,EAAG,SAAS,GACR4G,KAGFhG,KAAMvB,EAAE,IAAI,GAGZW,EAAG,CAAC,IAAItB,EAAQ,KAAKM,IAAW4B,IAAK5B,KAAYA,CAAQ,GACzDK,EAAE,IAAI,CAACuB,KAAM,KAIbZ,EAAG,CAAC,IAAIX,EAAE,IAAI,GAGTA;AAiBT,YAbIO,KAAK,KACPI,EAAG,SAAS6G,GACZpH,IAAI,GACJoH,QAEA7G,EAAG,SAAS6G,IAAM,GAClBpH,IAAIf,EAAQ,IAAIM,IAAWY,CAAC,GAI5BI,EAAG6G,CAAG,IAAIhH,IAAI,KAAKqB,IAAIxC,EAAQ,IAAIiI,IAAS9G,CAAC,IAAInB,EAAQ,IAAImB,CAAC,IAAI,KAAKJ,IAAI,IAGzEmH,EACF,WAGE,KAAIC,KAAO,GAAG;AAGZ,eAAKjH,IAAI,GAAGC,IAAIG,EAAG,CAAC,GAAGH,KAAK,IAAIA,KAAK,GAAID;AAEzC,eADAC,IAAIG,EAAG,CAAC,KAAKP,GACRA,IAAI,GAAGI,KAAK,IAAIA,KAAK,GAAIJ;AAG1BG,eAAKH,MACPJ,EAAE,KACEW,EAAG,CAAC,KAAKjB,MAAMiB,EAAG,CAAC,IAAI;AAG7B;QACF,OAAO;AAEL,cADAA,EAAG6G,CAAG,KAAKpH,GACPO,EAAG6G,CAAG,KAAK9H,EAAM;AACrBiB,YAAG6G,GAAK,IAAI,GACZpH,IAAI;QACN;AAKJ,aAAKG,IAAII,EAAG,QAAQA,EAAG,EAAEJ,CAAC,MAAM,IAAII,GAAG,IAAI;MAC7C;AAEA,aAAI7B,MAGEkB,EAAE,IAAIK,EAAK,QAGbL,EAAE,IAAI,MACNA,EAAE,IAAI,OAGGA,EAAE,IAAIK,EAAK,SAGpBL,EAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC,KAKLA;IACT;AAGA,aAASwE,EAAexE,GAAG0H,GAAOnG,GAAI;AACpC,UAAI,CAACvB,EAAE,SAAS,EAAG,QAAO2H,GAAkB3H,CAAC;AAC7C,UAAII,GACFoD,IAAIxD,EAAE,GACNuE,IAAM5C,EAAe3B,EAAE,CAAC,GACxB+B,IAAMwC,EAAI;AAEZ,aAAImD,KACEnG,MAAOnB,IAAImB,IAAKQ,KAAO,IACzBwC,IAAMA,EAAI,OAAO,CAAC,IAAI,MAAMA,EAAI,MAAM,CAAC,IAAIY,EAAc/E,CAAC,IACjD2B,IAAM,MACfwC,IAAMA,EAAI,OAAO,CAAC,IAAI,MAAMA,EAAI,MAAM,CAAC,IAGzCA,IAAMA,KAAOvE,EAAE,IAAI,IAAI,MAAM,QAAQA,EAAE,KAC9BwD,IAAI,KACbe,IAAM,OAAOY,EAAc,CAAC3B,IAAI,CAAC,IAAIe,GACjChD,MAAOnB,IAAImB,IAAKQ,KAAO,MAAGwC,KAAOY,EAAc/E,CAAC,MAC3CoD,KAAKzB,KACdwC,KAAOY,EAAc3B,IAAI,IAAIzB,CAAG,GAC5BR,MAAOnB,IAAImB,IAAKiC,IAAI,KAAK,MAAGe,IAAMA,IAAM,MAAMY,EAAc/E,CAAC,QAE5DA,IAAIoD,IAAI,KAAKzB,MAAKwC,IAAMA,EAAI,MAAM,GAAGnE,CAAC,IAAI,MAAMmE,EAAI,MAAMnE,CAAC,IAC5DmB,MAAOnB,IAAImB,IAAKQ,KAAO,MACrByB,IAAI,MAAMzB,MAAKwC,KAAO,MAC1BA,KAAOY,EAAc/E,CAAC,KAInBmE;IACT;AAIA,aAASZ,GAAkB2D,GAAQ9D,GAAG;AACpC,UAAI3B,IAAIyF,EAAO,CAAC;AAGhB,WAAM9D,KAAK7D,GAAUkC,KAAK,IAAIA,KAAK,GAAI2B;AACvC,aAAOA;IACT;AAGA,aAASF,GAAQjD,GAAMkB,GAAIR,GAAI;AAC7B,UAAIQ,IAAK1B,GAGP,OAAAf,IAAW,MACPiC,MAAIV,EAAK,YAAYU,IACnB,MAAM9B,EAAsB;AAEpC,aAAOgB,EAAS,IAAII,EAAK5B,EAAI,GAAG8C,GAAI,GAAG,IAAI;IAC7C;AAGA,aAASkB,EAAMpC,GAAMkB,GAAIP,GAAI;AAC3B,UAAIO,IAAKzB,GAAc,OAAM,MAAMb,EAAsB;AACzD,aAAOgB,EAAS,IAAII,EAAK3B,EAAE,GAAG6C,GAAIP,GAAI,IAAI;IAC5C;AAGA,aAASgD,GAAasD,GAAQ;AAC5B,UAAIzF,IAAIyF,EAAO,SAAS,GACtBvF,IAAMF,IAAIlC,IAAW;AAKvB,UAHAkC,IAAIyF,EAAOzF,CAAC,GAGRA,GAAG;AAGL,eAAOA,IAAI,MAAM,GAAGA,KAAK,GAAIE;AAG7B,aAAKF,IAAIyF,EAAO,CAAC,GAAGzF,KAAK,IAAIA,KAAK,GAAIE;MACxC;AAEA,aAAOA;IACT;AAGA,aAASoD,EAAc/E,GAAG;AAExB,eADIwH,IAAK,IACFxH,MAAMwH,MAAM;AACnB,aAAOA;IACT;AAUA,aAAS5C,GAAO3E,GAAML,GAAGoB,GAAGL,GAAI;AAC9B,UAAIsG,GACFhG,IAAI,IAAIhB,EAAK,CAAC,GAIdD,IAAI,KAAK,KAAKW,IAAKpB,IAAW,CAAC;AAIjC,WAFAb,IAAW,WAEF;AAOP,YANIsC,IAAI,MACNC,IAAIA,EAAE,MAAMrB,CAAC,GACT6H,GAASxG,EAAE,GAAGjB,CAAC,MAAGiH,IAAc,QAGtCjG,IAAIhC,EAAUgC,IAAI,CAAC,GACfA,MAAM,GAAG;AAGXA,cAAIC,EAAE,EAAE,SAAS,GACbgG,KAAehG,EAAE,EAAED,CAAC,MAAM,KAAG,EAAEC,EAAE,EAAED,CAAC;AACxC;QACF;AAEApB,YAAIA,EAAE,MAAMA,CAAC,GACb6H,GAAS7H,EAAE,GAAGI,CAAC;MACjB;AAEA,aAAAtB,IAAW,MAEJuC;IACT;AAGA,aAASyG,GAAM1G,GAAG;AAChB,aAAOA,EAAE,EAAEA,EAAE,EAAE,SAAS,CAAC,IAAI;IAC/B;AAMA,aAAS2G,GAAS1H,GAAM/D,GAAM8E,GAAG;AAK/B,eAJIhB,GAAGE,GACLN,IAAI,IAAIK,EAAK/D,EAAK,CAAC,CAAC,GACpBiE,IAAI,GAEC,EAAEA,IAAIjE,EAAK,UAAS;AAIzB,YAHAgE,IAAI,IAAID,EAAK/D,EAAKiE,CAAC,CAAC,GAGhB,CAACD,EAAE,GAAG;AACRN,cAAIM;AACJ;QACF;AAEAF,YAAIJ,EAAE,IAAIM,CAAC,IAEPF,MAAMgB,KAAKhB,MAAM,KAAKJ,EAAE,MAAMoB,OAChCpB,IAAIM;MAER;AAEA,aAAON;IACT;AAkCA,aAAS6D,GAAmB7D,GAAGuB,GAAI;AACjC,UAAI0B,GAAaG,GAAO5C,GAAGwH,GAAKC,GAAKzG,GAAGkB,GACtCpB,IAAM,GACNf,IAAI,GACJH,IAAI,GACJC,IAAOL,EAAE,aACTgB,IAAKX,EAAK,UACVU,IAAKV,EAAK;AAGZ,UAAI,CAACL,EAAE,KAAK,CAACA,EAAE,EAAE,CAAC,KAAKA,EAAE,IAAI,GAE3B,QAAO,IAAIK,EAAKL,EAAE,IACbA,EAAE,EAAE,CAAC,IAAQA,EAAE,IAAI,IAAI,IAAI,IAAA,IAAlB,IACVA,EAAE,IAAIA,EAAE,IAAI,IAAI,IAAIA,IAAI,GAAK;AAanC,WAVIuB,KAAM,QACRzC,IAAW,OACX4D,IAAM3B,KAEN2B,IAAMnB,GAGRC,IAAI,IAAInB,EAAK,OAAO,GAGbL,EAAE,IAAI,KAGXA,KAAIA,EAAE,MAAMwB,CAAC,GACbpB,KAAK;AAUP,WALAgD,IAAQ,KAAK,IAAI/D,EAAQ,GAAGe,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,GACtDsC,KAAOU,GACPH,IAAc+E,IAAMC,IAAM,IAAI5H,EAAK,CAAC,GACpCA,EAAK,YAAYqC,OAER;AAKP,YAJAsF,IAAM/H,EAAS+H,EAAI,MAAMhI,CAAC,GAAG0C,GAAK,CAAC,GACnCO,IAAcA,EAAY,MAAM,EAAE1C,CAAC,GACnCiB,IAAIyG,EAAI,KAAKrG,EAAOoG,GAAK/E,GAAaP,GAAK,CAAC,CAAC,GAEzCf,EAAeH,EAAE,CAAC,EAAE,MAAM,GAAGkB,CAAG,MAAMf,EAAesG,EAAI,CAAC,EAAE,MAAM,GAAGvF,CAAG,GAAG;AAE7E,eADAlC,IAAIJ,GACGI,MAAKyH,KAAMhI,EAASgI,EAAI,MAAMA,CAAG,GAAGvF,GAAK,CAAC;AAOjD,cAAInB,KAAM,KAER,KAAID,IAAM,KAAKiC,EAAoB0E,EAAI,GAAGvF,IAAMU,GAAOpC,GAAIM,CAAG,EAC5DjB,GAAK,YAAYqC,KAAO,IACxBO,IAAc+E,IAAMxG,IAAI,IAAInB,EAAK,CAAC,GAClCE,IAAI,GACJe;cAEA,QAAOrB,EAASgI,GAAK5H,EAAK,YAAYU,GAAIC,GAAIlC,IAAW,IAAI;cAG/D,QAAAuB,EAAK,YAAYU,GACVkH;QAEX;AAEAA,YAAMzG;MACR;IACF;AAkBA,aAAS6B,EAAiB/C,GAAGiB,GAAI;AAC/B,UAAI2G,GAAGC,GAAIlF,GAAaO,GAAG4E,GAAW9G,GAAK2G,GAAKzG,GAAGkB,GAAK2F,GAAIvF,GAC1D1B,IAAI,GACJgC,IAAQ,IACRpD,IAAIM,GACJK,IAAKX,EAAE,GACPK,IAAOL,EAAE,aACTgB,IAAKX,EAAK,UACVU,IAAKV,EAAK;AAGZ,UAAIL,EAAE,IAAI,KAAK,CAACW,KAAM,CAACA,EAAG,CAAC,KAAK,CAACX,EAAE,KAAKW,EAAG,CAAC,KAAK,KAAKA,EAAG,UAAU,EACjE,QAAO,IAAIN,EAAKM,KAAM,CAACA,EAAG,CAAC,IAAI,KAAA,IAASX,EAAE,KAAK,IAAI,MAAMW,IAAK,IAAIX,CAAC;AAcrE,UAXIuB,KAAM,QACRzC,IAAW,OACX4D,IAAM3B,KAEN2B,IAAMnB,GAGRlB,EAAK,YAAYqC,KAAOU,GACxB8E,IAAIvG,EAAehB,CAAE,GACrBwH,IAAKD,EAAE,OAAO,CAAC,GAEX,KAAK,IAAI1E,IAAIxD,EAAE,CAAC,IAAI,OAAQ;AAa9B,eAAOmI,IAAK,KAAKA,KAAM,KAAKA,KAAM,KAAKD,EAAE,OAAO,CAAC,IAAI,IACnDlI,KAAIA,EAAE,MAAMM,CAAC,GACb4H,IAAIvG,EAAe3B,EAAE,CAAC,GACtBmI,IAAKD,EAAE,OAAO,CAAC,GACf9G;AAGFoC,YAAIxD,EAAE,GAEFmI,IAAK,KACPnI,IAAI,IAAIK,EAAK,OAAO6H,CAAC,GACrB1E,OAEAxD,IAAI,IAAIK,EAAK8H,IAAK,MAAMD,EAAE,MAAM,CAAC,CAAC;MAEtC,MAKE,QAAA1G,IAAI8B,GAAQjD,GAAMqC,IAAM,GAAG3B,CAAE,EAAE,MAAMyC,IAAI,EAAE,GAC3CxD,IAAIqD,EAAiB,IAAIhD,EAAK8H,IAAK,MAAMD,EAAE,MAAM,CAAC,CAAC,GAAGxF,IAAMU,CAAK,EAAE,KAAK5B,CAAC,GACzEnB,EAAK,YAAYU,GAEVQ,KAAM,OAAOtB,EAASD,GAAGe,GAAIC,GAAIlC,IAAW,IAAI,IAAIkB;AAa7D,WATAqI,IAAKrI,GAKLiI,IAAMG,IAAYpI,IAAI4B,EAAO5B,EAAE,MAAM,CAAC,GAAGA,EAAE,KAAK,CAAC,GAAG0C,GAAK,CAAC,GAC1DI,IAAK7C,EAASD,EAAE,MAAMA,CAAC,GAAG0C,GAAK,CAAC,GAChCO,IAAc,OAEL;AAIP,YAHAmF,IAAYnI,EAASmI,EAAU,MAAMtF,CAAE,GAAGJ,GAAK,CAAC,GAChDlB,IAAIyG,EAAI,KAAKrG,EAAOwG,GAAW,IAAI/H,EAAK4C,CAAW,GAAGP,GAAK,CAAC,CAAC,GAEzDf,EAAeH,EAAE,CAAC,EAAE,MAAM,GAAGkB,CAAG,MAAMf,EAAesG,EAAI,CAAC,EAAE,MAAM,GAAGvF,CAAG,EAc1E,KAbAuF,IAAMA,EAAI,MAAM,CAAC,GAIbzE,MAAM,MAAGyE,IAAMA,EAAI,KAAK3E,GAAQjD,GAAMqC,IAAM,GAAG3B,CAAE,EAAE,MAAMyC,IAAI,EAAE,CAAC,IACpEyE,IAAMrG,EAAOqG,GAAK,IAAI5H,EAAKe,CAAC,GAAGsB,GAAK,CAAC,GAQjCnB,KAAM,KACR,KAAIgC,EAAoB0E,EAAI,GAAGvF,IAAMU,GAAOpC,GAAIM,CAAG,EACjDjB,GAAK,YAAYqC,KAAOU,GACxB5B,IAAI4G,IAAYpI,IAAI4B,EAAOyG,EAAG,MAAM,CAAC,GAAGA,EAAG,KAAK,CAAC,GAAG3F,GAAK,CAAC,GAC1DI,IAAK7C,EAASD,EAAE,MAAMA,CAAC,GAAG0C,GAAK,CAAC,GAChCO,IAAc3B,IAAM;YAEpB,QAAOrB,EAASgI,GAAK5H,EAAK,YAAYU,GAAIC,GAAIlC,IAAW,IAAI;YAG/D,QAAAuB,EAAK,YAAYU,GACVkH;AAIXA,YAAMzG,GACNyB,KAAe;MACjB;IACF;AAIA,aAAS0E,GAAkB3H,GAAG;AAE5B,aAAO,OAAOA,EAAE,IAAIA,EAAE,IAAI,CAAC;IAC7B;AAMA,aAASsI,GAAatI,GAAGuE,GAAK;AAC5B,UAAIf,GAAGjD,GAAGwB;AAoBV,YAhBKyB,IAAIe,EAAI,QAAQ,GAAG,KAAK,OAAIA,IAAMA,EAAI,QAAQ,KAAK,EAAE,KAGrDhE,IAAIgE,EAAI,OAAO,IAAI,KAAK,KAGvBf,IAAI,MAAGA,IAAIjD,IACfiD,KAAK,CAACe,EAAI,MAAMhE,IAAI,CAAC,GACrBgE,IAAMA,EAAI,UAAU,GAAGhE,CAAC,KACfiD,IAAI,MAGbA,IAAIe,EAAI,SAILhE,IAAI,GAAGgE,EAAI,WAAWhE,CAAC,MAAM,IAAIA,IAAI;AAG1C,WAAKwB,IAAMwC,EAAI,QAAQA,EAAI,WAAWxC,IAAM,CAAC,MAAM,IAAI,EAAEA,EAAI;AAG7D,UAFAwC,IAAMA,EAAI,MAAMhE,GAAGwB,CAAG,GAElBwC,GAAK;AAYP,YAXAxC,KAAOxB,GACPP,EAAE,IAAIwD,IAAIA,IAAIjD,IAAI,GAClBP,EAAE,IAAI,CAAC,GAMPO,KAAKiD,IAAI,KAAK7D,GACV6D,IAAI,MAAGjD,KAAKZ,IAEZY,IAAIwB,GAAK;AAEX,eADIxB,KAAGP,EAAE,EAAE,KAAK,CAACuE,EAAI,MAAM,GAAGhE,CAAC,CAAC,GAC3BwB,KAAOpC,GAAUY,IAAIwB,IAAM/B,GAAE,EAAE,KAAK,CAACuE,EAAI,MAAMhE,GAAGA,KAAKZ,CAAQ,CAAC;AACrE4E,cAAMA,EAAI,MAAMhE,CAAC,GACjBA,IAAIZ,IAAW4E,EAAI;QACrB,MACEhE,MAAKwB;AAGP,eAAOxB,MAAMgE,MAAO;AACpBvE,UAAE,EAAE,KAAK,CAACuE,CAAG,GAETzF,MAGEkB,EAAE,IAAIA,EAAE,YAAY,QAGtBA,EAAE,IAAI,MACNA,EAAE,IAAI,OAGGA,EAAE,IAAIA,EAAE,YAAY,SAG7BA,EAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC;MAId,MAGEA,GAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC;AAGV,aAAOA;IACT;AAMA,aAASuI,GAAWvI,GAAGuE,GAAK;AAC1B,UAAIxB,GAAM1C,GAAMmI,GAASjI,GAAGkI,GAAS1G,GAAK2G,GAAG/H,GAAI8C;AAEjD,UAAIc,EAAI,QAAQ,GAAG,IAAI,IAAA;AAErB,YADAA,IAAMA,EAAI,QAAQ,gBAAgB,IAAI,GAClC9E,GAAU,KAAK8E,CAAG,EAAG,QAAO+D,GAAatI,GAAGuE,CAAG;MAAA,WAC1CA,MAAQ,cAAcA,MAAQ,MACvC,QAAK,CAACA,MAAKvE,EAAE,IAAI,MACjBA,EAAE,IAAI,KACNA,EAAE,IAAI,MACCA;AAGT,UAAIT,GAAM,KAAKgF,CAAG,EAChBxB,KAAO,IACPwB,IAAMA,EAAI,YAAY;eACbjF,GAAS,KAAKiF,CAAG,EAC1BxB,KAAO;eACEvD,GAAQ,KAAK+E,CAAG,EACzBxB,KAAO;UAEP,OAAM,MAAM/D,IAAkBuF,CAAG;AAgCnC,WA5BAhE,IAAIgE,EAAI,OAAO,IAAI,GAEfhE,IAAI,KACNmI,IAAI,CAACnE,EAAI,MAAMhE,IAAI,CAAC,GACpBgE,IAAMA,EAAI,UAAU,GAAGhE,CAAC,KAExBgE,IAAMA,EAAI,MAAM,CAAC,GAKnBhE,IAAIgE,EAAI,QAAQ,GAAG,GACnBkE,IAAUlI,KAAK,GACfF,IAAOL,EAAE,aAELyI,MACFlE,IAAMA,EAAI,QAAQ,KAAK,EAAE,GACzBxC,IAAMwC,EAAI,QACVhE,IAAIwB,IAAMxB,GAGViI,IAAUxD,GAAO3E,GAAM,IAAIA,EAAK0C,CAAI,GAAGxC,GAAGA,IAAI,CAAC,IAGjDI,IAAK4E,GAAYhB,GAAKxB,GAAMrD,CAAI,GAChC+D,IAAK9C,EAAG,SAAS,GAGZJ,IAAIkD,GAAI9C,EAAGJ,CAAC,MAAM,GAAG,EAAEA,EAAGI,GAAG,IAAI;AACtC,aAAIJ,IAAI,IAAU,IAAIF,EAAKL,EAAE,IAAI,CAAC,KAClCA,EAAE,IAAI2D,GAAkBhD,GAAI8C,CAAE,GAC9BzD,EAAE,IAAIW,GACN7B,IAAW,OAQP2J,MAASzI,IAAI4B,EAAO5B,GAAGwI,GAASzG,IAAM,CAAC,IAGvC2G,MAAG1I,IAAIA,EAAE,MAAM,KAAK,IAAI0I,CAAC,IAAI,KAAKrJ,EAAQ,GAAGqJ,CAAC,IAAIC,EAAQ,IAAI,GAAGD,CAAC,CAAC,IACvE5J,IAAW,MAEJkB;IACT;AAQA,aAASiE,GAAK5D,GAAML,GAAG;AACrB,UAAII,GACF2B,IAAM/B,EAAE,EAAE;AAEZ,UAAI+B,IAAM,EACR,QAAO/B,EAAE,OAAO,IAAIA,IAAIkC,EAAa7B,GAAM,GAAGL,GAAGA,CAAC;AAQpDI,UAAI,MAAM,KAAK,KAAK2B,CAAG,GACvB3B,IAAIA,IAAI,KAAK,KAAKA,IAAI,GAEtBJ,IAAIA,EAAE,MAAM,IAAIiC,GAAQ,GAAG7B,CAAC,CAAC,GAC7BJ,IAAIkC,EAAa7B,GAAM,GAAGL,GAAGA,CAAC;AAO9B,eAJI4I,GACFtG,IAAK,IAAIjC,EAAK,CAAC,GACfkC,IAAM,IAAIlC,EAAK,EAAE,GACjBmC,IAAM,IAAInC,EAAK,EAAE,GACZD,MACLwI,KAAS5I,EAAE,MAAMA,CAAC,GAClBA,IAAIA,EAAE,MAAMsC,EAAG,KAAKsG,EAAO,MAAMrG,EAAI,MAAMqG,CAAM,EAAE,MAAMpG,CAAG,CAAC,CAAC,CAAC;AAGjE,aAAOxC;IACT;AAIA,aAASkC,EAAa7B,GAAM,GAAGL,GAAGM,GAAGuI,GAAc;AACjD,UAAIrI,GAAGgB,GAAG,GAAGsB,GACXvC,IAAI,GACJQ,IAAKV,EAAK,WACVD,IAAI,KAAK,KAAKW,IAAKpB,CAAQ;AAM7B,WAJAb,IAAW,OACXgE,IAAK9C,EAAE,MAAMA,CAAC,GACd,IAAI,IAAIK,EAAKC,CAAC,OAEL;AAMP,YALAkB,IAAII,EAAO,EAAE,MAAMkB,CAAE,GAAG,IAAIzC,EAAK,MAAM,GAAG,GAAGU,GAAI,CAAC,GAClD,IAAI8H,IAAevI,EAAE,KAAKkB,CAAC,IAAIlB,EAAE,MAAMkB,CAAC,GACxClB,IAAIsB,EAAOJ,EAAE,MAAMsB,CAAE,GAAG,IAAIzC,EAAK,MAAM,GAAG,GAAGU,GAAI,CAAC,GAClDS,IAAI,EAAE,KAAKlB,CAAC,GAERkB,EAAE,EAAEpB,CAAC,MAAM,QAAQ;AACrB,eAAKI,IAAIJ,GAAGoB,EAAE,EAAEhB,CAAC,MAAM,EAAE,EAAEA,CAAC,KAAKA,MAAK;AACtC,cAAIA,KAAK,GAAI;QACf;AAEAA,YAAI,GACJ,IAAIF,GACJA,IAAIkB,GACJA,IAAIhB,GACJD;MACF;AAEA,aAAAzB,IAAW,MACX0C,EAAE,EAAE,SAASpB,IAAI,GAEVoB;IACT;AAIA,aAASS,GAAQiE,GAAG1C,GAAG;AAErB,eADIpC,IAAI8E,GACD,EAAE1C,IAAGpC,MAAK8E;AACjB,aAAO9E;IACT;AAIA,aAASF,GAAiBb,GAAML,GAAG;AACjC,UAAIwB,GACFsH,IAAQ9I,EAAE,IAAI,GACd+I,IAAKtG,EAAMpC,GAAMA,EAAK,WAAW,CAAC,GAClCuC,IAASmG,EAAG,MAAM,GAAG;AAIvB,UAFA/I,IAAIA,EAAE,IAAI,GAENA,EAAE,IAAI4C,CAAM,EACd,QAAA/D,IAAWiK,IAAQ,IAAI,GAChB9I;AAKT,UAFAwB,IAAIxB,EAAE,SAAS+I,CAAE,GAEbvH,EAAE,OAAO,EACX3C,KAAWiK,IAAQ,IAAI;WAClB;AAIL,YAHA9I,IAAIA,EAAE,MAAMwB,EAAE,MAAMuH,CAAE,CAAC,GAGnB/I,EAAE,IAAI4C,CAAM,EACd,QAAA/D,IAAWiJ,GAAMtG,CAAC,IAAKsH,IAAQ,IAAI,IAAMA,IAAQ,IAAI,GAC9C9I;AAGTnB,YAAWiJ,GAAMtG,CAAC,IAAKsH,IAAQ,IAAI,IAAMA,IAAQ,IAAI;MACvD;AAEA,aAAO9I,EAAE,MAAM+I,CAAE,EAAE,IAAI;IACzB;AAQA,aAAS3E,GAAepE,GAAGyF,GAASlE,GAAIP,GAAI;AAC1C,UAAI+B,GAAMS,GAAGjD,GAAGH,GAAG2B,GAAKwF,GAAShD,GAAK5D,GAAIL,GACxCD,IAAOL,EAAE,aACT0H,IAAQnG,MAAO;AAWjB,UATImG,KACFpD,EAAW/C,GAAI,GAAGhD,CAAU,GACxByC,MAAO,SAAQA,IAAKX,EAAK,WACxBiE,EAAWtD,GAAI,GAAG,CAAC,MAExBO,IAAKlB,EAAK,WACVW,IAAKX,EAAK,WAGR,CAACL,EAAE,SAAS,EACduE,KAAMoD,GAAkB3H,CAAC;WACpB;AAoCL,aAnCAuE,IAAMC,EAAexE,CAAC,GACtBO,IAAIgE,EAAI,QAAQ,GAAG,GAOfmD,KACF3E,IAAO,GACH0C,KAAW,KACblE,IAAKA,IAAK,IAAI,IACLkE,KAAW,MACpBlE,IAAKA,IAAK,IAAI,MAGhBwB,IAAO0C,GAOLlF,KAAK,MACPgE,IAAMA,EAAI,QAAQ,KAAK,EAAE,GACzBjE,IAAI,IAAID,EAAK,CAAC,GACdC,EAAE,IAAIiE,EAAI,SAAShE,GACnBD,EAAE,IAAIiF,GAAYf,EAAelE,CAAC,GAAG,IAAIyC,CAAI,GAC7CzC,EAAE,IAAIA,EAAE,EAAE,SAGZK,IAAK4E,GAAYhB,GAAK,IAAIxB,CAAI,GAC9BS,IAAIzB,IAAMpB,EAAG,QAGNA,EAAG,EAAEoB,CAAG,KAAK,IAAIpB,GAAG,IAAI;AAE/B,YAAI,CAACA,EAAG,CAAC,EACP4D,KAAMmD,IAAQ,SAAS;aAClB;AAyBL,cAxBInH,IAAI,IACNiD,OAEAxD,IAAI,IAAIK,EAAKL,CAAC,GACdA,EAAE,IAAIW,GACNX,EAAE,IAAIwD,GACNxD,IAAI4B,EAAO5B,GAAGM,GAAGiB,GAAIP,GAAI,GAAG+B,CAAI,GAChCpC,IAAKX,EAAE,GACPwD,IAAIxD,EAAE,GACNuH,IAAU3I,KAIZ2B,IAAII,EAAGY,CAAE,GACTnB,IAAI2C,IAAO,GACXwE,IAAUA,KAAW5G,EAAGY,IAAK,CAAC,MAAM,QAEpCgG,IAAUvG,IAAK,KACVT,MAAM,UAAUgH,OAAavG,MAAO,KAAKA,OAAQhB,EAAE,IAAI,IAAI,IAAI,MAChEO,IAAIH,KAAKG,MAAMH,MAAMY,MAAO,KAAKuG,KAAWvG,MAAO,KAAKL,EAAGY,IAAK,CAAC,IAAI,KACrEP,OAAQhB,EAAE,IAAI,IAAI,IAAI,KAE1BW,EAAG,SAASY,GAERgG,EAGF,QAAO,EAAE5G,EAAG,EAAEY,CAAE,IAAIwB,IAAO,IACzBpC,GAAGY,CAAE,IAAI,GACJA,MACH,EAAEiC,GACF7C,EAAG,QAAQ,CAAC;AAMlB,eAAKoB,IAAMpB,EAAG,QAAQ,CAACA,EAAGoB,IAAM,CAAC,GAAG,EAAEA,EAAI;AAG1C,eAAKxB,IAAI,GAAGgE,IAAM,IAAIhE,IAAIwB,GAAKxB,IAAKgE,MAAO/F,GAAS,OAAOmC,EAAGJ,CAAC,CAAC;AAGhE,cAAImH,GAAO;AACT,gBAAI3F,IAAM,EACR,KAAI0D,KAAW,MAAMA,KAAW,GAAG;AAEjC,mBADAlF,IAAIkF,KAAW,KAAK,IAAI,GACnB,EAAE1D,GAAKA,IAAMxB,GAAGwB,IAAOwC,MAAO;AAEnC,mBADA5D,IAAK4E,GAAYhB,GAAKxB,GAAM0C,CAAO,GAC9B1D,IAAMpB,EAAG,QAAQ,CAACA,EAAGoB,IAAM,CAAC,GAAG,EAAEA,EAAI;AAG1C,mBAAKxB,IAAI,GAAGgE,IAAM,MAAMhE,IAAIwB,GAAKxB,IAAKgE,MAAO/F,GAAS,OAAOmC,EAAGJ,CAAC,CAAC;YACpE,MACEgE,KAAMA,EAAI,OAAO,CAAC,IAAI,MAAMA,EAAI,MAAM,CAAC;AAI3CA,gBAAOA,KAAOf,IAAI,IAAI,MAAM,QAAQA;UACtC,WAAWA,IAAI,GAAG;AAChB,mBAAO,EAAEA,IAAIe,KAAM,MAAMA;AACzBA,gBAAM,OAAOA;UACf,WACM,EAAEf,IAAIzB,EAAK,MAAKyB,KAAKzB,GAAKyB,MAAOe,MAAO;cACnCf,KAAIzB,MAAKwC,IAAMA,EAAI,MAAM,GAAGf,CAAC,IAAI,MAAMe,EAAI,MAAMf,CAAC;QAE/D;AAEAe,aAAOkB,KAAW,KAAK,OAAOA,KAAW,IAAI,OAAOA,KAAW,IAAI,OAAO,MAAMlB;MAClF;AAEA,aAAOvE,EAAE,IAAI,IAAI,MAAMuE,IAAMA;IAC/B;AAIA,aAASsD,GAASnC,GAAK3D,GAAK;AAC1B,UAAI2D,EAAI,SAAS3D,EACf,QAAA2D,EAAI,SAAS3D,GACN;IAEX;AAyDA,aAASiH,GAAIhJ,GAAG;AACd,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI;IACzB;AASA,aAASiJ,GAAKjJ,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAUA,aAASkJ,GAAMlJ,GAAG;AAChB,aAAO,IAAI,KAAKA,CAAC,EAAE,MAAM;IAC3B;AAWA,aAASmJ,GAAInJ,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,KAAKM,CAAC;IAC3B;AAUA,aAAS8I,GAAKpJ,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAUA,aAASqJ,GAAMrJ,GAAG;AAChB,aAAO,IAAI,KAAKA,CAAC,EAAE,MAAM;IAC3B;AAUA,aAASsJ,GAAKtJ,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAUA,aAASuJ,GAAMvJ,GAAG;AAChB,aAAO,IAAI,KAAKA,CAAC,EAAE,MAAM;IAC3B;AA4BA,aAASwJ,GAAMlJ,GAAGN,GAAG;AACnBM,UAAI,IAAI,KAAKA,CAAC,GACdN,IAAI,IAAI,KAAKA,CAAC;AACd,UAAIqB,GACFN,IAAK,KAAK,WACVC,IAAK,KAAK,UACV0B,IAAM3B,IAAK;AAGb,aAAI,CAACT,EAAE,KAAK,CAACN,EAAE,IACbqB,IAAI,IAAI,KAAK,GAAG,IAGP,CAACf,EAAE,KAAK,CAACN,EAAE,KACpBqB,IAAIoB,EAAM,MAAMC,GAAK,CAAC,EAAE,MAAM1C,EAAE,IAAI,IAAI,OAAO,IAAI,GACnDqB,EAAE,IAAIf,EAAE,KAGC,CAACN,EAAE,KAAKM,EAAE,OAAO,KAC1Be,IAAIrB,EAAE,IAAI,IAAIyC,EAAM,MAAM1B,GAAIC,CAAE,IAAI,IAAI,KAAK,CAAC,GAC9CK,EAAE,IAAIf,EAAE,KAGC,CAACA,EAAE,KAAKN,EAAE,OAAO,KAC1BqB,IAAIoB,EAAM,MAAMC,GAAK,CAAC,EAAE,MAAM,GAAG,GACjCrB,EAAE,IAAIf,EAAE,KAGCN,EAAE,IAAI,KACf,KAAK,YAAY0C,GACjB,KAAK,WAAW,GAChBrB,IAAI,KAAK,KAAKO,EAAOtB,GAAGN,GAAG0C,GAAK,CAAC,CAAC,GAClC1C,IAAIyC,EAAM,MAAMC,GAAK,CAAC,GACtB,KAAK,YAAY3B,GACjB,KAAK,WAAWC,GAChBK,IAAIf,EAAE,IAAI,IAAIe,EAAE,MAAMrB,CAAC,IAAIqB,EAAE,KAAKrB,CAAC,KAEnCqB,IAAI,KAAK,KAAKO,EAAOtB,GAAGN,GAAG0C,GAAK,CAAC,CAAC,GAG7BrB;IACT;AAUA,aAASoI,GAAKzJ,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AASA,aAAS0J,GAAK1J,GAAG;AACf,aAAOC,EAASD,IAAI,IAAI,KAAKA,CAAC,GAAGA,EAAE,IAAI,GAAG,CAAC;IAC7C;AAWA,aAAS2J,GAAM3J,GAAGE,GAAKC,GAAK;AAC1B,aAAO,IAAI,KAAKH,CAAC,EAAE,MAAME,GAAKC,CAAG;IACnC;AAqBA,aAASyJ,GAAOC,GAAK;AACnB,UAAI,CAACA,KAAO,OAAOA,KAAQ,SAAU,OAAM,MAAM9K,KAAe,iBAAiB;AACjF,UAAIwB,GAAGmI,GAAGoB,GACRC,IAAcF,EAAI,aAAa,MAC/BG,IAAK,CACH,aAAa,GAAGzL,GAChB,YAAY,GAAG,GACf,YAAY,CAACD,GAAW,GACxB,YAAY,GAAGA,GACf,QAAQ,GAAGA,GACX,QAAQ,CAACA,GAAW,GACpB,UAAU,GAAG,CACf;AAEF,WAAKiC,IAAI,GAAGA,IAAIyJ,EAAG,QAAQzJ,KAAK,EAE9B,KADImI,IAAIsB,EAAGzJ,CAAC,GAAGwJ,MAAa,KAAKrB,CAAC,IAAI/J,GAAS+J,CAAC,KAC3CoB,IAAID,EAAInB,CAAC,OAAO,OACnB,KAAItJ,EAAU0K,CAAC,MAAMA,KAAKA,KAAKE,EAAGzJ,IAAI,CAAC,KAAKuJ,KAAKE,EAAGzJ,IAAI,CAAC,EAAG,MAAKmI,CAAC,IAAIoB;UACjE,OAAM,MAAM9K,IAAkB0J,IAAI,OAAOoB,CAAC;AAKnD,UADIpB,IAAI,UAAUqB,MAAa,KAAKrB,CAAC,IAAI/J,GAAS+J,CAAC,KAC9CoB,IAAID,EAAInB,CAAC,OAAO,OACnB,KAAIoB,MAAM,QAAQA,MAAM,SAASA,MAAM,KAAKA,MAAM,EAChD,KAAIA,EACF,KAAI,OAAO,SAAU,OAAe,WACjC,OAAO,mBAAmB,OAAO,aAClC,MAAKpB,CAAC,IAAI;UAEV,OAAM,MAAMxJ,EAAiB;UAG/B,MAAKwJ,CAAC,IAAI;UAGZ,OAAM,MAAM1J,IAAkB0J,IAAI,OAAOoB,CAAC;AAI9C,aAAO;IACT;AAUA,aAASG,GAAIjK,GAAG;AACd,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI;IACzB;AAUA,aAASkK,GAAKlK,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAQA,aAASmK,GAAMN,GAAK;AAClB,UAAItJ,GAAGmI,GAAGsB;AASV,eAASrB,EAAQmB,GAAG;AAClB,YAAItG,GAAGjD,GAAGiB,GACRxB,IAAI;AAGN,YAAI,EAAEA,aAAa2I,GAAU,QAAO,IAAIA,EAAQmB,CAAC;AAMjD,YAFA9J,EAAE,cAAc2I,GAEZyB,GAAkBN,CAAC,GAAG;AACxB9J,YAAE,IAAI8J,EAAE,GAEJhL,IACE,CAACgL,EAAE,KAAKA,EAAE,IAAInB,EAAQ,QAGxB3I,EAAE,IAAI,KACNA,EAAE,IAAI,QACG8J,EAAE,IAAInB,EAAQ,QAGvB3I,EAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC,MAERA,EAAE,IAAI8J,EAAE,GACR9J,EAAE,IAAI8J,EAAE,EAAE,MAAM,MAGlB9J,EAAE,IAAI8J,EAAE,GACR9J,EAAE,IAAI8J,EAAE,IAAIA,EAAE,EAAE,MAAM,IAAIA,EAAE;AAG9B;QACF;AAIA,YAFAtI,IAAI,OAAOsI,GAEPtI,MAAM,UAAU;AAClB,cAAIsI,MAAM,GAAG;AACX9J,cAAE,IAAI,IAAI8J,IAAI,IAAI,KAAK,GACvB9J,EAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC;AACR;UACF;AAUA,cARI8J,IAAI,KACNA,IAAI,CAACA,GACL9J,EAAE,IAAI,MAENA,EAAE,IAAI,GAIJ8J,MAAM,CAAC,CAACA,KAAKA,IAAI,KAAK;AACxB,iBAAKtG,IAAI,GAAGjD,IAAIuJ,GAAGvJ,KAAK,IAAIA,KAAK,GAAIiD;AAEjC1E,gBACE0E,IAAImF,EAAQ,QACd3I,EAAE,IAAI,KACNA,EAAE,IAAI,QACGwD,IAAImF,EAAQ,QACrB3I,EAAE,IAAI,GACNA,EAAE,IAAI,CAAC,CAAC,MAERA,EAAE,IAAIwD,GACNxD,EAAE,IAAI,CAAC8J,CAAC,MAGV9J,EAAE,IAAIwD,GACNxD,EAAE,IAAI,CAAC8J,CAAC;AAGV;UACF;AAGA,cAAIA,IAAI,MAAM,GAAG;AACVA,kBAAG9J,EAAE,IAAI,MACdA,EAAE,IAAI,KACNA,EAAE,IAAI;AACN;UACF;AAEA,iBAAOsI,GAAatI,GAAG8J,EAAE,SAAS,CAAC;QACrC;AAEA,YAAItI,MAAM,SACR,SAAKjB,IAAIuJ,EAAE,WAAW,CAAC,OAAO,MAC5BA,IAAIA,EAAE,MAAM,CAAC,GACb9J,EAAE,IAAI,OAEFO,MAAM,OAAIuJ,IAAIA,EAAE,MAAM,CAAC,IAC3B9J,EAAE,IAAI,IAGDP,GAAU,KAAKqK,CAAC,IAAIxB,GAAatI,GAAG8J,CAAC,IAAIvB,GAAWvI,GAAG8J,CAAC;AAGjE,YAAItI,MAAM,SACR,QAAIsI,IAAI,KACNA,IAAI,CAACA,GACL9J,EAAE,IAAI,MAENA,EAAE,IAAI,GAGDsI,GAAatI,GAAG8J,EAAE,SAAS,CAAC;AAGrC,cAAM,MAAM9K,IAAkB8K,CAAC;MACjC;AA2DA,UAzDAnB,EAAQ,YAAY5I,GAEpB4I,EAAQ,WAAW,GACnBA,EAAQ,aAAa,GACrBA,EAAQ,aAAa,GACrBA,EAAQ,cAAc,GACtBA,EAAQ,gBAAgB,GACxBA,EAAQ,kBAAkB,GAC1BA,EAAQ,kBAAkB,GAC1BA,EAAQ,kBAAkB,GAC1BA,EAAQ,mBAAmB,GAC3BA,EAAQ,SAAS,GAEjBA,EAAQ,SAASA,EAAQ,MAAMiB,IAC/BjB,EAAQ,QAAQwB,IAChBxB,EAAQ,YAAYyB,IAEpBzB,EAAQ,MAAMK,IACdL,EAAQ,OAAOM,IACfN,EAAQ,QAAQO,IAChBP,EAAQ,MAAMQ,IACdR,EAAQ,OAAOS,IACfT,EAAQ,QAAQU,IAChBV,EAAQ,OAAOW,IACfX,EAAQ,QAAQY,IAChBZ,EAAQ,QAAQa,IAChBb,EAAQ,OAAOc,IACfd,EAAQ,OAAOe,IACff,EAAQ,QAAQgB,IAChBhB,EAAQ,MAAMsB,IACdtB,EAAQ,OAAOuB,IACfvB,EAAQ,MAAM0B,IACd1B,EAAQ,MAAM2B,IACd3B,EAAQ,QAAQ4B,IAChB5B,EAAQ,QAAQ6B,IAChB7B,EAAQ,KAAK8B,IACb9B,EAAQ,MAAM+B,IACd/B,EAAQ,QAAQgC,IAChBhC,EAAQ,OAAOiC,IACfjC,EAAQ,MAAMxI,IACdwI,EAAQ,MAAMzI,IACdyI,EAAQ,MAAMkC,IACdlC,EAAQ,MAAMmC,IACdnC,EAAQ,MAAMX,IACdW,EAAQ,SAASoC,IACjBpC,EAAQ,QAAQqC,IAChBrC,EAAQ,OAAOvB,IACfuB,EAAQ,MAAMsC,IACdtC,EAAQ,OAAOuC,IACfvC,EAAQ,OAAOwC,IACfxC,EAAQ,MAAMyC,IACdzC,EAAQ,MAAMV,IACdU,EAAQ,MAAM0C,IACd1C,EAAQ,OAAO2C,IACf3C,EAAQ,QAAQ4C,IAEZ1B,MAAQ,WAAQA,IAAM,CAAC,IACvBA,KACEA,EAAI,aAAa,KAEnB,MADAG,IAAK,CAAC,aAAa,YAAY,YAAY,YAAY,QAAQ,QAAQ,UAAU,QAAQ,GACpFzJ,IAAI,GAAGA,IAAIyJ,EAAG,SAAcH,GAAI,eAAenB,IAAIsB,EAAGzJ,GAAG,CAAC,MAAGsJ,EAAInB,CAAC,IAAI,KAAKA,CAAC;AAIrF,aAAAC,EAAQ,OAAOkB,CAAG,GAEXlB;IACT;AAWA,aAAS0B,GAAIrK,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAUA,aAASgK,GAAItK,GAAG;AACd,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI;IACzB;AASA,aAASuK,GAAMvK,GAAG;AAChB,aAAOC,EAASD,IAAI,IAAI,KAAKA,CAAC,GAAGA,EAAE,IAAI,GAAG,CAAC;IAC7C;AAYA,aAASwK,KAAQ;AACf,UAAIjK,GAAG,GACLiB,IAAI,IAAI,KAAK,CAAC;AAIhB,WAFA1C,IAAW,OAENyB,IAAI,GAAGA,IAAI,UAAU,SAExB,KADA,IAAI,IAAI,KAAK,UAAUA,GAAG,CAAC,GACtB,EAAE,EAMIiB,GAAE,MACXA,IAAIA,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;WAPb;AACR,YAAI,EAAE,EACJ,QAAA1C,IAAW,MACJ,IAAI,KAAK,IAAA,CAAK;AAEvB0C,YAAI;MACN;AAKF,aAAA1C,IAAW,MAEJ0C,EAAE,KAAK;IAChB;AAQA,aAAS4I,GAAkBP,GAAK;AAC9B,aAAOA,aAAelB,KAAWkB,KAAOA,EAAI,gBAAgB1K,MAAO;IACrE;AAUA,aAASsL,GAAGzK,GAAG;AACb,aAAO,IAAI,KAAKA,CAAC,EAAE,GAAG;IACxB;AAaA,aAAS0K,GAAI1K,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAUA,aAASsK,GAAK5K,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CAAC;IAC1B;AAUA,aAAS2K,GAAM3K,GAAG;AAChB,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,EAAE;IAC3B;AASA,aAASG,KAAM;AACb,aAAO4H,GAAS,MAAM,WAAW,EAAE;IACrC;AASA,aAAS7H,KAAM;AACb,aAAO6H,GAAS,MAAM,WAAW,CAAC;IACpC;AAWA,aAAS8C,GAAI7K,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAWA,aAASwK,GAAI9K,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAWA,aAAS0H,GAAIhI,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAWA,aAASyK,GAAOxJ,GAAI;AAClB,UAAIO,GAAG0B,GAAGpD,GAAGgB,GACXb,IAAI,GACJc,IAAI,IAAI,KAAK,CAAC,GACdiE,IAAK,CAAC;AAOR,UALI/D,MAAO,SAAQA,IAAK,KAAK,YACxB+C,EAAW/C,GAAI,GAAGhD,CAAU,GAEjC6B,IAAI,KAAK,KAAKmB,IAAK5B,CAAQ,GAEtB,KAAK,OAIH,KAAI,OAAO,gBAGhB,MAFAmC,IAAI,OAAO,gBAAgB,IAAI,YAAY1B,CAAC,CAAC,GAEtCG,IAAIH,IACTgB,KAAIU,EAAEvB,CAAC,GAIHa,KAAK,QACPU,EAAEvB,CAAC,IAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,IAKnD+E,EAAG/E,GAAG,IAAIa,IAAI;eAKT,OAAO,aAAa;AAK7B,aAFAU,IAAI,OAAO,YAAY1B,KAAK,CAAC,GAEtBG,IAAIH,IAGTgB,KAAIU,EAAEvB,CAAC,KAAKuB,EAAEvB,IAAI,CAAC,KAAK,MAAMuB,EAAEvB,IAAI,CAAC,KAAK,QAAQuB,EAAEvB,IAAI,CAAC,IAAI,QAAS,KAGlEa,KAAK,QACP,OAAO,YAAY,CAAC,EAAE,KAAKU,GAAGvB,CAAC,KAK/B+E,EAAG,KAAKlE,IAAI,GAAG,GACfb,KAAK;AAITA,YAAIH,IAAI;MACV,MACE,OAAM,MAAMlB,EAAiB;UA9C7B,QAAOqB,IAAIH,IAAIkF,GAAG/E,GAAG,IAAI,KAAK,OAAO,IAAI,MAAM;AA2DjD,WAVAH,IAAIkF,EAAG,EAAE/E,CAAC,GACVgB,KAAM5B,GAGFS,KAAKmB,MACPH,IAAI/B,EAAQ,IAAIM,IAAW4B,CAAE,GAC7B+D,EAAG/E,CAAC,KAAKH,IAAIgB,IAAI,KAAKA,IAIjBkE,EAAG/E,CAAC,MAAM,GAAGA,IAAK+E,GAAG,IAAI;AAGhC,UAAI/E,IAAI,EACNiD,KAAI,GACJ8B,IAAK,CAAC,CAAC;WACF;AAIL,aAHA9B,IAAI,IAGG8B,EAAG,CAAC,MAAM,GAAG9B,KAAK7D,EAAU2F,GAAG,MAAM;AAG5C,aAAKlF,IAAI,GAAGgB,IAAIkE,EAAG,CAAC,GAAGlE,KAAK,IAAIA,KAAK,GAAIhB;AAGrCA,YAAIT,MAAU6D,KAAK7D,IAAWS;MACpC;AAEA,aAAAiB,EAAE,IAAImC,GACNnC,EAAE,IAAIiE,GAECjE;IACT;AAWA,aAAS2J,GAAMhL,GAAG;AAChB,aAAOC,EAASD,IAAI,IAAI,KAAKA,CAAC,GAAGA,EAAE,IAAI,GAAG,KAAK,QAAQ;IACzD;AAcA,aAASoH,GAAKpH,GAAG;AACf,aAAAA,IAAI,IAAI,KAAKA,CAAC,GACPA,EAAE,IAAKA,EAAE,EAAE,CAAC,IAAIA,EAAE,IAAI,IAAIA,EAAE,IAAKA,EAAE,KAAK;IACjD;AAUA,aAASiL,GAAIjL,GAAG;AACd,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI;IACzB;AAUA,aAASkL,GAAKlL,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAUA,aAASmL,GAAKnL,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AAWA,aAASoL,GAAIpL,GAAGM,GAAG;AACjB,aAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC;IAC1B;AAYA,aAAS2H,KAAM;AACb,UAAI1H,IAAI,GACNjE,IAAO,WACP0D,IAAI,IAAI,KAAK1D,EAAKiE,CAAC,CAAC;AAGtB,WADAzB,IAAW,OACJkB,EAAE,KAAK,EAAEO,IAAIjE,EAAK,SAAS0D,KAAIA,EAAE,KAAK1D,EAAKiE,CAAC,CAAC;AACpD,aAAAzB,IAAW,MAEJmB,EAASD,GAAG,KAAK,WAAW,KAAK,QAAQ;IAClD;AAUA,aAASqL,GAAIrL,GAAG;AACd,aAAO,IAAI,KAAKA,CAAC,EAAE,IAAI;IACzB;AAUA,aAASsL,GAAKtL,GAAG;AACf,aAAO,IAAI,KAAKA,CAAC,EAAE,KAAK;IAC1B;AASA,aAASuL,GAAMvL,GAAG;AAChB,aAAOC,EAASD,IAAI,IAAI,KAAKA,CAAC,GAAGA,EAAE,IAAI,GAAG,CAAC;IAC7C;AAGAD,MAAE,OAAO,IAAI,4BAA4B,CAAC,IAAIA,EAAE;AAChDA,MAAE,OAAO,WAAW,IAAI;AAGjB,QAAI4I,IAAU5I,EAAE,cAAcoK,GAAMxL,EAAQ;AAGnDF,SAAO,IAAIkK,EAAQlK,EAAI;AACvBC,SAAK,IAAIiK,EAAQjK,EAAE;AAEnB,QAAO5C,KAAQ6M;;;;;ACjzJf,IAAA6C,yBAAA;AAAA;AAIA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAM;AAAA,MACJ,SAAAC;AAAA,MACA,kBAAAC;AAAA,MACA,gBAAAC;AAAA,MACA,QAAAC;AAAA,MACA,YAAAC;AAAA,MACA;AAAA,IACF,IAAI;AAGJ,QAAM,SAAS,CAAC;AAEhB,YAAQ,SAAS;AACjB,YAAQ,SAAS,CAAC;AAMlB,WAAO,gBAAgB;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,WAAO,gCAAgC,MAAM;AAC3C,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,+HAA+H,WAAW;AAAA;AAAA,MAE5J;AAAA,IAAC;AACD,WAAO,kCAAkC,MAAM;AAC7C,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,iIAAiI,WAAW;AAAA;AAAA,MAE9J;AAAA,IAAC;AACD,WAAO,6BAA6B,MAAM;AACxC,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,4HAA4H,WAAW;AAAA;AAAA,MAEzJ;AAAA,IAAC;AACD,WAAO,kCAAkC,MAAM;AAC7C,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,iIAAiI,WAAW;AAAA;AAAA,MAE9J;AAAA,IAAC;AACD,WAAO,8BAA8B,MAAM;AACzC,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,6HAA6H,WAAW;AAAA;AAAA,MAE1J;AAAA,IAAC;AACD,WAAO,UAAUJ;AAKjB,WAAO,MAAM,MAAM;AACjB,YAAM,cAAcI,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,wGAAwG,WAAW;AAAA;AAAA,MAErI;AAAA,IAAC;AACD,WAAO,QAAQ,MAAM;AACnB,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,uGAAuG,WAAW;AAAA;AAAA,MAEpI;AAAA,IAAC;AACD,WAAO,OAAO,MAAM;AAClB,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,sGAAsG,WAAW;AAAA;AAAA,MAEnI;AAAA,IAAC;AACD,WAAO,MAAM,MAAM;AACjB,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,qGAAqG,WAAW;AAAA;AAAA,MAElI;AAAA,IAAC;AACD,WAAO,YAAYD,QAAO;AAK1B,WAAO,sBAAsB,MAAM;AACjC,YAAM,cAAcC,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,gIAAgI,WAAW;AAAA;AAAA,MAE7J;AAAA,IAAC;AACD,WAAO,kBAAkB,MAAM;AAC7B,YAAM,cAAcA,YAAW,EAAE;AACjC,YAAM,IAAI;AAAA,QAAM,4HAA4H,WAAW;AAAA;AAAA,MAEzJ;AAAA,IAAC;AAKD,WAAO,SAASH,kBAAiB,UAAU;AAC3C,WAAO,WAAWA,kBAAiB,UAAU;AAC7C,WAAO,UAAUA,kBAAiB,UAAU;AAE5C,WAAO,YAAY;AAAA,MACjB,QAAQA,kBAAiB,QAAQ;AAAA,MACjC,UAAUA,kBAAiB,QAAQ;AAAA,MACnC,SAASA,kBAAiB,QAAQ;AAAA,IACpC;AAQA,YAAQ,OAAO,4BAA4BC,gBAAe;AAAA,MACxD,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB,CAAC;AAED,YAAQ,OAAO,sBAAsB;AAAA,MACnC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,cAAc;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,mBAAmB;AAAA,IACrB;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,eAAe;AAAA,MACf,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,8BAA8B;AAAA,MAC3C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,6BAA6B;AAAA,MAC7B,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,iCAAiC;AAAA,MAC9C,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,8BAA8B;AAAA,MAC3C,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,cAAc;AAAA,MACd,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,8BAA8B;AAAA,MAC3C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,yBAAyB;AAAA,MACtC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,8BAA8B;AAAA,MAC9B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,oCAAoC;AAAA,MACpC,sBAAsB;AAAA,MACtB,wCAAwC;AAAA,MACxC,wCAAwC;AAAA,MACxC,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAEA,YAAQ,OAAO,qBAAqB;AAAA,MAClC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,uBAAuB;AAAA,MACpC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,qCAAqC;AAAA,MAClD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,MAAM;AAAA,MACN,aAAa;AAAA,MACb,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,8BAA8B;AAAA,MAC3C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AAEA,YAAQ,OAAO,uBAAuB;AAAA,MACpC,IAAI;AAAA,MACJ,wBAAwB;AAAA,MACxB,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,yBAAyB;AAAA,MACtC,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,iCAAiC;AAAA,MAC9C,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,eAAe;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,cAAc;AAAA,MACd,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,MACd,WAAW;AAAA,MACX,0BAA0B;AAAA,MAC1B,mCAAmC;AAAA,MACnC,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,mBAAmB;AAAA,IACrB;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,4CAA4C;AAAA,MACzD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IACtB;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,iCAAiC;AAAA,MAC9C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,qBAAqB;AAAA,MAClC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,OAAO;AAAA,MACP,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,yBAAyB;AAAA,MACtC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,yBAAyB;AAAA,MACtC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,qCAAqC;AAAA,MAClD,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,0CAA0C;AAAA,MACvD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,4BAA4B;AAAA,MAC5B,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,8BAA8B;AAAA,MAC3C,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,uCAAuC;AAAA,MACpD,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,8CAA8C;AAAA,MAC3D,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,OAAO;AAAA,MACP,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,eAAe;AAAA,MACf,UAAU;AAAA,MACV,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,2CAA2C;AAAA,MACxD,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,WAAW;AAAA,MACX,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,+CAA+C;AAAA,MAC5D,WAAW;AAAA,MACX,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,yCAAyC;AAAA,MACtD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,8CAA8C;AAAA,MAC3D,IAAI;AAAA,MACJ,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACvB;AAEA,YAAQ,OAAO,uCAAuC;AAAA,MACpD,IAAI;AAAA,MACJ,qBAAqB;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,sBAAsB;AAAA,MACnC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,sBAAsB;AAAA,MACnC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,uBAAuB;AAAA,MACpC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,2BAA2B;AAAA,MACxC,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,8BAA8B;AAAA,MAC9B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,MACjB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,oCAAoC;AAAA,MACpC,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,wBAAwB;AAAA,MACxB,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,cAAc;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,6BAA6B;AAAA,MAC7B,qBAAqB;AAAA,IACvB;AAEA,YAAQ,OAAO,2CAA2C;AAAA,MACxD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,yCAAyC;AAAA,MACtD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,eAAe;AAAA,MACf,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,2CAA2C;AAAA,MACxD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,+CAA+C;AAAA,MAC5D,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,0BAA0B;AAAA,MAC1B,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,oDAAoD;AAAA,MACjE,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,iCAAiC;AAAA,MACjC,qBAAqB;AAAA,IACvB;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAEA,YAAQ,OAAO,iCAAiC;AAAA,MAC9C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,yCAAyC;AAAA,MACtD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,mBAAmB;AAAA,IACrB;AAEA,YAAQ,OAAO,uCAAuC;AAAA,MACpD,UAAU;AAAA,MACV,mBAAmB;AAAA,IACrB;AAEA,YAAQ,OAAO,4BAA4B;AAAA,MACzC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA,MACb,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,eAAe;AAAA,IACjB;AAEA,YAAQ,OAAO,yCAAyC;AAAA,MACtD,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,yCAAyC;AAAA,MACtD,IAAI;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,qCAAqC;AAAA,MAClD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,gCAAgC;AAAA,MAC7C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,uCAAuC;AAAA,MACpD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,yBAAyB;AAAA,MACzB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,kDAAkD;AAAA,MAC/D,IAAI;AAAA,MACJ,wBAAwB;AAAA,MACxB,yBAAyB;AAAA,IAC3B;AAEA,YAAQ,OAAO,2CAA2C;AAAA,MACxD,IAAI;AAAA,MACJ,yBAAyB;AAAA,MACzB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,oBAAoB;AAAA,MACpB,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,qCAAqC;AAAA,MAClD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,uBAAuB;AAAA,MACvB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,eAAe;AAAA,MACf,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,uBAAuB;AAAA,MACvB,UAAU;AAAA,MACV,eAAe;AAAA,MACf,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,eAAe;AAAA,IACjB;AAEA,YAAQ,OAAO,4CAA4C;AAAA,MACzD,IAAI;AAAA,MACJ,+BAA+B;AAAA,MAC/B,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AAEA,YAAQ,OAAO,qCAAqC;AAAA,MAClD,IAAI;AAAA,MACJ,uBAAuB;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,2CAA2C;AAAA,MACxD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,IACpB;AAEA,YAAQ,OAAO,gDAAgD;AAAA,MAC7D,IAAI;AAAA,MACJ,6BAA6B;AAAA,MAC7B,qBAAqB;AAAA,MACrB,+BAA+B;AAAA,IACjC;AAEA,YAAQ,OAAO,+CAA+C;AAAA,MAC5D,IAAI;AAAA,MACJ,kCAAkC;AAAA,MAClC,WAAW;AAAA,MACX,UAAU;AAAA,MACV,0BAA0B;AAAA,IAC5B;AAEA,YAAQ,OAAO,uCAAuC;AAAA,MACpD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB;AAEA,YAAQ,OAAO,wBAAwB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,YAAQ,OAAO,0BAA0B;AAAA,MACvC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,IACvB;AAEA,YAAQ,OAAO,+BAA+B;AAAA,MAC5C,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,6CAA6C;AAAA,MAC1D,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,+BAA+B;AAAA,MAC/B,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAEA,YAAQ,OAAO,mCAAmC;AAAA,MAChD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,IACvB;AAEA,YAAQ,OAAO,sCAAsC;AAAA,MACnD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,wCAAwC;AAAA,MACrD,IAAI;AAAA,MACJ,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAEA,YAAQ,OAAO,kCAAkC;AAAA,MAC/C,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,oCAAoC;AAAA,MACjD,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,YAAY;AAAA,MACzB,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,qBAAqB;AAAA,MAClC,UAAU,OAAO;AAAA,IACnB;AAEA,YAAQ,OAAO,6BAA6B;AAAA,MAC1C,QAAQ,OAAO;AAAA,MACf,UAAU,OAAO;AAAA,IACnB;AAEA,YAAQ,OAAO,YAAY;AAAA,MACzB,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAEA,YAAQ,OAAO,aAAa;AAAA,MAC1B,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAEA,YAAQ,OAAO,sBAAsB;AAAA,MACnC,QAAQ,OAAO;AAAA,MACf,UAAU,OAAO;AAAA,MACjB,SAAS,OAAO;AAAA,IAClB;AACA,YAAQ,iBAAiB,QAAQ,OAAO,iBAAiB;AAAA,MACvD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAEA,YAAQ,OAAO,YAAY;AAAA,MACzB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,qBAAqB;AAAA,MACrB,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,sBAAsB;AAAA,MACtB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,4BAA4B;AAAA,MAC5B,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,uBAAuB;AAAA,MACvB,8BAA8B;AAAA,MAC9B,sBAAsB;AAAA,MACtB,6BAA6B;AAAA,MAC7B,oBAAoB;AAAA,MACpB,2BAA2B;AAAA,MAC3B,6BAA6B;AAAA,MAC7B,+BAA+B;AAAA,MAC/B,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,8BAA8B;AAAA,MAC9B,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,wBAAwB;AAAA,MACxB,2BAA2B;AAAA,MAC3B,yBAAyB;AAAA,MACzB,2BAA2B;AAAA,MAC3B,wBAAwB;AAAA,MACxB,+BAA+B;AAAA,MAC/B,oCAAoC;AAAA,MACpC,6BAA6B;AAAA,MAC7B,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,MAChB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,MACvB,6BAA6B;AAAA,MAC7B,kCAAkC;AAAA,MAClC,2BAA2B;AAAA,MAC3B,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,6BAA6B;AAAA,MAC7B,4BAA4B;AAAA,MAC5B,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,2BAA2B;AAAA,MAC3B,gCAAgC;AAAA,MAChC,+BAA+B;AAAA,MAC/B,uBAAuB;AAAA,MACvB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,eAAe;AAAA,MACf,6BAA6B;AAAA,MAC7B,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,MACxB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,IACtB;AAKA,QAAM,eAAN,MAAmB;AAAA,MACjB,cAAc;AACZ,eAAO,IAAI,MAAM,MAAM;AAAA,UACrB,IAAI,QAAQ,MAAM;AAChB,gBAAI;AACJ,kBAAM,UAAUE,YAAW;AAC3B,gBAAI,QAAQ,QAAQ;AAClB,wBAAU,4CAA4C,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAI1E,OAAO;AACL,wBAAU,iHAAiH,QAAQ,aAAa;AAAA,YAClJ;AAEA,uBAAW;AAAA;AAGX,kBAAM,IAAI,MAAM,OAAO;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,YAAQ,eAAe;AAEvB,WAAO,OAAO,SAAS,MAAM;AAAA;AAAA;;;ACjnE7B,IAAAC,yBAAA;AAAA;AAAA,QAAM,SAAS;AAEf,WAAO,UAAU;AAAA;AAAA;", "names": ["index_browser_exports", "__export", "decimal_default", "public_exports", "getRuntime", "makeStrictEnum", "objectEnumValues", "__toCommonJS", "validator", "_args", "args", "secret", "representations", "ObjectEnumValue", "arg", "NullTypesEnumValue", "__brand_DbNull", "DbNull", "__privateAdd", "setClassName", "__brand_<PERSON>son<PERSON><PERSON>", "JsonNull", "__brand_<PERSON><PERSON><PERSON>", "AnyNull", "classObject", "name", "allowList", "definition", "target", "property", "isNode", "_a", "_b", "isBun", "isDeno", "isNetlify", "isEdgeLight", "isWorkerd", "detectRuntime", "check", "runtimes<PERSON><PERSON><PERSON>Names", "runtimeId", "EXP_LIMIT", "MAX_DIGITS", "NUMERALS", "LN10", "PI", "DEFAULTS", "inexact", "quadrant", "external", "decimalError", "invalidArgument", "precisionLimitExceeded", "cryptoUnavailable", "tag", "mathfloor", "mathpow", "isBinary", "isHex", "isOctal", "isDecimal", "BASE", "LOG_BASE", "MAX_SAFE_INTEGER", "LN10_PRECISION", "PI_PRECISION", "P", "x", "finalise", "min", "max", "k", "Ctor", "y", "i", "j", "xdL", "ydL", "xd", "yd", "xs", "ys", "pr", "rm", "cosine", "toLessThanHalfPi", "m", "n", "r", "rep", "sd", "t", "t3", "t3plusx", "digitsToString", "divide", "w", "d", "len", "one", "tinyPow", "taylorSeries", "cosh2_x", "d8", "sinh2_x", "d5", "d16", "d20", "getPi", "wpr", "xsd", "halfPi", "px", "x2", "base", "isBase10", "denominator", "inf", "num", "guard", "naturalLogarithm", "getLn10", "checkRoundingDigits", "e", "xe", "xLTy", "getBase10Exponent", "q", "naturalExponential", "carry", "z", "getPrecision", "sine", "s", "rL", "toStringBinary", "dp", "checkInt32", "str", "finiteToString", "maxD", "d0", "d1", "d2", "n0", "n1", "yn", "intPow", "ws", "indexOfLastWord", "getZeroString", "repeating", "di", "rd", "convertBase", "baseIn", "baseOut", "arr", "arrL", "strL", "cos2x", "multiplyInteger", "temp", "compare", "a", "b", "aL", "bL", "subtract", "cmp", "logBase", "more", "prod", "prodL", "qd", "rem", "remL", "rem0", "xi", "xL", "yd0", "yL", "yz", "sign", "isTruncated", "digits", "roundUp", "xdi", "out", "isExp", "nonFiniteToString", "zs", "truncate", "isOdd", "maxOrMin", "pow", "sum", "c", "c0", "numerator", "x1", "parseDecimal", "parseOther", "divisor", "isFloat", "p", "Decimal", "sin2_x", "isHyperbolic", "isNeg", "pi", "abs", "acos", "acosh", "add", "asin", "asinh", "atan", "atanh", "atan2", "cbrt", "ceil", "clamp", "config", "obj", "v", "useDefaults", "ps", "cos", "cosh", "clone", "isDecimalInstance", "div", "exp", "floor", "hypot", "ln", "log", "log10", "log2", "mod", "mul", "random", "round", "sin", "sinh", "sqrt", "sub", "tan", "tanh", "trunc", "require_index_browser", "Decimal", "objectEnumValues", "makeStrictEnum", "Public", "getRuntime", "require_index_browser"]}