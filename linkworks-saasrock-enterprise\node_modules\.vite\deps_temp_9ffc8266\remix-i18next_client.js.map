{"version": 3, "sources": ["../../remix-i18next/src/client.ts"], "sourcesContent": ["import type { UNSAFE_RouteModules } from \"react-router\";\n\ndeclare global {\n\tinterface Window {\n\t\t__reactRouterRouteModules: UNSAFE_RouteModules;\n\t}\n}\n\n/**\n * Get the list of namespaces used by the application server-side so you could\n * set it on i18next init options.\n * @example\n * i18next.init({\n *   ns: getInitialNamespaces(), // this function\n *   // ...more options\n * })\n */\nexport function getInitialNamespaces(): string[] {\n\tlet namespaces = Object.values(window.__reactRouterRouteModules).flatMap(\n\t\t(route) => {\n\t\t\tif (typeof route?.handle !== \"object\") return [];\n\t\t\tif (!route.handle) return [];\n\t\t\tif (!(\"i18n\" in route.handle)) return [];\n\t\t\tif (typeof route.handle.i18n === \"string\") return [route.handle.i18n];\n\t\t\tif (\n\t\t\t\tArray.isArray(route.handle.i18n) &&\n\t\t\t\troute.handle.i18n.every((value: unknown) => typeof value === \"string\")\n\t\t\t) {\n\t\t\t\treturn route.handle.i18n as string[];\n\t\t\t}\n\t\t\treturn [];\n\t\t},\n\t);\n\n\treturn [...namespaces];\n}\n"], "mappings": ";;;AAiBM,SAAU,uBAAoB;AACnC,MAAI,aAAa,OAAO,OAAO,OAAO,yBAAyB,EAAE,QAChE,CAAC,UAAS;AACT,QAAI,OAAO,OAAO,WAAW;AAAU,aAAO,CAAA;AAC9C,QAAI,CAAC,MAAM;AAAQ,aAAO,CAAA;AAC1B,QAAI,EAAE,UAAU,MAAM;AAAS,aAAO,CAAA;AACtC,QAAI,OAAO,MAAM,OAAO,SAAS;AAAU,aAAO,CAAC,MAAM,OAAO,IAAI;AACpE,QACC,MAAM,QAAQ,MAAM,OAAO,IAAI,KAC/B,MAAM,OAAO,KAAK,MAAM,CAAC,UAAmB,OAAO,UAAU,QAAQ,GACpE;AACD,aAAO,MAAM,OAAO;IACrB;AACA,WAAO,CAAA;EACR,CAAC;AAGF,SAAO,CAAC,GAAG,UAAU;AACtB;", "names": []}