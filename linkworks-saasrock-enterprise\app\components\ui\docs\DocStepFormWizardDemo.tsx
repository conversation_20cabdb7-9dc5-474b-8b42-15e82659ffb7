import { useEffect, useState } from "react";
import { saasrockStepFormWizardStepBlocks } from "~/custom/modules/stepFormWizard/blocks/defaultStepFormWizard/saasrockStepFormWizard";
import StepFormWizardBlock from "~/custom/modules/stepFormWizard/blocks/StepFormWizardBlock";
import { StepFormWizardBlockDto } from "~/custom/modules/stepFormWizard/blocks/StepFormWizardBlockUtils";
import ButtonPrimary from "../buttons/ButtonPrimary";

export default function DocStepFormWizardDemo() {
  const [open, setOpen] = useState(false);
  const [stepFormWizardBlock, setStepFormWizardBlock] = useState<StepFormWizardBlockDto>();

  useEffect(() => {
    setStepFormWizardBlock({
      style: "modal",
      title: "Step form wizard Demo",
      canBeDismissed: true,
      steps: saasrockStepFormWizardStepBlocks,
      height: "lg",
      progressBar: "horizontal progress bar",
    });
  }, []);
  return (
    <div>
      <ButtonPrimary onClick={() => setOpen(true)}>Open step form wizard Demo</ButtonPrimary>
      {stepFormWizardBlock && <StepFormWizardBlock open={open} onClose={() => setOpen(false)} item={stepFormWizardBlock} />}
      {/* <StepFormWizardBlockForm item={stepFormWizardBlock} onUpdate={onUpdateStepFormWizardBlock} /> */}
    </div>
  );
}
