import { ActionFunction, LoaderFunctionArgs, MetaFunction } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import { StepFormWizardSessionsIndexApi } from "~/custom/modules/stepFormWizard/routes/api/sessions/StepFormWizardSessionsIndexApi.server";
import StepFormWizardSessionsIndexRoute from "~/custom/modules/stepFormWizard/routes/components/sessions/StepFormWizardSessionsIndexRoute";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
export const loader = (args: LoaderFunctionArgs) => StepFormWizardSessionsIndexApi.loader(args);
export const action: ActionFunction = (args) => StepFormWizardSessionsIndexApi.action(args);

export default () => (
  <div className="mx-auto w-full max-w-5xl space-y-3 px-4 py-2 pb-6 sm:px-6 sm:pt-3 lg:px-8 xl:max-w-full">
    <StepFormWizardSessionsIndexRoute />
  </div>
);

export function ErrorBoundary() {
  return <ServerError />;
}
