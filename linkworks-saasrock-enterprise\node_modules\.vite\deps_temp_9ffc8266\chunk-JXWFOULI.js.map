{"version": 3, "sources": ["../../remix-auth/build/authenticator.js", "../../remix-auth/build/authorizer.js", "../../remix-auth/build/error.js", "../../remix-auth/build/strategy.js", "../../remix-auth/build/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Authenticator = void 0;\nconst server_runtime_1 = require(\"@remix-run/server-runtime\");\nclass Authenticator {\n    /**\n     * Create a new instance of the Authenticator.\n     *\n     * It receives a instance of the SessionStorage. This session storage could\n     * be created using any method exported by Remix, this includes:\n     * - `createSessionStorage`\n     * - `createFileSystemSessionStorage`\n     * - `createCookieSessionStorage`\n     * - `createMemorySessionStorage`\n     *\n     * It optionally receives an object with extra options. The supported options\n     * are:\n     * - `sessionKey`: The key used to store and read the user in the session storage.\n     * @example\n     * import { sessionStorage } from \"./session.server\";\n     * let authenticator = new Authenticator(sessionStorage);\n     * @example\n     * import { sessionStorage } from \"./session.server\";\n     * let authenticator = new Authenticator(sessionStorage, {\n     *   sessionKey: \"token\",\n     * });\n     */\n    constructor(sessionStorage, options = {}) {\n        var _a;\n        this.sessionStorage = sessionStorage;\n        /**\n         * A map of the configured strategies, the key is the name of the strategy\n         * @private\n         */\n        this.strategies = new Map();\n        this.sessionKey = options.sessionKey || \"user\";\n        this.sessionErrorKey = options.sessionErrorKey || \"auth:error\";\n        this.sessionStrategyKey = options.sessionStrategyKey || \"strategy\";\n        this.throwOnError = (_a = options.throwOnError) !== null && _a !== void 0 ? _a : false;\n    }\n    /**\n     * Call this method with the Strategy, the optional name allows you to setup\n     * the same strategy multiple times with different names.\n     * It returns the Authenticator instance for concatenation.\n     * @example\n     * authenticator\n     *  .use(new SomeStrategy({}, (user) => Promise.resolve(user)))\n     *  .use(new SomeStrategy({}, (user) => Promise.resolve(user)), \"another\");\n     */\n    use(strategy, name) {\n        this.strategies.set(name !== null && name !== void 0 ? name : strategy.name, strategy);\n        return this;\n    }\n    /**\n     * Call this method with the name of the strategy you want to remove.\n     * It returns the Authenticator instance for concatenation.\n     * @example\n     * authenticator.unuse(\"another\").unuse(\"some\");\n     */\n    unuse(name) {\n        this.strategies.delete(name);\n        return this;\n    }\n    authenticate(strategy, request, options = {}) {\n        const strategyObj = this.strategies.get(strategy);\n        if (!strategyObj)\n            throw new Error(`Strategy ${strategy} not found.`);\n        return strategyObj.authenticate(new Request(request.url, request), this.sessionStorage, {\n            throwOnError: this.throwOnError,\n            ...options,\n            name: strategy,\n            sessionKey: this.sessionKey,\n            sessionErrorKey: this.sessionErrorKey,\n            sessionStrategyKey: this.sessionStrategyKey,\n        });\n    }\n    async isAuthenticated(request, options = {}) {\n        var _a;\n        let session = (0, server_runtime_1.isSession)(request)\n            ? request\n            : await this.sessionStorage.getSession(request.headers.get(\"Cookie\"));\n        let user = (_a = session.get(this.sessionKey)) !== null && _a !== void 0 ? _a : null;\n        if (user) {\n            if (options.successRedirect) {\n                throw (0, server_runtime_1.redirect)(options.successRedirect, { headers: options.headers });\n            }\n            else\n                return user;\n        }\n        if (options.failureRedirect) {\n            throw (0, server_runtime_1.redirect)(options.failureRedirect, { headers: options.headers });\n        }\n        else\n            return null;\n    }\n    /**\n     * Destroy the user session throw a redirect to another URL.\n     * @example\n     * async function action({ request }: ActionFunctionArgs) {\n     *   await authenticator.logout(request, { redirectTo: \"/login\" });\n     * }\n     */\n    async logout(request, options) {\n        let session = (0, server_runtime_1.isSession)(request)\n            ? request\n            : await this.sessionStorage.getSession(request.headers.get(\"Cookie\"));\n        let headers = new Headers(options.headers);\n        headers.append(\"Set-Cookie\", await this.sessionStorage.destroySession(session));\n        throw (0, server_runtime_1.redirect)(options.redirectTo, { headers });\n    }\n}\nexports.Authenticator = Authenticator;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Authorizer = void 0;\nconst server_runtime_1 = require(\"@remix-run/server-runtime\");\nclass Authorizer {\n    constructor(authenticator, rules = []) {\n        this.authenticator = authenticator;\n        this.rules = rules;\n    }\n    async authorize(args, { failureRedirect, raise = \"response\", rules = [], } = {}) {\n        let user = await this.authenticator.isAuthenticated(args.request);\n        if (!user) {\n            if (raise === \"response\") {\n                throw (0, server_runtime_1.json)({ message: \"Not authenticated.\" }, { status: 401 });\n            }\n            if (raise === \"redirect\") {\n                // @ts-expect-error failureRedirect is a string if raise is redirect\n                throw (0, server_runtime_1.redirect)(failureRedirect);\n            }\n            throw new Error(\"Not authenticated.\");\n        }\n        for (let rule of [...this.rules, ...rules]) {\n            if (await rule({ user, ...args }))\n                continue;\n            // @ts-expect-error failureRedirect is a string if raise is redirect\n            if (raise === \"redirect\")\n                throw (0, server_runtime_1.redirect)(failureRedirect);\n            if (raise === \"response\") {\n                if (!rule.name)\n                    throw (0, server_runtime_1.json)({ message: \"Forbidden\" }, { status: 403 });\n                throw (0, server_runtime_1.json)({ message: `Forbidden by policy ${rule.name}` }, { status: 403 });\n            }\n            if (!rule.name)\n                throw new Error(\"Forbidden.\");\n            throw new Error(`Forbidden by policy ${rule.name}`);\n        }\n        return user;\n    }\n}\nexports.Authorizer = Authorizer;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthorizationError = void 0;\nclass AuthorizationError extends Error {\n    constructor(message, cause) {\n        super(message);\n        this.cause = cause;\n    }\n}\nexports.AuthorizationError = AuthorizationError;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Strategy = void 0;\nconst server_runtime_1 = require(\"@remix-run/server-runtime\");\nconst error_1 = require(\"./error\");\n/**\n * The Strategy class is the base class every strategy should extend.\n *\n * This class receives two generics, a User and a VerifyParams.\n * - User is the type of the user data.\n * - VerifyParams is the type of the params the verify callback will receive from the strategy.\n *\n * This class also defines as protected two methods, `success` and `failure`.\n * - `success` is called when the authentication was successful.\n * - `failure` is called when the authentication failed.\n * These methods helps you return or throw the correct value, response or error\n * from within the strategy `authenticate` method.\n */\nclass Strategy {\n    constructor(verify) {\n        this.verify = verify;\n    }\n    /**\n     * Throw an AuthorizationError or a redirect to the failureRedirect.\n     * @param message The error message to set in the session.\n     * @param request The request to get the cookie out of.\n     * @param sessionStorage The session storage to retrieve the session from.\n     * @param options The strategy options.\n     * @throws {AuthorizationError} If the throwOnError is set to true.\n     * @throws {Response} If the failureRedirect is set or throwOnError is false.\n     * @returns {Promise<never>}\n     */\n    async failure(message, request, sessionStorage, options, cause) {\n        // if a failureRedirect is not set, we throw a 401 Response or an error\n        if (!options.failureRedirect) {\n            if (options.throwOnError)\n                throw new error_1.AuthorizationError(message, cause);\n            throw (0, server_runtime_1.json)({ message }, 401);\n        }\n        let session = await sessionStorage.getSession(request.headers.get(\"Cookie\"));\n        // if we do have a failureRedirect, we redirect to it and set the error\n        // in the session errorKey\n        session.flash(options.sessionErrorKey, { message });\n        throw (0, server_runtime_1.redirect)(options.failureRedirect, {\n            headers: { \"Set-Cookie\": await sessionStorage.commitSession(session) },\n        });\n    }\n    /**\n     * Returns the user data or throw a redirect to the successRedirect.\n     * @param user The user data to set in the session.\n     * @param request The request to get the cookie out of.\n     * @param sessionStorage The session storage to retrieve the session from.\n     * @param options The strategy options.\n     * @returns {Promise<User>} The user data.\n     * @throws {Response} If the successRedirect is set, it will redirect to it.\n     */\n    async success(user, request, sessionStorage, options) {\n        var _a;\n        // if a successRedirect is not set, we return the user\n        if (!options.successRedirect)\n            return user;\n        let session = await sessionStorage.getSession(request.headers.get(\"Cookie\"));\n        // if we do have a successRedirect, we redirect to it and set the user\n        // in the session sessionKey\n        session.set(options.sessionKey, user);\n        session.set(options.sessionStrategyKey, (_a = options.name) !== null && _a !== void 0 ? _a : this.name);\n        throw (0, server_runtime_1.redirect)(options.successRedirect, {\n            headers: { \"Set-Cookie\": await sessionStorage.commitSession(session) },\n        });\n    }\n}\nexports.Strategy = Strategy;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./authenticator\"), exports);\n__exportStar(require(\"./authorizer\"), exports);\n__exportStar(require(\"./error\"), exports);\n__exportStar(require(\"./strategy\"), exports);\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB;AACxB,QAAM,mBAAmB;AACzB,QAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBhB,YAAY,gBAAgB,UAAU,CAAC,GAAG;AACtC,YAAI;AACJ,aAAK,iBAAiB;AAKtB,aAAK,aAAa,oBAAI,IAAI;AAC1B,aAAK,aAAa,QAAQ,cAAc;AACxC,aAAK,kBAAkB,QAAQ,mBAAmB;AAClD,aAAK,qBAAqB,QAAQ,sBAAsB;AACxD,aAAK,gBAAgB,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,MACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,IAAI,UAAU,MAAM;AAChB,aAAK,WAAW,IAAI,SAAS,QAAQ,SAAS,SAAS,OAAO,SAAS,MAAM,QAAQ;AACrF,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,MAAM,MAAM;AACR,aAAK,WAAW,OAAO,IAAI;AAC3B,eAAO;AAAA,MACX;AAAA,MACA,aAAa,UAAU,SAAS,UAAU,CAAC,GAAG;AAC1C,cAAM,cAAc,KAAK,WAAW,IAAI,QAAQ;AAChD,YAAI,CAAC;AACD,gBAAM,IAAI,MAAM,YAAY,QAAQ,aAAa;AACrD,eAAO,YAAY,aAAa,IAAI,QAAQ,QAAQ,KAAK,OAAO,GAAG,KAAK,gBAAgB;AAAA,UACpF,cAAc,KAAK;AAAA,UACnB,GAAG;AAAA,UACH,MAAM;AAAA,UACN,YAAY,KAAK;AAAA,UACjB,iBAAiB,KAAK;AAAA,UACtB,oBAAoB,KAAK;AAAA,QAC7B,CAAC;AAAA,MACL;AAAA,MACA,MAAM,gBAAgB,SAAS,UAAU,CAAC,GAAG;AACzC,YAAI;AACJ,YAAI,WAAW,GAAG,iBAAiB,WAAW,OAAO,IAC/C,UACA,MAAM,KAAK,eAAe,WAAW,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AACxE,YAAI,QAAQ,KAAK,QAAQ,IAAI,KAAK,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK;AAChF,YAAI,MAAM;AACN,cAAI,QAAQ,iBAAiB;AACzB,mBAAO,GAAG,iBAAiB,UAAU,QAAQ,iBAAiB,EAAE,SAAS,QAAQ,QAAQ,CAAC;AAAA,UAC9F;AAEI,mBAAO;AAAA,QACf;AACA,YAAI,QAAQ,iBAAiB;AACzB,iBAAO,GAAG,iBAAiB,UAAU,QAAQ,iBAAiB,EAAE,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAC9F;AAEI,iBAAO;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,MAAM,OAAO,SAAS,SAAS;AAC3B,YAAI,WAAW,GAAG,iBAAiB,WAAW,OAAO,IAC/C,UACA,MAAM,KAAK,eAAe,WAAW,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AACxE,YAAI,UAAU,IAAI,QAAQ,QAAQ,OAAO;AACzC,gBAAQ,OAAO,cAAc,MAAM,KAAK,eAAe,eAAe,OAAO,CAAC;AAC9E,eAAO,GAAG,iBAAiB,UAAU,QAAQ,YAAY,EAAE,QAAQ,CAAC;AAAA,MACxE;AAAA,IACJ;AACA,YAAQ,gBAAgB;AAAA;AAAA;;;AC/GxB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,QAAM,mBAAmB;AACzB,QAAM,aAAN,MAAiB;AAAA,MACb,YAAY,eAAe,QAAQ,CAAC,GAAG;AACnC,aAAK,gBAAgB;AACrB,aAAK,QAAQ;AAAA,MACjB;AAAA,MACA,MAAM,UAAU,MAAM,EAAE,iBAAiB,QAAQ,YAAY,QAAQ,CAAC,EAAG,IAAI,CAAC,GAAG;AAC7E,YAAI,OAAO,MAAM,KAAK,cAAc,gBAAgB,KAAK,OAAO;AAChE,YAAI,CAAC,MAAM;AACP,cAAI,UAAU,YAAY;AACtB,mBAAO,GAAG,iBAAiB,MAAM,EAAE,SAAS,qBAAqB,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,UACvF;AACA,cAAI,UAAU,YAAY;AAEtB,mBAAO,GAAG,iBAAiB,UAAU,eAAe;AAAA,UACxD;AACA,gBAAM,IAAI,MAAM,oBAAoB;AAAA,QACxC;AACA,iBAAS,QAAQ,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,GAAG;AACxC,cAAI,MAAM,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC;AAC5B;AAEJ,cAAI,UAAU;AACV,mBAAO,GAAG,iBAAiB,UAAU,eAAe;AACxD,cAAI,UAAU,YAAY;AACtB,gBAAI,CAAC,KAAK;AACN,qBAAO,GAAG,iBAAiB,MAAM,EAAE,SAAS,YAAY,GAAG,EAAE,QAAQ,IAAI,CAAC;AAC9E,mBAAO,GAAG,iBAAiB,MAAM,EAAE,SAAS,uBAAuB,KAAK,IAAI,GAAG,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,UACrG;AACA,cAAI,CAAC,KAAK;AACN,kBAAM,IAAI,MAAM,YAAY;AAChC,gBAAM,IAAI,MAAM,uBAAuB,KAAK,IAAI,EAAE;AAAA,QACtD;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,YAAQ,aAAa;AAAA;AAAA;;;ACvCrB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,qBAAqB;AAC7B,QAAM,qBAAN,cAAiC,MAAM;AAAA,MACnC,YAAY,SAAS,OAAO;AACxB,cAAM,OAAO;AACb,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,YAAQ,qBAAqB;AAAA;AAAA;;;ACT7B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAM,mBAAmB;AACzB,QAAM,UAAU;AAchB,QAAM,WAAN,MAAe;AAAA,MACX,YAAY,QAAQ;AAChB,aAAK,SAAS;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,MAAM,QAAQ,SAAS,SAAS,gBAAgB,SAAS,OAAO;AAE5D,YAAI,CAAC,QAAQ,iBAAiB;AAC1B,cAAI,QAAQ;AACR,kBAAM,IAAI,QAAQ,mBAAmB,SAAS,KAAK;AACvD,iBAAO,GAAG,iBAAiB,MAAM,EAAE,QAAQ,GAAG,GAAG;AAAA,QACrD;AACA,YAAI,UAAU,MAAM,eAAe,WAAW,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AAG3E,gBAAQ,MAAM,QAAQ,iBAAiB,EAAE,QAAQ,CAAC;AAClD,eAAO,GAAG,iBAAiB,UAAU,QAAQ,iBAAiB;AAAA,UAC1D,SAAS,EAAE,cAAc,MAAM,eAAe,cAAc,OAAO,EAAE;AAAA,QACzE,CAAC;AAAA,MACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,MAAM,QAAQ,MAAM,SAAS,gBAAgB,SAAS;AAClD,YAAI;AAEJ,YAAI,CAAC,QAAQ;AACT,iBAAO;AACX,YAAI,UAAU,MAAM,eAAe,WAAW,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AAG3E,gBAAQ,IAAI,QAAQ,YAAY,IAAI;AACpC,gBAAQ,IAAI,QAAQ,qBAAqB,KAAK,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,KAAK,IAAI;AACtG,eAAO,GAAG,iBAAiB,UAAU,QAAQ,iBAAiB;AAAA,UAC1D,SAAS,EAAE,cAAc,MAAM,eAAe,cAAc,OAAO,EAAE;AAAA,QACzE,CAAC;AAAA,MACL;AAAA,IACJ;AACA,YAAQ,WAAW;AAAA;AAAA;;;ACvEnB;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGA,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,yBAA4B,OAAO;AAChD,iBAAa,sBAAyB,OAAO;AAC7C,iBAAa,iBAAoB,OAAO;AACxC,iBAAa,oBAAuB,OAAO;AAAA;AAAA;", "names": ["exports"]}