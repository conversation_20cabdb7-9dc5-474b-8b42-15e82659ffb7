import {
  StepFormWizard,
  StepForm<PERSON><PERSON>rdFilter,
  StepFormWizardSession,
  StepFormWizardSessionAction,
  StepFormWizardSessionFilterMatch,
  StepFormWizardSessionStep,
  StepFormWizardStep,
  Prisma,
  Tenant,
} from "@prisma/client";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import { db } from "~/utils/db.server";
import { UserSimple } from "~/utils/db/users.db.server";
import { StepFormWizardSessionActionDto } from "../dtos/StepFormWizardSessionActionDto";
import { StepFormWizardSessionStatus } from "../dtos/StepFormWizardSessionStatus";

export type StepFormWizardSessionWithDetails = StepFormWizardSession & {
  stepFormWizard: StepFormWizard;
  user: UserSimple;
  tenant: Tenant | null;
  sessionSteps: (StepFormWizardSessionStep & {
    step: StepFormWizardStep;
  })[];
  actions: StepFormWizardSessionAction[];
  matches: (StepFormWizardSessionFilterMatch & { stepFormWizardFilter: StepFormWizardFilter })[];
};

export async function getStepFormWizardSessions({
  stepFormWizardId,
  userId,
  tenantId,
  status,
  hasBeenStarted,
  hasBeenCompleted,
  hasBeenDismissed,
}: {
  stepFormWizardId?: string;
  userId?: string;
  tenantId?: string | null;
  status?: StepFormWizardSessionStatus;
  hasBeenStarted?: boolean;
  hasBeenCompleted?: boolean;
  hasBeenDismissed?: boolean;
}): Promise<StepFormWizardSessionWithDetails[]> {
  const where: Prisma.StepFormWizardSessionWhereInput = {
    stepFormWizardId,
    userId,
    tenantId,
    status: status ? status.toString() : undefined,
  };
  if (hasBeenStarted !== undefined) {
    where.startedAt = hasBeenStarted ? { not: null } : null;
  }
  if (hasBeenCompleted !== undefined) {
    where.completedAt = hasBeenCompleted ? { not: null } : null;
  }
  if (hasBeenDismissed !== undefined) {
    where.dismissedAt = hasBeenDismissed ? { not: null } : null;
  }
  return await db.stepFormWizardSession.findMany({
    where,
    include: {
      stepFormWizard: true,
      user: true,
      tenant: true,
      sessionSteps: { include: { step: true }, orderBy: { step: { order: "asc" } } },
      actions: true,
      matches: { include: { stepFormWizardFilter: true } },
    },
    orderBy: { createdAt: "desc" },
  });
}

export async function getStepFormWizardSessionsWithPagination({
  where,
  pagination,
}: {
  where?: Prisma.StepFormWizardSessionWhereInput;
  pagination: { page: number; pageSize: number };
}): Promise<{ items: StepFormWizardSessionWithDetails[]; pagination: PaginationDto }> {
  const items = await db.stepFormWizardSession.findMany({
    where,
    take: pagination.pageSize,
    skip: (pagination.page - 1) * pagination.pageSize,
    include: {
      stepFormWizard: true,
      user: true,
      tenant: true,
      sessionSteps: { include: { step: true }, orderBy: { step: { order: "asc" } } },
      actions: true,
      matches: { include: { stepFormWizardFilter: true } },
    },
    orderBy: { createdAt: "desc" },
  });
  const totalItems = await db.stepFormWizardSession.count({
    where,
  });
  return {
    items,
    pagination: {
      page: pagination.page,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages: Math.ceil(totalItems / pagination.pageSize),
    },
  };
}

export async function getStepFormWizardSession(id: string): Promise<StepFormWizardSessionWithDetails | null> {
  return await db.stepFormWizardSession.findUnique({
    where: { id },
    include: {
      stepFormWizard: true,
      user: true,
      tenant: true,
      sessionSteps: { include: { step: true }, orderBy: { step: { order: "asc" } } },
      actions: true,
      matches: { include: { stepFormWizardFilter: true } },
    },
  });
}

export async function getStepFormWizardSessionsByUser({
  userId,
  tenantId,
  status,
}: {
  userId: string;
  tenantId: string | null;
  status?: StepFormWizardSessionStatus;
}): Promise<StepFormWizardSessionWithDetails[]> {
  return await db.stepFormWizardSession.findMany({
    where: { userId, tenantId, status },
    include: {
      stepFormWizard: true,
      user: true,
      tenant: true,
      sessionSteps: { include: { step: true }, orderBy: { step: { order: "asc" } } },
      actions: true,
      matches: { include: { stepFormWizardFilter: true } },
    },
    orderBy: { stepFormWizard: { createdAt: "asc" } },
  });
}

export async function updateStepFormWizardSession(
  id: string,
  data: { status?: StepFormWizardSessionStatus; startedAt?: Date; logicAppRunId?: string; currentStepIndex?: string; completedAt?: Date; dismissedAt?: Date; error?:string[] }
) {
  return await db.stepFormWizardSession.update({
    where: { id },
    data,
  });
}

export async function setStepFormWizardSessionActions(id: string, data: { actions: StepFormWizardSessionActionDto[] }) {
  await db.stepFormWizardSessionAction.deleteMany({
    where: { stepFormWizardSessionId: id },
  });
  return await db.stepFormWizardSessionAction.createMany({
    data: data.actions.map((action) => ({
      stepFormWizardSessionId: id,
      type: action.type.toString(),
      name: action.name,
      value: action.value,
      createdAt: action.createdAt,
    })),
  });
}

export async function updateStepFormWizardSessions(ids: string[], data: { status?: StepFormWizardSessionStatus }) {
  return await db.stepFormWizardSession.updateMany({
    where: { id: { in: ids } },
    data,
  });
}

export async function deleteStepFormWizardSession(id: string) {
  return await db.stepFormWizardSession.delete({
    where: { id },
  });
}

export async function deleteStepFormWizardSessions(stepFormWizardId: string) {
  return await db.stepFormWizardSession.deleteMany({
    where: { stepFormWizardId },
  });
}

export async function groupStepFormWizardSessionsByUser() {
  return await db.stepFormWizardSession.groupBy({
    by: ["userId"],
    _count: {
      _all: true,
    },
  });
}
