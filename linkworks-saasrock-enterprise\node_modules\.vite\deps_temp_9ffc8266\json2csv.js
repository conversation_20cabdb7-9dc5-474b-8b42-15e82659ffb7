import {
  require_stream
} from "./chunk-U2BUN7BW.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// browser-external:os
var require_os = __commonJS({
  "browser-external:os"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "os" has been externalized for browser compatibility. Cannot access "os.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/json2csv/dist/json2csv.esm.js
var import_stream = __toESM(require_stream());
var import_os = __toESM(require_os());
function _typeof(obj) {
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function(obj2) {
      return typeof obj2;
    };
  } else {
    _typeof = function(obj2) {
      return obj2 && typeof Symbol === "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
    };
  }
  return _typeof(obj);
}
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor) descriptor.writable = true;
    Object.defineProperty(target, descriptor.key, descriptor);
  }
}
function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) _defineProperties(Constructor.prototype, protoProps);
  if (staticProps) _defineProperties(Constructor, staticProps);
  return Constructor;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: {
      value: subClass,
      writable: true,
      configurable: true
    }
  });
  if (superClass) _setPrototypeOf(subClass, superClass);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _assertThisInitialized(self2) {
  if (self2 === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self2;
}
function _possibleConstructorReturn(self2, call) {
  if (call && (typeof call === "object" || typeof call === "function")) {
    return call;
  }
  return _assertThisInitialized(self2);
}
function _toArray(arr) {
  return _arrayWithHoles(arr) || _iterableToArray(arr) || _nonIterableRest();
}
function _toConsumableArray(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread();
}
function _arrayWithoutHoles(arr) {
  if (Array.isArray(arr)) {
    for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) arr2[i] = arr[i];
    return arr2;
  }
}
function _arrayWithHoles(arr) {
  if (Array.isArray(arr)) return arr;
}
function _iterableToArray(iter) {
  if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter);
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance");
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance");
}
var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
var FUNC_ERROR_TEXT = "Expected a function";
var HASH_UNDEFINED = "__lodash_hash_undefined__";
var INFINITY = 1 / 0;
var funcTag = "[object Function]";
var genTag = "[object GeneratorFunction]";
var symbolTag = "[object Symbol]";
var reIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
var reIsPlainProp = /^\w*$/;
var reLeadingDot = /^\./;
var rePropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;
var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
var reEscapeChar = /\\(\\)?/g;
var reIsHostCtor = /^\[object .+?Constructor\]$/;
var freeGlobal = typeof commonjsGlobal == "object" && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;
var freeSelf = typeof self == "object" && self && self.Object === Object && self;
var root = freeGlobal || freeSelf || Function("return this")();
function getValue(object, key) {
  return object == null ? void 0 : object[key];
}
function isHostObject(value) {
  var result = false;
  if (value != null && typeof value.toString != "function") {
    try {
      result = !!(value + "");
    } catch (e) {
    }
  }
  return result;
}
var arrayProto = Array.prototype;
var funcProto = Function.prototype;
var objectProto = Object.prototype;
var coreJsData = root["__core-js_shared__"];
var maskSrcKey = function() {
  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || "");
  return uid ? "Symbol(src)_1." + uid : "";
}();
var funcToString = funcProto.toString;
var hasOwnProperty = objectProto.hasOwnProperty;
var objectToString = objectProto.toString;
var reIsNative = RegExp(
  "^" + funcToString.call(hasOwnProperty).replace(reRegExpChar, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
);
var Symbol$1 = root.Symbol;
var splice = arrayProto.splice;
var Map = getNative(root, "Map");
var nativeCreate = getNative(Object, "create");
var symbolProto = Symbol$1 ? Symbol$1.prototype : void 0;
var symbolToString = symbolProto ? symbolProto.toString : void 0;
function Hash(entries) {
  var index = -1, length = entries ? entries.length : 0;
  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}
function hashClear() {
  this.__data__ = nativeCreate ? nativeCreate(null) : {};
}
function hashDelete(key) {
  return this.has(key) && delete this.__data__[key];
}
function hashGet(key) {
  var data = this.__data__;
  if (nativeCreate) {
    var result = data[key];
    return result === HASH_UNDEFINED ? void 0 : result;
  }
  return hasOwnProperty.call(data, key) ? data[key] : void 0;
}
function hashHas(key) {
  var data = this.__data__;
  return nativeCreate ? data[key] !== void 0 : hasOwnProperty.call(data, key);
}
function hashSet(key, value) {
  var data = this.__data__;
  data[key] = nativeCreate && value === void 0 ? HASH_UNDEFINED : value;
  return this;
}
Hash.prototype.clear = hashClear;
Hash.prototype["delete"] = hashDelete;
Hash.prototype.get = hashGet;
Hash.prototype.has = hashHas;
Hash.prototype.set = hashSet;
function ListCache(entries) {
  var index = -1, length = entries ? entries.length : 0;
  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}
function listCacheClear() {
  this.__data__ = [];
}
function listCacheDelete(key) {
  var data = this.__data__, index = assocIndexOf(data, key);
  if (index < 0) {
    return false;
  }
  var lastIndex = data.length - 1;
  if (index == lastIndex) {
    data.pop();
  } else {
    splice.call(data, index, 1);
  }
  return true;
}
function listCacheGet(key) {
  var data = this.__data__, index = assocIndexOf(data, key);
  return index < 0 ? void 0 : data[index][1];
}
function listCacheHas(key) {
  return assocIndexOf(this.__data__, key) > -1;
}
function listCacheSet(key, value) {
  var data = this.__data__, index = assocIndexOf(data, key);
  if (index < 0) {
    data.push([key, value]);
  } else {
    data[index][1] = value;
  }
  return this;
}
ListCache.prototype.clear = listCacheClear;
ListCache.prototype["delete"] = listCacheDelete;
ListCache.prototype.get = listCacheGet;
ListCache.prototype.has = listCacheHas;
ListCache.prototype.set = listCacheSet;
function MapCache(entries) {
  var index = -1, length = entries ? entries.length : 0;
  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}
function mapCacheClear() {
  this.__data__ = {
    "hash": new Hash(),
    "map": new (Map || ListCache)(),
    "string": new Hash()
  };
}
function mapCacheDelete(key) {
  return getMapData(this, key)["delete"](key);
}
function mapCacheGet(key) {
  return getMapData(this, key).get(key);
}
function mapCacheHas(key) {
  return getMapData(this, key).has(key);
}
function mapCacheSet(key, value) {
  getMapData(this, key).set(key, value);
  return this;
}
MapCache.prototype.clear = mapCacheClear;
MapCache.prototype["delete"] = mapCacheDelete;
MapCache.prototype.get = mapCacheGet;
MapCache.prototype.has = mapCacheHas;
MapCache.prototype.set = mapCacheSet;
function assocIndexOf(array, key) {
  var length = array.length;
  while (length--) {
    if (eq(array[length][0], key)) {
      return length;
    }
  }
  return -1;
}
function baseGet(object, path) {
  path = isKey(path, object) ? [path] : castPath(path);
  var index = 0, length = path.length;
  while (object != null && index < length) {
    object = object[toKey(path[index++])];
  }
  return index && index == length ? object : void 0;
}
function baseIsNative(value) {
  if (!isObject(value) || isMasked(value)) {
    return false;
  }
  var pattern = isFunction(value) || isHostObject(value) ? reIsNative : reIsHostCtor;
  return pattern.test(toSource(value));
}
function baseToString(value) {
  if (typeof value == "string") {
    return value;
  }
  if (isSymbol(value)) {
    return symbolToString ? symbolToString.call(value) : "";
  }
  var result = value + "";
  return result == "0" && 1 / value == -INFINITY ? "-0" : result;
}
function castPath(value) {
  return isArray(value) ? value : stringToPath(value);
}
function getMapData(map, key) {
  var data = map.__data__;
  return isKeyable(key) ? data[typeof key == "string" ? "string" : "hash"] : data.map;
}
function getNative(object, key) {
  var value = getValue(object, key);
  return baseIsNative(value) ? value : void 0;
}
function isKey(value, object) {
  if (isArray(value)) {
    return false;
  }
  var type = typeof value;
  if (type == "number" || type == "symbol" || type == "boolean" || value == null || isSymbol(value)) {
    return true;
  }
  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);
}
function isKeyable(value) {
  var type = typeof value;
  return type == "string" || type == "number" || type == "symbol" || type == "boolean" ? value !== "__proto__" : value === null;
}
function isMasked(func) {
  return !!maskSrcKey && maskSrcKey in func;
}
var stringToPath = memoize(function(string) {
  string = toString(string);
  var result = [];
  if (reLeadingDot.test(string)) {
    result.push("");
  }
  string.replace(rePropName, function(match, number, quote, string2) {
    result.push(quote ? string2.replace(reEscapeChar, "$1") : number || match);
  });
  return result;
});
function toKey(value) {
  if (typeof value == "string" || isSymbol(value)) {
    return value;
  }
  var result = value + "";
  return result == "0" && 1 / value == -INFINITY ? "-0" : result;
}
function toSource(func) {
  if (func != null) {
    try {
      return funcToString.call(func);
    } catch (e) {
    }
    try {
      return func + "";
    } catch (e) {
    }
  }
  return "";
}
function memoize(func, resolver) {
  if (typeof func != "function" || resolver && typeof resolver != "function") {
    throw new TypeError(FUNC_ERROR_TEXT);
  }
  var memoized = function() {
    var args = arguments, key = resolver ? resolver.apply(this, args) : args[0], cache = memoized.cache;
    if (cache.has(key)) {
      return cache.get(key);
    }
    var result = func.apply(this, args);
    memoized.cache = cache.set(key, result);
    return result;
  };
  memoized.cache = new (memoize.Cache || MapCache)();
  return memoized;
}
memoize.Cache = MapCache;
function eq(value, other) {
  return value === other || value !== value && other !== other;
}
var isArray = Array.isArray;
function isFunction(value) {
  var tag = isObject(value) ? objectToString.call(value) : "";
  return tag == funcTag || tag == genTag;
}
function isObject(value) {
  var type = typeof value;
  return !!value && (type == "object" || type == "function");
}
function isObjectLike(value) {
  return !!value && typeof value == "object";
}
function isSymbol(value) {
  return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
}
function toString(value) {
  return value == null ? "" : baseToString(value);
}
function get(object, path, defaultValue) {
  var result = object == null ? void 0 : baseGet(object, path);
  return result === void 0 ? defaultValue : result;
}
var lodash_get = get;
function getProp(obj, path, defaultValue) {
  return obj[path] === void 0 ? defaultValue : obj[path];
}
function setProp(obj, path, value) {
  var pathArray = Array.isArray(path) ? path : path.split(".");
  var _pathArray = _toArray(pathArray), key = _pathArray[0], restPath = _pathArray.slice(1);
  return _objectSpread({}, obj, _defineProperty({}, key, pathArray.length > 1 ? setProp(obj[key] || {}, restPath, value) : value));
}
function unsetProp(obj, path) {
  var pathArray = Array.isArray(path) ? path : path.split(".");
  var _pathArray2 = _toArray(pathArray), key = _pathArray2[0], restPath = _pathArray2.slice(1);
  if (_typeof(obj[key]) !== "object") {
    return obj;
  }
  if (pathArray.length === 1) {
    return Object.keys(obj).filter(function(prop) {
      return prop !== key;
    }).reduce(function(acc, prop) {
      return Object.assign(acc, _defineProperty({}, prop, obj[prop]));
    }, {});
  }
  return Object.keys(obj).reduce(function(acc, prop) {
    return _objectSpread({}, acc, _defineProperty({}, prop, prop !== key ? obj[prop] : unsetProp(obj[key], restPath)));
  }, {});
}
function flattenReducer(acc, arr) {
  try {
    acc.push.apply(acc, _toConsumableArray(arr));
    return acc;
  } catch (err) {
    return acc.concat(arr);
  }
}
function fastJoin(arr, separator) {
  var isFirst = true;
  return arr.reduce(function(acc, elem) {
    if (elem === null || elem === void 0) {
      elem = "";
    }
    if (isFirst) {
      isFirst = false;
      return "".concat(elem);
    }
    return "".concat(acc).concat(separator).concat(elem);
  }, "");
}
var utils = {
  getProp,
  setProp,
  unsetProp,
  fastJoin,
  flattenReducer
};
var getProp$1 = utils.getProp;
var fastJoin$1 = utils.fastJoin;
var flattenReducer$1 = utils.flattenReducer;
var JSON2CSVBase = function() {
  function JSON2CSVBase2(opts) {
    _classCallCheck(this, JSON2CSVBase2);
    this.opts = this.preprocessOpts(opts);
  }
  _createClass(JSON2CSVBase2, [{
    key: "preprocessOpts",
    value: function preprocessOpts(opts) {
      var processedOpts = Object.assign({}, opts);
      processedOpts.transforms = !Array.isArray(processedOpts.transforms) ? processedOpts.transforms ? [processedOpts.transforms] : [] : processedOpts.transforms;
      processedOpts.delimiter = processedOpts.delimiter || ",";
      processedOpts.eol = processedOpts.eol || import_os.default.EOL;
      processedOpts.quote = typeof processedOpts.quote === "string" ? processedOpts.quote : '"';
      processedOpts.escapedQuote = typeof processedOpts.escapedQuote === "string" ? processedOpts.escapedQuote : "".concat(processedOpts.quote).concat(processedOpts.quote);
      processedOpts.header = processedOpts.header !== false;
      processedOpts.includeEmptyRows = processedOpts.includeEmptyRows || false;
      processedOpts.withBOM = processedOpts.withBOM || false;
      return processedOpts;
    }
    /**
     * Check and normalize the fields configuration.
     *
     * @param {(string|object)[]} fields Fields configuration provided by the user
     * or inferred from the data
     * @returns {object[]} preprocessed FieldsInfo array
     */
  }, {
    key: "preprocessFieldsInfo",
    value: function preprocessFieldsInfo(fields) {
      var _this = this;
      return fields.map(function(fieldInfo) {
        if (typeof fieldInfo === "string") {
          return {
            label: fieldInfo,
            value: fieldInfo.includes(".") || fieldInfo.includes("[") ? function(row) {
              return lodash_get(row, fieldInfo, _this.opts.defaultValue);
            } : function(row) {
              return getProp$1(row, fieldInfo, _this.opts.defaultValue);
            }
          };
        }
        if (_typeof(fieldInfo) === "object") {
          var defaultValue = "default" in fieldInfo ? fieldInfo.default : _this.opts.defaultValue;
          if (typeof fieldInfo.value === "string") {
            return {
              label: fieldInfo.label || fieldInfo.value,
              value: fieldInfo.value.includes(".") || fieldInfo.value.includes("[") ? function(row) {
                return lodash_get(row, fieldInfo.value, defaultValue);
              } : function(row) {
                return getProp$1(row, fieldInfo.value, defaultValue);
              }
            };
          }
          if (typeof fieldInfo.value === "function") {
            var label = fieldInfo.label || fieldInfo.value.name || "";
            var field = {
              label,
              default: defaultValue
            };
            return {
              label,
              value: function value(row) {
                var value2 = fieldInfo.value(row, field);
                return value2 === null || value2 === void 0 ? defaultValue : value2;
              }
            };
          }
        }
        throw new Error("Invalid field info option. " + JSON.stringify(fieldInfo));
      });
    }
    /**
     * Create the title row with all the provided fields as column headings
     *
     * @returns {String} titles as a string
     */
  }, {
    key: "getHeader",
    value: function getHeader() {
      var _this2 = this;
      return fastJoin$1(this.opts.fields.map(function(fieldInfo) {
        return _this2.processValue(fieldInfo.label);
      }), this.opts.delimiter);
    }
    /**
     * Preprocess each object according to the given transforms (unwind, flatten, etc.).
     * @param {Object} row JSON object to be converted in a CSV row
     */
  }, {
    key: "preprocessRow",
    value: function preprocessRow(row) {
      return this.opts.transforms.reduce(function(rows, transform) {
        return rows.map(function(row2) {
          return transform(row2);
        }).reduce(flattenReducer$1, []);
      }, [row]);
    }
    /**
     * Create the content of a specific CSV row
     *
     * @param {Object} row JSON object to be converted in a CSV row
     * @returns {String} CSV string (row)
     */
  }, {
    key: "processRow",
    value: function processRow(row) {
      var _this3 = this;
      if (!row) {
        return void 0;
      }
      var processedRow = this.opts.fields.map(function(fieldInfo) {
        return _this3.processCell(row, fieldInfo);
      });
      if (!this.opts.includeEmptyRows && processedRow.every(function(field) {
        return field === void 0;
      })) {
        return void 0;
      }
      return fastJoin$1(processedRow, this.opts.delimiter);
    }
    /**
     * Create the content of a specfic CSV row cell
     *
     * @param {Object} row JSON object representing the  CSV row that the cell belongs to
     * @param {FieldInfo} fieldInfo Details of the field to process to be a CSV cell
     * @returns {String} CSV string (cell)
     */
  }, {
    key: "processCell",
    value: function processCell(row, fieldInfo) {
      return this.processValue(fieldInfo.value(row));
    }
    /**
     * Create the content of a specfic CSV row cell
     *
     * @param {Any} value Value to be included in a CSV cell
     * @returns {String} Value stringified and processed
     */
  }, {
    key: "processValue",
    value: function processValue(value) {
      if (value === null || value === void 0) {
        return void 0;
      }
      var valueType = _typeof(value);
      if (valueType !== "boolean" && valueType !== "number" && valueType !== "string") {
        value = JSON.stringify(value);
        if (value === void 0) {
          return void 0;
        }
        if (value[0] === '"') {
          value = value.replace(/^"(.+)"$/, "$1");
        }
      }
      if (typeof value === "string") {
        if (this.opts.excelStrings) {
          if (value.includes(this.opts.quote)) {
            value = value.replace(new RegExp(this.opts.quote, "g"), "".concat(this.opts.escapedQuote).concat(this.opts.escapedQuote));
          }
          value = '"=""'.concat(value, '"""');
        } else {
          if (value.includes(this.opts.quote)) {
            value = value.replace(new RegExp(this.opts.quote, "g"), this.opts.escapedQuote);
          }
          value = "".concat(this.opts.quote).concat(value).concat(this.opts.quote);
        }
      }
      return value;
    }
  }]);
  return JSON2CSVBase2;
}();
var JSON2CSVBase_1 = JSON2CSVBase;
var fastJoin$2 = utils.fastJoin;
var flattenReducer$2 = utils.flattenReducer;
var JSON2CSVParser = function(_JSON2CSVBase) {
  _inherits(JSON2CSVParser2, _JSON2CSVBase);
  function JSON2CSVParser2(opts) {
    var _this;
    _classCallCheck(this, JSON2CSVParser2);
    _this = _possibleConstructorReturn(this, _getPrototypeOf(JSON2CSVParser2).call(this, opts));
    if (_this.opts.fields) {
      _this.opts.fields = _this.preprocessFieldsInfo(_this.opts.fields);
    }
    return _this;
  }
  _createClass(JSON2CSVParser2, [{
    key: "parse",
    value: function parse3(data) {
      var processedData = this.preprocessData(data);
      if (!this.opts.fields) {
        this.opts.fields = processedData.reduce(function(fields, item) {
          Object.keys(item).forEach(function(field) {
            if (!fields.includes(field)) {
              fields.push(field);
            }
          });
          return fields;
        }, []);
        this.opts.fields = this.preprocessFieldsInfo(this.opts.fields);
      }
      var header = this.opts.header ? this.getHeader() : "";
      var rows = this.processData(processedData);
      var csv = (this.opts.withBOM ? "\uFEFF" : "") + header + (header && rows ? this.opts.eol : "") + rows;
      return csv;
    }
    /**
     * Preprocess the data according to the give opts (unwind, flatten, etc.)
      and calculate the fields and field names if they are not provided.
     *
     * @param {Array|Object} data Array or object to be converted to CSV
     */
  }, {
    key: "preprocessData",
    value: function preprocessData(data) {
      var _this2 = this;
      var processedData = Array.isArray(data) ? data : [data];
      if (!this.opts.fields && (processedData.length === 0 || _typeof(processedData[0]) !== "object")) {
        throw new Error('Data should not be empty or the "fields" option should be included');
      }
      if (this.opts.transforms.length === 0) return processedData;
      return processedData.map(function(row) {
        return _this2.preprocessRow(row);
      }).reduce(flattenReducer$2, []);
    }
    /**
     * Create the content row by row below the header
     *
     * @param {Array} data Array of JSON objects to be converted to CSV
     * @returns {String} CSV string (body)
     */
  }, {
    key: "processData",
    value: function processData(data) {
      var _this3 = this;
      return fastJoin$2(
        data.map(function(row) {
          return _this3.processRow(row);
        }).filter(function(row) {
          return row;
        }),
        // Filter empty rows
        this.opts.eol
      );
    }
  }]);
  return JSON2CSVParser2;
}(JSON2CSVBase_1);
var JSON2CSVParser_1 = JSON2CSVParser;
var C = {};
var LEFT_BRACE = C.LEFT_BRACE = 1;
var RIGHT_BRACE = C.RIGHT_BRACE = 2;
var LEFT_BRACKET = C.LEFT_BRACKET = 3;
var RIGHT_BRACKET = C.RIGHT_BRACKET = 4;
var COLON = C.COLON = 5;
var COMMA = C.COMMA = 6;
var TRUE = C.TRUE = 7;
var FALSE = C.FALSE = 8;
var NULL = C.NULL = 9;
var STRING = C.STRING = 10;
var NUMBER = C.NUMBER = 11;
var START = C.START = 17;
var STOP = C.STOP = 18;
var TRUE1 = C.TRUE1 = 33;
var TRUE2 = C.TRUE2 = 34;
var TRUE3 = C.TRUE3 = 35;
var FALSE1 = C.FALSE1 = 49;
var FALSE2 = C.FALSE2 = 50;
var FALSE3 = C.FALSE3 = 51;
var FALSE4 = C.FALSE4 = 52;
var NULL1 = C.NULL1 = 65;
var NULL2 = C.NULL2 = 66;
var NULL3 = C.NULL3 = 67;
var NUMBER1 = C.NUMBER1 = 81;
var NUMBER3 = C.NUMBER3 = 83;
var STRING1 = C.STRING1 = 97;
var STRING2 = C.STRING2 = 98;
var STRING3 = C.STRING3 = 99;
var STRING4 = C.STRING4 = 100;
var STRING5 = C.STRING5 = 101;
var STRING6 = C.STRING6 = 102;
var VALUE = C.VALUE = 113;
var KEY = C.KEY = 114;
var OBJECT = C.OBJECT = 129;
var ARRAY = C.ARRAY = 130;
var BACK_SLASH = "\\".charCodeAt(0);
var FORWARD_SLASH = "/".charCodeAt(0);
var BACKSPACE = "\b".charCodeAt(0);
var FORM_FEED = "\f".charCodeAt(0);
var NEWLINE = "\n".charCodeAt(0);
var CARRIAGE_RETURN = "\r".charCodeAt(0);
var TAB = "	".charCodeAt(0);
var STRING_BUFFER_SIZE = 64 * 1024;
function Parser() {
  this.tState = START;
  this.value = void 0;
  this.string = void 0;
  this.stringBuffer = Buffer.alloc ? Buffer.alloc(STRING_BUFFER_SIZE) : new Buffer(STRING_BUFFER_SIZE);
  this.stringBufferOffset = 0;
  this.unicode = void 0;
  this.highSurrogate = void 0;
  this.key = void 0;
  this.mode = void 0;
  this.stack = [];
  this.state = VALUE;
  this.bytes_remaining = 0;
  this.bytes_in_sequence = 0;
  this.temp_buffs = { "2": new Buffer(2), "3": new Buffer(3), "4": new Buffer(4) };
  this.offset = -1;
}
Parser.toknam = function(code) {
  var keys = Object.keys(C);
  for (var i = 0, l = keys.length; i < l; i++) {
    var key = keys[i];
    if (C[key] === code) {
      return key;
    }
  }
  return code && "0x" + code.toString(16);
};
var proto = Parser.prototype;
proto.onError = function(err) {
  throw err;
};
proto.charError = function(buffer, i) {
  this.tState = STOP;
  this.onError(new Error("Unexpected " + JSON.stringify(String.fromCharCode(buffer[i])) + " at position " + i + " in state " + Parser.toknam(this.tState)));
};
proto.appendStringChar = function(char) {
  if (this.stringBufferOffset >= STRING_BUFFER_SIZE) {
    this.string += this.stringBuffer.toString("utf8");
    this.stringBufferOffset = 0;
  }
  this.stringBuffer[this.stringBufferOffset++] = char;
};
proto.appendStringBuf = function(buf, start, end) {
  var size = buf.length;
  if (typeof start === "number") {
    if (typeof end === "number") {
      if (end < 0) {
        size = buf.length - start + end;
      } else {
        size = end - start;
      }
    } else {
      size = buf.length - start;
    }
  }
  if (size < 0) {
    size = 0;
  }
  if (this.stringBufferOffset + size > STRING_BUFFER_SIZE) {
    this.string += this.stringBuffer.toString("utf8", 0, this.stringBufferOffset);
    this.stringBufferOffset = 0;
  }
  buf.copy(this.stringBuffer, this.stringBufferOffset, start, end);
  this.stringBufferOffset += size;
};
proto.write = function(buffer) {
  if (typeof buffer === "string") buffer = new Buffer(buffer);
  var n;
  for (var i = 0, l = buffer.length; i < l; i++) {
    if (this.tState === START) {
      n = buffer[i];
      this.offset++;
      if (n === 123) {
        this.onToken(LEFT_BRACE, "{");
      } else if (n === 125) {
        this.onToken(RIGHT_BRACE, "}");
      } else if (n === 91) {
        this.onToken(LEFT_BRACKET, "[");
      } else if (n === 93) {
        this.onToken(RIGHT_BRACKET, "]");
      } else if (n === 58) {
        this.onToken(COLON, ":");
      } else if (n === 44) {
        this.onToken(COMMA, ",");
      } else if (n === 116) {
        this.tState = TRUE1;
      } else if (n === 102) {
        this.tState = FALSE1;
      } else if (n === 110) {
        this.tState = NULL1;
      } else if (n === 34) {
        this.string = "";
        this.stringBufferOffset = 0;
        this.tState = STRING1;
      } else if (n === 45) {
        this.string = "-";
        this.tState = NUMBER1;
      } else {
        if (n >= 48 && n < 64) {
          this.string = String.fromCharCode(n);
          this.tState = NUMBER3;
        } else if (n === 32 || n === 9 || n === 10 || n === 13) ;
        else {
          return this.charError(buffer, i);
        }
      }
    } else if (this.tState === STRING1) {
      n = buffer[i];
      if (this.bytes_remaining > 0) {
        for (var j = 0; j < this.bytes_remaining; j++) {
          this.temp_buffs[this.bytes_in_sequence][this.bytes_in_sequence - this.bytes_remaining + j] = buffer[j];
        }
        this.appendStringBuf(this.temp_buffs[this.bytes_in_sequence]);
        this.bytes_in_sequence = this.bytes_remaining = 0;
        i = i + j - 1;
      } else if (this.bytes_remaining === 0 && n >= 128) {
        if (n <= 193 || n > 244) {
          return this.onError(new Error("Invalid UTF-8 character at position " + i + " in state " + Parser.toknam(this.tState)));
        }
        if (n >= 194 && n <= 223) this.bytes_in_sequence = 2;
        if (n >= 224 && n <= 239) this.bytes_in_sequence = 3;
        if (n >= 240 && n <= 244) this.bytes_in_sequence = 4;
        if (this.bytes_in_sequence + i > buffer.length) {
          for (var k = 0; k <= buffer.length - 1 - i; k++) {
            this.temp_buffs[this.bytes_in_sequence][k] = buffer[i + k];
          }
          this.bytes_remaining = i + this.bytes_in_sequence - buffer.length;
          i = buffer.length - 1;
        } else {
          this.appendStringBuf(buffer, i, i + this.bytes_in_sequence);
          i = i + this.bytes_in_sequence - 1;
        }
      } else if (n === 34) {
        this.tState = START;
        this.string += this.stringBuffer.toString("utf8", 0, this.stringBufferOffset);
        this.stringBufferOffset = 0;
        this.onToken(STRING, this.string);
        this.offset += Buffer.byteLength(this.string, "utf8") + 1;
        this.string = void 0;
      } else if (n === 92) {
        this.tState = STRING2;
      } else if (n >= 32) {
        this.appendStringChar(n);
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === STRING2) {
      n = buffer[i];
      if (n === 34) {
        this.appendStringChar(n);
        this.tState = STRING1;
      } else if (n === 92) {
        this.appendStringChar(BACK_SLASH);
        this.tState = STRING1;
      } else if (n === 47) {
        this.appendStringChar(FORWARD_SLASH);
        this.tState = STRING1;
      } else if (n === 98) {
        this.appendStringChar(BACKSPACE);
        this.tState = STRING1;
      } else if (n === 102) {
        this.appendStringChar(FORM_FEED);
        this.tState = STRING1;
      } else if (n === 110) {
        this.appendStringChar(NEWLINE);
        this.tState = STRING1;
      } else if (n === 114) {
        this.appendStringChar(CARRIAGE_RETURN);
        this.tState = STRING1;
      } else if (n === 116) {
        this.appendStringChar(TAB);
        this.tState = STRING1;
      } else if (n === 117) {
        this.unicode = "";
        this.tState = STRING3;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === STRING3 || this.tState === STRING4 || this.tState === STRING5 || this.tState === STRING6) {
      n = buffer[i];
      if (n >= 48 && n < 64 || n > 64 && n <= 70 || n > 96 && n <= 102) {
        this.unicode += String.fromCharCode(n);
        if (this.tState++ === STRING6) {
          var intVal = parseInt(this.unicode, 16);
          this.unicode = void 0;
          if (this.highSurrogate !== void 0 && intVal >= 56320 && intVal < 57343 + 1) {
            this.appendStringBuf(new Buffer(String.fromCharCode(this.highSurrogate, intVal)));
            this.highSurrogate = void 0;
          } else if (this.highSurrogate === void 0 && intVal >= 55296 && intVal < 56319 + 1) {
            this.highSurrogate = intVal;
          } else {
            if (this.highSurrogate !== void 0) {
              this.appendStringBuf(new Buffer(String.fromCharCode(this.highSurrogate)));
              this.highSurrogate = void 0;
            }
            this.appendStringBuf(new Buffer(String.fromCharCode(intVal)));
          }
          this.tState = STRING1;
        }
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === NUMBER1 || this.tState === NUMBER3) {
      n = buffer[i];
      switch (n) {
        case 48:
        case 49:
        case 50:
        case 51:
        case 52:
        case 53:
        case 54:
        case 55:
        case 56:
        case 57:
        case 46:
        case 101:
        case 69:
        case 43:
        case 45:
          this.string += String.fromCharCode(n);
          this.tState = NUMBER3;
          break;
        default:
          this.tState = START;
          var result = Number(this.string);
          if (isNaN(result)) {
            return this.charError(buffer, i);
          }
          if (this.string.match(/[0-9]+/) == this.string && result.toString() != this.string) {
            this.onToken(STRING, this.string);
          } else {
            this.onToken(NUMBER, result);
          }
          this.offset += this.string.length - 1;
          this.string = void 0;
          i--;
          break;
      }
    } else if (this.tState === TRUE1) {
      if (buffer[i] === 114) {
        this.tState = TRUE2;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === TRUE2) {
      if (buffer[i] === 117) {
        this.tState = TRUE3;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === TRUE3) {
      if (buffer[i] === 101) {
        this.tState = START;
        this.onToken(TRUE, true);
        this.offset += 3;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === FALSE1) {
      if (buffer[i] === 97) {
        this.tState = FALSE2;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === FALSE2) {
      if (buffer[i] === 108) {
        this.tState = FALSE3;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === FALSE3) {
      if (buffer[i] === 115) {
        this.tState = FALSE4;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === FALSE4) {
      if (buffer[i] === 101) {
        this.tState = START;
        this.onToken(FALSE, false);
        this.offset += 4;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === NULL1) {
      if (buffer[i] === 117) {
        this.tState = NULL2;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === NULL2) {
      if (buffer[i] === 108) {
        this.tState = NULL3;
      } else {
        return this.charError(buffer, i);
      }
    } else if (this.tState === NULL3) {
      if (buffer[i] === 108) {
        this.tState = START;
        this.onToken(NULL, null);
        this.offset += 3;
      } else {
        return this.charError(buffer, i);
      }
    }
  }
};
proto.onToken = function(token, value) {
};
proto.parseError = function(token, value) {
  this.tState = STOP;
  this.onError(new Error("Unexpected " + Parser.toknam(token) + (value ? "(" + JSON.stringify(value) + ")" : "") + " in state " + Parser.toknam(this.state)));
};
proto.push = function() {
  this.stack.push({ value: this.value, key: this.key, mode: this.mode });
};
proto.pop = function() {
  var value = this.value;
  var parent = this.stack.pop();
  this.value = parent.value;
  this.key = parent.key;
  this.mode = parent.mode;
  this.emit(value);
  if (!this.mode) {
    this.state = VALUE;
  }
};
proto.emit = function(value) {
  if (this.mode) {
    this.state = COMMA;
  }
  this.onValue(value);
};
proto.onValue = function(value) {
};
proto.onToken = function(token, value) {
  if (this.state === VALUE) {
    if (token === STRING || token === NUMBER || token === TRUE || token === FALSE || token === NULL) {
      if (this.value) {
        this.value[this.key] = value;
      }
      this.emit(value);
    } else if (token === LEFT_BRACE) {
      this.push();
      if (this.value) {
        this.value = this.value[this.key] = {};
      } else {
        this.value = {};
      }
      this.key = void 0;
      this.state = KEY;
      this.mode = OBJECT;
    } else if (token === LEFT_BRACKET) {
      this.push();
      if (this.value) {
        this.value = this.value[this.key] = [];
      } else {
        this.value = [];
      }
      this.key = 0;
      this.mode = ARRAY;
      this.state = VALUE;
    } else if (token === RIGHT_BRACE) {
      if (this.mode === OBJECT) {
        this.pop();
      } else {
        return this.parseError(token, value);
      }
    } else if (token === RIGHT_BRACKET) {
      if (this.mode === ARRAY) {
        this.pop();
      } else {
        return this.parseError(token, value);
      }
    } else {
      return this.parseError(token, value);
    }
  } else if (this.state === KEY) {
    if (token === STRING) {
      this.key = value;
      this.state = COLON;
    } else if (token === RIGHT_BRACE) {
      this.pop();
    } else {
      return this.parseError(token, value);
    }
  } else if (this.state === COLON) {
    if (token === COLON) {
      this.state = VALUE;
    } else {
      return this.parseError(token, value);
    }
  } else if (this.state === COMMA) {
    if (token === COMMA) {
      if (this.mode === ARRAY) {
        this.key++;
        this.state = VALUE;
      } else if (this.mode === OBJECT) {
        this.state = KEY;
      }
    } else if (token === RIGHT_BRACKET && this.mode === ARRAY || token === RIGHT_BRACE && this.mode === OBJECT) {
      this.pop();
    } else {
      return this.parseError(token, value);
    }
  } else {
    return this.parseError(token, value);
  }
};
Parser.C = C;
var jsonparse = Parser;
var Transform = import_stream.default.Transform;
var JSON2CSVTransform = function(_Transform) {
  _inherits(JSON2CSVTransform2, _Transform);
  function JSON2CSVTransform2(opts, transformOpts) {
    var _this;
    _classCallCheck(this, JSON2CSVTransform2);
    _this = _possibleConstructorReturn(this, _getPrototypeOf(JSON2CSVTransform2).call(this, transformOpts));
    Object.getOwnPropertyNames(JSON2CSVBase_1.prototype).forEach(function(key) {
      return _this[key] = JSON2CSVBase_1.prototype[key];
    });
    _this.opts = _this.preprocessOpts(opts);
    _this._data = "";
    _this._hasWritten = false;
    if (_this._readableState.objectMode) {
      _this.initObjectModeParse();
    } else if (_this.opts.ndjson) {
      _this.initNDJSONParse();
    } else {
      _this.initJSONParser();
    }
    if (_this.opts.withBOM) {
      _this.push("\uFEFF");
    }
    if (_this.opts.fields) {
      _this.opts.fields = _this.preprocessFieldsInfo(_this.opts.fields);
      _this.pushHeader();
    }
    return _this;
  }
  _createClass(JSON2CSVTransform2, [{
    key: "initObjectModeParse",
    value: function initObjectModeParse() {
      var transform = this;
      this.parser = {
        write: function write(line) {
          transform.pushLine(line);
        },
        getPendingData: function getPendingData() {
          return void 0;
        }
      };
    }
    /**
     * Init the transform with a parser to process NDJSON data.
     * It maintains a buffer of received data, parses each line
     * as JSON and send it to `pushLine for processing.
     */
  }, {
    key: "initNDJSONParse",
    value: function initNDJSONParse() {
      var transform = this;
      this.parser = {
        _data: "",
        write: function write(chunk) {
          this._data += chunk.toString();
          var lines = this._data.split("\n").map(function(line) {
            return line.trim();
          }).filter(function(line) {
            return line !== "";
          });
          var pendingData = false;
          lines.forEach(function(line, i) {
            try {
              transform.pushLine(JSON.parse(line));
            } catch (e) {
              if (i === lines.length - 1) {
                pendingData = true;
              } else {
                e.message = "Invalid JSON (".concat(line, ")");
                transform.emit("error", e);
              }
            }
          });
          this._data = pendingData ? this._data.slice(this._data.lastIndexOf("\n")) : "";
        },
        getPendingData: function getPendingData() {
          return this._data;
        }
      };
    }
    /**
     * Init the transform with a parser to process JSON data.
     * It maintains a buffer of received data, parses each as JSON 
     * item if the data is an array or the data itself otherwise
     * and send it to `pushLine` for processing.
     */
  }, {
    key: "initJSONParser",
    value: function initJSONParser() {
      var transform = this;
      this.parser = new jsonparse();
      this.parser.onValue = function(value) {
        if (this.stack.length !== this.depthToEmit) return;
        transform.pushLine(value);
      };
      this.parser._onToken = this.parser.onToken;
      this.parser.onToken = function(token, value) {
        transform.parser._onToken(token, value);
        if (this.stack.length === 0 && !transform.opts.fields && this.mode !== jsonparse.C.ARRAY && this.mode !== jsonparse.C.OBJECT) {
          this.onError(new Error('Data should not be empty or the "fields" option should be included'));
        }
        if (this.stack.length === 1) {
          if (this.depthToEmit === void 0) {
            this.depthToEmit = this.mode === jsonparse.C.ARRAY ? 1 : 0;
          }
          if (this.depthToEmit !== 0 && this.stack.length === 1) {
            this.value = void 0;
          }
        }
      };
      this.parser.getPendingData = function() {
        return this.value;
      };
      this.parser.onError = function(err) {
        if (err.message.includes("Unexpected")) {
          err.message = "Invalid JSON (".concat(err.message, ")");
        }
        transform.emit("error", err);
      };
    }
    /**
     * Main function that send data to the parse to be processed.
     *
     * @param {Buffer} chunk Incoming data
     * @param {String} encoding Encoding of the incoming data. Defaults to 'utf8'
     * @param {Function} done Called when the proceesing of the supplied chunk is done
     */
  }, {
    key: "_transform",
    value: function _transform(chunk, encoding, done) {
      this.parser.write(chunk);
      done();
    }
  }, {
    key: "_flush",
    value: function _flush(done) {
      if (this.parser.getPendingData()) {
        done(new Error("Invalid data received from stdin", this.parser.getPendingData()));
      }
      done();
    }
    /**
     * Generate the csv header and pushes it downstream.
     */
  }, {
    key: "pushHeader",
    value: function pushHeader() {
      if (this.opts.header) {
        var header = this.getHeader();
        this.emit("header", header);
        this.push(header);
        this._hasWritten = true;
      }
    }
    /**
     * Transforms an incoming json data to csv and pushes it downstream.
     *
     * @param {Object} data JSON object to be converted in a CSV row
     */
  }, {
    key: "pushLine",
    value: function pushLine(data) {
      var _this2 = this;
      var processedData = this.preprocessRow(data);
      if (!this._hasWritten) {
        this.opts.fields = this.opts.fields || this.preprocessFieldsInfo(Object.keys(processedData[0]));
        this.pushHeader();
      }
      processedData.forEach(function(row) {
        var line = _this2.processRow(row, _this2.opts);
        if (line === void 0) return;
        _this2.emit("line", line);
        _this2.push(_this2._hasWritten ? _this2.opts.eol + line : line);
        _this2._hasWritten = true;
      });
    }
  }]);
  return JSON2CSVTransform2;
}(Transform);
var JSON2CSVTransform_1 = JSON2CSVTransform;
var Transform$1 = import_stream.default.Transform;
var fastJoin$3 = utils.fastJoin;
var JSON2CSVAsyncParser = function() {
  function JSON2CSVAsyncParser2(opts, transformOpts) {
    _classCallCheck(this, JSON2CSVAsyncParser2);
    this.input = new Transform$1(transformOpts);
    this.input._read = function() {
    };
    this.transform = new JSON2CSVTransform_1(opts, transformOpts);
    this.processor = this.input.pipe(this.transform);
  }
  _createClass(JSON2CSVAsyncParser2, [{
    key: "fromInput",
    value: function fromInput(input) {
      if (this._input) {
        throw new Error("Async parser already has an input.");
      }
      this._input = input;
      this.input = this._input.pipe(this.processor);
      return this;
    }
  }, {
    key: "throughTransform",
    value: function throughTransform(transform) {
      if (this._output) {
        throw new Error("Can't add transforms once an output has been added.");
      }
      this.processor = this.processor.pipe(transform);
      return this;
    }
  }, {
    key: "toOutput",
    value: function toOutput(output) {
      if (this._output) {
        throw new Error("Async parser already has an output.");
      }
      this._output = output;
      this.processor = this.processor.pipe(output);
      return this;
    }
  }, {
    key: "promise",
    value: function promise() {
      var _this = this;
      var returnCSV = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
      return new Promise(function(resolve, reject) {
        if (!returnCSV) {
          _this.processor.on("finish", function() {
            return resolve();
          }).on("error", function(err) {
            return reject(err);
          });
          return;
        }
        var csvBuffer = [];
        _this.processor.on("data", function(chunk) {
          return csvBuffer.push(chunk.toString());
        }).on("finish", function() {
          return resolve(fastJoin$3(csvBuffer, ""));
        }).on("error", function(err) {
          return reject(err);
        });
      });
    }
  }]);
  return JSON2CSVAsyncParser2;
}();
var JSON2CSVAsyncParser_1 = JSON2CSVAsyncParser;
function flatten() {
  var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _ref$objects = _ref.objects, objects = _ref$objects === void 0 ? true : _ref$objects, _ref$arrays = _ref.arrays, arrays = _ref$arrays === void 0 ? false : _ref$arrays, _ref$separator = _ref.separator, separator = _ref$separator === void 0 ? "." : _ref$separator;
  function step(obj, flatDataRow, currentPath) {
    Object.keys(obj).forEach(function(key) {
      var newPath = currentPath ? "".concat(currentPath).concat(separator).concat(key) : key;
      var value = obj[key];
      if (objects && _typeof(value) === "object" && value !== null && !Array.isArray(value) && Object.prototype.toString.call(value.toJSON) !== "[object Function]" && Object.keys(value).length) {
        step(value, flatDataRow, newPath);
        return;
      }
      if (arrays && Array.isArray(value)) {
        step(value, flatDataRow, newPath);
        return;
      }
      flatDataRow[newPath] = value;
    });
    return flatDataRow;
  }
  return function(dataRow) {
    return step(dataRow, {});
  };
}
var flatten_1 = flatten;
var setProp$1 = utils.setProp;
var unsetProp$1 = utils.unsetProp;
var flattenReducer$3 = utils.flattenReducer;
function getUnwindablePaths(obj, currentPath) {
  return Object.keys(obj).reduce(function(unwindablePaths, key) {
    var newPath = currentPath ? "".concat(currentPath, ".").concat(key) : key;
    var value = obj[key];
    if (_typeof(value) === "object" && value !== null && !Array.isArray(value) && Object.prototype.toString.call(value.toJSON) !== "[object Function]" && Object.keys(value).length) {
      unwindablePaths = unwindablePaths.concat(getUnwindablePaths(value, newPath));
    } else if (Array.isArray(value)) {
      unwindablePaths.push(newPath);
      unwindablePaths = unwindablePaths.concat(value.map(function(arrObj) {
        return getUnwindablePaths(arrObj, newPath);
      }).reduce(flattenReducer$3, []).filter(function(item, index, arr) {
        return arr.indexOf(item) !== index;
      }));
    }
    return unwindablePaths;
  }, []);
}
function unwind() {
  var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _ref$paths = _ref.paths, paths = _ref$paths === void 0 ? void 0 : _ref$paths, _ref$blankOut = _ref.blankOut, blankOut = _ref$blankOut === void 0 ? false : _ref$blankOut;
  function unwindReducer(rows, unwindPath) {
    return rows.map(function(row) {
      var unwindArray = lodash_get(row, unwindPath);
      if (!Array.isArray(unwindArray)) {
        return row;
      }
      if (!unwindArray.length) {
        return unsetProp$1(row, unwindPath);
      }
      return unwindArray.map(function(unwindRow, index) {
        var clonedRow = blankOut && index > 0 ? {} : row;
        return setProp$1(clonedRow, unwindPath, unwindRow);
      });
    }).reduce(flattenReducer$3, []);
  }
  paths = Array.isArray(paths) ? paths : paths ? [paths] : void 0;
  return function(dataRow) {
    return (paths || getUnwindablePaths(dataRow)).reduce(unwindReducer, [dataRow]);
  };
}
var unwind_1 = unwind;
var Readable = import_stream.default.Readable;
var Parser$1 = JSON2CSVParser_1;
var AsyncParser = JSON2CSVAsyncParser_1;
var Transform$2 = JSON2CSVTransform_1;
var parse = function parse2(data, opts) {
  return new JSON2CSVParser_1(opts).parse(data);
};
var parseAsync = function parseAsync2(data, opts, transformOpts) {
  try {
    if (!(data instanceof Readable)) {
      transformOpts = Object.assign({}, transformOpts, {
        objectMode: true
      });
    }
    var asyncParser = new JSON2CSVAsyncParser_1(opts, transformOpts);
    var promise = asyncParser.promise();
    if (Array.isArray(data)) {
      data.forEach(function(item) {
        return asyncParser.input.push(item);
      });
      asyncParser.input.push(null);
    } else if (data instanceof Readable) {
      asyncParser.fromInput(data);
    } else {
      asyncParser.input.push(data);
      asyncParser.input.push(null);
    }
    return promise;
  } catch (err) {
    return Promise.reject(err);
  }
};
var transforms = {
  flatten: flatten_1,
  unwind: unwind_1
};
var json2csv = {
  Parser: Parser$1,
  AsyncParser,
  Transform: Transform$2,
  parse,
  parseAsync,
  transforms
};
var json2csv_esm_default = json2csv;
export {
  AsyncParser,
  Parser$1 as Parser,
  Transform$2 as Transform,
  json2csv_esm_default as default,
  parse,
  parseAsync,
  transforms
};
//# sourceMappingURL=json2csv.js.map
