import { GalleryItemDto } from "../components/blocks/marketing/gallery/GalleryBlockUtils";

export const defaultGallery: GalleryItemDto[] = [
  { type: "image", title: "cover", src: "https://yahooder.sirv.com/saasrock/launch/images/1-cover.png " },
  { type: "image", title: "admin-dashboard", src: "https://yahooder.sirv.com/saasrock/launch/images/2-admin-dashboard.png " },
  { type: "image", title: "stripe-subscriptions", src: "https://yahooder.sirv.com/saasrock/launch/images/3-stripe-subscriptions.png " },
  { type: "image", title: "workflows", src: "https://yahooder.sirv.com/saasrock/launch/images/4-workflows.png " },
  { type: "image", title: "notifications", src: "https://yahooder.sirv.com/saasrock/launch/images/5-notifications.png " },
  { type: "image", title: "email-marketing", src: "https://yahooder.sirv.com/saasrock/launch/images/6-email-marketing.png " },
  { type: "image", title: "no-code-builder", src: "https://yahooder.sirv.com/saasrock/launch/images/7-no-code-builder.png " },
  { type: "image", title: "api-keys", src: "https://yahooder.sirv.com/saasrock/launch/images/8-api-keys.png " },
  { type: "image", title: "accounts-management", src: "https://yahooder.sirv.com/saasrock/launch/images/9-accounts-management.png " },
  { type: "image", title: "roles-and-permissions", src: "https://yahooder.sirv.com/saasrock/launch/images/10-roles-and-permissions.png " },
  { type: "image", title: "metrics-tracker", src: "https://yahooder.sirv.com/saasrock/launch/images/11-metrics-tracker.png " },
  { type: "image", title: "cache", src: "https://yahooder.sirv.com/saasrock/launch/images/12-cache.png " },
  { type: "image", title: "events", src: "https://yahooder.sirv.com/saasrock/launch/images/13-events.png " },
  { type: "image", title: "onboarding-flows", src: "https://yahooder.sirv.com/saasrock/launch/images/14-onboarding-flows.png " },
  { type: "image", title: "stepFormWizard-flows", src: "https://yahooder.sirv.com/saasrock/launch/images/14-stepFormWizard-flows.png " },
  { type: "image", title: "feature-flags", src: "https://yahooder.sirv.com/saasrock/launch/images/15-feature-flags.png " },
  { type: "image", title: "app-dashboard", src: "https://yahooder.sirv.com/saasrock/launch/images/16-app-dashboard.png " },
  { type: "image", title: "marketing-pages", src: "https://yahooder.sirv.com/saasrock/launch/images/17-marketing-pages.png " },
  { type: "image", title: "advanced-pricing", src: "https://yahooder.sirv.com/saasrock/launch/images/18-advanced-pricing.png " },
  { type: "image", title: "built-in-blog", src: "https://yahooder.sirv.com/saasrock/launch/images/19-built-in-blog.png " },
  { type: "image", title: "build-in-knowledge-base", src: "https://yahooder.sirv.com/saasrock/launch/images/20-build-in-knowledge-base.png " },
  { type: "image", title: "page-blocks-builder", src: "https://yahooder.sirv.com/saasrock/launch/images/21-page-blocks-builder.png " },
  { type: "image", title: "seo-optimized", src: "https://yahooder.sirv.com/saasrock/launch/images/22-seo-optimized.png " },
  { type: "image", title: "5-star-boilerplate", src: "https://yahooder.sirv.com/saasrock/launch/images/23-5-star-boilerplate.png" },
  { type: "image", title: "remix-stack", src: "https://yahooder.sirv.com/saasrock/launch/images/24-remix-stack.png " },
];
