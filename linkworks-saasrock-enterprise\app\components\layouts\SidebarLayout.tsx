import { Transition } from "@headlessui/react";
import { Fragment, ReactNode, useEffect, useRef, useState } from "react";
import SidebarMenu from "./SidebarMenu";
import ProfileButton from "./buttons/ProfileButton";
import QuickActionsButton from "./buttons/QuickActionsButton";
import CurrentSubscriptionButton from "./buttons/CurrentSubscriptionButton";
import TenantSelect from "./selectors/TenantSelect";
import { Link, useNavigate, useParams } from "react-router";
import LogoDark from "~/assets/img/logo-dark.png";
import SearchButton from "./buttons/SearchButton";
import { useTitleData } from "~/utils/data/useTitleData";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import ChatSupportButton from "./buttons/ChatSupportButton";
import { useRootData } from "~/utils/data/useRootData";
import NotificationsButton from "./buttons/NotificationsButton";
import OnboardingButton from "./buttons/OnboardingButton";
import OnboardingSession from "~/modules/onboarding/components/OnboardingSession";
import { useKBar } from "kbar";
import { getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import SaasRockUpdateButton from "./buttons/SaasRockUpdateButton";
import AddFeedbackButton from "./buttons/AddFeedbackButton";
import { SideBarItem } from "~/application/sidebar/SidebarItem";
import clsx from "clsx";
import ThemeSelector from "../ui/selectors/ThemeSelector";
import { Inbox } from "@novu/react";

interface Props {
  layout: "app" | "admin" | "docs";
  children: ReactNode;
  menuItems?: SideBarItem[];
  className?: string;
}

export default function SidebarLayout({ layout, children, menuItems, className = "h-screen" }: Props) {
  const { query } = useKBar();

  const rootData = useRootData();
  const appOrAdminData = useAppOrAdminData();
  const { appConfiguration } = useRootData();
  const params = useParams();
  const title = useTitleData() ?? "";

  const mainElement = useRef<HTMLElement>(null);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [onboardingModalOpen, setOnboardingModalOpen] = useState(false);

  useEffect(() => {
    try {
      // @ts-ignore
      $crisp?.push(["do", "chat:hide"]);
    } catch (e) {
      // ignore
    }
  }, []);

  function onOpenCommandPalette() {
    query.toggle();
  }

  function getLogoDarkMode() {
    if (appConfiguration.branding.logoDarkMode?.length) {
      return appConfiguration.branding.logoDarkMode;
    }
    if (appConfiguration.branding.logo?.length) {
      return appConfiguration.branding.logo;
    }
    return LogoDark;
  }
  return (
    <Fragment>
      <OnboardingSession open={onboardingModalOpen} setOpen={setOnboardingModalOpen} />

      <div
        className={clsx("text-foreground bg-secondary/90 flex overflow-hidden", className)}
        style={{
          colorScheme: "light",
        }}
      >
        {/*Mobile sidebar */}
        <div className="md:hidden">
          {sidebarOpen && (
            <div className="fixed inset-0 z-40 flex">
              <Transition
                as={Fragment}
                show={sidebarOpen}
                enter="transition-opacity ease-linear duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transition-opacity ease-linear duration-300"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0">
                  <div className="bg-foreground/90 absolute inset-0 opacity-75" />
                </div>
              </Transition>

              <Transition
                as={Fragment}
                show={sidebarOpen}
                enter="transition ease-in-out duration-300 transform"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
              >
                <div className="relative flex w-full max-w-xs flex-1 flex-col bg-gray-900">
                  <div className="absolute top-0 right-0 mt-2 -mr-14 p-1">
                    <button
                      className="flex h-12 w-12 items-center justify-center rounded-sm focus:bg-gray-600 focus:outline-hidden"
                      aria-label="Close sidebar"
                      onClick={() => setSidebarOpen(!sidebarOpen)}
                    >
                      <svg className="h-7 w-7 text-white" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div className="mt-5 h-0 flex-1 overflow-y-auto">
                    <nav className="space-y-3 px-2">
                      <div className="flex flex-col space-y-2">
                        <Link to={"/"}>
                          <img
                            className={"mx-auto h-8 w-auto"}
                            src={appConfiguration.branding.logoDarkMode || appConfiguration.branding.logo || LogoDark}
                            alt="Logo"
                          />
                        </Link>
                      </div>
                      <SidebarMenu layout={layout} onSelected={() => setSidebarOpen(!sidebarOpen)} menuItems={menuItems} />
                    </nav>
                  </div>
                  {layout == "app" && <TenantSelect onOpenCommandPalette={onOpenCommandPalette} />}
                </div>
              </Transition>
              <div className="w-14 shrink-0">{/*Dummy element to force sidebar to shrink to fit close icon */}</div>
            </div>
          )}
        </div>

        {/*Desktop sidebar */}
        <div
          className={
            sidebarOpen
              ? "hidden transition duration-1000 ease-in"
              : "border-border dark:border-border hidden overflow-x-hidden border-r shadow-2xs md:flex md:shrink-0 dark:border-r-0 dark:shadow-lg"
          }
        >
          <div className="flex w-64 flex-col">
            <div className="bg-theme-600 flex h-0 flex-1 flex-col shadow-md">
              <div className="flex flex-1 flex-col overflow-y-auto">
                <nav className="flex-1 space-y-3 bg-gray-900 px-2 py-4 select-none">
                  <div className="flex flex-col space-y-2">
                    <Link to={"/"}>
                      <img className={"mx-auto h-8 w-auto"} src={getLogoDarkMode()} alt="Logo" />
                    </Link>
                  </div>
                  <SidebarMenu layout={layout} menuItems={menuItems} />
                </nav>
              </div>
            </div>

            {layout == "app" && <TenantSelect onOpenCommandPalette={onOpenCommandPalette} />}
          </div>
        </div>

        {/*Content */}
        <div className="flex w-0 flex-1 flex-col overflow-hidden">
          <div className="border-border bg-background relative flex h-14 shrink-0 border-b shadow-inner">
            <button
              className="border-border text-muted-foreground focus:text-muted-foreground focus:bg-secondary/90 border-r px-4 focus:outline-hidden"
              aria-label="Open sidebar"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <svg className="h-5 w-5" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </button>

            <NavBar
              layout={layout}
              title={title}
              buttons={{
                mySubscription: getUserHasPermission(appOrAdminData, "app.settings.subscription.update"),
                feedback: rootData.appConfiguration.app.features.tenantFeedback,
                chatSupport: !!rootData.chatWebsiteId,
                quickActions: appOrAdminData?.entities?.filter((f) => f.showInSidebar).length > 0,
                search: true,
                notifications: appConfiguration.notifications.enabled && (layout === "admin" || layout === "app"),
                onboarding: appConfiguration.onboarding.enabled,
              }}
              onOpenCommandPalette={onOpenCommandPalette}
              onOpenOnboardingModal={() => setOnboardingModalOpen(true)}
            />
          </div>

          <main ref={mainElement} className="bg-secondary flex-1 overflow-y-auto focus:outline-hidden" tabIndex={0}>
            <div key={params.tenant} className="pb-20 sm:pb-0">
              {children}
            </div>
          </main>
        </div>
      </div>
    </Fragment>
  );
}

function NavBar({
  layout,
  title,
  buttons,
  onOpenCommandPalette,
  onOpenOnboardingModal,
}: {
  layout?: string;
  title?: string;
  buttons: {
    mySubscription: boolean;
    feedback: boolean;
    chatSupport: boolean;
    quickActions: boolean;
    search: boolean;
    notifications: boolean;
    onboarding: boolean;
  };
  onOpenCommandPalette: () => void;
  onOpenOnboardingModal: () => void;
}) {
  const appOrAdminData = useAppOrAdminData();
  const rootData = useRootData();
  const navigate = useNavigate();
  return (
    <div className="flex flex-1 justify-between space-x-2 px-3">
      <div className="flex flex-1 items-center">
        <div className="font-extrabold">{title}</div>
      </div>
      <div className="flex items-center space-x-2 md:ml-6">
        {buttons.onboarding && appOrAdminData?.onboardingSession && (
          <OnboardingButton item={appOrAdminData?.onboardingSession} onClick={onOpenOnboardingModal} />
        )}
        {layout === "app" && buttons.mySubscription && <CurrentSubscriptionButton />}
        {buttons.notifications && appOrAdminData?.user && (
          <Inbox
            applicationIdentifier={rootData?.appConfiguration.notifications.novuAppId || ""}
            subscriberId={appOrAdminData?.user.id || ""}
            routerPush={(path: string) => navigate(path)}
          />
        )}
        {buttons.search && <SearchButton onClick={onOpenCommandPalette} />}
        {layout === "app" && buttons.feedback && <AddFeedbackButton />}
        {layout === "app" && buttons.chatSupport && <ChatSupportButton />}
        {layout === "app" && buttons.quickActions && <QuickActionsButton entities={appOrAdminData?.entities?.filter((f) => f.showInSidebar)} />}
        {(layout === "app" || layout === "admin") && <ThemeSelector variant="secondary" />}
        {(layout === "app" || layout === "admin") && <ProfileButton user={appOrAdminData?.user} layout={layout} />}
      </div>
    </div>
  );
}
