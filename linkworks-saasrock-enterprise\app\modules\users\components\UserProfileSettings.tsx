import { Form, useLocation, useNavigation, useSearchParams, useSubmit } from "react-router";
import { useRef, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import InputSelect from "~/components/ui/input/InputSelect";
import InputText from "~/components/ui/input/InputText";
import ConfirmModal, { RefConfirmModal } from "~/components/ui/modals/ConfirmModal";
import ErrorModal, { RefErrorModal } from "~/components/ui/modals/ErrorModal";
import SuccessModal, { RefSuccessModal } from "~/components/ui/modals/SuccessModal";
import SettingSection from "~/components/ui/sections/SettingSection";
import UploadImage from "~/components/ui/uploaders/UploadImage";
import { i18nConfig } from "~/locale/i18n";
import InputEmail from "~/components/ui/input/subtypes/InputEmail";
import InputMedia from "~/components/ui/input/InputMedia";
import { EyeClose, EyeOpen } from "~/custom/components/OnBoarding/Svg";

export default function UserProfileSettings({
  user,
}: {
  user: {
    email: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
    admin?: any | null;
  };
}) {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const submit = useSubmit();

  const errorModal = useRef<RefErrorModal>(null);
  const successModal = useRef<RefSuccessModal>(null);
  const confirmModal = useRef<RefConfirmModal>(null);

  const inputFirstName = useRef<HTMLInputElement>(null);

  useEffect(() => {
    inputFirstName.current?.focus();
    inputFirstName.current?.select();
  }, []);

  const [avatar, setAvatar] = useState<string | undefined>(user?.avatar ?? undefined);
  const [showUploadImage, setShowUploadImage] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [newConfirmPasswordVisible, setNewConfirmPasswordVisible] = useState(false);
  const [passwordVaildation, setPasswordValidation] = useState({
    confirmPassword: "",
    newPassword: "",
    newConfirmPassword: "",
  });
  const inputMediaRef = useRef(null);

  const defaultLocale = i18nConfig.supportedLngs[0];
  function changedLocale(locale: string) {
    const form = new FormData();
    form.set("action", "setLocale");
    searchParams.set("lng", locale);
    form.set("redirect", location.pathname + "?" + searchParams.toString());
    form.set("lng", locale);
    submit(form, { method: "post", action: "/", preventScrollReset: true });
  }

  function deleteAccount() {
    if (user?.admin) {
      errorModal.current?.show(t("settings.profile.errors.cannotDeleteAdmin"));
    } else {
      confirmModal.current?.show(t("settings.danger.confirmDelete"), t("shared.confirm"), t("shared.cancel"), t("shared.warningCannotUndo"));
    }
  }
  function confirmDelete() {
    const form = new FormData();
    form.set("action", "deleteAccount");
    submit(form, { method: "post" });
  }
  function loadedImage(image: string | undefined) {
    setAvatar(image);
    setUploadingImage(true);
  }
  const handleCancel = () => {
    setPasswordValidation({ confirmPassword: "", newPassword: "", newConfirmPassword: "" });
  };

  const handleDropdownCancel = () => {
    changedLocale(defaultLocale);
  };
  const avatarInputRef = useRef<{ reset: () => void }>(null);

  const handleDelete = () => {
    loadedImage(""); // Clear the avatar
    avatarInputRef.current?.reset(); // Reset the InputMedia component
  };

  return (
    <div>
      <div>
        <SettingSection title={t("settings.profile.profileTitle")} description={t("settings.profile.profileText")}>
          <Form method="post" ref={inputMediaRef}>
            <div className="shadow-input mt-2 rounded-md shadow-sm md:col-span-2 md:mt-0">
              <input hidden type="text" name="action" value="profile" readOnly />
              <div className="p-3 md:p-6">
                <div className="">
                  <div className="grid grid-cols-6 gap-5">
                    <div className="col-span-6 md:col-span-3">
                      <InputText name="firstName" title={t("settings.profile.firstName")} defaultValue={user?.firstName} required />
                    </div>

                    <div className="col-span-6 md:col-span-3">
                      <InputText name="lastName" title={t("settings.profile.lastName")} defaultValue={user?.lastName} />
                    </div>
                    <div className="col-span-6 sm:col-span-6 md:col-span-6">
                      <InputEmail
                        type="email"
                        name="email"
                        title={t("account.shared.emailAddress")}
                        className="border-none"
                        defaultValue={user?.email}
                        required
                      />
                    </div>

                    <div className="col-span-6 sm:col-span-6">
                      <InputMedia
                        name="avatar"
                        title={t("shared.avatar")}
                        initialMedia={
                          avatar
                            ? [
                                {
                                  file: avatar,
                                  name: "avatar",
                                  type: avatar.startsWith("data:image/png") ? "image/png" : avatar.startsWith("data:image/jpg") ? "image/jpg" : "image/jpeg",
                                  title: "Avatar",
                                },
                              ]
                            : []
                        }
                        onSelected={(media) => {
                          if (media.length > 0) {
                            loadedImage(media[0].file);
                          } else {
                            loadedImage("");
                          }
                        }}
                        max={1}
                        accept="png, jpg, jpeg"
                        readOnly={false}
                        maxSize={10}
                        ref={avatarInputRef} // Pass the ref to the InputMedia component
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-border dark:border-border/50 mt-3 block border-t p-3 md:px-6">
                <div className="pt-3">
                  <div className="my-2 flex items-center justify-end space-x-2">
                    <div id="form-reset-message" className="flex items-center space-x-2"></div>
                    <button
                      disabled={navigation.state === "submitting"}
                      onClick={handleDelete} // Add the onClick handler to the button
                      type="reset"
                      className="border-input text-foreground bg-primary-foreground p flex h-8 w-16 cursor-pointer items-center justify-center gap-2 rounded-sm border-1 px-6 py-4.5 text-sm font-normal"
                    >
                      {t("shared.cancel")}
                    </button>

                    <button
                      disabled={navigation.state === "submitting"}
                      type="submit"
                      className="bg-primary hover:bg-primary/90 focus:ring-primary text-primary-foreground inline-flex cursor-pointer items-center space-x-2 rounded-md border border-transparent px-4 py-2 text-sm font-medium focus:ring-2 focus:ring-offset-2 focus:outline-hidden"
                    >
                      {t("shared.saveDetails")}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        </SettingSection>

        {/*Separator */}
        <div className="block">
          <div className="py-5"></div>
        </div>

        <SettingSection
          title={t("settings.profile.securityTitle")}
          description={
            <p className="text-muted-foreground mt-0.5 text-sm leading-5">
              {t("account.login.forgot")}{" "}
              <a className="text-primary mt-2 block font-bold underline hover:underline" href={"/forgot-password?e=" + user?.email || ""}>
                {t("account.reset.button")}
              </a>
            </p>
          }
        >
          {/*Security */}
          <div className="shadow-input rounded-md shadow-sm md:col-span-2 md:mt-0">
            <Form method="post">
              <input hidden type="text" name="action" value="password" readOnly />
              <div className="">
                <div className="">
                  <div className="p-3 md:px-6">
                    <div className="grid grid-cols-6 gap-2">
                      <div className="col-span-6 sm:col-span-6">
                        <div className="relative mt-4">
                          <InputText
                            title={t("settings.profile.passwordCurrent")}
                            type={passwordVisible ? "text" : "password"}
                            id="passwordCurrent"
                            name="passwordCurrent"
                            placeholder="Enter password"
                            className="bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                            disabled={navigation.state !== "idle"}
                            defaultValue=""
                            required
                            value={passwordVaildation.newPassword}
                            setValue={(e) => setPasswordValidation({ ...passwordVaildation, newPassword: e.toString() })}
                          />
                          {passwordVaildation.newPassword.length > 0 && (
                            <button
                              type="button"
                              onClick={() => setPasswordVisible(!passwordVisible)}
                              className={`absolute top-10 right-2 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center`}
                            >
                              {passwordVisible ? <EyeClose /> : <EyeOpen />}
                            </button>
                          )}
                        </div>
                      </div>
                      <div className="col-span-6 space-y-6 md:col-span-3">
                        <div className="relative mt-4">
                          <InputText
                            title={t("settings.profile.newPassword")}
                            type={confirmPasswordVisible ? "text" : "password"}
                            id="passwordNew"
                            name="passwordNew"
                            placeholder="Enter password"
                            className="bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                            disabled={navigation.state !== "idle"}
                            defaultValue=""
                            required
                            value={passwordVaildation.confirmPassword}
                            setValue={(e) => setPasswordValidation({ ...passwordVaildation, confirmPassword: e.toString() })}
                          />
                          {passwordVaildation.confirmPassword.length > 0 && (
                            <button
                              type="button"
                              onClick={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                              className={`absolute top-10 right-2 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center`}
                            >
                              {confirmPasswordVisible ? <EyeClose /> : <EyeOpen />}
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="col-span-6 md:col-span-3">
                        <div className="relative mt-4">
                          <InputText
                            title={t("settings.profile.passwordConfirm")}
                            type={newConfirmPasswordVisible ? "text" : "password"}
                            id="passwordNewConfirm"
                            name="passwordNewConfirm"
                            placeholder="Enter password"
                            className="bg-background placeholder:text-muted-foreground/45 focus:ring-foreground mt-1 mb-2 h-10 w-full cursor-pointer rounded-[4px] text-sm focus:rounded-[4px] focus:border-0 focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                            disabled={navigation.state !== "idle"}
                            defaultValue=""
                            required
                            value={passwordVaildation.newConfirmPassword}
                            setValue={(e) => setPasswordValidation({ ...passwordVaildation, newConfirmPassword: e.toString() })}
                          />
                          {passwordVaildation.newConfirmPassword.length > 0 && (
                            <button
                              type="button"
                              onClick={() => setNewConfirmPasswordVisible(!newConfirmPasswordVisible)}
                              className={`absolute top-10 right-2 flex h-5 w-5 -translate-y-1/2 cursor-pointer items-center justify-center`}
                            >
                              {newConfirmPasswordVisible ? <EyeClose /> : <EyeOpen />}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 pt-3">
                    <div className="border-border dark:border-border/50 mt-3 block border-t p-3 md:px-6">
                      <div className="my-2 mt-2 flex justify-end space-x-2">
                        <div id="form-reset-message" className="flex items-center space-x-2"></div>
                        <button
                          disabled={navigation.state === "submitting"}
                          type="reset"
                          onClick={handleCancel}
                          className="border-input text-foreground bg-primary-foreground p flex h-8 w-16 cursor-pointer items-center justify-center gap-2 rounded-sm border-1 px-6 py-4.5 text-sm font-normal"
                        >
                          {t("shared.cancel")}
                        </button>
                        <button
                          type="submit"
                          className="bg-primary hover:bg-primary/90 focus:ring-primary text-primary-foreground inline-flex cursor-pointer items-center space-x-2 rounded-md border border-transparent px-4 py-2 text-sm font-medium focus:ring-2 focus:ring-offset-2 focus:outline-hidden"
                        >
                          {t("shared.saveDetails")}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </SettingSection>

        {/*Separator */}
        <div className="block">
          <div className="py-5"></div>
        </div>

        <SettingSection title={t("settings.preferences.title")} description={t("settings.preferences.description")}>
          {/*Preferences */}
          <div className="shadow-input rounded-md shadow-sm md:col-span-2 md:mt-0">
            <div className="p-3 sm:px-6">
              <div className="">
                <div className="grid grid-cols-6 gap-2">
                  <div className="col-span-6 sm:col-span-6">
                    <label htmlFor="locale" className="mb-1 block text-xs leading-5 font-medium">
                      {t("settings.preferences.language")}
                    </label>
                    <InputSelect
                      required
                      defaultValue={t("shared.locales.select")}
                      value={i18n.language}
                      setValue={(e) => changedLocale(e?.toString() || "")}
                      options={i18nConfig.supportedLngs.map((locale) => ({ value: locale, name: t("shared.locales." + locale) }))}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-2 pt-3">
              <div className="border-border dark:border-border/50 mt-3 block border-t p-3 sm:px-6">
                <div className="my-2 mt-2 flex justify-end space-x-2">
                  <div id="form-reset-message" className="flex items-center space-x-2"></div>
                  <button
                    disabled={navigation.state === "submitting"}
                    type="reset"
                    onClick={handleDropdownCancel}
                    className="border-input text-foreground bg-primary-foreground p flex h-8 w-16 cursor-pointer items-center justify-center gap-2 rounded-sm border-1 px-6 py-4.5 text-sm font-normal"
                  >
                    {t("shared.cancel")}
                  </button>
                  <button
                    type="submit"
                    className="bg-primary hover:bg-primary/90 focus:ring-primary text-primary-foreground inline-flex cursor-pointer items-center space-x-2 rounded-md border border-transparent px-4 py-2 text-sm font-medium focus:ring-2 focus:ring-offset-2 focus:outline-hidden"
                  >
                    {t("shared.saveDetails")}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </SettingSection>

        {/*Separator */}
        <div className="block">
          <div className="py-5"></div>
        </div>

        <SettingSection title={t("settings.danger.title")} description={t("settings.danger.description")}>
          {/*Danger */}
          <div className="shadow-input rounded-md p-6 shadow-sm md:col-span-2 md:mt-0">
            <div>
              <input hidden type="text" name="action" value="deleteAccount" readOnly />
              <div className="">
                <div className="">
                  {/* <h3 className="text-lg font-medium leading-6 text-foreground">{t("settings.danger.deleteYourAccount")}</h3>
                  <div className="mt-2 max-w-xl text-sm leading-5 text-muted-foreground">
                    <p>{t("settings.danger.onceYouDelete")}.</p>
                  </div> */}
                  <div className="sm:px-6">
                    <ButtonPrimary
                      destructive={true}
                      className="bg-primary hover:bg-primary/90 focus:ring-primary text-primary-foreground cursor-pointer"
                      onClick={deleteAccount}
                    >
                      {t("settings.danger.deleteAccount")}
                    </ButtonPrimary>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SettingSection>
      </div>
      {showUploadImage && !uploadingImage && (
        <UploadImage onClose={() => setShowUploadImage(false)} title={t("shared.avatar")} initialImage={avatar} onLoaded={loadedImage} />
      )}
      <SuccessModal ref={successModal} />
      <ErrorModal ref={errorModal} />
      <ConfirmModal ref={confirmModal} onYes={confirmDelete} />
    </div>
  );
}
