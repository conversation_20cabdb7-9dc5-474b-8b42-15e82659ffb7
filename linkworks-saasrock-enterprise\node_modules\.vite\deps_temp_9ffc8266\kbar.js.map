{"version": 3, "sources": ["../../kbar/lib/utils.js", "../../fast-equals/src/utils.ts", "../../fast-equals/src/comparator.ts", "../../fast-equals/src/index.ts", "../../tiny-invariant/dist/tiny-invariant.cjs.js", "../../kbar/lib/action/Command.js", "../../kbar/lib/action/ActionImpl.js", "../../kbar/lib/action/ActionInterface.js", "../../kbar/lib/action/HistoryImpl.js", "../../kbar/lib/types.js", "../../kbar/lib/useStore.js", "../../kbar/lib/tinykeys.js", "../../kbar/lib/InternalEvents.js", "../../kbar/lib/KBarContextProvider.js", "../../kbar/lib/useKBar.js", "../../fuse.js/dist/fuse.esm.js", "../../kbar/lib/useMatches.js", "../../@radix-ui/react-compose-refs/src/index.ts", "../../@radix-ui/react-compose-refs/src/compose-refs.tsx", "../../@radix-ui/react-slot/src/index.ts", "../../@radix-ui/react-slot/src/slot.tsx", "../../@radix-ui/react-primitive/src/index.ts", "../../@radix-ui/react-primitive/src/primitive.tsx", "../../@radix-ui/react-use-layout-effect/src/index.ts", "../../@radix-ui/react-use-layout-effect/src/use-layout-effect.tsx", "../../@radix-ui/react-portal/src/index.ts", "../../@radix-ui/react-portal/src/portal.tsx", "../../kbar/lib/KBarPortal.js", "../../kbar/lib/KBarPositioner.js", "../../kbar/lib/KBarSearch.js", "../../react-virtual/node_modules/@reach/observe-rect/dist/observe-rect.esm.js", "../../react-virtual/src/useIsomorphicLayoutEffect.js", "../../react-virtual/src/useRect.js", "../../react-virtual/src/index.js", "../../kbar/lib/KBarResults.js", "../../kbar/lib/useRegisterActions.js", "../../kbar/lib/KBarAnimator.js", "../../kbar/lib/action/index.js", "../../kbar/lib/index.js"], "sourcesContent": ["\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Priority = exports.isModKey = exports.shouldRejectKeystrokes = exports.useThrottledValue = exports.getScrollbarWidth = exports.useIsomorphicLayout = exports.noop = exports.createAction = exports.randomId = exports.usePointerMovedSinceMount = exports.useOuterClick = exports.swallowEvent = void 0;\nvar React = __importStar(require(\"react\"));\nfunction swallowEvent(event) {\n    event.stopPropagation();\n    event.preventDefault();\n}\nexports.swallowEvent = swallowEvent;\nfunction useOuterClick(dom, cb) {\n    var cbRef = React.useRef(cb);\n    cbRef.current = cb;\n    React.useEffect(function () {\n        function handler(event) {\n            var _a, _b;\n            if (((_a = dom.current) === null || _a === void 0 ? void 0 : _a.contains(event.target)) ||\n                // Add support for ReactShadowRoot\n                // @ts-expect-error wrong types, the `host` property exists https://stackoverflow.com/a/25340456\n                event.target === ((_b = dom.current) === null || _b === void 0 ? void 0 : _b.getRootNode().host)) {\n                return;\n            }\n            event.preventDefault();\n            event.stopPropagation();\n            cbRef.current();\n        }\n        window.addEventListener(\"pointerdown\", handler, true);\n        return function () { return window.removeEventListener(\"pointerdown\", handler, true); };\n    }, [dom]);\n}\nexports.useOuterClick = useOuterClick;\nfunction usePointerMovedSinceMount() {\n    var _a = React.useState(false), moved = _a[0], setMoved = _a[1];\n    React.useEffect(function () {\n        function handler() {\n            setMoved(true);\n        }\n        if (!moved) {\n            window.addEventListener(\"pointermove\", handler);\n            return function () { return window.removeEventListener(\"pointermove\", handler); };\n        }\n    }, [moved]);\n    return moved;\n}\nexports.usePointerMovedSinceMount = usePointerMovedSinceMount;\nfunction randomId() {\n    return Math.random().toString(36).substring(2, 9);\n}\nexports.randomId = randomId;\nfunction createAction(params) {\n    return __assign({ id: randomId() }, params);\n}\nexports.createAction = createAction;\nfunction noop() { }\nexports.noop = noop;\nexports.useIsomorphicLayout = typeof window === \"undefined\" ? noop : React.useLayoutEffect;\n// https://stackoverflow.com/questions/13382516/getting-scroll-bar-width-using-javascript\nfunction getScrollbarWidth() {\n    var outer = document.createElement(\"div\");\n    outer.style.visibility = \"hidden\";\n    outer.style.overflow = \"scroll\";\n    document.body.appendChild(outer);\n    var inner = document.createElement(\"div\");\n    outer.appendChild(inner);\n    var scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n    outer.parentNode.removeChild(outer);\n    return scrollbarWidth;\n}\nexports.getScrollbarWidth = getScrollbarWidth;\nfunction useThrottledValue(value, ms) {\n    if (ms === void 0) { ms = 100; }\n    var _a = React.useState(value), throttledValue = _a[0], setThrottledValue = _a[1];\n    var lastRan = React.useRef(Date.now());\n    React.useEffect(function () {\n        if (ms === 0)\n            return;\n        var timeout = setTimeout(function () {\n            setThrottledValue(value);\n            lastRan.current = Date.now();\n        }, lastRan.current - (Date.now() - ms));\n        return function () {\n            clearTimeout(timeout);\n        };\n    }, [ms, value]);\n    return ms === 0 ? value : throttledValue;\n}\nexports.useThrottledValue = useThrottledValue;\nfunction shouldRejectKeystrokes(_a) {\n    var _b, _c, _d;\n    var _e = _a === void 0 ? { ignoreWhenFocused: [] } : _a, ignoreWhenFocused = _e.ignoreWhenFocused;\n    var inputs = __spreadArray([\"input\", \"textarea\"], ignoreWhenFocused, true).map(function (el) {\n        return el.toLowerCase();\n    });\n    var activeElement = document.activeElement;\n    var ignoreStrokes = activeElement &&\n        (inputs.indexOf(activeElement.tagName.toLowerCase()) !== -1 ||\n            ((_b = activeElement.attributes.getNamedItem(\"role\")) === null || _b === void 0 ? void 0 : _b.value) === \"textbox\" ||\n            ((_c = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _c === void 0 ? void 0 : _c.value) ===\n                \"true\" ||\n            ((_d = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _d === void 0 ? void 0 : _d.value) ===\n                \"plaintext-only\");\n    return ignoreStrokes;\n}\nexports.shouldRejectKeystrokes = shouldRejectKeystrokes;\nvar SSR = typeof window === \"undefined\";\nvar isMac = !SSR && window.navigator.platform === \"MacIntel\";\nfunction isModKey(event) {\n    return isMac ? event.metaKey : event.ctrlKey;\n}\nexports.isModKey = isModKey;\nexports.Priority = {\n    HIGH: 1,\n    NORMAL: 0,\n    LOW: -1,\n};\n", "const HAS_WEAKSET_SUPPORT = typeof WeakSet === 'function';\n\nconst { keys } = Object;\n\ntype Cache = {\n  add: (value: any) => void;\n  has: (value: any) => boolean;\n};\n\nexport type EqualityComparator = (a: any, b: any, meta?: any) => boolean;\n\n/**\n * are the values passed strictly equal or both NaN\n *\n * @param a the value to compare against\n * @param b the value to test\n * @returns are the values equal by the SameValueZero principle\n */\nexport function sameValueZeroEqual(a: any, b: any) {\n  return a === b || (a !== a && b !== b);\n}\n\n/**\n * is the value a plain object\n *\n * @param value the value to test\n * @returns is the value a plain object\n */\nexport function isPlainObject(value: any) {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * is the value promise-like (meaning it is thenable)\n *\n * @param value the value to test\n * @returns is the value promise-like\n */\nexport function isPromiseLike(value: any) {\n  return !!value && typeof value.then === 'function';\n}\n\n/**\n * is the value passed a react element\n *\n * @param value the value to test\n * @returns is the value a react element\n */\nexport function isReactElement(value: any) {\n  return !!(value && value.$$typeof);\n}\n\n/**\n * in cases where WeakSet is not supported, creates a new custom\n * object that mimics the necessary API aspects for cache purposes\n *\n * @returns the new cache object\n */\nexport function getNewCacheFallback(): Cache {\n  const values: any[] = [];\n\n  return {\n    add(value: any) {\n      values.push(value);\n    },\n\n    has(value: any) {\n      return values.indexOf(value) !== -1;\n    },\n  };\n}\n\n/**\n * get a new cache object to prevent circular references\n *\n * @returns the new cache object\n */\nexport const getNewCache = ((canUseWeakMap: boolean) => {\n  if (canUseWeakMap) {\n    return function _getNewCache(): Cache {\n      return new WeakSet();\n    };\n  }\n\n  return getNewCacheFallback;\n})(HAS_WEAKSET_SUPPORT);\n\n/**\n * create a custom isEqual handler specific to circular objects\n *\n * @param [isEqual] the isEqual comparator to use instead of isDeepEqual\n * @returns the method to create the `isEqual` function\n */\nexport function createCircularEqualCreator(isEqual?: EqualityComparator) {\n  return function createCircularEqual(comparator: EqualityComparator) {\n    const _comparator = isEqual || comparator;\n\n    return function circularEqual(\n      a: any,\n      b: any,\n      cache: Cache = getNewCache(),\n    ) {\n      const isCacheableA = !!a && typeof a === 'object';\n      const isCacheableB = !!b && typeof b === 'object';\n\n      if (isCacheableA || isCacheableB) {\n        const hasA = isCacheableA && cache.has(a);\n        const hasB = isCacheableB && cache.has(b);\n\n        if (hasA || hasB) {\n          return hasA && hasB;\n        }\n\n        if (isCacheableA) {\n          cache.add(a);\n        }\n\n        if (isCacheableB) {\n          cache.add(b);\n        }\n      }\n\n      return _comparator(a, b, cache);\n    };\n  };\n}\n\n/**\n * are the arrays equal in value\n *\n * @param a the array to test\n * @param b the array to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the arrays equal\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: EqualityComparator,\n  meta: any,\n) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the maps equal in value\n *\n * @param a the map to test\n * @param b the map to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta map to pass through\n * @returns are the maps equal\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: EqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n\n    a.forEach((aValue, aKey) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndex = 0;\n\n        b.forEach((bValue, bKey) => {\n          if (!hasMatch && !matchedIndices[matchIndex]) {\n            hasMatch =\n              isEqual(aKey, bKey, meta) && isEqual(aValue, bValue, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndex] = true;\n            }\n          }\n\n          matchIndex++;\n        });\n\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n\ntype Dictionary<Type> = {\n  [key: string]: Type;\n  [index: number]: Type;\n};\n\nconst OWNER = '_owner';\n\nconst hasOwnProperty = Function.prototype.bind.call(\n  Function.prototype.call,\n  Object.prototype.hasOwnProperty,\n);\n\n/**\n * are the objects equal in value\n *\n * @param a the object to test\n * @param b the object to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the objects equal\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: EqualityComparator,\n  meta: any,\n) {\n  const keysA = keys(a);\n\n  let index = keysA.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  if (index) {\n    let key: string;\n\n    while (index-- > 0) {\n      key = keysA[index];\n\n      if (key === OWNER) {\n        const reactElementA = isReactElement(a);\n        const reactElementB = isReactElement(b);\n\n        if (\n          (reactElementA || reactElementB) &&\n          reactElementA !== reactElementB\n        ) {\n          return false;\n        }\n      }\n\n      if (!hasOwnProperty(b, key) || !isEqual(a[key], b[key], meta)) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the regExps equal in value\n *\n * @param a the regExp to test\n * @param b the regExp to test agains\n * @returns are the regExps equal\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp) {\n  return (\n    a.source === b.source &&\n    a.global === b.global &&\n    a.ignoreCase === b.ignoreCase &&\n    a.multiline === b.multiline &&\n    a.unicode === b.unicode &&\n    a.sticky === b.sticky &&\n    a.lastIndex === b.lastIndex\n  );\n}\n\n/**\n * are the sets equal in value\n *\n * @param a the set to test\n * @param b the set to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta set to pass through\n * @returns are the sets equal\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: EqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n\n    a.forEach((aValue) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndex = 0;\n\n        b.forEach((bValue) => {\n          if (!hasMatch && !matchedIndices[matchIndex]) {\n            hasMatch = isEqual(aValue, bValue, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndex] = true;\n            }\n          }\n\n          matchIndex++;\n        });\n\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n", "import {\n  EqualityComparator,\n  areArraysEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  isPlainObject,\n  isPromiseLike,\n  sameValueZeroEqual,\n} from './utils';\n\nconst HAS_MAP_SUPPORT = typeof Map === 'function';\nconst HAS_SET_SUPPORT = typeof Set === 'function';\n\ntype EqualityComparatorCreator = (fn: EqualityComparator) => EqualityComparator;\n\nexport function createComparator(createIsEqual?: EqualityComparatorCreator) {\n  const isEqual: EqualityComparator =\n    /* eslint-disable no-use-before-define */\n    typeof createIsEqual === 'function'\n      ? createIsEqual(comparator)\n      : comparator;\n  /* eslint-enable */\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   *\n   * @param a the value to test against\n   * @param b the value to test\n   * @param [meta] an optional meta object that is passed through to all equality test calls\n   * @returns are a and b equivalent in value\n   */\n  function comparator(a: any, b: any, meta?: any) {\n    if (a === b) {\n      return true;\n    }\n\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n      if (isPlainObject(a) && isPlainObject(b)) {\n        return areObjectsEqual(a, b, isEqual, meta);\n      }\n\n      let aShape = Array.isArray(a);\n      let bShape = Array.isArray(b);\n\n      if (aShape || bShape) {\n        return aShape === bShape && areArraysEqual(a, b, isEqual, meta);\n      }\n\n      aShape = a instanceof Date;\n      bShape = b instanceof Date;\n\n      if (aShape || bShape) {\n        return (\n          aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime())\n        );\n      }\n\n      aShape = a instanceof RegExp;\n      bShape = b instanceof RegExp;\n\n      if (aShape || bShape) {\n        return aShape === bShape && areRegExpsEqual(a, b);\n      }\n\n      if (isPromiseLike(a) || isPromiseLike(b)) {\n        return a === b;\n      }\n\n      if (HAS_MAP_SUPPORT) {\n        aShape = a instanceof Map;\n        bShape = b instanceof Map;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areMapsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      if (HAS_SET_SUPPORT) {\n        aShape = a instanceof Set;\n        bShape = b instanceof Set;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areSetsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    return a !== a && b !== b;\n  }\n\n  return comparator;\n}\n", "import { createComparator } from './comparator';\nimport { createCircularEqualCreator, sameValueZeroEqual } from './utils';\n\nexport { createComparator as createCustomEqual, sameValueZeroEqual };\n\nexport const deepEqual = createComparator();\nexport const shallowEqual = createComparator(() => sameValueZeroEqual);\n\nexport const circularDeepEqual = createComparator(createCircularEqualCreator());\nexport const circularShallowEqual = createComparator(\n  createCircularEqualCreator(sameValueZeroEqual),\n);\n", "'use strict';\n\nvar isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nmodule.exports = invariant;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Command = void 0;\nvar Command = /** @class */ (function () {\n    function Command(command, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        this.perform = function () {\n            var negate = command.perform();\n            // no need for history if non negatable\n            if (typeof negate !== \"function\")\n                return;\n            // return if no history enabled\n            var history = options.history;\n            if (!history)\n                return;\n            // since we are performing the same action, we'll clean up the\n            // previous call to the action and create a new history record\n            if (_this.historyItem) {\n                history.remove(_this.historyItem);\n            }\n            _this.historyItem = history.add({\n                perform: command.perform,\n                negate: negate,\n            });\n            _this.history = {\n                undo: function () { return history.undo(_this.historyItem); },\n                redo: function () { return history.redo(_this.historyItem); },\n            };\n        };\n    }\n    return Command;\n}());\nexports.Command = Command;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ActionImpl = void 0;\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar Command_1 = require(\"./Command\");\nvar utils_1 = require(\"../utils\");\n/**\n * Extends the configured keywords to include the section\n * This allows section names to be searched for.\n */\nvar extendKeywords = function (_a) {\n    var _b = _a.keywords, keywords = _b === void 0 ? \"\" : _b, _c = _a.section, section = _c === void 0 ? \"\" : _c;\n    return (keywords + \" \" + (typeof section === \"string\" ? section : section.name)).trim();\n};\nvar ActionImpl = /** @class */ (function () {\n    function ActionImpl(action, options) {\n        var _this = this;\n        var _a;\n        this.priority = utils_1.Priority.NORMAL;\n        this.ancestors = [];\n        this.children = [];\n        Object.assign(this, action);\n        this.id = action.id;\n        this.name = action.name;\n        this.keywords = extendKeywords(action);\n        var perform = action.perform;\n        this.command =\n            perform &&\n                new Command_1.Command({\n                    perform: function () { return perform(_this); },\n                }, {\n                    history: options.history,\n                });\n        // Backwards compatibility\n        this.perform = (_a = this.command) === null || _a === void 0 ? void 0 : _a.perform;\n        if (action.parent) {\n            var parentActionImpl = options.store[action.parent];\n            (0, tiny_invariant_1.default)(parentActionImpl, \"attempted to create an action whos parent: \" + action.parent + \" does not exist in the store.\");\n            parentActionImpl.addChild(this);\n        }\n    }\n    ActionImpl.prototype.addChild = function (childActionImpl) {\n        // add all ancestors for the child action\n        childActionImpl.ancestors.unshift(this);\n        var parent = this.parentActionImpl;\n        while (parent) {\n            childActionImpl.ancestors.unshift(parent);\n            parent = parent.parentActionImpl;\n        }\n        // we ensure that order of adding always goes\n        // parent -> children, so no need to recurse\n        this.children.push(childActionImpl);\n    };\n    ActionImpl.prototype.removeChild = function (actionImpl) {\n        var _this = this;\n        // recursively remove all children\n        var index = this.children.indexOf(actionImpl);\n        if (index !== -1) {\n            this.children.splice(index, 1);\n        }\n        if (actionImpl.children) {\n            actionImpl.children.forEach(function (child) {\n                _this.removeChild(child);\n            });\n        }\n    };\n    Object.defineProperty(ActionImpl.prototype, \"parentActionImpl\", {\n        // easily access parentActionImpl after creation\n        get: function () {\n            return this.ancestors[this.ancestors.length - 1];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ActionImpl.create = function (action, options) {\n        return new ActionImpl(action, options);\n    };\n    return ActionImpl;\n}());\nexports.ActionImpl = ActionImpl;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ActionInterface = void 0;\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar ActionImpl_1 = require(\"./ActionImpl\");\nvar ActionInterface = /** @class */ (function () {\n    function ActionInterface(actions, options) {\n        if (actions === void 0) { actions = []; }\n        if (options === void 0) { options = {}; }\n        this.actions = {};\n        this.options = options;\n        this.add(actions);\n    }\n    ActionInterface.prototype.add = function (actions) {\n        for (var i = 0; i < actions.length; i++) {\n            var action = actions[i];\n            if (action.parent) {\n                (0, tiny_invariant_1.default)(this.actions[action.parent], \"Attempted to create action \\\"\" + action.name + \"\\\" without registering its parent \\\"\" + action.parent + \"\\\" first.\");\n            }\n            this.actions[action.id] = ActionImpl_1.ActionImpl.create(action, {\n                history: this.options.historyManager,\n                store: this.actions,\n            });\n        }\n        return __assign({}, this.actions);\n    };\n    ActionInterface.prototype.remove = function (actions) {\n        var _this = this;\n        actions.forEach(function (action) {\n            var actionImpl = _this.actions[action.id];\n            if (!actionImpl)\n                return;\n            var children = actionImpl.children;\n            while (children.length) {\n                var child = children.pop();\n                if (!child)\n                    return;\n                delete _this.actions[child.id];\n                if (child.parentActionImpl)\n                    child.parentActionImpl.removeChild(child);\n                if (child.children)\n                    children.push.apply(children, child.children);\n            }\n            if (actionImpl.parentActionImpl) {\n                actionImpl.parentActionImpl.removeChild(actionImpl);\n            }\n            delete _this.actions[action.id];\n        });\n        return __assign({}, this.actions);\n    };\n    return ActionInterface;\n}());\nexports.ActionInterface = ActionInterface;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.history = exports.HistoryItemImpl = void 0;\nvar utils_1 = require(\"../utils\");\nvar HistoryItemImpl = /** @class */ (function () {\n    function HistoryItemImpl(item) {\n        this.perform = item.perform;\n        this.negate = item.negate;\n    }\n    HistoryItemImpl.create = function (item) {\n        return new HistoryItemImpl(item);\n    };\n    return HistoryItemImpl;\n}());\nexports.HistoryItemImpl = HistoryItemImpl;\nvar HistoryImpl = /** @class */ (function () {\n    function HistoryImpl() {\n        this.undoStack = [];\n        this.redoStack = [];\n        if (!HistoryImpl.instance) {\n            HistoryImpl.instance = this;\n            this.init();\n        }\n        return HistoryImpl.instance;\n    }\n    HistoryImpl.prototype.init = function () {\n        var _this = this;\n        if (typeof window === \"undefined\")\n            return;\n        window.addEventListener(\"keydown\", function (event) {\n            var _a;\n            if ((!_this.redoStack.length && !_this.undoStack.length) ||\n                (0, utils_1.shouldRejectKeystrokes)()) {\n                return;\n            }\n            var key = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n            if (event.metaKey && key === \"z\" && event.shiftKey) {\n                _this.redo();\n            }\n            else if (event.metaKey && key === \"z\") {\n                _this.undo();\n            }\n        });\n    };\n    HistoryImpl.prototype.add = function (item) {\n        var historyItem = HistoryItemImpl.create(item);\n        this.undoStack.push(historyItem);\n        return historyItem;\n    };\n    HistoryImpl.prototype.remove = function (item) {\n        var undoIndex = this.undoStack.findIndex(function (i) { return i === item; });\n        if (undoIndex !== -1) {\n            this.undoStack.splice(undoIndex, 1);\n            return;\n        }\n        var redoIndex = this.redoStack.findIndex(function (i) { return i === item; });\n        if (redoIndex !== -1) {\n            this.redoStack.splice(redoIndex, 1);\n        }\n    };\n    HistoryImpl.prototype.undo = function (item) {\n        // if not undoing a specific item, just undo the latest\n        if (!item) {\n            var item_1 = this.undoStack.pop();\n            if (!item_1)\n                return;\n            item_1 === null || item_1 === void 0 ? void 0 : item_1.negate();\n            this.redoStack.push(item_1);\n            return item_1;\n        }\n        // else undo the specific item\n        var index = this.undoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.undoStack.splice(index, 1);\n        item.negate();\n        this.redoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.redo = function (item) {\n        if (!item) {\n            var item_2 = this.redoStack.pop();\n            if (!item_2)\n                return;\n            item_2 === null || item_2 === void 0 ? void 0 : item_2.perform();\n            this.undoStack.push(item_2);\n            return item_2;\n        }\n        var index = this.redoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.redoStack.splice(index, 1);\n        item.perform();\n        this.undoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.reset = function () {\n        this.undoStack.splice(0);\n        this.redoStack.splice(0);\n    };\n    return HistoryImpl;\n}());\nvar history = new HistoryImpl();\nexports.history = history;\nObject.freeze(history);\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VisualState = void 0;\nvar VisualState;\n(function (VisualState) {\n    VisualState[\"animatingIn\"] = \"animating-in\";\n    VisualState[\"showing\"] = \"showing\";\n    VisualState[\"animatingOut\"] = \"animating-out\";\n    VisualState[\"hidden\"] = \"hidden\";\n})(VisualState = exports.VisualState || (exports.VisualState = {}));\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useStore = void 0;\nvar fast_equals_1 = require(\"fast-equals\");\nvar React = __importStar(require(\"react\"));\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar ActionInterface_1 = require(\"./action/ActionInterface\");\nvar HistoryImpl_1 = require(\"./action/HistoryImpl\");\nvar types_1 = require(\"./types\");\nfunction useStore(props) {\n    var optionsRef = React.useRef(__assign({ animations: {\n            enterMs: 200,\n            exitMs: 100,\n        } }, props.options));\n    var actionsInterface = React.useMemo(function () {\n        return new ActionInterface_1.ActionInterface(props.actions || [], {\n            historyManager: optionsRef.current.enableHistory ? HistoryImpl_1.history : undefined,\n        });\n    }, \n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // TODO: at this point useReducer might be a better approach to managing state.\n    var _a = React.useState({\n        searchQuery: \"\",\n        currentRootActionId: null,\n        visualState: types_1.VisualState.hidden,\n        actions: __assign({}, actionsInterface.actions),\n        activeIndex: 0,\n        disabled: false,\n    }), state = _a[0], setState = _a[1];\n    var currState = React.useRef(state);\n    currState.current = state;\n    var getState = React.useCallback(function () { return currState.current; }, []);\n    var publisher = React.useMemo(function () { return new Publisher(getState); }, [getState]);\n    React.useEffect(function () {\n        currState.current = state;\n        publisher.notify();\n    }, [state, publisher]);\n    var registerActions = React.useCallback(function (actions) {\n        setState(function (state) {\n            return __assign(__assign({}, state), { actions: actionsInterface.add(actions) });\n        });\n        return function unregister() {\n            setState(function (state) {\n                return __assign(__assign({}, state), { actions: actionsInterface.remove(actions) });\n            });\n        };\n    }, [actionsInterface]);\n    var inputRef = React.useRef(null);\n    return React.useMemo(function () {\n        var query = {\n            setCurrentRootAction: function (actionId) {\n                setState(function (state) { return (__assign(__assign({}, state), { currentRootActionId: actionId })); });\n            },\n            setVisualState: function (cb) {\n                setState(function (state) { return (__assign(__assign({}, state), { visualState: typeof cb === \"function\" ? cb(state.visualState) : cb })); });\n            },\n            setSearch: function (searchQuery) {\n                return setState(function (state) { return (__assign(__assign({}, state), { searchQuery: searchQuery })); });\n            },\n            registerActions: registerActions,\n            toggle: function () {\n                return setState(function (state) { return (__assign(__assign({}, state), { visualState: [types_1.VisualState.animatingOut, types_1.VisualState.hidden].includes(state.visualState)\n                        ? types_1.VisualState.animatingIn\n                        : types_1.VisualState.animatingOut })); });\n            },\n            setActiveIndex: function (cb) {\n                return setState(function (state) { return (__assign(__assign({}, state), { activeIndex: typeof cb === \"number\" ? cb : cb(state.activeIndex) })); });\n            },\n            inputRefSetter: function (el) {\n                inputRef.current = el;\n            },\n            getInput: function () {\n                (0, tiny_invariant_1.default)(inputRef.current, \"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input.\");\n                return inputRef.current;\n            },\n            disable: function (disable) {\n                setState(function (state) { return (__assign(__assign({}, state), { disabled: disable })); });\n            },\n        };\n        return {\n            getState: getState,\n            query: query,\n            options: optionsRef.current,\n            subscribe: function (collector, cb) { return publisher.subscribe(collector, cb); },\n        };\n    }, [getState, publisher, registerActions]);\n}\nexports.useStore = useStore;\nvar Publisher = /** @class */ (function () {\n    function Publisher(getState) {\n        this.subscribers = [];\n        this.getState = getState;\n    }\n    Publisher.prototype.subscribe = function (collector, onChange) {\n        var _this = this;\n        var subscriber = new Subscriber(function () { return collector(_this.getState()); }, onChange);\n        this.subscribers.push(subscriber);\n        return this.unsubscribe.bind(this, subscriber);\n    };\n    Publisher.prototype.unsubscribe = function (subscriber) {\n        if (this.subscribers.length) {\n            var index = this.subscribers.indexOf(subscriber);\n            if (index > -1) {\n                return this.subscribers.splice(index, 1);\n            }\n        }\n    };\n    Publisher.prototype.notify = function () {\n        this.subscribers.forEach(function (subscriber) { return subscriber.collect(); });\n    };\n    return Publisher;\n}());\nvar Subscriber = /** @class */ (function () {\n    function Subscriber(collector, onChange) {\n        this.collector = collector;\n        this.onChange = onChange;\n    }\n    Subscriber.prototype.collect = function () {\n        try {\n            // grab latest state\n            var recollect = this.collector();\n            if (!(0, fast_equals_1.deepEqual)(recollect, this.collected)) {\n                this.collected = recollect;\n                if (this.onChange) {\n                    this.onChange(this.collected);\n                }\n            }\n        }\n        catch (error) {\n            console.warn(error);\n        }\n    };\n    return Subscriber;\n}());\n", "\"use strict\";\n// Fixes special character issues; `?` -> `shift+/` + build issue\n// https://github.com/jamiebuilds/tinykeys\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * These are the modifier keys that change the meaning of keybindings.\n *\n * Note: Ignoring \"AltGraph\" because it is covered by the others.\n */\nvar KEYBINDING_MODIFIER_KEYS = [\"Shift\", \"Meta\", \"Alt\", \"Control\"];\n/**\n * Keybinding sequences should timeout if individual key presses are more than\n * 1s apart by default.\n */\nvar DEFAULT_TIMEOUT = 1000;\n/**\n * Keybinding sequences should bind to this event by default.\n */\nvar DEFAULT_EVENT = \"keydown\";\n/**\n * An alias for creating platform-specific keybinding aliases.\n */\nvar MOD = typeof navigator === \"object\" &&\n    /Mac|iPod|iPhone|iPad/.test(navigator.platform)\n    ? \"Meta\"\n    : \"Control\";\n/**\n * There's a bug in Chrome that causes event.getModifierState not to exist on\n * KeyboardEvent's for F1/F2/etc keys.\n */\nfunction getModifierState(event, mod) {\n    return typeof event.getModifierState === \"function\"\n        ? event.getModifierState(mod)\n        : false;\n}\n/**\n * Parses a \"Key Binding String\" into its parts\n *\n * grammar    = `<sequence>`\n * <sequence> = `<press> <press> <press> ...`\n * <press>    = `<key>` or `<mods>+<key>`\n * <mods>     = `<mod>+<mod>+...`\n */\nfunction parse(str) {\n    return str\n        .trim()\n        .split(\" \")\n        .map(function (press) {\n        var mods = press.split(/\\b\\+/);\n        var key = mods.pop();\n        mods = mods.map(function (mod) { return (mod === \"$mod\" ? MOD : mod); });\n        return [mods, key];\n    });\n}\n/**\n * This tells us if a series of events matches a key binding sequence either\n * partially or exactly.\n */\nfunction match(event, press) {\n    // Special characters; `?` `!`\n    if (/^[^A-Za-z0-9]$/.test(event.key) && press[1] === event.key) {\n        return true;\n    }\n    // prettier-ignore\n    return !(\n    // Allow either the `event.key` or the `event.code`\n    // MDN event.key: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key\n    // MDN event.code: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/code\n    (press[1].toUpperCase() !== event.key.toUpperCase() &&\n        press[1] !== event.code) ||\n        // Ensure all the modifiers in the keybinding are pressed.\n        press[0].find(function (mod) {\n            return !getModifierState(event, mod);\n        }) ||\n        // KEYBINDING_MODIFIER_KEYS (Shift/Control/etc) change the meaning of a\n        // keybinding. So if they are pressed but aren't part of the current\n        // keybinding press, then we don't have a match.\n        KEYBINDING_MODIFIER_KEYS.find(function (mod) {\n            return !press[0].includes(mod) && press[1] !== mod && getModifierState(event, mod);\n        }));\n}\n/**\n * Subscribes to keybindings.\n *\n * Returns an unsubscribe method.\n *\n * @example\n * ```js\n * import keybindings from \"../src/keybindings\"\n *\n * keybindings(window, {\n * \t\"Shift+d\": () => {\n * \t\talert(\"The 'Shift' and 'd' keys were pressed at the same time\")\n * \t},\n * \t\"y e e t\": () => {\n * \t\talert(\"The keys 'y', 'e', 'e', and 't' were pressed in order\")\n * \t},\n * \t\"$mod+d\": () => {\n * \t\talert(\"Either 'Control+d' or 'Meta+d' were pressed\")\n * \t},\n * })\n * ```\n */\nfunction keybindings(target, keyBindingMap, options) {\n    var _a, _b;\n    if (options === void 0) { options = {}; }\n    var timeout = (_a = options.timeout) !== null && _a !== void 0 ? _a : DEFAULT_TIMEOUT;\n    var event = (_b = options.event) !== null && _b !== void 0 ? _b : DEFAULT_EVENT;\n    var keyBindings = Object.keys(keyBindingMap).map(function (key) {\n        return [parse(key), keyBindingMap[key]];\n    });\n    var possibleMatches = new Map();\n    var timer = null;\n    var onKeyEvent = function (event) {\n        // Ensure and stop any event that isn't a full keyboard event.\n        // Autocomplete option navigation and selection would fire a instanceof Event,\n        // instead of the expected KeyboardEvent\n        if (!(event instanceof KeyboardEvent)) {\n            return;\n        }\n        keyBindings.forEach(function (keyBinding) {\n            var sequence = keyBinding[0];\n            var callback = keyBinding[1];\n            var prev = possibleMatches.get(sequence);\n            var remainingExpectedPresses = prev ? prev : sequence;\n            var currentExpectedPress = remainingExpectedPresses[0];\n            var matches = match(event, currentExpectedPress);\n            if (!matches) {\n                // Modifier keydown events shouldn't break sequences\n                // Note: This works because:\n                // - non-modifiers will always return false\n                // - if the current keypress is a modifier then it will return true when we check its state\n                // MDN: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/getModifierState\n                if (!getModifierState(event, event.key)) {\n                    possibleMatches.delete(sequence);\n                }\n            }\n            else if (remainingExpectedPresses.length > 1) {\n                possibleMatches.set(sequence, remainingExpectedPresses.slice(1));\n            }\n            else {\n                possibleMatches.delete(sequence);\n                callback(event);\n            }\n        });\n        if (timer) {\n            clearTimeout(timer);\n        }\n        // @ts-ignore\n        timer = setTimeout(possibleMatches.clear.bind(possibleMatches), timeout);\n    };\n    target.addEventListener(event, onKeyEvent);\n    return function () {\n        target.removeEventListener(event, onKeyEvent);\n    };\n}\nexports.default = keybindings;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.InternalEvents = void 0;\nvar React = __importStar(require(\"react\"));\nvar tinykeys_1 = __importDefault(require(\"./tinykeys\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nfunction InternalEvents() {\n    useToggleHandler();\n    useDocumentLock();\n    useShortcuts();\n    useFocusHandler();\n    return null;\n}\nexports.InternalEvents = InternalEvents;\n/**\n * `useToggleHandler` handles the keyboard events for toggling kbar.\n */\nfunction useToggleHandler() {\n    var _a, _b;\n    var _c = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        showing: state.visualState !== types_1.VisualState.hidden,\n        disabled: state.disabled,\n    }); }), query = _c.query, options = _c.options, visualState = _c.visualState, showing = _c.showing, disabled = _c.disabled;\n    React.useEffect(function () {\n        var _a;\n        var close = function () {\n            query.setVisualState(function (vs) {\n                if (vs === types_1.VisualState.hidden || vs === types_1.VisualState.animatingOut) {\n                    return vs;\n                }\n                return types_1.VisualState.animatingOut;\n            });\n        };\n        if (disabled) {\n            close();\n            return;\n        }\n        var shortcut = options.toggleShortcut || \"$mod+k\";\n        var unsubscribe = (0, tinykeys_1.default)(window, (_a = {},\n            _a[shortcut] = function (event) {\n                var _a, _b, _c, _d;\n                if (event.defaultPrevented)\n                    return;\n                event.preventDefault();\n                query.toggle();\n                if (showing) {\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                else {\n                    (_d = (_c = options.callbacks) === null || _c === void 0 ? void 0 : _c.onOpen) === null || _d === void 0 ? void 0 : _d.call(_c);\n                }\n            },\n            _a.Escape = function (event) {\n                var _a, _b;\n                if (showing) {\n                    event.stopPropagation();\n                    event.preventDefault();\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                close();\n            },\n            _a));\n        return function () {\n            unsubscribe();\n        };\n    }, [options.callbacks, options.toggleShortcut, query, showing, disabled]);\n    var timeoutRef = React.useRef();\n    var runAnimateTimer = React.useCallback(function (vs) {\n        var _a, _b;\n        var ms = 0;\n        if (vs === types_1.VisualState.animatingIn) {\n            ms = ((_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs) || 0;\n        }\n        if (vs === types_1.VisualState.animatingOut) {\n            ms = ((_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs) || 0;\n        }\n        clearTimeout(timeoutRef.current);\n        timeoutRef.current = setTimeout(function () {\n            var backToRoot = false;\n            // TODO: setVisualState argument should be a function or just a VisualState value.\n            query.setVisualState(function () {\n                var finalVs = vs === types_1.VisualState.animatingIn\n                    ? types_1.VisualState.showing\n                    : types_1.VisualState.hidden;\n                if (finalVs === types_1.VisualState.hidden) {\n                    backToRoot = true;\n                }\n                return finalVs;\n            });\n            if (backToRoot) {\n                query.setCurrentRootAction(null);\n            }\n        }, ms);\n    }, [(_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs, (_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs, query]);\n    React.useEffect(function () {\n        switch (visualState) {\n            case types_1.VisualState.animatingIn:\n            case types_1.VisualState.animatingOut:\n                runAnimateTimer(visualState);\n                break;\n        }\n    }, [runAnimateTimer, visualState]);\n}\n/**\n * `useDocumentLock` is a simple implementation for preventing the\n * underlying page content from scrolling when kbar is open.\n */\nfunction useDocumentLock() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n    }); }), visualState = _a.visualState, options = _a.options;\n    React.useEffect(function () {\n        if (options.disableDocumentLock)\n            return;\n        if (visualState === types_1.VisualState.animatingIn) {\n            document.body.style.overflow = \"hidden\";\n            if (!options.disableScrollbarManagement) {\n                var scrollbarWidth = (0, utils_1.getScrollbarWidth)();\n                // take into account the margins explicitly added by the consumer\n                var mr = getComputedStyle(document.body)[\"margin-right\"];\n                if (mr) {\n                    // remove non-numeric values; px, rem, em, etc.\n                    scrollbarWidth += Number(mr.replace(/\\D/g, \"\"));\n                }\n                document.body.style.marginRight = scrollbarWidth + \"px\";\n            }\n        }\n        else if (visualState === types_1.VisualState.hidden) {\n            document.body.style.removeProperty(\"overflow\");\n            if (!options.disableScrollbarManagement) {\n                document.body.style.removeProperty(\"margin-right\");\n            }\n        }\n    }, [\n        options.disableDocumentLock,\n        options.disableScrollbarManagement,\n        visualState,\n    ]);\n}\n/**\n * Reference: https://github.com/jamiebuilds/tinykeys/issues/37\n *\n * Fixes an issue where simultaneous key commands for shortcuts;\n * ie given two actions with shortcuts ['t','s'] and ['s'], pressing\n * 't' and 's' consecutively will cause both shortcuts to fire.\n *\n * `wrap` sets each keystroke event in a WeakSet, and ensures that\n * if ['t', 's'] are pressed, then the subsequent ['s'] event will\n * be ignored. This depends on the order in which we register the\n * shortcuts to tinykeys, which is handled below.\n */\nvar handled = new WeakSet();\nfunction wrap(handler) {\n    return function (event) {\n        if (handled.has(event))\n            return;\n        handler(event);\n        handled.add(event);\n    };\n}\n/**\n * `useShortcuts` registers and listens to keyboard strokes and\n * performs actions for patterns that match the user defined `shortcut`.\n */\nfunction useShortcuts() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        actions: state.actions,\n        open: state.visualState === types_1.VisualState.showing,\n        disabled: state.disabled,\n    }); }), actions = _a.actions, query = _a.query, open = _a.open, options = _a.options, disabled = _a.disabled;\n    React.useEffect(function () {\n        var _a;\n        if (open || disabled)\n            return;\n        var actionsList = Object.keys(actions).map(function (key) { return actions[key]; });\n        var actionsWithShortcuts = [];\n        for (var _i = 0, actionsList_1 = actionsList; _i < actionsList_1.length; _i++) {\n            var action = actionsList_1[_i];\n            if (!((_a = action.shortcut) === null || _a === void 0 ? void 0 : _a.length)) {\n                continue;\n            }\n            actionsWithShortcuts.push(action);\n        }\n        actionsWithShortcuts = actionsWithShortcuts.sort(function (a, b) { return b.shortcut.join(\" \").length - a.shortcut.join(\" \").length; });\n        var shortcutsMap = {};\n        var _loop_1 = function (action) {\n            var shortcut = action.shortcut.join(\" \");\n            shortcutsMap[shortcut] = wrap(function (event) {\n                var _a, _b, _c, _d, _e, _f;\n                if ((0, utils_1.shouldRejectKeystrokes)())\n                    return;\n                event.preventDefault();\n                if ((_a = action.children) === null || _a === void 0 ? void 0 : _a.length) {\n                    query.setCurrentRootAction(action.id);\n                    query.toggle();\n                    (_c = (_b = options.callbacks) === null || _b === void 0 ? void 0 : _b.onOpen) === null || _c === void 0 ? void 0 : _c.call(_b);\n                }\n                else {\n                    (_d = action.command) === null || _d === void 0 ? void 0 : _d.perform();\n                    (_f = (_e = options.callbacks) === null || _e === void 0 ? void 0 : _e.onSelectAction) === null || _f === void 0 ? void 0 : _f.call(_e, action);\n                }\n            });\n        };\n        for (var _b = 0, actionsWithShortcuts_1 = actionsWithShortcuts; _b < actionsWithShortcuts_1.length; _b++) {\n            var action = actionsWithShortcuts_1[_b];\n            _loop_1(action);\n        }\n        var unsubscribe = (0, tinykeys_1.default)(window, shortcutsMap, {\n            timeout: 400,\n        });\n        return function () {\n            unsubscribe();\n        };\n    }, [actions, open, options.callbacks, query, disabled]);\n}\n/**\n * `useFocusHandler` ensures that focus is set back on the element which was\n * in focus prior to kbar being triggered.\n */\nfunction useFocusHandler() {\n    var rFirstRender = React.useRef(true);\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        isShowing: state.visualState === types_1.VisualState.showing ||\n            state.visualState === types_1.VisualState.animatingIn,\n    }); }), isShowing = _a.isShowing, query = _a.query;\n    var activeElementRef = React.useRef(null);\n    React.useEffect(function () {\n        if (rFirstRender.current) {\n            rFirstRender.current = false;\n            return;\n        }\n        if (isShowing) {\n            activeElementRef.current = document.activeElement;\n            return;\n        }\n        // This fixes an issue on Safari where closing kbar causes the entire\n        // page to scroll to the bottom. The reason this was happening was due\n        // to the search input still in focus when we removed it from the dom.\n        var currentActiveElement = document.activeElement;\n        if ((currentActiveElement === null || currentActiveElement === void 0 ? void 0 : currentActiveElement.tagName.toLowerCase()) === \"input\") {\n            currentActiveElement.blur();\n        }\n        var activeElement = activeElementRef.current;\n        if (activeElement && activeElement !== currentActiveElement) {\n            activeElement.focus();\n        }\n    }, [isShowing]);\n    // When focus is blurred from the search input while kbar is still\n    // open, any keystroke should set focus back to the search input.\n    React.useEffect(function () {\n        function handler(event) {\n            var input = query.getInput();\n            if (event.target !== input) {\n                input.focus();\n            }\n        }\n        if (isShowing) {\n            window.addEventListener(\"keydown\", handler);\n            return function () {\n                window.removeEventListener(\"keydown\", handler);\n            };\n        }\n    }, [isShowing, query]);\n}\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarProvider = exports.KBarContext = void 0;\nvar useStore_1 = require(\"./useStore\");\nvar React = __importStar(require(\"react\"));\nvar InternalEvents_1 = require(\"./InternalEvents\");\nexports.KBarContext = React.createContext({});\nvar KBarProvider = function (props) {\n    var contextValue = (0, useStore_1.useStore)(props);\n    return (React.createElement(exports.KBarContext.Provider, { value: contextValue },\n        React.createElement(InternalEvents_1.InternalEvents, null),\n        props.children));\n};\nexports.KBarProvider = KBarProvider;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useKBar = void 0;\nvar React = __importStar(require(\"react\"));\nvar KBarContextProvider_1 = require(\"./KBarContextProvider\");\nfunction useKBar(collector) {\n    var _a = React.useContext(KBarContextProvider_1.KBarContext), query = _a.query, getState = _a.getState, subscribe = _a.subscribe, options = _a.options;\n    var collected = React.useRef(collector === null || collector === void 0 ? void 0 : collector(getState()));\n    var collectorRef = React.useRef(collector);\n    var onCollect = React.useCallback(function (collected) { return (__assign(__assign({}, collected), { query: query, options: options })); }, [query, options]);\n    var _b = React.useState(onCollect(collected.current)), render = _b[0], setRender = _b[1];\n    React.useEffect(function () {\n        var unsubscribe;\n        if (collectorRef.current) {\n            unsubscribe = subscribe(function (current) { return collectorRef.current(current); }, function (collected) { return setRender(onCollect(collected)); });\n        }\n        return function () {\n            if (unsubscribe) {\n                unsubscribe();\n            }\n        };\n    }, [onCollect, subscribe]);\n    return render;\n}\nexports.useKBar = useKBar;\n", "/**\n * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)\n *\n * Copyright (c) 2022 Kiro Risk (http://kiro.me)\n * All Rights Reserved. Apache Software License 2.0\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nfunction isArray(value) {\n  return !Array.isArray\n    ? getTag(value) === '[object Array]'\n    : Array.isArray(value)\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/baseToString.js\nconst INFINITY = 1 / 0;\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value\n  }\n  let result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result\n}\n\nfunction toString(value) {\n  return value == null ? '' : baseToString(value)\n}\n\nfunction isString(value) {\n  return typeof value === 'string'\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number'\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/isBoolean.js\nfunction isBoolean(value) {\n  return (\n    value === true ||\n    value === false ||\n    (isObjectLike(value) && getTag(value) == '[object Boolean]')\n  )\n}\n\nfunction isObject(value) {\n  return typeof value === 'object'\n}\n\n// Checks if `value` is object-like.\nfunction isObjectLike(value) {\n  return isObject(value) && value !== null\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null\n}\n\nfunction isBlank(value) {\n  return !value.trim().length\n}\n\n// Gets the `toStringTag` of `value`.\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/getTag.js\nfunction getTag(value) {\n  return value == null\n    ? value === undefined\n      ? '[object Undefined]'\n      : '[object Null]'\n    : Object.prototype.toString.call(value)\n}\n\nconst EXTENDED_SEARCH_UNAVAILABLE = 'Extended search is not available';\n\nconst INCORRECT_INDEX_TYPE = \"Incorrect 'index' type\";\n\nconst LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY = (key) =>\n  `Invalid value for key ${key}`;\n\nconst PATTERN_LENGTH_TOO_LARGE = (max) =>\n  `Pattern length exceeds max of ${max}.`;\n\nconst MISSING_KEY_PROPERTY = (name) => `Missing ${name} property in key`;\n\nconst INVALID_KEY_WEIGHT_VALUE = (key) =>\n  `Property 'weight' in key '${key}' must be a positive integer`;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nclass KeyStore {\n  constructor(keys) {\n    this._keys = [];\n    this._keyMap = {};\n\n    let totalWeight = 0;\n\n    keys.forEach((key) => {\n      let obj = createKey(key);\n\n      totalWeight += obj.weight;\n\n      this._keys.push(obj);\n      this._keyMap[obj.id] = obj;\n\n      totalWeight += obj.weight;\n    });\n\n    // Normalize weights so that their sum is equal to 1\n    this._keys.forEach((key) => {\n      key.weight /= totalWeight;\n    });\n  }\n  get(keyId) {\n    return this._keyMap[keyId]\n  }\n  keys() {\n    return this._keys\n  }\n  toJSON() {\n    return JSON.stringify(this._keys)\n  }\n}\n\nfunction createKey(key) {\n  let path = null;\n  let id = null;\n  let src = null;\n  let weight = 1;\n  let getFn = null;\n\n  if (isString(key) || isArray(key)) {\n    src = key;\n    path = createKeyPath(key);\n    id = createKeyId(key);\n  } else {\n    if (!hasOwn.call(key, 'name')) {\n      throw new Error(MISSING_KEY_PROPERTY('name'))\n    }\n\n    const name = key.name;\n    src = name;\n\n    if (hasOwn.call(key, 'weight')) {\n      weight = key.weight;\n\n      if (weight <= 0) {\n        throw new Error(INVALID_KEY_WEIGHT_VALUE(name))\n      }\n    }\n\n    path = createKeyPath(name);\n    id = createKeyId(name);\n    getFn = key.getFn;\n  }\n\n  return { path, id, weight, src, getFn }\n}\n\nfunction createKeyPath(key) {\n  return isArray(key) ? key : key.split('.')\n}\n\nfunction createKeyId(key) {\n  return isArray(key) ? key.join('.') : key\n}\n\nfunction get(obj, path) {\n  let list = [];\n  let arr = false;\n\n  const deepGet = (obj, path, index) => {\n    if (!isDefined(obj)) {\n      return\n    }\n    if (!path[index]) {\n      // If there's no path left, we've arrived at the object we care about.\n      list.push(obj);\n    } else {\n      let key = path[index];\n\n      const value = obj[key];\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      // If we're at the last value in the path, and if it's a string/number/bool,\n      // add it to the list\n      if (\n        index === path.length - 1 &&\n        (isString(value) || isNumber(value) || isBoolean(value))\n      ) {\n        list.push(toString(value));\n      } else if (isArray(value)) {\n        arr = true;\n        // Search each item in the array.\n        for (let i = 0, len = value.length; i < len; i += 1) {\n          deepGet(value[i], path, index + 1);\n        }\n      } else if (path.length) {\n        // An object. Recurse further.\n        deepGet(value, path, index + 1);\n      }\n    }\n  };\n\n  // Backwards compatibility (since path used to be a string)\n  deepGet(obj, isString(path) ? path.split('.') : path, 0);\n\n  return arr ? list : list[0]\n}\n\nconst MatchOptions = {\n  // Whether the matches should be included in the result set. When `true`, each record in the result\n  // set will include the indices of the matched characters.\n  // These can consequently be used for highlighting purposes.\n  includeMatches: false,\n  // When `true`, the matching function will continue to the end of a search pattern even if\n  // a perfect match has already been located in the string.\n  findAllMatches: false,\n  // Minimum number of characters that must be matched before a result is considered a match\n  minMatchCharLength: 1\n};\n\nconst BasicOptions = {\n  // When `true`, the algorithm continues searching to the end of the input even if a perfect\n  // match is found before the end of the same input.\n  isCaseSensitive: false,\n  // When true, the matching function will continue to the end of a search pattern even if\n  includeScore: false,\n  // List of properties that will be searched. This also supports nested properties.\n  keys: [],\n  // Whether to sort the result list, by score\n  shouldSort: true,\n  // Default sort function: sort by ascending score, ascending index\n  sortFn: (a, b) =>\n    a.score === b.score ? (a.idx < b.idx ? -1 : 1) : a.score < b.score ? -1 : 1\n};\n\nconst FuzzyOptions = {\n  // Approximately where in the text is the pattern expected to be found?\n  location: 0,\n  // At what point does the match algorithm give up. A threshold of '0.0' requires a perfect match\n  // (of both letters and location), a threshold of '1.0' would match anything.\n  threshold: 0.6,\n  // Determines how close the match must be to the fuzzy location (specified above).\n  // An exact letter match which is 'distance' characters away from the fuzzy location\n  // would score as a complete mismatch. A distance of '0' requires the match be at\n  // the exact location specified, a threshold of '1000' would require a perfect match\n  // to be within 800 characters of the fuzzy location to be found using a 0.8 threshold.\n  distance: 100\n};\n\nconst AdvancedOptions = {\n  // When `true`, it enables the use of unix-like search commands\n  useExtendedSearch: false,\n  // The get function to use when fetching an object's properties.\n  // The default will search nested paths *ie foo.bar.baz*\n  getFn: get,\n  // When `true`, search will ignore `location` and `distance`, so it won't matter\n  // where in the string the pattern appears.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#fuzziness-score\n  ignoreLocation: false,\n  // When `true`, the calculation for the relevance score (used for sorting) will\n  // ignore the field-length norm.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#field-length-norm\n  ignoreFieldNorm: false,\n  // The weight to determine how much field length norm effects scoring.\n  fieldNormWeight: 1\n};\n\nvar Config = {\n  ...BasicOptions,\n  ...MatchOptions,\n  ...FuzzyOptions,\n  ...AdvancedOptions\n};\n\nconst SPACE = /[^ ]+/g;\n\n// Field-length norm: the shorter the field, the higher the weight.\n// Set to 3 decimals to reduce index size.\nfunction norm(weight = 1, mantissa = 3) {\n  const cache = new Map();\n  const m = Math.pow(10, mantissa);\n\n  return {\n    get(value) {\n      const numTokens = value.match(SPACE).length;\n\n      if (cache.has(numTokens)) {\n        return cache.get(numTokens)\n      }\n\n      // Default function is 1/sqrt(x), weight makes that variable\n      const norm = 1 / Math.pow(numTokens, 0.5 * weight);\n\n      // In place of `toFixed(mantissa)`, for faster computation\n      const n = parseFloat(Math.round(norm * m) / m);\n\n      cache.set(numTokens, n);\n\n      return n\n    },\n    clear() {\n      cache.clear();\n    }\n  }\n}\n\nclass FuseIndex {\n  constructor({\n    getFn = Config.getFn,\n    fieldNormWeight = Config.fieldNormWeight\n  } = {}) {\n    this.norm = norm(fieldNormWeight, 3);\n    this.getFn = getFn;\n    this.isCreated = false;\n\n    this.setIndexRecords();\n  }\n  setSources(docs = []) {\n    this.docs = docs;\n  }\n  setIndexRecords(records = []) {\n    this.records = records;\n  }\n  setKeys(keys = []) {\n    this.keys = keys;\n    this._keysMap = {};\n    keys.forEach((key, idx) => {\n      this._keysMap[key.id] = idx;\n    });\n  }\n  create() {\n    if (this.isCreated || !this.docs.length) {\n      return\n    }\n\n    this.isCreated = true;\n\n    // List is Array<String>\n    if (isString(this.docs[0])) {\n      this.docs.forEach((doc, docIndex) => {\n        this._addString(doc, docIndex);\n      });\n    } else {\n      // List is Array<Object>\n      this.docs.forEach((doc, docIndex) => {\n        this._addObject(doc, docIndex);\n      });\n    }\n\n    this.norm.clear();\n  }\n  // Adds a doc to the end of the index\n  add(doc) {\n    const idx = this.size();\n\n    if (isString(doc)) {\n      this._addString(doc, idx);\n    } else {\n      this._addObject(doc, idx);\n    }\n  }\n  // Removes the doc at the specified index of the index\n  removeAt(idx) {\n    this.records.splice(idx, 1);\n\n    // Change ref index of every subsquent doc\n    for (let i = idx, len = this.size(); i < len; i += 1) {\n      this.records[i].i -= 1;\n    }\n  }\n  getValueForItemAtKeyId(item, keyId) {\n    return item[this._keysMap[keyId]]\n  }\n  size() {\n    return this.records.length\n  }\n  _addString(doc, docIndex) {\n    if (!isDefined(doc) || isBlank(doc)) {\n      return\n    }\n\n    let record = {\n      v: doc,\n      i: docIndex,\n      n: this.norm.get(doc)\n    };\n\n    this.records.push(record);\n  }\n  _addObject(doc, docIndex) {\n    let record = { i: docIndex, $: {} };\n\n    // Iterate over every key (i.e, path), and fetch the value at that key\n    this.keys.forEach((key, keyIndex) => {\n      let value = key.getFn ? key.getFn(doc) : this.getFn(doc, key.path);\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      if (isArray(value)) {\n        let subRecords = [];\n        const stack = [{ nestedArrIndex: -1, value }];\n\n        while (stack.length) {\n          const { nestedArrIndex, value } = stack.pop();\n\n          if (!isDefined(value)) {\n            continue\n          }\n\n          if (isString(value) && !isBlank(value)) {\n            let subRecord = {\n              v: value,\n              i: nestedArrIndex,\n              n: this.norm.get(value)\n            };\n\n            subRecords.push(subRecord);\n          } else if (isArray(value)) {\n            value.forEach((item, k) => {\n              stack.push({\n                nestedArrIndex: k,\n                value: item\n              });\n            });\n          } else ;\n        }\n        record.$[keyIndex] = subRecords;\n      } else if (isString(value) && !isBlank(value)) {\n        let subRecord = {\n          v: value,\n          n: this.norm.get(value)\n        };\n\n        record.$[keyIndex] = subRecord;\n      }\n    });\n\n    this.records.push(record);\n  }\n  toJSON() {\n    return {\n      keys: this.keys,\n      records: this.records\n    }\n  }\n}\n\nfunction createIndex(\n  keys,\n  docs,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys.map(createKey));\n  myIndex.setSources(docs);\n  myIndex.create();\n  return myIndex\n}\n\nfunction parseIndex(\n  data,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const { keys, records } = data;\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys);\n  myIndex.setIndexRecords(records);\n  return myIndex\n}\n\nfunction computeScore$1(\n  pattern,\n  {\n    errors = 0,\n    currentLocation = 0,\n    expectedLocation = 0,\n    distance = Config.distance,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  const accuracy = errors / pattern.length;\n\n  if (ignoreLocation) {\n    return accuracy\n  }\n\n  const proximity = Math.abs(expectedLocation - currentLocation);\n\n  if (!distance) {\n    // Dodge divide by zero error.\n    return proximity ? 1.0 : accuracy\n  }\n\n  return accuracy + proximity / distance\n}\n\nfunction convertMaskToIndices(\n  matchmask = [],\n  minMatchCharLength = Config.minMatchCharLength\n) {\n  let indices = [];\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (let len = matchmask.length; i < len; i += 1) {\n    let match = matchmask[i];\n    if (match && start === -1) {\n      start = i;\n    } else if (!match && start !== -1) {\n      end = i - 1;\n      if (end - start + 1 >= minMatchCharLength) {\n        indices.push([start, end]);\n      }\n      start = -1;\n    }\n  }\n\n  // (i-1 - start) + 1 => i - start\n  if (matchmask[i - 1] && i - start >= minMatchCharLength) {\n    indices.push([start, i - 1]);\n  }\n\n  return indices\n}\n\n// Machine word size\nconst MAX_BITS = 32;\n\nfunction search(\n  text,\n  pattern,\n  patternAlphabet,\n  {\n    location = Config.location,\n    distance = Config.distance,\n    threshold = Config.threshold,\n    findAllMatches = Config.findAllMatches,\n    minMatchCharLength = Config.minMatchCharLength,\n    includeMatches = Config.includeMatches,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  if (pattern.length > MAX_BITS) {\n    throw new Error(PATTERN_LENGTH_TOO_LARGE(MAX_BITS))\n  }\n\n  const patternLen = pattern.length;\n  // Set starting location at beginning text and initialize the alphabet.\n  const textLen = text.length;\n  // Handle the case when location > text.length\n  const expectedLocation = Math.max(0, Math.min(location, textLen));\n  // Highest score beyond which we give up.\n  let currentThreshold = threshold;\n  // Is there a nearby exact match? (speedup)\n  let bestLocation = expectedLocation;\n\n  // Performance: only computer matches when the minMatchCharLength > 1\n  // OR if `includeMatches` is true.\n  const computeMatches = minMatchCharLength > 1 || includeMatches;\n  // A mask of the matches, used for building the indices\n  const matchMask = computeMatches ? Array(textLen) : [];\n\n  let index;\n\n  // Get all exact matches, here for speed up\n  while ((index = text.indexOf(pattern, bestLocation)) > -1) {\n    let score = computeScore$1(pattern, {\n      currentLocation: index,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    currentThreshold = Math.min(score, currentThreshold);\n    bestLocation = index + patternLen;\n\n    if (computeMatches) {\n      let i = 0;\n      while (i < patternLen) {\n        matchMask[index + i] = 1;\n        i += 1;\n      }\n    }\n  }\n\n  // Reset the best location\n  bestLocation = -1;\n\n  let lastBitArr = [];\n  let finalScore = 1;\n  let binMax = patternLen + textLen;\n\n  const mask = 1 << (patternLen - 1);\n\n  for (let i = 0; i < patternLen; i += 1) {\n    // Scan for the best match; each iteration allows for one more error.\n    // Run a binary search to determine how far from the match location we can stray\n    // at this error level.\n    let binMin = 0;\n    let binMid = binMax;\n\n    while (binMin < binMid) {\n      const score = computeScore$1(pattern, {\n        errors: i,\n        currentLocation: expectedLocation + binMid,\n        expectedLocation,\n        distance,\n        ignoreLocation\n      });\n\n      if (score <= currentThreshold) {\n        binMin = binMid;\n      } else {\n        binMax = binMid;\n      }\n\n      binMid = Math.floor((binMax - binMin) / 2 + binMin);\n    }\n\n    // Use the result from this iteration as the maximum for the next.\n    binMax = binMid;\n\n    let start = Math.max(1, expectedLocation - binMid + 1);\n    let finish = findAllMatches\n      ? textLen\n      : Math.min(expectedLocation + binMid, textLen) + patternLen;\n\n    // Initialize the bit array\n    let bitArr = Array(finish + 2);\n\n    bitArr[finish + 1] = (1 << i) - 1;\n\n    for (let j = finish; j >= start; j -= 1) {\n      let currentLocation = j - 1;\n      let charMatch = patternAlphabet[text.charAt(currentLocation)];\n\n      if (computeMatches) {\n        // Speed up: quick bool to int conversion (i.e, `charMatch ? 1 : 0`)\n        matchMask[currentLocation] = +!!charMatch;\n      }\n\n      // First pass: exact match\n      bitArr[j] = ((bitArr[j + 1] << 1) | 1) & charMatch;\n\n      // Subsequent passes: fuzzy match\n      if (i) {\n        bitArr[j] |=\n          ((lastBitArr[j + 1] | lastBitArr[j]) << 1) | 1 | lastBitArr[j + 1];\n      }\n\n      if (bitArr[j] & mask) {\n        finalScore = computeScore$1(pattern, {\n          errors: i,\n          currentLocation,\n          expectedLocation,\n          distance,\n          ignoreLocation\n        });\n\n        // This match will almost certainly be better than any existing match.\n        // But check anyway.\n        if (finalScore <= currentThreshold) {\n          // Indeed it is\n          currentThreshold = finalScore;\n          bestLocation = currentLocation;\n\n          // Already passed `loc`, downhill from here on in.\n          if (bestLocation <= expectedLocation) {\n            break\n          }\n\n          // When passing `bestLocation`, don't exceed our current distance from `expectedLocation`.\n          start = Math.max(1, 2 * expectedLocation - bestLocation);\n        }\n      }\n    }\n\n    // No hope for a (better) match at greater error levels.\n    const score = computeScore$1(pattern, {\n      errors: i + 1,\n      currentLocation: expectedLocation,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    if (score > currentThreshold) {\n      break\n    }\n\n    lastBitArr = bitArr;\n  }\n\n  const result = {\n    isMatch: bestLocation >= 0,\n    // Count exact matches (those with a score of 0) to be \"almost\" exact\n    score: Math.max(0.001, finalScore)\n  };\n\n  if (computeMatches) {\n    const indices = convertMaskToIndices(matchMask, minMatchCharLength);\n    if (!indices.length) {\n      result.isMatch = false;\n    } else if (includeMatches) {\n      result.indices = indices;\n    }\n  }\n\n  return result\n}\n\nfunction createPatternAlphabet(pattern) {\n  let mask = {};\n\n  for (let i = 0, len = pattern.length; i < len; i += 1) {\n    const char = pattern.charAt(i);\n    mask[char] = (mask[char] || 0) | (1 << (len - i - 1));\n  }\n\n  return mask\n}\n\nclass BitapSearch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    this.options = {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n\n    this.chunks = [];\n\n    if (!this.pattern.length) {\n      return\n    }\n\n    const addChunk = (pattern, startIndex) => {\n      this.chunks.push({\n        pattern,\n        alphabet: createPatternAlphabet(pattern),\n        startIndex\n      });\n    };\n\n    const len = this.pattern.length;\n\n    if (len > MAX_BITS) {\n      let i = 0;\n      const remainder = len % MAX_BITS;\n      const end = len - remainder;\n\n      while (i < end) {\n        addChunk(this.pattern.substr(i, MAX_BITS), i);\n        i += MAX_BITS;\n      }\n\n      if (remainder) {\n        const startIndex = len - MAX_BITS;\n        addChunk(this.pattern.substr(startIndex), startIndex);\n      }\n    } else {\n      addChunk(this.pattern, 0);\n    }\n  }\n\n  searchIn(text) {\n    const { isCaseSensitive, includeMatches } = this.options;\n\n    if (!isCaseSensitive) {\n      text = text.toLowerCase();\n    }\n\n    // Exact match\n    if (this.pattern === text) {\n      let result = {\n        isMatch: true,\n        score: 0\n      };\n\n      if (includeMatches) {\n        result.indices = [[0, text.length - 1]];\n      }\n\n      return result\n    }\n\n    // Otherwise, use Bitap algorithm\n    const {\n      location,\n      distance,\n      threshold,\n      findAllMatches,\n      minMatchCharLength,\n      ignoreLocation\n    } = this.options;\n\n    let allIndices = [];\n    let totalScore = 0;\n    let hasMatches = false;\n\n    this.chunks.forEach(({ pattern, alphabet, startIndex }) => {\n      const { isMatch, score, indices } = search(text, pattern, alphabet, {\n        location: location + startIndex,\n        distance,\n        threshold,\n        findAllMatches,\n        minMatchCharLength,\n        includeMatches,\n        ignoreLocation\n      });\n\n      if (isMatch) {\n        hasMatches = true;\n      }\n\n      totalScore += score;\n\n      if (isMatch && indices) {\n        allIndices = [...allIndices, ...indices];\n      }\n    });\n\n    let result = {\n      isMatch: hasMatches,\n      score: hasMatches ? totalScore / this.chunks.length : 1\n    };\n\n    if (hasMatches && includeMatches) {\n      result.indices = allIndices;\n    }\n\n    return result\n  }\n}\n\nclass BaseMatch {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n  static isMultiMatch(pattern) {\n    return getMatch(pattern, this.multiRegex)\n  }\n  static isSingleMatch(pattern) {\n    return getMatch(pattern, this.singleRegex)\n  }\n  search(/*text*/) {}\n}\n\nfunction getMatch(pattern, exp) {\n  const matches = pattern.match(exp);\n  return matches ? matches[1] : null\n}\n\n// Token: 'file\n\nclass ExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'exact'\n  }\n  static get multiRegex() {\n    return /^=\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^=(.*)$/\n  }\n  search(text) {\n    const isMatch = text === this.pattern;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !fire\n\nclass InverseExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!(.*)$/\n  }\n  search(text) {\n    const index = text.indexOf(this.pattern);\n    const isMatch = index === -1;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: ^file\n\nclass PrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'prefix-exact'\n  }\n  static get multiRegex() {\n    return /^\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !^fire\n\nclass InversePrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-prefix-exact'\n  }\n  static get multiRegex() {\n    return /^!\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = !text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: .file$\n\nclass SuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'suffix-exact'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = text.endsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [text.length - this.pattern.length, text.length - 1]\n    }\n  }\n}\n\n// Token: !.file$\n\nclass InverseSuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-suffix-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^!(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = !text.endsWith(this.pattern);\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\nclass FuzzyMatch extends BaseMatch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    super(pattern);\n    this._bitapSearch = new BitapSearch(pattern, {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    });\n  }\n  static get type() {\n    return 'fuzzy'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^(.*)$/\n  }\n  search(text) {\n    return this._bitapSearch.searchIn(text)\n  }\n}\n\n// Token: 'file\n\nclass IncludeMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'include'\n  }\n  static get multiRegex() {\n    return /^'\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^'(.*)$/\n  }\n  search(text) {\n    let location = 0;\n    let index;\n\n    const indices = [];\n    const patternLen = this.pattern.length;\n\n    // Get all exact matches\n    while ((index = text.indexOf(this.pattern, location)) > -1) {\n      location = index + patternLen;\n      indices.push([index, location - 1]);\n    }\n\n    const isMatch = !!indices.length;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices\n    }\n  }\n}\n\n// ❗Order is important. DO NOT CHANGE.\nconst searchers = [\n  ExactMatch,\n  IncludeMatch,\n  PrefixExactMatch,\n  InversePrefixExactMatch,\n  InverseSuffixExactMatch,\n  SuffixExactMatch,\n  InverseExactMatch,\n  FuzzyMatch\n];\n\nconst searchersLen = searchers.length;\n\n// Regex to split by spaces, but keep anything in quotes together\nconst SPACE_RE = / +(?=(?:[^\\\"]*\\\"[^\\\"]*\\\")*[^\\\"]*$)/;\nconst OR_TOKEN = '|';\n\n// Return a 2D array representation of the query, for simpler parsing.\n// Example:\n// \"^core go$ | rb$ | py$ xy$\" => [[\"^core\", \"go$\"], [\"rb$\"], [\"py$\", \"xy$\"]]\nfunction parseQuery(pattern, options = {}) {\n  return pattern.split(OR_TOKEN).map((item) => {\n    let query = item\n      .trim()\n      .split(SPACE_RE)\n      .filter((item) => item && !!item.trim());\n\n    let results = [];\n    for (let i = 0, len = query.length; i < len; i += 1) {\n      const queryItem = query[i];\n\n      // 1. Handle multiple query match (i.e, once that are quoted, like `\"hello world\"`)\n      let found = false;\n      let idx = -1;\n      while (!found && ++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isMultiMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          found = true;\n        }\n      }\n\n      if (found) {\n        continue\n      }\n\n      // 2. Handle single query matches (i.e, once that are *not* quoted)\n      idx = -1;\n      while (++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isSingleMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          break\n        }\n      }\n    }\n\n    return results\n  })\n}\n\n// These extended matchers can return an array of matches, as opposed\n// to a singl match\nconst MultiMatchSet = new Set([FuzzyMatch.type, IncludeMatch.type]);\n\n/**\n * Command-like searching\n * ======================\n *\n * Given multiple search terms delimited by spaces.e.g. `^jscript .python$ ruby !java`,\n * search in a given text.\n *\n * Search syntax:\n *\n * | Token       | Match type                 | Description                            |\n * | ----------- | -------------------------- | -------------------------------------- |\n * | `jscript`   | fuzzy-match                | Items that fuzzy match `jscript`       |\n * | `=scheme`   | exact-match                | Items that are `scheme`                |\n * | `'python`   | include-match              | Items that include `python`            |\n * | `!ruby`     | inverse-exact-match        | Items that do not include `ruby`       |\n * | `^java`     | prefix-exact-match         | Items that start with `java`           |\n * | `!^earlang` | inverse-prefix-exact-match | Items that do not start with `earlang` |\n * | `.js$`      | suffix-exact-match         | Items that end with `.js`              |\n * | `!.go$`     | inverse-suffix-exact-match | Items that do not end with `.go`       |\n *\n * A single pipe character acts as an OR operator. For example, the following\n * query matches entries that start with `core` and end with either`go`, `rb`,\n * or`py`.\n *\n * ```\n * ^core go$ | rb$ | py$\n * ```\n */\nclass ExtendedSearch {\n  constructor(\n    pattern,\n    {\n      isCaseSensitive = Config.isCaseSensitive,\n      includeMatches = Config.includeMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      ignoreLocation = Config.ignoreLocation,\n      findAllMatches = Config.findAllMatches,\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance\n    } = {}\n  ) {\n    this.query = null;\n    this.options = {\n      isCaseSensitive,\n      includeMatches,\n      minMatchCharLength,\n      findAllMatches,\n      ignoreLocation,\n      location,\n      threshold,\n      distance\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n    this.query = parseQuery(this.pattern, this.options);\n  }\n\n  static condition(_, options) {\n    return options.useExtendedSearch\n  }\n\n  searchIn(text) {\n    const query = this.query;\n\n    if (!query) {\n      return {\n        isMatch: false,\n        score: 1\n      }\n    }\n\n    const { includeMatches, isCaseSensitive } = this.options;\n\n    text = isCaseSensitive ? text : text.toLowerCase();\n\n    let numMatches = 0;\n    let allIndices = [];\n    let totalScore = 0;\n\n    // ORs\n    for (let i = 0, qLen = query.length; i < qLen; i += 1) {\n      const searchers = query[i];\n\n      // Reset indices\n      allIndices.length = 0;\n      numMatches = 0;\n\n      // ANDs\n      for (let j = 0, pLen = searchers.length; j < pLen; j += 1) {\n        const searcher = searchers[j];\n        const { isMatch, indices, score } = searcher.search(text);\n\n        if (isMatch) {\n          numMatches += 1;\n          totalScore += score;\n          if (includeMatches) {\n            const type = searcher.constructor.type;\n            if (MultiMatchSet.has(type)) {\n              allIndices = [...allIndices, ...indices];\n            } else {\n              allIndices.push(indices);\n            }\n          }\n        } else {\n          totalScore = 0;\n          numMatches = 0;\n          allIndices.length = 0;\n          break\n        }\n      }\n\n      // OR condition, so if TRUE, return\n      if (numMatches) {\n        let result = {\n          isMatch: true,\n          score: totalScore / numMatches\n        };\n\n        if (includeMatches) {\n          result.indices = allIndices;\n        }\n\n        return result\n      }\n    }\n\n    // Nothing was matched\n    return {\n      isMatch: false,\n      score: 1\n    }\n  }\n}\n\nconst registeredSearchers = [];\n\nfunction register(...args) {\n  registeredSearchers.push(...args);\n}\n\nfunction createSearcher(pattern, options) {\n  for (let i = 0, len = registeredSearchers.length; i < len; i += 1) {\n    let searcherClass = registeredSearchers[i];\n    if (searcherClass.condition(pattern, options)) {\n      return new searcherClass(pattern, options)\n    }\n  }\n\n  return new BitapSearch(pattern, options)\n}\n\nconst LogicalOperator = {\n  AND: '$and',\n  OR: '$or'\n};\n\nconst KeyType = {\n  PATH: '$path',\n  PATTERN: '$val'\n};\n\nconst isExpression = (query) =>\n  !!(query[LogicalOperator.AND] || query[LogicalOperator.OR]);\n\nconst isPath = (query) => !!query[KeyType.PATH];\n\nconst isLeaf = (query) =>\n  !isArray(query) && isObject(query) && !isExpression(query);\n\nconst convertToExplicit = (query) => ({\n  [LogicalOperator.AND]: Object.keys(query).map((key) => ({\n    [key]: query[key]\n  }))\n});\n\n// When `auto` is `true`, the parse function will infer and initialize and add\n// the appropriate `Searcher` instance\nfunction parse(query, options, { auto = true } = {}) {\n  const next = (query) => {\n    let keys = Object.keys(query);\n\n    const isQueryPath = isPath(query);\n\n    if (!isQueryPath && keys.length > 1 && !isExpression(query)) {\n      return next(convertToExplicit(query))\n    }\n\n    if (isLeaf(query)) {\n      const key = isQueryPath ? query[KeyType.PATH] : keys[0];\n\n      const pattern = isQueryPath ? query[KeyType.PATTERN] : query[key];\n\n      if (!isString(pattern)) {\n        throw new Error(LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY(key))\n      }\n\n      const obj = {\n        keyId: createKeyId(key),\n        pattern\n      };\n\n      if (auto) {\n        obj.searcher = createSearcher(pattern, options);\n      }\n\n      return obj\n    }\n\n    let node = {\n      children: [],\n      operator: keys[0]\n    };\n\n    keys.forEach((key) => {\n      const value = query[key];\n\n      if (isArray(value)) {\n        value.forEach((item) => {\n          node.children.push(next(item));\n        });\n      }\n    });\n\n    return node\n  };\n\n  if (!isExpression(query)) {\n    query = convertToExplicit(query);\n  }\n\n  return next(query)\n}\n\n// Practical scoring function\nfunction computeScore(\n  results,\n  { ignoreFieldNorm = Config.ignoreFieldNorm }\n) {\n  results.forEach((result) => {\n    let totalScore = 1;\n\n    result.matches.forEach(({ key, norm, score }) => {\n      const weight = key ? key.weight : null;\n\n      totalScore *= Math.pow(\n        score === 0 && weight ? Number.EPSILON : score,\n        (weight || 1) * (ignoreFieldNorm ? 1 : norm)\n      );\n    });\n\n    result.score = totalScore;\n  });\n}\n\nfunction transformMatches(result, data) {\n  const matches = result.matches;\n  data.matches = [];\n\n  if (!isDefined(matches)) {\n    return\n  }\n\n  matches.forEach((match) => {\n    if (!isDefined(match.indices) || !match.indices.length) {\n      return\n    }\n\n    const { indices, value } = match;\n\n    let obj = {\n      indices,\n      value\n    };\n\n    if (match.key) {\n      obj.key = match.key.src;\n    }\n\n    if (match.idx > -1) {\n      obj.refIndex = match.idx;\n    }\n\n    data.matches.push(obj);\n  });\n}\n\nfunction transformScore(result, data) {\n  data.score = result.score;\n}\n\nfunction format(\n  results,\n  docs,\n  {\n    includeMatches = Config.includeMatches,\n    includeScore = Config.includeScore\n  } = {}\n) {\n  const transformers = [];\n\n  if (includeMatches) transformers.push(transformMatches);\n  if (includeScore) transformers.push(transformScore);\n\n  return results.map((result) => {\n    const { idx } = result;\n\n    const data = {\n      item: docs[idx],\n      refIndex: idx\n    };\n\n    if (transformers.length) {\n      transformers.forEach((transformer) => {\n        transformer(result, data);\n      });\n    }\n\n    return data\n  })\n}\n\nclass Fuse {\n  constructor(docs, options = {}, index) {\n    this.options = { ...Config, ...options };\n\n    if (\n      this.options.useExtendedSearch &&\n      !true\n    ) {\n      throw new Error(EXTENDED_SEARCH_UNAVAILABLE)\n    }\n\n    this._keyStore = new KeyStore(this.options.keys);\n\n    this.setCollection(docs, index);\n  }\n\n  setCollection(docs, index) {\n    this._docs = docs;\n\n    if (index && !(index instanceof FuseIndex)) {\n      throw new Error(INCORRECT_INDEX_TYPE)\n    }\n\n    this._myIndex =\n      index ||\n      createIndex(this.options.keys, this._docs, {\n        getFn: this.options.getFn,\n        fieldNormWeight: this.options.fieldNormWeight\n      });\n  }\n\n  add(doc) {\n    if (!isDefined(doc)) {\n      return\n    }\n\n    this._docs.push(doc);\n    this._myIndex.add(doc);\n  }\n\n  remove(predicate = (/* doc, idx */) => false) {\n    const results = [];\n\n    for (let i = 0, len = this._docs.length; i < len; i += 1) {\n      const doc = this._docs[i];\n      if (predicate(doc, i)) {\n        this.removeAt(i);\n        i -= 1;\n        len -= 1;\n\n        results.push(doc);\n      }\n    }\n\n    return results\n  }\n\n  removeAt(idx) {\n    this._docs.splice(idx, 1);\n    this._myIndex.removeAt(idx);\n  }\n\n  getIndex() {\n    return this._myIndex\n  }\n\n  search(query, { limit = -1 } = {}) {\n    const {\n      includeMatches,\n      includeScore,\n      shouldSort,\n      sortFn,\n      ignoreFieldNorm\n    } = this.options;\n\n    let results = isString(query)\n      ? isString(this._docs[0])\n        ? this._searchStringList(query)\n        : this._searchObjectList(query)\n      : this._searchLogical(query);\n\n    computeScore(results, { ignoreFieldNorm });\n\n    if (shouldSort) {\n      results.sort(sortFn);\n    }\n\n    if (isNumber(limit) && limit > -1) {\n      results = results.slice(0, limit);\n    }\n\n    return format(results, this._docs, {\n      includeMatches,\n      includeScore\n    })\n  }\n\n  _searchStringList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { records } = this._myIndex;\n    const results = [];\n\n    // Iterate over every string in the index\n    records.forEach(({ v: text, i: idx, n: norm }) => {\n      if (!isDefined(text)) {\n        return\n      }\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        results.push({\n          item: text,\n          idx,\n          matches: [{ score, value: text, norm, indices }]\n        });\n      }\n    });\n\n    return results\n  }\n\n  _searchLogical(query) {\n\n    const expression = parse(query, this.options);\n\n    const evaluate = (node, item, idx) => {\n      if (!node.children) {\n        const { keyId, searcher } = node;\n\n        const matches = this._findMatches({\n          key: this._keyStore.get(keyId),\n          value: this._myIndex.getValueForItemAtKeyId(item, keyId),\n          searcher\n        });\n\n        if (matches && matches.length) {\n          return [\n            {\n              idx,\n              item,\n              matches\n            }\n          ]\n        }\n\n        return []\n      }\n\n      const res = [];\n      for (let i = 0, len = node.children.length; i < len; i += 1) {\n        const child = node.children[i];\n        const result = evaluate(child, item, idx);\n        if (result.length) {\n          res.push(...result);\n        } else if (node.operator === LogicalOperator.AND) {\n          return []\n        }\n      }\n      return res\n    };\n\n    const records = this._myIndex.records;\n    const resultMap = {};\n    const results = [];\n\n    records.forEach(({ $: item, i: idx }) => {\n      if (isDefined(item)) {\n        let expResults = evaluate(expression, item, idx);\n\n        if (expResults.length) {\n          // Dedupe when adding\n          if (!resultMap[idx]) {\n            resultMap[idx] = { idx, item, matches: [] };\n            results.push(resultMap[idx]);\n          }\n          expResults.forEach(({ matches }) => {\n            resultMap[idx].matches.push(...matches);\n          });\n        }\n      }\n    });\n\n    return results\n  }\n\n  _searchObjectList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { keys, records } = this._myIndex;\n    const results = [];\n\n    // List is Array<Object>\n    records.forEach(({ $: item, i: idx }) => {\n      if (!isDefined(item)) {\n        return\n      }\n\n      let matches = [];\n\n      // Iterate over every key (i.e, path), and fetch the value at that key\n      keys.forEach((key, keyIndex) => {\n        matches.push(\n          ...this._findMatches({\n            key,\n            value: item[keyIndex],\n            searcher\n          })\n        );\n      });\n\n      if (matches.length) {\n        results.push({\n          idx,\n          item,\n          matches\n        });\n      }\n    });\n\n    return results\n  }\n  _findMatches({ key, value, searcher }) {\n    if (!isDefined(value)) {\n      return []\n    }\n\n    let matches = [];\n\n    if (isArray(value)) {\n      value.forEach(({ v: text, i: idx, n: norm }) => {\n        if (!isDefined(text)) {\n          return\n        }\n\n        const { isMatch, score, indices } = searcher.searchIn(text);\n\n        if (isMatch) {\n          matches.push({\n            score,\n            key,\n            value: text,\n            idx,\n            norm,\n            indices\n          });\n        }\n      });\n    } else {\n      const { v: text, n: norm } = value;\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        matches.push({ score, key, value: text, norm, indices });\n      }\n    }\n\n    return matches\n  }\n}\n\nFuse.version = '6.6.2';\nFuse.createIndex = createIndex;\nFuse.parseIndex = parseIndex;\nFuse.config = Config;\n\n{\n  Fuse.parseQuery = parse;\n}\n\n{\n  register(ExtendedSearch);\n}\n\nexport { Fuse as default };\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useDeepMatches = exports.useMatches = exports.NO_GROUP = void 0;\nvar React = __importStar(require(\"react\"));\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar fuse_js_1 = __importDefault(require(\"fuse.js\"));\nexports.NO_GROUP = {\n    name: \"none\",\n    priority: utils_1.Priority.NORMAL,\n};\nvar fuseOptions = {\n    keys: [\n        {\n            name: \"name\",\n            weight: 0.5,\n        },\n        {\n            name: \"keywords\",\n            getFn: function (item) { var _a; return ((_a = item.keywords) !== null && _a !== void 0 ? _a : \"\").split(\",\"); },\n            weight: 0.5,\n        },\n        \"subtitle\",\n    ],\n    ignoreLocation: true,\n    includeScore: true,\n    includeMatches: true,\n    threshold: 0.2,\n    minMatchCharLength: 1,\n};\nfunction order(a, b) {\n    /**\n     * Larger the priority = higher up the list\n     */\n    return b.priority - a.priority;\n}\n/**\n * returns deep matches only when a search query is present\n */\nfunction useMatches() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        actions: state.actions,\n        rootActionId: state.currentRootActionId,\n    }); }), search = _a.search, actions = _a.actions, rootActionId = _a.rootActionId;\n    var rootResults = React.useMemo(function () {\n        return Object.keys(actions)\n            .reduce(function (acc, actionId) {\n            var action = actions[actionId];\n            if (!action.parent && !rootActionId) {\n                acc.push(action);\n            }\n            if (action.id === rootActionId) {\n                for (var i = 0; i < action.children.length; i++) {\n                    acc.push(action.children[i]);\n                }\n            }\n            return acc;\n        }, [])\n            .sort(order);\n    }, [actions, rootActionId]);\n    var getDeepResults = React.useCallback(function (actions) {\n        var actionsClone = [];\n        for (var i = 0; i < actions.length; i++) {\n            actionsClone.push(actions[i]);\n        }\n        return (function collectChildren(actions, all) {\n            if (all === void 0) { all = actionsClone; }\n            for (var i = 0; i < actions.length; i++) {\n                if (actions[i].children.length > 0) {\n                    var childsChildren = actions[i].children;\n                    for (var i_1 = 0; i_1 < childsChildren.length; i_1++) {\n                        all.push(childsChildren[i_1]);\n                    }\n                    collectChildren(actions[i].children, all);\n                }\n            }\n            return all;\n        })(actions);\n    }, []);\n    var emptySearch = !search;\n    var filtered = React.useMemo(function () {\n        if (emptySearch)\n            return rootResults;\n        return getDeepResults(rootResults);\n    }, [getDeepResults, rootResults, emptySearch]);\n    var fuse = React.useMemo(function () { return new fuse_js_1.default(filtered, fuseOptions); }, [filtered]);\n    var matches = useInternalMatches(filtered, search, fuse);\n    var results = React.useMemo(function () {\n        var _a, _b;\n        /**\n         * Store a reference to a section and it's list of actions.\n         * Alongside these actions, we'll keep a temporary record of the\n         * final priority calculated by taking the commandScore + the\n         * explicitly set `action.priority` value.\n         */\n        var map = {};\n        /**\n         * Store another reference to a list of sections alongside\n         * the section's final priority, calculated the same as above.\n         */\n        var list = [];\n        /**\n         * We'll take the list above and sort by its priority. Then we'll\n         * collect all actions from the map above for this specific name and\n         * sort by its priority as well.\n         */\n        var ordered = [];\n        for (var i = 0; i < matches.length; i++) {\n            var match = matches[i];\n            var action = match.action;\n            var score = match.score || utils_1.Priority.NORMAL;\n            var section = {\n                name: typeof action.section === \"string\"\n                    ? action.section\n                    : ((_a = action.section) === null || _a === void 0 ? void 0 : _a.name) || exports.NO_GROUP.name,\n                priority: typeof action.section === \"string\"\n                    ? score\n                    : ((_b = action.section) === null || _b === void 0 ? void 0 : _b.priority) || 0 + score,\n            };\n            if (!map[section.name]) {\n                map[section.name] = [];\n                list.push(section);\n            }\n            map[section.name].push({\n                priority: action.priority + score,\n                action: action,\n            });\n        }\n        ordered = list.sort(order).map(function (group) { return ({\n            name: group.name,\n            actions: map[group.name].sort(order).map(function (item) { return item.action; }),\n        }); });\n        /**\n         * Our final result is simply flattening the ordered list into\n         * our familiar (ActionImpl | string)[] shape.\n         */\n        var results = [];\n        for (var i = 0; i < ordered.length; i++) {\n            var group = ordered[i];\n            if (group.name !== exports.NO_GROUP.name)\n                results.push(group.name);\n            for (var i_2 = 0; i_2 < group.actions.length; i_2++) {\n                results.push(group.actions[i_2]);\n            }\n        }\n        return results;\n    }, [matches]);\n    // ensure that users have an accurate `currentRootActionId`\n    // that syncs with the throttled return value.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var memoRootActionId = React.useMemo(function () { return rootActionId; }, [results]);\n    return React.useMemo(function () { return ({\n        results: results,\n        rootActionId: memoRootActionId,\n    }); }, [memoRootActionId, results]);\n}\nexports.useMatches = useMatches;\nfunction useInternalMatches(filtered, search, fuse) {\n    var value = React.useMemo(function () { return ({\n        filtered: filtered,\n        search: search,\n    }); }, [filtered, search]);\n    var _a = (0, utils_1.useThrottledValue)(value), throttledFiltered = _a.filtered, throttledSearch = _a.search;\n    return React.useMemo(function () {\n        if (throttledSearch.trim() === \"\") {\n            return throttledFiltered.map(function (action) { return ({ score: 0, action: action }); });\n        }\n        var matches = [];\n        // Use Fuse's `search` method to perform the search efficiently\n        var searchResults = fuse.search(throttledSearch);\n        // Format the search results to match the existing structure\n        matches = searchResults.map(function (_a) {\n            var action = _a.item, score = _a.score;\n            return ({\n                score: 1 / ((score !== null && score !== void 0 ? score : 0) + 1),\n                action: action,\n            });\n        });\n        return matches;\n    }, [throttledFiltered, throttledSearch, fuse]);\n}\n/**\n * @deprecated use useMatches\n */\nexports.useDeepMatches = useMatches;\n", "export { composeRefs, useComposedRefs } from './compose-refs';\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "export {\n  Slot,\n  Slottable,\n  //\n  Root,\n  createSlot,\n  createSlottable,\n} from './slot';\nexport type { SlotProps } from './slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n", "export {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n} from './primitive';\nexport type { PrimitivePropsWithRef } from './primitive';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n", "export { useLayoutEffect } from './use-layout-effect';\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n", "'use client';\nexport {\n  Portal,\n  //\n  Root,\n} from './portal';\nexport type { PortalProps } from './portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarPortal = void 0;\nvar react_portal_1 = require(\"@radix-ui/react-portal\");\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nfunction KBarPortal(_a) {\n    var children = _a.children, container = _a.container;\n    var showing = (0, useKBar_1.useKBar)(function (state) { return ({\n        showing: state.visualState !== types_1.VisualState.hidden,\n    }); }).showing;\n    if (!showing) {\n        return null;\n    }\n    return React.createElement(react_portal_1.Portal, { container: container }, children);\n}\nexports.KBarPortal = KBarPortal;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarPositioner = void 0;\nvar React = __importStar(require(\"react\"));\nvar defaultStyle = {\n    position: \"fixed\",\n    display: \"flex\",\n    alignItems: \"flex-start\",\n    justifyContent: \"center\",\n    width: \"100%\",\n    inset: \"0px\",\n    padding: \"14vh 16px 16px\",\n};\nfunction getStyle(style) {\n    return style ? __assign(__assign({}, defaultStyle), style) : defaultStyle;\n}\nexports.KBarPositioner = React.forwardRef(function (_a, ref) {\n    var style = _a.style, children = _a.children, props = __rest(_a, [\"style\", \"children\"]);\n    return (React.createElement(\"div\", __assign({ ref: ref, style: getStyle(style) }, props), children));\n});\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarSearch = exports.getListboxItemId = exports.KBAR_LISTBOX = void 0;\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nexports.KBAR_LISTBOX = \"kbar-listbox\";\nvar getListboxItemId = function (id) { return \"kbar-listbox-item-\" + id; };\nexports.getListboxItemId = getListboxItemId;\nfunction KBarSearch(props) {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        actions: state.actions,\n        activeIndex: state.activeIndex,\n        showing: state.visualState === types_1.VisualState.showing,\n    }); }), query = _a.query, search = _a.search, actions = _a.actions, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, showing = _a.showing, options = _a.options;\n    var _b = React.useState(search), inputValue = _b[0], setInputValue = _b[1];\n    React.useEffect(function () {\n        query.setSearch(inputValue);\n    }, [inputValue, query]);\n    var defaultPlaceholder = props.defaultPlaceholder, rest = __rest(props, [\"defaultPlaceholder\"]);\n    React.useEffect(function () {\n        query.setSearch(\"\");\n        query.getInput().focus();\n        return function () { return query.setSearch(\"\"); };\n    }, [currentRootActionId, query]);\n    var placeholder = React.useMemo(function () {\n        var defaultText = defaultPlaceholder !== null && defaultPlaceholder !== void 0 ? defaultPlaceholder : \"Type a command or search…\";\n        return currentRootActionId && actions[currentRootActionId]\n            ? actions[currentRootActionId].name\n            : defaultText;\n    }, [actions, currentRootActionId, defaultPlaceholder]);\n    return (React.createElement(\"input\", __assign({}, rest, { ref: query.inputRefSetter, autoFocus: true, autoComplete: \"off\", role: \"combobox\", spellCheck: \"false\", \"aria-expanded\": showing, \"aria-controls\": exports.KBAR_LISTBOX, \"aria-activedescendant\": (0, exports.getListboxItemId)(activeIndex), value: inputValue, placeholder: placeholder, onChange: function (event) {\n            var _a, _b, _c;\n            (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            setInputValue(event.target.value);\n            (_c = (_b = options === null || options === void 0 ? void 0 : options.callbacks) === null || _b === void 0 ? void 0 : _b.onQueryChange) === null || _c === void 0 ? void 0 : _c.call(_b, event.target.value);\n        }, onKeyDown: function (event) {\n            var _a;\n            (_a = props.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            if (currentRootActionId && !search && event.key === \"Backspace\") {\n                var parent_1 = actions[currentRootActionId].parent;\n                query.setCurrentRootAction(parent_1);\n            }\n        } })));\n}\nexports.KBarSearch = KBarSearch;\n", "var props = ['bottom', 'height', 'left', 'right', 'top', 'width'];\n\nvar rectChanged = function rectChanged(a, b) {\n  if (a === void 0) {\n    a = {};\n  }\n\n  if (b === void 0) {\n    b = {};\n  }\n\n  return props.some(function (prop) {\n    return a[prop] !== b[prop];\n  });\n};\n\nvar observedNodes =\n/*#__PURE__*/\nnew Map();\nvar rafId;\n\nvar run = function run() {\n  var changedStates = [];\n  observedNodes.forEach(function (state, node) {\n    var newRect = node.getBoundingClientRect();\n\n    if (rectChanged(newRect, state.rect)) {\n      state.rect = newRect;\n      changedStates.push(state);\n    }\n  });\n  changedStates.forEach(function (state) {\n    state.callbacks.forEach(function (cb) {\n      return cb(state.rect);\n    });\n  });\n  rafId = window.requestAnimationFrame(run);\n};\n\nfunction observeRect(node, cb) {\n  return {\n    observe: function observe() {\n      var wasEmpty = observedNodes.size === 0;\n\n      if (observedNodes.has(node)) {\n        observedNodes.get(node).callbacks.push(cb);\n      } else {\n        observedNodes.set(node, {\n          rect: undefined,\n          hasRectChanged: false,\n          callbacks: [cb]\n        });\n      }\n\n      if (wasEmpty) run();\n    },\n    unobserve: function unobserve() {\n      var state = observedNodes.get(node);\n\n      if (state) {\n        // Remove the callback\n        var index = state.callbacks.indexOf(cb);\n        if (index >= 0) state.callbacks.splice(index, 1); // Remove the node reference\n\n        if (!state.callbacks.length) observedNodes[\"delete\"](node); // Stop the loop\n\n        if (!observedNodes.size) cancelAnimationFrame(rafId);\n      }\n    }\n  };\n}\n\nexport default observeRect;\n//# sourceMappingURL=observe-rect.esm.js.map\n", "import React from 'react'\n\nexport default typeof window !== 'undefined'\n  ? React.useLayoutEffect\n  : React.useEffect\n", "import React from 'react'\nimport observeRect from '@reach/observe-rect'\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect'\n\nexport default function useRect(\n  nodeRef,\n  initialRect = { width: 0, height: 0 }\n) {\n  const [element, setElement] = React.useState(nodeRef.current)\n  const [rect, dispatch] = React.useReducer(rectReducer, initialRect)\n  const initialRectSet = React.useRef(false)\n\n  useIsomorphicLayoutEffect(() => {\n    if (nodeRef.current !== element) {\n      setElement(nodeRef.current)\n    }\n  })\n\n  useIsomorphicLayoutEffect(() => {\n    if (element && !initialRectSet.current) {\n      initialRectSet.current = true\n      const rect = element.getBoundingClientRect()\n      dispatch({ rect })\n    }\n  }, [element])\n\n  React.useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    const observer = observeRect(element, rect => {\n      dispatch({ rect })\n    })\n\n    observer.observe()\n\n    return () => {\n      observer.unobserve()\n    }\n  }, [element])\n\n  return rect\n}\n\nfunction rectReducer(state, action) {\n  const rect = action.rect\n  if (state.height !== rect.height || state.width !== rect.width) {\n    return rect\n  }\n  return state\n}\n", "import React from 'react'\nimport useRect from './useRect'\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect'\n\nconst defaultEstimateSize = () => 50\n\nconst defaultKeyExtractor = index => index\n\nconst defaultMeasureSize = (el, horizontal) => {\n  const key = horizontal ? 'offsetWidth' : 'offsetHeight'\n\n  return el[key]\n}\n\nexport const defaultRangeExtractor = range => {\n  const start = Math.max(range.start - range.overscan, 0)\n  const end = Math.min(range.end + range.overscan, range.size - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport function useVirtual({\n  size = 0,\n  estimateSize = defaultEstimateSize,\n  overscan = 1,\n  paddingStart = 0,\n  paddingEnd = 0,\n  parentRef,\n  horizontal,\n  scrollToFn,\n  useObserver,\n  initialRect,\n  onScrollElement,\n  scrollOffsetFn,\n  keyExtractor = defaultKeyExtractor,\n  measureSize = defaultMeasureSize,\n  rangeExtractor = defaultRangeExtractor,\n}) {\n  const sizeKey = horizontal ? 'width' : 'height'\n  const scrollKey = horizontal ? 'scrollLeft' : 'scrollTop'\n\n  const latestRef = React.useRef({\n    scrollOffset: 0,\n    measurements: [],\n  })\n\n  const [scrollOffset, setScrollOffset] = React.useState(0)\n  latestRef.current.scrollOffset = scrollOffset\n\n  const useMeasureParent = useObserver || useRect\n\n  const { [sizeKey]: outerSize } = useMeasureParent(parentRef, initialRect)\n\n  latestRef.current.outerSize = outerSize\n\n  const defaultScrollToFn = React.useCallback(\n    offset => {\n      if (parentRef.current) {\n        parentRef.current[scrollKey] = offset\n      }\n    },\n    [parentRef, scrollKey]\n  )\n\n  const resolvedScrollToFn = scrollToFn || defaultScrollToFn\n\n  scrollToFn = React.useCallback(\n    offset => {\n      resolvedScrollToFn(offset, defaultScrollToFn)\n    },\n    [defaultScrollToFn, resolvedScrollToFn]\n  )\n\n  const [measuredCache, setMeasuredCache] = React.useState({})\n\n  const measure = React.useCallback(() => setMeasuredCache({}), [])\n\n  const pendingMeasuredCacheIndexesRef = React.useRef([])\n\n  const measurements = React.useMemo(() => {\n    const min =\n      pendingMeasuredCacheIndexesRef.current.length > 0\n        ? Math.min(...pendingMeasuredCacheIndexesRef.current)\n        : 0\n    pendingMeasuredCacheIndexesRef.current = []\n\n    const measurements = latestRef.current.measurements.slice(0, min)\n\n    for (let i = min; i < size; i++) {\n      const key = keyExtractor(i)\n      const measuredSize = measuredCache[key]\n      const start = measurements[i - 1] ? measurements[i - 1].end : paddingStart\n      const size =\n        typeof measuredSize === 'number' ? measuredSize : estimateSize(i)\n      const end = start + size\n      measurements[i] = { index: i, start, size, end, key }\n    }\n    return measurements\n  }, [estimateSize, measuredCache, paddingStart, size, keyExtractor])\n\n  const totalSize = (measurements[size - 1]?.end || paddingStart) + paddingEnd\n\n  latestRef.current.measurements = measurements\n  latestRef.current.totalSize = totalSize\n\n  const element = onScrollElement ? onScrollElement.current : parentRef.current\n\n  const scrollOffsetFnRef = React.useRef(scrollOffsetFn)\n  scrollOffsetFnRef.current = scrollOffsetFn\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      setScrollOffset(0)\n\n      return\n    }\n\n    const onScroll = event => {\n      const offset = scrollOffsetFnRef.current\n        ? scrollOffsetFnRef.current(event)\n        : element[scrollKey]\n\n      setScrollOffset(offset)\n    }\n\n    onScroll()\n\n    element.addEventListener('scroll', onScroll, {\n      capture: false,\n      passive: true,\n    })\n\n    return () => {\n      element.removeEventListener('scroll', onScroll)\n    }\n  }, [element, scrollKey])\n\n  const { start, end } = calculateRange(latestRef.current)\n\n  const indexes = React.useMemo(\n    () =>\n      rangeExtractor({\n        start,\n        end,\n        overscan,\n        size: measurements.length,\n      }),\n    [start, end, overscan, measurements.length, rangeExtractor]\n  )\n\n  const measureSizeRef = React.useRef(measureSize)\n  measureSizeRef.current = measureSize\n\n  const virtualItems = React.useMemo(() => {\n    const virtualItems = []\n\n    for (let k = 0, len = indexes.length; k < len; k++) {\n      const i = indexes[k]\n      const measurement = measurements[i]\n\n      const item = {\n        ...measurement,\n        measureRef: el => {\n          if (el) {\n            const measuredSize = measureSizeRef.current(el, horizontal)\n\n            if (measuredSize !== item.size) {\n              const { scrollOffset } = latestRef.current\n\n              if (item.start < scrollOffset) {\n                defaultScrollToFn(scrollOffset + (measuredSize - item.size))\n              }\n\n              pendingMeasuredCacheIndexesRef.current.push(i)\n\n              setMeasuredCache(old => ({\n                ...old,\n                [item.key]: measuredSize,\n              }))\n            }\n          }\n        },\n      }\n\n      virtualItems.push(item)\n    }\n\n    return virtualItems\n  }, [indexes, defaultScrollToFn, horizontal, measurements])\n\n  const mountedRef = React.useRef(false)\n\n  useIsomorphicLayoutEffect(() => {\n    if (mountedRef.current) {\n      setMeasuredCache({})\n    }\n    mountedRef.current = true\n  }, [estimateSize])\n\n  const scrollToOffset = React.useCallback(\n    (toOffset, { align = 'start' } = {}) => {\n      const { scrollOffset, outerSize } = latestRef.current\n\n      if (align === 'auto') {\n        if (toOffset <= scrollOffset) {\n          align = 'start'\n        } else if (toOffset >= scrollOffset + outerSize) {\n          align = 'end'\n        } else {\n          align = 'start'\n        }\n      }\n\n      if (align === 'start') {\n        scrollToFn(toOffset)\n      } else if (align === 'end') {\n        scrollToFn(toOffset - outerSize)\n      } else if (align === 'center') {\n        scrollToFn(toOffset - outerSize / 2)\n      }\n    },\n    [scrollToFn]\n  )\n\n  const tryScrollToIndex = React.useCallback(\n    (index, { align = 'auto', ...rest } = {}) => {\n      const { measurements, scrollOffset, outerSize } = latestRef.current\n\n      const measurement = measurements[Math.max(0, Math.min(index, size - 1))]\n\n      if (!measurement) {\n        return\n      }\n\n      if (align === 'auto') {\n        if (measurement.end >= scrollOffset + outerSize) {\n          align = 'end'\n        } else if (measurement.start <= scrollOffset) {\n          align = 'start'\n        } else {\n          return\n        }\n      }\n\n      const toOffset =\n        align === 'center'\n          ? measurement.start + measurement.size / 2\n          : align === 'end'\n          ? measurement.end\n          : measurement.start\n\n      scrollToOffset(toOffset, { align, ...rest })\n    },\n    [scrollToOffset, size]\n  )\n\n  const scrollToIndex = React.useCallback(\n    (...args) => {\n      // We do a double request here because of\n      // dynamic sizes which can cause offset shift\n      // and end up in the wrong spot. Unfortunately,\n      // we can't know about those dynamic sizes until\n      // we try and render them. So double down!\n      tryScrollToIndex(...args)\n      requestAnimationFrame(() => {\n        tryScrollToIndex(...args)\n      })\n    },\n    [tryScrollToIndex]\n  )\n\n  return {\n    virtualItems,\n    totalSize,\n    scrollToOffset,\n    scrollToIndex,\n    measure,\n  }\n}\n\nconst findNearestBinarySearch = (low, high, getCurrentValue, value) => {\n  while (low <= high) {\n    let middle = ((low + high) / 2) | 0\n    let currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({ measurements, outerSize, scrollOffset }) {\n  const size = measurements.length - 1\n  const getOffset = index => measurements[index].start\n\n  let start = findNearestBinarySearch(0, size, getOffset, scrollOffset)\n  let end = start\n\n  while (end < size && measurements[end].end < scrollOffset + outerSize) {\n    end++\n  }\n\n  return { start, end }\n}\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarResults = void 0;\nvar React = __importStar(require(\"react\"));\nvar react_virtual_1 = require(\"react-virtual\");\nvar KBarSearch_1 = require(\"./KBarSearch\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar START_INDEX = 0;\nvar KBarResults = function (props) {\n    var activeRef = React.useRef(null);\n    var parentRef = React.useRef(null);\n    // store a ref to all items so we do not have to pass\n    // them as a dependency when setting up event listeners.\n    var itemsRef = React.useRef(props.items);\n    itemsRef.current = props.items;\n    var rowVirtualizer = (0, react_virtual_1.useVirtual)({\n        size: itemsRef.current.length,\n        parentRef: parentRef,\n    });\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        activeIndex: state.activeIndex,\n    }); }), query = _a.query, search = _a.search, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, options = _a.options;\n    React.useEffect(function () {\n        var handler = function (event) {\n            var _a;\n            if (event.isComposing) {\n                return;\n            }\n            if (event.key === \"ArrowUp\" || (event.ctrlKey && event.key === \"p\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index > START_INDEX ? index - 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === 0)\n                            return index;\n                        nextIndex -= 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"ArrowDown\" ||\n                (event.ctrlKey && event.key === \"n\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index < itemsRef.current.length - 1 ? index + 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === itemsRef.current.length - 1)\n                            return index;\n                        nextIndex += 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"Enter\") {\n                event.preventDefault();\n                event.stopPropagation();\n                // storing the active dom element in a ref prevents us from\n                // having to calculate the current action to perform based\n                // on the `activeIndex`, which we would have needed to add\n                // as part of the dependencies array.\n                (_a = activeRef.current) === null || _a === void 0 ? void 0 : _a.click();\n            }\n        };\n        window.addEventListener(\"keydown\", handler, { capture: true });\n        return function () { return window.removeEventListener(\"keydown\", handler, { capture: true }); };\n    }, [query]);\n    // destructuring here to prevent linter warning to pass\n    // entire rowVirtualizer in the dependencies array.\n    var scrollToIndex = rowVirtualizer.scrollToIndex;\n    React.useEffect(function () {\n        scrollToIndex(activeIndex, {\n            // ensure that if the first item in the list is a group\n            // name and we are focused on the second item, to not\n            // scroll past that group, hiding it.\n            align: activeIndex <= 1 ? \"end\" : \"auto\",\n        });\n    }, [activeIndex, scrollToIndex]);\n    React.useEffect(function () {\n        // TODO(tim): fix scenario where async actions load in\n        // and active index is reset to the first item. i.e. when\n        // users register actions and bust the `useRegisterActions`\n        // cache, we won't want to reset their active index as they\n        // are navigating the list.\n        query.setActiveIndex(\n        // avoid setting active index on a group\n        typeof props.items[START_INDEX] === \"string\"\n            ? START_INDEX + 1\n            : START_INDEX);\n    }, [search, currentRootActionId, props.items, query]);\n    var execute = React.useCallback(function (item) {\n        var _a, _b;\n        if (typeof item === \"string\")\n            return;\n        if (item.command) {\n            item.command.perform(item);\n            query.toggle();\n        }\n        else {\n            query.setSearch(\"\");\n            query.setCurrentRootAction(item.id);\n        }\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onSelectAction) === null || _b === void 0 ? void 0 : _b.call(_a, item);\n    }, [query, options]);\n    var pointerMoved = (0, utils_1.usePointerMovedSinceMount)();\n    return (React.createElement(\"div\", { ref: parentRef, style: {\n            maxHeight: props.maxHeight || 400,\n            position: \"relative\",\n            overflow: \"auto\",\n        } },\n        React.createElement(\"div\", { role: \"listbox\", id: KBarSearch_1.KBAR_LISTBOX, style: {\n                height: rowVirtualizer.totalSize + \"px\",\n                width: \"100%\",\n            } }, rowVirtualizer.virtualItems.map(function (virtualRow) {\n            var item = itemsRef.current[virtualRow.index];\n            var handlers = typeof item !== \"string\" && {\n                onPointerMove: function () {\n                    return pointerMoved &&\n                        activeIndex !== virtualRow.index &&\n                        query.setActiveIndex(virtualRow.index);\n                },\n                onPointerDown: function () { return query.setActiveIndex(virtualRow.index); },\n                onClick: function () { return execute(item); },\n            };\n            var active = virtualRow.index === activeIndex;\n            return (React.createElement(\"div\", __assign({ ref: active ? activeRef : null, id: (0, KBarSearch_1.getListboxItemId)(virtualRow.index), role: \"option\", \"aria-selected\": active, key: virtualRow.index, style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    transform: \"translateY(\" + virtualRow.start + \"px)\",\n                } }, handlers), React.cloneElement(props.onRender({\n                item: item,\n                active: active,\n            }), {\n                ref: virtualRow.measureRef,\n            })));\n        }))));\n};\nexports.KBarResults = KBarResults;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useRegisterActions = void 0;\nvar React = __importStar(require(\"react\"));\nvar useKBar_1 = require(\"./useKBar\");\nfunction useRegisterActions(actions, dependencies) {\n    if (dependencies === void 0) { dependencies = []; }\n    var query = (0, useKBar_1.useKBar)().query;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var actionsCache = React.useMemo(function () { return actions; }, dependencies);\n    React.useEffect(function () {\n        if (!actionsCache.length) {\n            return;\n        }\n        var unregister = query.registerActions(actionsCache);\n        return function () {\n            unregister();\n        };\n    }, [query, actionsCache]);\n}\nexports.useRegisterActions = useRegisterActions;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarAnimator = void 0;\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar appearanceAnimationKeyframes = [\n    {\n        opacity: 0,\n        transform: \"scale(.99)\",\n    },\n    { opacity: 1, transform: \"scale(1.01)\" },\n    { opacity: 1, transform: \"scale(1)\" },\n];\nvar bumpAnimationKeyframes = [\n    {\n        transform: \"scale(1)\",\n    },\n    {\n        transform: \"scale(.98)\",\n    },\n    {\n        transform: \"scale(1)\",\n    },\n];\nvar KBarAnimator = function (_a) {\n    var _b, _c;\n    var children = _a.children, style = _a.style, className = _a.className, disableCloseOnOuterClick = _a.disableCloseOnOuterClick;\n    var _d = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        currentRootActionId: state.currentRootActionId,\n    }); }), visualState = _d.visualState, currentRootActionId = _d.currentRootActionId, query = _d.query, options = _d.options;\n    var outerRef = React.useRef(null);\n    var innerRef = React.useRef(null);\n    var enterMs = ((_b = options === null || options === void 0 ? void 0 : options.animations) === null || _b === void 0 ? void 0 : _b.enterMs) || 0;\n    var exitMs = ((_c = options === null || options === void 0 ? void 0 : options.animations) === null || _c === void 0 ? void 0 : _c.exitMs) || 0;\n    // Show/hide animation\n    React.useEffect(function () {\n        if (visualState === types_1.VisualState.showing) {\n            return;\n        }\n        var duration = visualState === types_1.VisualState.animatingIn ? enterMs : exitMs;\n        var element = outerRef.current;\n        element === null || element === void 0 ? void 0 : element.animate(appearanceAnimationKeyframes, {\n            duration: duration,\n            easing: \n            // TODO: expose easing in options\n            visualState === types_1.VisualState.animatingOut ? \"ease-in\" : \"ease-out\",\n            direction: visualState === types_1.VisualState.animatingOut ? \"reverse\" : \"normal\",\n            fill: \"forwards\",\n        });\n    }, [options, visualState, enterMs, exitMs]);\n    // Height animation\n    var previousHeight = React.useRef();\n    React.useEffect(function () {\n        // Only animate if we're actually showing\n        if (visualState === types_1.VisualState.showing) {\n            var outer_1 = outerRef.current;\n            var inner_1 = innerRef.current;\n            if (!outer_1 || !inner_1) {\n                return;\n            }\n            var ro_1 = new ResizeObserver(function (entries) {\n                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                    var entry = entries_1[_i];\n                    var cr = entry.contentRect;\n                    if (!previousHeight.current) {\n                        previousHeight.current = cr.height;\n                    }\n                    outer_1.animate([\n                        {\n                            height: previousHeight.current + \"px\",\n                        },\n                        {\n                            height: cr.height + \"px\",\n                        },\n                    ], {\n                        duration: enterMs / 2,\n                        // TODO: expose configs here\n                        easing: \"ease-out\",\n                        fill: \"forwards\",\n                    });\n                    previousHeight.current = cr.height;\n                }\n            });\n            ro_1.observe(inner_1);\n            return function () {\n                ro_1.unobserve(inner_1);\n            };\n        }\n    }, [visualState, options, enterMs, exitMs]);\n    // Bump animation between nested actions\n    var firstRender = React.useRef(true);\n    React.useEffect(function () {\n        if (firstRender.current) {\n            firstRender.current = false;\n            return;\n        }\n        var element = outerRef.current;\n        if (element) {\n            element.animate(bumpAnimationKeyframes, {\n                duration: enterMs,\n                easing: \"ease-out\",\n            });\n        }\n    }, [currentRootActionId, enterMs]);\n    (0, utils_1.useOuterClick)(outerRef, function () {\n        var _a, _b;\n        if (disableCloseOnOuterClick) {\n            return;\n        }\n        query.setVisualState(types_1.VisualState.animatingOut);\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    });\n    return (React.createElement(\"div\", { ref: outerRef, style: __assign(__assign(__assign({}, appearanceAnimationKeyframes[0]), style), { pointerEvents: \"auto\" }), className: className },\n        React.createElement(\"div\", { ref: innerRef }, children)));\n};\nexports.KBarAnimator = KBarAnimator;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./ActionInterface\"), exports);\n__exportStar(require(\"./ActionImpl\"), exports);\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Priority = exports.createAction = void 0;\nvar utils_1 = require(\"./utils\");\nObject.defineProperty(exports, \"createAction\", { enumerable: true, get: function () { return utils_1.createAction; } });\nObject.defineProperty(exports, \"Priority\", { enumerable: true, get: function () { return utils_1.Priority; } });\n__exportStar(require(\"./useMatches\"), exports);\n__exportStar(require(\"./KBarPortal\"), exports);\n__exportStar(require(\"./KBarPositioner\"), exports);\n__exportStar(require(\"./KBarSearch\"), exports);\n__exportStar(require(\"./KBarResults\"), exports);\n__exportStar(require(\"./useKBar\"), exports);\n__exportStar(require(\"./useRegisterActions\"), exports);\n__exportStar(require(\"./KBarContextProvider\"), exports);\n__exportStar(require(\"./KBarAnimator\"), exports);\n__exportStar(require(\"./types\"), exports);\n__exportStar(require(\"./action\"), exports);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,gBAAiB,WAAQ,QAAK,iBAAkB,SAAU,IAAI,MAAM,MAAM;AAC1E,UAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,YAAI,MAAM,EAAE,KAAK,OAAO;AACpB,cAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,aAAG,CAAC,IAAI,KAAK,CAAC;AAAA,QAClB;AAAA,MACJ;AACA,aAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAAA,IAC3D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,WAAW,QAAQ,yBAAyB,QAAQ,oBAAoB,QAAQ,oBAAoB,QAAQ,sBAAsB,QAAQ,OAAO,QAAQ,eAAe,QAAQ,WAAW,QAAQ,4BAA4B,QAAQ,gBAAgB,QAAQ,eAAe;AACzS,QAAIA,SAAQ,aAAa,eAAgB;AACzC,aAAS,aAAa,OAAO;AACzB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AAAA,IACzB;AACA,YAAQ,eAAe;AACvB,aAAS,cAAc,KAAK,IAAI;AAC5B,UAAI,QAAQA,OAAM,OAAO,EAAE;AAC3B,YAAM,UAAU;AAChB,MAAAA,OAAM,UAAU,WAAY;AACxB,iBAAS,QAAQ,OAAO;AACpB,cAAI,IAAI;AACR,gBAAM,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,MAAM;AAAA;AAAA,UAGjF,MAAM,aAAa,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,EAAE,OAAO;AAClG;AAAA,UACJ;AACA,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;AACtB,gBAAM,QAAQ;AAAA,QAClB;AACA,eAAO,iBAAiB,eAAe,SAAS,IAAI;AACpD,eAAO,WAAY;AAAE,iBAAO,OAAO,oBAAoB,eAAe,SAAS,IAAI;AAAA,QAAG;AAAA,MAC1F,GAAG,CAAC,GAAG,CAAC;AAAA,IACZ;AACA,YAAQ,gBAAgB;AACxB,aAAS,4BAA4B;AACjC,UAAI,KAAKA,OAAM,SAAS,KAAK,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9D,MAAAA,OAAM,UAAU,WAAY;AACxB,iBAAS,UAAU;AACf,mBAAS,IAAI;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AACR,iBAAO,iBAAiB,eAAe,OAAO;AAC9C,iBAAO,WAAY;AAAE,mBAAO,OAAO,oBAAoB,eAAe,OAAO;AAAA,UAAG;AAAA,QACpF;AAAA,MACJ,GAAG,CAAC,KAAK,CAAC;AACV,aAAO;AAAA,IACX;AACA,YAAQ,4BAA4B;AACpC,aAAS,WAAW;AAChB,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;AAAA,IACpD;AACA,YAAQ,WAAW;AACnB,aAAS,aAAa,QAAQ;AAC1B,aAAO,SAAS,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM;AAAA,IAC9C;AACA,YAAQ,eAAe;AACvB,aAAS,OAAO;AAAA,IAAE;AAClB,YAAQ,OAAO;AACf,YAAQ,sBAAsB,OAAO,WAAW,cAAc,OAAOA,OAAM;AAE3E,aAAS,oBAAoB;AACzB,UAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,YAAM,MAAM,aAAa;AACzB,YAAM,MAAM,WAAW;AACvB,eAAS,KAAK,YAAY,KAAK;AAC/B,UAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,YAAM,YAAY,KAAK;AACvB,UAAI,iBAAiB,MAAM,cAAc,MAAM;AAC/C,YAAM,WAAW,YAAY,KAAK;AAClC,aAAO;AAAA,IACX;AACA,YAAQ,oBAAoB;AAC5B,aAAS,kBAAkB,OAAO,IAAI;AAClC,UAAI,OAAO,QAAQ;AAAE,aAAK;AAAA,MAAK;AAC/B,UAAI,KAAKA,OAAM,SAAS,KAAK,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AAChF,UAAI,UAAUA,OAAM,OAAO,KAAK,IAAI,CAAC;AACrC,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,OAAO;AACP;AACJ,YAAI,UAAU,WAAW,WAAY;AACjC,4BAAkB,KAAK;AACvB,kBAAQ,UAAU,KAAK,IAAI;AAAA,QAC/B,GAAG,QAAQ,WAAW,KAAK,IAAI,IAAI,GAAG;AACtC,eAAO,WAAY;AACf,uBAAa,OAAO;AAAA,QACxB;AAAA,MACJ,GAAG,CAAC,IAAI,KAAK,CAAC;AACd,aAAO,OAAO,IAAI,QAAQ;AAAA,IAC9B;AACA,YAAQ,oBAAoB;AAC5B,aAAS,uBAAuB,IAAI;AAChC,UAAI,IAAI,IAAI;AACZ,UAAI,KAAK,OAAO,SAAS,EAAE,mBAAmB,CAAC,EAAE,IAAI,IAAI,oBAAoB,GAAG;AAChF,UAAI,SAAS,cAAc,CAAC,SAAS,UAAU,GAAG,mBAAmB,IAAI,EAAE,IAAI,SAAU,IAAI;AACzF,eAAO,GAAG,YAAY;AAAA,MAC1B,CAAC;AACD,UAAI,gBAAgB,SAAS;AAC7B,UAAI,gBAAgB,kBACf,OAAO,QAAQ,cAAc,QAAQ,YAAY,CAAC,MAAM,QACnD,KAAK,cAAc,WAAW,aAAa,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,eACvG,KAAK,cAAc,WAAW,aAAa,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WACrG,YACF,KAAK,cAAc,WAAW,aAAa,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WACrG;AACZ,aAAO;AAAA,IACX;AACA,YAAQ,yBAAyB;AACjC,QAAI,MAAM,OAAO,WAAW;AAC5B,QAAI,QAAQ,CAAC,OAAO,OAAO,UAAU,aAAa;AAClD,aAAS,SAAS,OAAO;AACrB,aAAO,QAAQ,MAAM,UAAU,MAAM;AAAA,IACzC;AACA,YAAQ,WAAW;AACnB,YAAQ,WAAW;AAAA,MACf,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,KAAK;AAAA,IACT;AAAA;AAAA;;;;;;;;;ACxJA,UAAM,sBAAsB,OAAO,YAAY;AAEvC,UAAA,OAAS,OAAM;eAgBP,mBAAmB,GAAQ,GAAM;AAC/C,eAAO,MAAM,KAAM,MAAM,KAAK,MAAM;MACtC;eAQgB,cAAc,OAAU;AACtC,eAAO,MAAM,gBAAgB,UAAU,MAAM,eAAe;MAC9D;eAQgB,cAAc,OAAU;AACtC,eAAO,CAAC,CAAC,SAAS,OAAO,MAAM,SAAS;MAC1C;eAQgB,eAAe,OAAU;AACvC,eAAO,CAAC,EAAE,SAAS,MAAM;MAC3B;eAQgB,sBAAmB;AACjC,YAAM,SAAgB,CAAA;AAEtB,eAAO;UACL,KAAA,SAAI,OAAU;AACZ,mBAAO,KAAK,KAAK;;UAGnB,KAAA,SAAI,OAAU;AACZ,mBAAO,OAAO,QAAQ,KAAK,MAAM;;;MAGvC;AAOO,UAAM,cAAe,SAAC,eAAsB;AACjD,YAAI,eAAe;AACjB,iBAAO,SAAS,eAAY;AAC1B,mBAAO,oBAAI,QAAO;;;AAItB,eAAO;MACT,EAAG,mBAAmB;eAQN,2BAA2B,SAA4B;AACrE,eAAO,SAAS,oBAAoB,YAA8B;AAChE,cAAM,cAAc,WAAW;AAE/B,iBAAO,SAAS,cACd,GACA,GACA,OAA4B;AAA5B,gBAAA,UAAA,QAAA;AAAA,sBAAe,YAAW;YAAE;AAE5B,gBAAM,eAAe,CAAC,CAAC,KAAK,OAAO,MAAM;AACzC,gBAAM,eAAe,CAAC,CAAC,KAAK,OAAO,MAAM;AAEzC,gBAAI,gBAAgB,cAAc;AAChC,kBAAM,OAAO,gBAAgB,MAAM,IAAI,CAAC;AACxC,kBAAM,OAAO,gBAAgB,MAAM,IAAI,CAAC;AAExC,kBAAI,QAAQ,MAAM;AAChB,uBAAO,QAAQ;;AAGjB,kBAAI,cAAc;AAChB,sBAAM,IAAI,CAAC;;AAGb,kBAAI,cAAc;AAChB,sBAAM,IAAI,CAAC;;;AAIf,mBAAO,YAAY,GAAG,GAAG,KAAK;;;MAGpC;eAWgB,eACd,GACA,GACA,SACA,MAAS;AAET,YAAI,QAAQ,EAAE;AAEd,YAAI,EAAE,WAAW,OAAO;AACtB,iBAAO;;AAGT,eAAO,UAAU,GAAG;AAClB,cAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,IAAI,GAAG;AACtC,mBAAO;;;AAIX,eAAO;MACT;eAWgB,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,gBAAgB,EAAE,MAAM;AAC1B,cAAM,mBAAuC,CAAA;AAE7C,YAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,gBAAI,cAAc;AAChB,kBAAI,aAAW;AACf,kBAAI,eAAa;AAEjB,gBAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,oBAAI,CAAC,cAAY,CAAC,iBAAe,YAAU,GAAG;AAC5C,+BACE,QAAQ,MAAM,MAAM,IAAI,KAAK,QAAQ,QAAQ,QAAQ,IAAI;AAE3D,sBAAI,YAAU;AACZ,qCAAe,YAAU,IAAI;;;AAIjC;eACD;AAED,6BAAe;;WAElB;;AAGH,eAAO;MACT;AAOA,UAAM,QAAQ;AAEd,UAAM,iBAAiB,SAAS,UAAU,KAAK,KAC7C,SAAS,UAAU,MACnB,OAAO,UAAU,cAAc;eAYjB,gBACd,GACA,GACA,SACA,MAAS;AAET,YAAM,QAAQ,KAAK,CAAC;AAEpB,YAAI,QAAQ,MAAM;AAElB,YAAI,KAAK,CAAC,EAAE,WAAW,OAAO;AAC5B,iBAAO;;AAGT,YAAI,OAAO;AACT,cAAI,MAAG;AAEP,iBAAO,UAAU,GAAG;AAClB,kBAAM,MAAM,KAAK;AAEjB,gBAAI,QAAQ,OAAO;AACjB,kBAAM,gBAAgB,eAAe,CAAC;AACtC,kBAAM,gBAAgB,eAAe,CAAC;AAEtC,mBACG,iBAAiB,kBAClB,kBAAkB,eAClB;AACA,uBAAO;;;AAIX,gBAAI,CAAC,eAAe,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG;AAC7D,qBAAO;;;;AAKb,eAAO;MACT;eASgB,gBAAgB,GAAW,GAAS;AAClD,eACE,EAAE,WAAW,EAAE,UACf,EAAE,WAAW,EAAE,UACf,EAAE,eAAe,EAAE,cACnB,EAAE,cAAc,EAAE,aAClB,EAAE,YAAY,EAAE,WAChB,EAAE,WAAW,EAAE,UACf,EAAE,cAAc,EAAE;MAEtB;eAWgB,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,gBAAgB,EAAE,MAAM;AAC1B,cAAM,mBAAuC,CAAA;AAE7C,YAAE,QAAQ,SAAC,QAAM;AACf,gBAAI,cAAc;AAChB,kBAAI,aAAW;AACf,kBAAI,eAAa;AAEjB,gBAAE,QAAQ,SAAC,QAAM;AACf,oBAAI,CAAC,cAAY,CAAC,iBAAe,YAAU,GAAG;AAC5C,+BAAW,QAAQ,QAAQ,QAAQ,IAAI;AAEvC,sBAAI,YAAU;AACZ,qCAAe,YAAU,IAAI;;;AAIjC;eACD;AAED,6BAAe;;WAElB;;AAGH,eAAO;MACT;AC3TA,UAAM,kBAAkB,OAAO,QAAQ;AACvC,UAAM,kBAAkB,OAAO,QAAQ;eAIvB,iBAAiB,eAAyC;AACxE,YAAM;;UAEJ,OAAO,kBAAkB,aACrB,cAAc,UAAU,IACxB;;AAWN,iBAAS,WAAW,GAAQ,GAAQ,MAAU;AAC5C,cAAI,MAAM,GAAG;AACX,mBAAO;;AAGT,cAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,gBAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACxC,qBAAO,gBAAgB,GAAG,GAAG,SAAS,IAAI;;AAG5C,gBAAI,SAAS,MAAM,QAAQ,CAAC;AAC5B,gBAAI,SAAS,MAAM,QAAQ,CAAC;AAE5B,gBAAI,UAAU,QAAQ;AACpB,qBAAO,WAAW,UAAU,eAAe,GAAG,GAAG,SAAS,IAAI;;AAGhE,qBAAS,aAAa;AACtB,qBAAS,aAAa;AAEtB,gBAAI,UAAU,QAAQ;AACpB,qBACE,WAAW,UAAU,mBAAmB,EAAE,QAAO,GAAI,EAAE,QAAO,CAAE;;AAIpE,qBAAS,aAAa;AACtB,qBAAS,aAAa;AAEtB,gBAAI,UAAU,QAAQ;AACpB,qBAAO,WAAW,UAAU,gBAAgB,GAAG,CAAC;;AAGlD,gBAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACxC,qBAAO,MAAM;;AAGf,gBAAI,iBAAiB;AACnB,uBAAS,aAAa;AACtB,uBAAS,aAAa;AAEtB,kBAAI,UAAU,QAAQ;AACpB,uBAAO,WAAW,UAAU,aAAa,GAAG,GAAG,SAAS,IAAI;;;AAIhE,gBAAI,iBAAiB;AACnB,uBAAS,aAAa;AACtB,uBAAS,aAAa;AAEtB,kBAAI,UAAU,QAAQ;AACpB,uBAAO,WAAW,UAAU,aAAa,GAAG,GAAG,SAAS,IAAI;;;AAIhE,mBAAO,gBAAgB,GAAG,GAAG,SAAS,IAAI;;AAG5C,iBAAO,MAAM,KAAK,MAAM;;AAG1B,eAAO;MACT;UC1Fa,YAAY,iBAAgB;UAC5B,eAAe,iBAAiB,WAAA;AAAM,eAAA;MAAkB,CAAA;UAExD,oBAAoB,iBAAiB,2BAA0B,CAAE;UACjE,uBAAuB,iBAClC,2BAA2B,kBAAkB,CAAC;;;;;;;;;;;;;ACVhD;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,aAAS,UAAU,WAAW,SAAS;AACnC,UAAI,WAAW;AACX;AAAA,MACJ;AACA,UAAI,cAAc;AACd,cAAM,IAAI,MAAM,MAAM;AAAA,MAC1B;AACA,UAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,UAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,YAAM,IAAI,MAAM,KAAK;AAAA,IACzB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU;AAClB,QAAI;AAAA;AAAA,MAAyB,2BAAY;AACrC,iBAASC,SAAQ,SAAS,SAAS;AAC/B,cAAI,QAAQ;AACZ,cAAI,YAAY,QAAQ;AAAE,sBAAU,CAAC;AAAA,UAAG;AACxC,eAAK,UAAU,WAAY;AACvB,gBAAI,SAAS,QAAQ,QAAQ;AAE7B,gBAAI,OAAO,WAAW;AAClB;AAEJ,gBAAI,UAAU,QAAQ;AACtB,gBAAI,CAAC;AACD;AAGJ,gBAAI,MAAM,aAAa;AACnB,sBAAQ,OAAO,MAAM,WAAW;AAAA,YACpC;AACA,kBAAM,cAAc,QAAQ,IAAI;AAAA,cAC5B,SAAS,QAAQ;AAAA,cACjB;AAAA,YACJ,CAAC;AACD,kBAAM,UAAU;AAAA,cACZ,MAAM,WAAY;AAAE,uBAAO,QAAQ,KAAK,MAAM,WAAW;AAAA,cAAG;AAAA,cAC5D,MAAM,WAAY;AAAE,uBAAO,QAAQ,KAAK,MAAM,WAAW;AAAA,cAAG;AAAA,YAChE;AAAA,UACJ;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,UAAU;AAAA;AAAA;;;ACjClB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,QAAI,mBAAmB,gBAAgB,4BAAyB;AAChE,QAAI,YAAY;AAChB,QAAI,UAAU;AAKd,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,KAAK,GAAG,UAAU,WAAW,OAAO,SAAS,KAAK,IAAI,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,KAAK;AAC1G,cAAQ,WAAW,OAAO,OAAO,YAAY,WAAW,UAAU,QAAQ,OAAO,KAAK;AAAA,IAC1F;AACA,QAAI;AAAA;AAAA,MAA4B,WAAY;AACxC,iBAASC,YAAW,QAAQ,SAAS;AACjC,cAAI,QAAQ;AACZ,cAAI;AACJ,eAAK,WAAW,QAAQ,SAAS;AACjC,eAAK,YAAY,CAAC;AAClB,eAAK,WAAW,CAAC;AACjB,iBAAO,OAAO,MAAM,MAAM;AAC1B,eAAK,KAAK,OAAO;AACjB,eAAK,OAAO,OAAO;AACnB,eAAK,WAAW,eAAe,MAAM;AACrC,cAAI,UAAU,OAAO;AACrB,eAAK,UACD,WACI,IAAI,UAAU,QAAQ;AAAA,YAClB,SAAS,WAAY;AAAE,qBAAO,QAAQ,KAAK;AAAA,YAAG;AAAA,UAClD,GAAG;AAAA,YACC,SAAS,QAAQ;AAAA,UACrB,CAAC;AAET,eAAK,WAAW,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC3E,cAAI,OAAO,QAAQ;AACf,gBAAI,mBAAmB,QAAQ,MAAM,OAAO,MAAM;AAClD,aAAC,GAAG,iBAAiB,SAAS,kBAAkB,gDAAgD,OAAO,SAAS,+BAA+B;AAC/I,6BAAiB,SAAS,IAAI;AAAA,UAClC;AAAA,QACJ;AACA,QAAAA,YAAW,UAAU,WAAW,SAAU,iBAAiB;AAEvD,0BAAgB,UAAU,QAAQ,IAAI;AACtC,cAAI,SAAS,KAAK;AAClB,iBAAO,QAAQ;AACX,4BAAgB,UAAU,QAAQ,MAAM;AACxC,qBAAS,OAAO;AAAA,UACpB;AAGA,eAAK,SAAS,KAAK,eAAe;AAAA,QACtC;AACA,QAAAA,YAAW,UAAU,cAAc,SAAU,YAAY;AACrD,cAAI,QAAQ;AAEZ,cAAI,QAAQ,KAAK,SAAS,QAAQ,UAAU;AAC5C,cAAI,UAAU,IAAI;AACd,iBAAK,SAAS,OAAO,OAAO,CAAC;AAAA,UACjC;AACA,cAAI,WAAW,UAAU;AACrB,uBAAW,SAAS,QAAQ,SAAU,OAAO;AACzC,oBAAM,YAAY,KAAK;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAO,eAAeA,YAAW,WAAW,oBAAoB;AAAA;AAAA,UAE5D,KAAK,WAAY;AACb,mBAAO,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AAAA,UACnD;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,QAAAA,YAAW,SAAS,SAAU,QAAQ,SAAS;AAC3C,iBAAO,IAAIA,YAAW,QAAQ,OAAO;AAAA,QACzC;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,aAAa;AAAA;AAAA;;;AClFrB;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB;AAC1B,QAAI,mBAAmB,gBAAgB,4BAAyB;AAChE,QAAI,eAAe;AACnB,QAAI;AAAA;AAAA,MAAiC,WAAY;AAC7C,iBAASC,iBAAgB,SAAS,SAAS;AACvC,cAAI,YAAY,QAAQ;AAAE,sBAAU,CAAC;AAAA,UAAG;AACxC,cAAI,YAAY,QAAQ;AAAE,sBAAU,CAAC;AAAA,UAAG;AACxC,eAAK,UAAU,CAAC;AAChB,eAAK,UAAU;AACf,eAAK,IAAI,OAAO;AAAA,QACpB;AACA,QAAAA,iBAAgB,UAAU,MAAM,SAAU,SAAS;AAC/C,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,gBAAI,SAAS,QAAQ,CAAC;AACtB,gBAAI,OAAO,QAAQ;AACf,eAAC,GAAG,iBAAiB,SAAS,KAAK,QAAQ,OAAO,MAAM,GAAG,iCAAkC,OAAO,OAAO,uCAAyC,OAAO,SAAS,UAAW;AAAA,YACnL;AACA,iBAAK,QAAQ,OAAO,EAAE,IAAI,aAAa,WAAW,OAAO,QAAQ;AAAA,cAC7D,SAAS,KAAK,QAAQ;AAAA,cACtB,OAAO,KAAK;AAAA,YAChB,CAAC;AAAA,UACL;AACA,iBAAO,SAAS,CAAC,GAAG,KAAK,OAAO;AAAA,QACpC;AACA,QAAAA,iBAAgB,UAAU,SAAS,SAAU,SAAS;AAClD,cAAI,QAAQ;AACZ,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,aAAa,MAAM,QAAQ,OAAO,EAAE;AACxC,gBAAI,CAAC;AACD;AACJ,gBAAI,WAAW,WAAW;AAC1B,mBAAO,SAAS,QAAQ;AACpB,kBAAI,QAAQ,SAAS,IAAI;AACzB,kBAAI,CAAC;AACD;AACJ,qBAAO,MAAM,QAAQ,MAAM,EAAE;AAC7B,kBAAI,MAAM;AACN,sBAAM,iBAAiB,YAAY,KAAK;AAC5C,kBAAI,MAAM;AACN,yBAAS,KAAK,MAAM,UAAU,MAAM,QAAQ;AAAA,YACpD;AACA,gBAAI,WAAW,kBAAkB;AAC7B,yBAAW,iBAAiB,YAAY,UAAU;AAAA,YACtD;AACA,mBAAO,MAAM,QAAQ,OAAO,EAAE;AAAA,UAClC,CAAC;AACD,iBAAO,SAAS,CAAC,GAAG,KAAK,OAAO;AAAA,QACpC;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,kBAAkB;AAAA;AAAA;;;AClE1B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU,QAAQ,kBAAkB;AAC5C,QAAI,UAAU;AACd,QAAI;AAAA;AAAA,MAAiC,WAAY;AAC7C,iBAASC,iBAAgB,MAAM;AAC3B,eAAK,UAAU,KAAK;AACpB,eAAK,SAAS,KAAK;AAAA,QACvB;AACA,QAAAA,iBAAgB,SAAS,SAAU,MAAM;AACrC,iBAAO,IAAIA,iBAAgB,IAAI;AAAA,QACnC;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,kBAAkB;AAC1B,QAAI;AAAA;AAAA,MAA6B,WAAY;AACzC,iBAASC,eAAc;AACnB,eAAK,YAAY,CAAC;AAClB,eAAK,YAAY,CAAC;AAClB,cAAI,CAACA,aAAY,UAAU;AACvB,YAAAA,aAAY,WAAW;AACvB,iBAAK,KAAK;AAAA,UACd;AACA,iBAAOA,aAAY;AAAA,QACvB;AACA,QAAAA,aAAY,UAAU,OAAO,WAAY;AACrC,cAAI,QAAQ;AACZ,cAAI,OAAO,WAAW;AAClB;AACJ,iBAAO,iBAAiB,WAAW,SAAU,OAAO;AAChD,gBAAI;AACJ,gBAAK,CAAC,MAAM,UAAU,UAAU,CAAC,MAAM,UAAU,WAC5C,GAAG,QAAQ,wBAAwB,GAAG;AACvC;AAAA,YACJ;AACA,gBAAI,OAAO,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAC/E,gBAAI,MAAM,WAAW,QAAQ,OAAO,MAAM,UAAU;AAChD,oBAAM,KAAK;AAAA,YACf,WACS,MAAM,WAAW,QAAQ,KAAK;AACnC,oBAAM,KAAK;AAAA,YACf;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAA,aAAY,UAAU,MAAM,SAAU,MAAM;AACxC,cAAI,cAAc,gBAAgB,OAAO,IAAI;AAC7C,eAAK,UAAU,KAAK,WAAW;AAC/B,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,UAAU,SAAS,SAAU,MAAM;AAC3C,cAAI,YAAY,KAAK,UAAU,UAAU,SAAU,GAAG;AAAE,mBAAO,MAAM;AAAA,UAAM,CAAC;AAC5E,cAAI,cAAc,IAAI;AAClB,iBAAK,UAAU,OAAO,WAAW,CAAC;AAClC;AAAA,UACJ;AACA,cAAI,YAAY,KAAK,UAAU,UAAU,SAAU,GAAG;AAAE,mBAAO,MAAM;AAAA,UAAM,CAAC;AAC5E,cAAI,cAAc,IAAI;AAClB,iBAAK,UAAU,OAAO,WAAW,CAAC;AAAA,UACtC;AAAA,QACJ;AACA,QAAAA,aAAY,UAAU,OAAO,SAAU,MAAM;AAEzC,cAAI,CAAC,MAAM;AACP,gBAAI,SAAS,KAAK,UAAU,IAAI;AAChC,gBAAI,CAAC;AACD;AACJ,uBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO;AAC9D,iBAAK,UAAU,KAAK,MAAM;AAC1B,mBAAO;AAAA,UACX;AAEA,cAAI,QAAQ,KAAK,UAAU,UAAU,SAAU,GAAG;AAAE,mBAAO,MAAM;AAAA,UAAM,CAAC;AACxE,cAAI,UAAU;AACV;AACJ,eAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,eAAK,OAAO;AACZ,eAAK,UAAU,KAAK,IAAI;AACxB,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,UAAU,OAAO,SAAU,MAAM;AACzC,cAAI,CAAC,MAAM;AACP,gBAAI,SAAS,KAAK,UAAU,IAAI;AAChC,gBAAI,CAAC;AACD;AACJ,uBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ;AAC/D,iBAAK,UAAU,KAAK,MAAM;AAC1B,mBAAO;AAAA,UACX;AACA,cAAI,QAAQ,KAAK,UAAU,UAAU,SAAU,GAAG;AAAE,mBAAO,MAAM;AAAA,UAAM,CAAC;AACxE,cAAI,UAAU;AACV;AACJ,eAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,eAAK,QAAQ;AACb,eAAK,UAAU,KAAK,IAAI;AACxB,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,UAAU,QAAQ,WAAY;AACtC,eAAK,UAAU,OAAO,CAAC;AACvB,eAAK,UAAU,OAAO,CAAC;AAAA,QAC3B;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,QAAI,UAAU,IAAI,YAAY;AAC9B,YAAQ,UAAU;AAClB,WAAO,OAAO,OAAO;AAAA;AAAA;;;ACxGrB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAI;AACJ,KAAC,SAAUC,cAAa;AACpB,MAAAA,aAAY,aAAa,IAAI;AAC7B,MAAAA,aAAY,SAAS,IAAI;AACzB,MAAAA,aAAY,cAAc,IAAI;AAC9B,MAAAA,aAAY,QAAQ,IAAI;AAAA,IAC5B,GAAG,cAAc,QAAQ,gBAAgB,QAAQ,cAAc,CAAC,EAAE;AAAA;AAAA;;;ACTlE;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAI,gBAAgB;AACpB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,mBAAmB,gBAAgB,4BAAyB;AAChE,QAAI,oBAAoB;AACxB,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACd,aAAS,SAASC,QAAO;AACrB,UAAI,aAAaD,OAAM,OAAO,SAAS,EAAE,YAAY;AAAA,QAC7C,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ,EAAE,GAAGC,OAAM,OAAO,CAAC;AACvB,UAAI,mBAAmBD,OAAM;AAAA,QAAQ,WAAY;AAC7C,iBAAO,IAAI,kBAAkB,gBAAgBC,OAAM,WAAW,CAAC,GAAG;AAAA,YAC9D,gBAAgB,WAAW,QAAQ,gBAAgB,cAAc,UAAU;AAAA,UAC/E,CAAC;AAAA,QACL;AAAA;AAAA,QAEA,CAAC;AAAA,MAAC;AAEF,UAAI,KAAKD,OAAM,SAAS;AAAA,QACpB,aAAa;AAAA,QACb,qBAAqB;AAAA,QACrB,aAAa,QAAQ,YAAY;AAAA,QACjC,SAAS,SAAS,CAAC,GAAG,iBAAiB,OAAO;AAAA,QAC9C,aAAa;AAAA,QACb,UAAU;AAAA,MACd,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAClC,UAAI,YAAYA,OAAM,OAAO,KAAK;AAClC,gBAAU,UAAU;AACpB,UAAI,WAAWA,OAAM,YAAY,WAAY;AAAE,eAAO,UAAU;AAAA,MAAS,GAAG,CAAC,CAAC;AAC9E,UAAI,YAAYA,OAAM,QAAQ,WAAY;AAAE,eAAO,IAAI,UAAU,QAAQ;AAAA,MAAG,GAAG,CAAC,QAAQ,CAAC;AACzF,MAAAA,OAAM,UAAU,WAAY;AACxB,kBAAU,UAAU;AACpB,kBAAU,OAAO;AAAA,MACrB,GAAG,CAAC,OAAO,SAAS,CAAC;AACrB,UAAI,kBAAkBA,OAAM,YAAY,SAAU,SAAS;AACvD,iBAAS,SAAUE,QAAO;AACtB,iBAAO,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,SAAS,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAAA,QACnF,CAAC;AACD,eAAO,SAAS,aAAa;AACzB,mBAAS,SAAUA,QAAO;AACtB,mBAAO,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,SAAS,iBAAiB,OAAO,OAAO,EAAE,CAAC;AAAA,UACtF,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,gBAAgB,CAAC;AACrB,UAAI,WAAWF,OAAM,OAAO,IAAI;AAChC,aAAOA,OAAM,QAAQ,WAAY;AAC7B,YAAI,QAAQ;AAAA,UACR,sBAAsB,SAAU,UAAU;AACtC,qBAAS,SAAUE,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,qBAAqB,SAAS,CAAC;AAAA,YAAI,CAAC;AAAA,UAC5G;AAAA,UACA,gBAAgB,SAAU,IAAI;AAC1B,qBAAS,SAAUA,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,aAAa,OAAO,OAAO,aAAa,GAAGA,OAAM,WAAW,IAAI,GAAG,CAAC;AAAA,YAAI,CAAC;AAAA,UACjJ;AAAA,UACA,WAAW,SAAU,aAAa;AAC9B,mBAAO,SAAS,SAAUA,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,YAAyB,CAAC;AAAA,YAAI,CAAC;AAAA,UAC9G;AAAA,UACA;AAAA,UACA,QAAQ,WAAY;AAChB,mBAAO,SAAS,SAAUA,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,aAAa,CAAC,QAAQ,YAAY,cAAc,QAAQ,YAAY,MAAM,EAAE,SAASA,OAAM,WAAW,IACvK,QAAQ,YAAY,cACpB,QAAQ,YAAY,aAAa,CAAC;AAAA,YAAI,CAAC;AAAA,UACrD;AAAA,UACA,gBAAgB,SAAU,IAAI;AAC1B,mBAAO,SAAS,SAAUA,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,aAAa,OAAO,OAAO,WAAW,KAAK,GAAGA,OAAM,WAAW,EAAE,CAAC;AAAA,YAAI,CAAC;AAAA,UACtJ;AAAA,UACA,gBAAgB,SAAU,IAAI;AAC1B,qBAAS,UAAU;AAAA,UACvB;AAAA,UACA,UAAU,WAAY;AAClB,aAAC,GAAG,iBAAiB,SAAS,SAAS,SAAS,2FAA2F;AAC3I,mBAAO,SAAS;AAAA,UACpB;AAAA,UACA,SAAS,SAAU,SAAS;AACxB,qBAAS,SAAUA,QAAO;AAAE,qBAAQ,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,UAAU,QAAQ,CAAC;AAAA,YAAI,CAAC;AAAA,UAChG;AAAA,QACJ;AACA,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA,SAAS,WAAW;AAAA,UACpB,WAAW,SAAU,WAAW,IAAI;AAAE,mBAAO,UAAU,UAAU,WAAW,EAAE;AAAA,UAAG;AAAA,QACrF;AAAA,MACJ,GAAG,CAAC,UAAU,WAAW,eAAe,CAAC;AAAA,IAC7C;AACA,YAAQ,WAAW;AACnB,QAAI;AAAA;AAAA,MAA2B,WAAY;AACvC,iBAASC,WAAU,UAAU;AACzB,eAAK,cAAc,CAAC;AACpB,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,WAAU,UAAU,YAAY,SAAU,WAAW,UAAU;AAC3D,cAAI,QAAQ;AACZ,cAAI,aAAa,IAAI,WAAW,WAAY;AAAE,mBAAO,UAAU,MAAM,SAAS,CAAC;AAAA,UAAG,GAAG,QAAQ;AAC7F,eAAK,YAAY,KAAK,UAAU;AAChC,iBAAO,KAAK,YAAY,KAAK,MAAM,UAAU;AAAA,QACjD;AACA,QAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACpD,cAAI,KAAK,YAAY,QAAQ;AACzB,gBAAI,QAAQ,KAAK,YAAY,QAAQ,UAAU;AAC/C,gBAAI,QAAQ,IAAI;AACZ,qBAAO,KAAK,YAAY,OAAO,OAAO,CAAC;AAAA,YAC3C;AAAA,UACJ;AAAA,QACJ;AACA,QAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,eAAK,YAAY,QAAQ,SAAU,YAAY;AAAE,mBAAO,WAAW,QAAQ;AAAA,UAAG,CAAC;AAAA,QACnF;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,QAAI;AAAA;AAAA,MAA4B,WAAY;AACxC,iBAASC,YAAW,WAAW,UAAU;AACrC,eAAK,YAAY;AACjB,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,cAAI;AAEA,gBAAI,YAAY,KAAK,UAAU;AAC/B,gBAAI,EAAE,GAAG,cAAc,WAAW,WAAW,KAAK,SAAS,GAAG;AAC1D,mBAAK,YAAY;AACjB,kBAAI,KAAK,UAAU;AACf,qBAAK,SAAS,KAAK,SAAS;AAAA,cAChC;AAAA,YACJ;AAAA,UACJ,SACO,OAAO;AACV,oBAAQ,KAAK,KAAK;AAAA,UACtB;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AAAA;AAAA;;;ACvKF;AAAA;AAAA;AAGA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAM5D,QAAI,2BAA2B,CAAC,SAAS,QAAQ,OAAO,SAAS;AAKjE,QAAI,kBAAkB;AAItB,QAAI,gBAAgB;AAIpB,QAAI,MAAM,OAAO,cAAc,YAC3B,uBAAuB,KAAK,UAAU,QAAQ,IAC5C,SACA;AAKN,aAAS,iBAAiB,OAAO,KAAK;AAClC,aAAO,OAAO,MAAM,qBAAqB,aACnC,MAAM,iBAAiB,GAAG,IAC1B;AAAA,IACV;AASA,aAASC,OAAM,KAAK;AAChB,aAAO,IACF,KAAK,EACL,MAAM,GAAG,EACT,IAAI,SAAU,OAAO;AACtB,YAAI,OAAO,MAAM,MAAM,MAAM;AAC7B,YAAI,MAAM,KAAK,IAAI;AACnB,eAAO,KAAK,IAAI,SAAU,KAAK;AAAE,iBAAQ,QAAQ,SAAS,MAAM;AAAA,QAAM,CAAC;AACvE,eAAO,CAAC,MAAM,GAAG;AAAA,MACrB,CAAC;AAAA,IACL;AAKA,aAAS,MAAM,OAAO,OAAO;AAEzB,UAAI,iBAAiB,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,MAAM,MAAM,KAAK;AAC5D,eAAO;AAAA,MACX;AAEA,aAAO;AAAA;AAAA;AAAA,OAIN,MAAM,CAAC,EAAE,YAAY,MAAM,MAAM,IAAI,YAAY,KAC9C,MAAM,CAAC,MAAM,MAAM;AAAA,MAEnB,MAAM,CAAC,EAAE,KAAK,SAAU,KAAK;AACzB,eAAO,CAAC,iBAAiB,OAAO,GAAG;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA,MAID,yBAAyB,KAAK,SAAU,KAAK;AACzC,eAAO,CAAC,MAAM,CAAC,EAAE,SAAS,GAAG,KAAK,MAAM,CAAC,MAAM,OAAO,iBAAiB,OAAO,GAAG;AAAA,MACrF,CAAC;AAAA,IACT;AAuBA,aAAS,YAAY,QAAQ,eAAe,SAAS;AACjD,UAAI,IAAI;AACR,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,UAAI,WAAW,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK;AACtE,UAAI,SAAS,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK;AAClE,UAAI,cAAc,OAAO,KAAK,aAAa,EAAE,IAAI,SAAU,KAAK;AAC5D,eAAO,CAACA,OAAM,GAAG,GAAG,cAAc,GAAG,CAAC;AAAA,MAC1C,CAAC;AACD,UAAI,kBAAkB,oBAAI,IAAI;AAC9B,UAAI,QAAQ;AACZ,UAAI,aAAa,SAAUC,QAAO;AAI9B,YAAI,EAAEA,kBAAiB,gBAAgB;AACnC;AAAA,QACJ;AACA,oBAAY,QAAQ,SAAU,YAAY;AACtC,cAAI,WAAW,WAAW,CAAC;AAC3B,cAAI,WAAW,WAAW,CAAC;AAC3B,cAAI,OAAO,gBAAgB,IAAI,QAAQ;AACvC,cAAI,2BAA2B,OAAO,OAAO;AAC7C,cAAI,uBAAuB,yBAAyB,CAAC;AACrD,cAAI,UAAU,MAAMA,QAAO,oBAAoB;AAC/C,cAAI,CAAC,SAAS;AAMV,gBAAI,CAAC,iBAAiBA,QAAOA,OAAM,GAAG,GAAG;AACrC,8BAAgB,OAAO,QAAQ;AAAA,YACnC;AAAA,UACJ,WACS,yBAAyB,SAAS,GAAG;AAC1C,4BAAgB,IAAI,UAAU,yBAAyB,MAAM,CAAC,CAAC;AAAA,UACnE,OACK;AACD,4BAAgB,OAAO,QAAQ;AAC/B,qBAASA,MAAK;AAAA,UAClB;AAAA,QACJ,CAAC;AACD,YAAI,OAAO;AACP,uBAAa,KAAK;AAAA,QACtB;AAEA,gBAAQ,WAAW,gBAAgB,MAAM,KAAK,eAAe,GAAG,OAAO;AAAA,MAC3E;AACA,aAAO,iBAAiB,OAAO,UAAU;AACzC,aAAO,WAAY;AACf,eAAO,oBAAoB,OAAO,UAAU;AAAA,MAChD;AAAA,IACJ;AACA,YAAQ,UAAU;AAAA;AAAA;;;AC5JlB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,aAAa,gBAAgB,kBAAqB;AACtD,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,aAAS,iBAAiB;AACtB,uBAAiB;AACjB,sBAAgB;AAChB,mBAAa;AACb,sBAAgB;AAChB,aAAO;AAAA,IACX;AACA,YAAQ,iBAAiB;AAIzB,aAAS,mBAAmB;AACxB,UAAI,IAAI;AACR,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,aAAa,MAAM;AAAA,UACnB,SAAS,MAAM,gBAAgB,QAAQ,YAAY;AAAA,UACnD,UAAU,MAAM;AAAA,QACpB;AAAA,MAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,UAAU,GAAG,SAAS,cAAc,GAAG,aAAa,UAAU,GAAG,SAAS,WAAW,GAAG;AAClH,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAIC;AACJ,YAAI,QAAQ,WAAY;AACpB,gBAAM,eAAe,SAAU,IAAI;AAC/B,gBAAI,OAAO,QAAQ,YAAY,UAAU,OAAO,QAAQ,YAAY,cAAc;AAC9E,qBAAO;AAAA,YACX;AACA,mBAAO,QAAQ,YAAY;AAAA,UAC/B,CAAC;AAAA,QACL;AACA,YAAI,UAAU;AACV,gBAAM;AACN;AAAA,QACJ;AACA,YAAI,WAAW,QAAQ,kBAAkB;AACzC,YAAI,eAAe,GAAG,WAAW,SAAS,SAASA,MAAK,CAAC,GACrDA,IAAG,QAAQ,IAAI,SAAU,OAAO;AAC5B,cAAIA,KAAIC,KAAIC,KAAI;AAChB,cAAI,MAAM;AACN;AACJ,gBAAM,eAAe;AACrB,gBAAM,OAAO;AACb,cAAI,SAAS;AACT,aAACD,OAAMD,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,GAAE;AAAA,UACnI,OACK;AACD,aAAC,MAAME,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKA,GAAE;AAAA,UAClI;AAAA,QACJ,GACAF,IAAG,SAAS,SAAU,OAAO;AACzB,cAAIA,KAAIC;AACR,cAAI,SAAS;AACT,kBAAM,gBAAgB;AACtB,kBAAM,eAAe;AACrB,aAACA,OAAMD,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,GAAE;AAAA,UACnI;AACA,gBAAM;AAAA,QACV,GACAA,IAAG;AACP,eAAO,WAAY;AACf,sBAAY;AAAA,QAChB;AAAA,MACJ,GAAG,CAAC,QAAQ,WAAW,QAAQ,gBAAgB,OAAO,SAAS,QAAQ,CAAC;AACxE,UAAI,aAAaD,OAAM,OAAO;AAC9B,UAAI,kBAAkBA,OAAM,YAAY,SAAU,IAAI;AAClD,YAAIC,KAAIC;AACR,YAAI,KAAK;AACT,YAAI,OAAO,QAAQ,YAAY,aAAa;AACxC,iBAAOD,MAAK,QAAQ,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY;AAAA,QACxF;AACA,YAAI,OAAO,QAAQ,YAAY,cAAc;AACzC,iBAAOC,MAAK,QAAQ,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW;AAAA,QACvF;AACA,qBAAa,WAAW,OAAO;AAC/B,mBAAW,UAAU,WAAW,WAAY;AACxC,cAAI,aAAa;AAEjB,gBAAM,eAAe,WAAY;AAC7B,gBAAI,UAAU,OAAO,QAAQ,YAAY,cACnC,QAAQ,YAAY,UACpB,QAAQ,YAAY;AAC1B,gBAAI,YAAY,QAAQ,YAAY,QAAQ;AACxC,2BAAa;AAAA,YACjB;AACA,mBAAO;AAAA,UACX,CAAC;AACD,cAAI,YAAY;AACZ,kBAAM,qBAAqB,IAAI;AAAA,UACnC;AAAA,QACJ,GAAG,EAAE;AAAA,MACT,GAAG,EAAE,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,KAAK,CAAC;AAC/J,MAAAF,OAAM,UAAU,WAAY;AACxB,gBAAQ,aAAa;AAAA,UACjB,KAAK,QAAQ,YAAY;AAAA,UACzB,KAAK,QAAQ,YAAY;AACrB,4BAAgB,WAAW;AAC3B;AAAA,QACR;AAAA,MACJ,GAAG,CAAC,iBAAiB,WAAW,CAAC;AAAA,IACrC;AAKA,aAAS,kBAAkB;AACvB,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,aAAa,MAAM;AAAA,QACvB;AAAA,MAAI,CAAC,GAAG,cAAc,GAAG,aAAa,UAAU,GAAG;AACnD,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,QAAQ;AACR;AACJ,YAAI,gBAAgB,QAAQ,YAAY,aAAa;AACjD,mBAAS,KAAK,MAAM,WAAW;AAC/B,cAAI,CAAC,QAAQ,4BAA4B;AACrC,gBAAI,kBAAkB,GAAG,QAAQ,mBAAmB;AAEpD,gBAAI,KAAK,iBAAiB,SAAS,IAAI,EAAE,cAAc;AACvD,gBAAI,IAAI;AAEJ,gCAAkB,OAAO,GAAG,QAAQ,OAAO,EAAE,CAAC;AAAA,YAClD;AACA,qBAAS,KAAK,MAAM,cAAc,iBAAiB;AAAA,UACvD;AAAA,QACJ,WACS,gBAAgB,QAAQ,YAAY,QAAQ;AACjD,mBAAS,KAAK,MAAM,eAAe,UAAU;AAC7C,cAAI,CAAC,QAAQ,4BAA4B;AACrC,qBAAS,KAAK,MAAM,eAAe,cAAc;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AAAA,IACL;AAaA,QAAI,UAAU,oBAAI,QAAQ;AAC1B,aAAS,KAAK,SAAS;AACnB,aAAO,SAAU,OAAO;AACpB,YAAI,QAAQ,IAAI,KAAK;AACjB;AACJ,gBAAQ,KAAK;AACb,gBAAQ,IAAI,KAAK;AAAA,MACrB;AAAA,IACJ;AAKA,aAAS,eAAe;AACpB,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,SAAS,MAAM;AAAA,UACf,MAAM,MAAM,gBAAgB,QAAQ,YAAY;AAAA,UAChD,UAAU,MAAM;AAAA,QACpB;AAAA,MAAI,CAAC,GAAG,UAAU,GAAG,SAAS,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,UAAU,GAAG,SAAS,WAAW,GAAG;AACpG,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAIC;AACJ,YAAI,QAAQ;AACR;AACJ,YAAI,cAAc,OAAO,KAAK,OAAO,EAAE,IAAI,SAAU,KAAK;AAAE,iBAAO,QAAQ,GAAG;AAAA,QAAG,CAAC;AAClF,YAAI,uBAAuB,CAAC;AAC5B,iBAAS,KAAK,GAAG,gBAAgB,aAAa,KAAK,cAAc,QAAQ,MAAM;AAC3E,cAAI,SAAS,cAAc,EAAE;AAC7B,cAAI,GAAGA,MAAK,OAAO,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS;AAC1E;AAAA,UACJ;AACA,+BAAqB,KAAK,MAAM;AAAA,QACpC;AACA,+BAAuB,qBAAqB,KAAK,SAAU,GAAG,GAAG;AAAE,iBAAO,EAAE,SAAS,KAAK,GAAG,EAAE,SAAS,EAAE,SAAS,KAAK,GAAG,EAAE;AAAA,QAAQ,CAAC;AACtI,YAAI,eAAe,CAAC;AACpB,YAAI,UAAU,SAAUG,SAAQ;AAC5B,cAAI,WAAWA,QAAO,SAAS,KAAK,GAAG;AACvC,uBAAa,QAAQ,IAAI,KAAK,SAAU,OAAO;AAC3C,gBAAIH,KAAIC,KAAI,IAAI,IAAI,IAAI;AACxB,iBAAK,GAAG,QAAQ,wBAAwB;AACpC;AACJ,kBAAM,eAAe;AACrB,iBAAKD,MAAKG,QAAO,cAAc,QAAQH,QAAO,SAAS,SAASA,IAAG,QAAQ;AACvE,oBAAM,qBAAqBG,QAAO,EAAE;AACpC,oBAAM,OAAO;AACb,eAAC,MAAMF,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKA,GAAE;AAAA,YAClI,OACK;AACD,eAAC,KAAKE,QAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACtE,eAAC,MAAM,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAIA,OAAM;AAAA,YAClJ;AAAA,UACJ,CAAC;AAAA,QACL;AACA,iBAAS,KAAK,GAAG,yBAAyB,sBAAsB,KAAK,uBAAuB,QAAQ,MAAM;AACtG,cAAI,SAAS,uBAAuB,EAAE;AACtC,kBAAQ,MAAM;AAAA,QAClB;AACA,YAAI,eAAe,GAAG,WAAW,SAAS,QAAQ,cAAc;AAAA,UAC5D,SAAS;AAAA,QACb,CAAC;AACD,eAAO,WAAY;AACf,sBAAY;AAAA,QAChB;AAAA,MACJ,GAAG,CAAC,SAAS,MAAM,QAAQ,WAAW,OAAO,QAAQ,CAAC;AAAA,IAC1D;AAKA,aAAS,kBAAkB;AACvB,UAAI,eAAeJ,OAAM,OAAO,IAAI;AACpC,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,WAAW,MAAM,gBAAgB,QAAQ,YAAY,WACjD,MAAM,gBAAgB,QAAQ,YAAY;AAAA,QAClD;AAAA,MAAI,CAAC,GAAG,YAAY,GAAG,WAAW,QAAQ,GAAG;AAC7C,UAAI,mBAAmBA,OAAM,OAAO,IAAI;AACxC,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,aAAa,SAAS;AACtB,uBAAa,UAAU;AACvB;AAAA,QACJ;AACA,YAAI,WAAW;AACX,2BAAiB,UAAU,SAAS;AACpC;AAAA,QACJ;AAIA,YAAI,uBAAuB,SAAS;AACpC,aAAK,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,QAAQ,YAAY,OAAO,SAAS;AACtI,+BAAqB,KAAK;AAAA,QAC9B;AACA,YAAI,gBAAgB,iBAAiB;AACrC,YAAI,iBAAiB,kBAAkB,sBAAsB;AACzD,wBAAc,MAAM;AAAA,QACxB;AAAA,MACJ,GAAG,CAAC,SAAS,CAAC;AAGd,MAAAA,OAAM,UAAU,WAAY;AACxB,iBAAS,QAAQ,OAAO;AACpB,cAAI,QAAQ,MAAM,SAAS;AAC3B,cAAI,MAAM,WAAW,OAAO;AACxB,kBAAM,MAAM;AAAA,UAChB;AAAA,QACJ;AACA,YAAI,WAAW;AACX,iBAAO,iBAAiB,WAAW,OAAO;AAC1C,iBAAO,WAAY;AACf,mBAAO,oBAAoB,WAAW,OAAO;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,WAAW,KAAK,CAAC;AAAA,IACzB;AAAA;AAAA;;;AChSA;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,eAAe,QAAQ,cAAc;AAC7C,QAAI,aAAa;AACjB,QAAIK,SAAQ,aAAa,eAAgB;AACzC,QAAI,mBAAmB;AACvB,YAAQ,cAAcA,OAAM,cAAc,CAAC,CAAC;AAC5C,QAAI,eAAe,SAAUC,QAAO;AAChC,UAAI,gBAAgB,GAAG,WAAW,UAAUA,MAAK;AACjD,aAAQD,OAAM;AAAA,QAAc,QAAQ,YAAY;AAAA,QAAU,EAAE,OAAO,aAAa;AAAA,QAC5EA,OAAM,cAAc,iBAAiB,gBAAgB,IAAI;AAAA,QACzDC,OAAM;AAAA,MAAQ;AAAA,IACtB;AACA,YAAQ,eAAe;AAAA;AAAA;;;AChCvB;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU;AAClB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,wBAAwB;AAC5B,aAAS,QAAQ,WAAW;AACxB,UAAI,KAAKA,OAAM,WAAW,sBAAsB,WAAW,GAAG,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,UAAU,GAAG;AAC/I,UAAI,YAAYA,OAAM,OAAO,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,CAAC,CAAC;AACxG,UAAI,eAAeA,OAAM,OAAO,SAAS;AACzC,UAAI,YAAYA,OAAM,YAAY,SAAUC,YAAW;AAAE,eAAQ,SAAS,SAAS,CAAC,GAAGA,UAAS,GAAG,EAAE,OAAc,QAAiB,CAAC;AAAA,MAAI,GAAG,CAAC,OAAO,OAAO,CAAC;AAC5J,UAAI,KAAKD,OAAM,SAAS,UAAU,UAAU,OAAO,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACvF,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI;AACJ,YAAI,aAAa,SAAS;AACtB,wBAAc,UAAU,SAAU,SAAS;AAAE,mBAAO,aAAa,QAAQ,OAAO;AAAA,UAAG,GAAG,SAAUC,YAAW;AAAE,mBAAO,UAAU,UAAUA,UAAS,CAAC;AAAA,UAAG,CAAC;AAAA,QAC1J;AACA,eAAO,WAAY;AACf,cAAI,aAAa;AACb,wBAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,WAAW,SAAS,CAAC;AACzB,aAAO;AAAA,IACX;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACtDlB;AAAA;AAAA;AAAA;AASA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,MAAM,UACV,OAAO,KAAK,MAAM,mBAClB,MAAM,QAAQ,KAAK;AACzB;AAIA,SAAS,aAAa,OAAO;AAE3B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAQ;AACrB,SAAO,UAAU,OAAO,IAAI,SAAS,CAAC,WAAW,OAAO;AAC1D;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,OAAO,KAAK,aAAa,KAAK;AAChD;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAGA,SAAS,UAAU,OAAO;AACxB,SACE,UAAU,QACV,UAAU,SACT,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAE7C;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAGA,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,KAAK,KAAK,UAAU;AACtC;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAa,UAAU;AAC1C;AAEA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,MAAM,KAAK,EAAE;AACvB;AAIA,SAAS,OAAO,OAAO;AACrB,SAAO,SAAS,OACZ,UAAU,SACR,uBACA,kBACF,OAAO,UAAU,SAAS,KAAK,KAAK;AAC1C;AAqDA,SAAS,UAAU,KAAK;AACtB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,QAAQ;AAEZ,MAAI,SAAS,GAAG,KAAK,QAAQ,GAAG,GAAG;AACjC,UAAM;AACN,WAAO,cAAc,GAAG;AACxB,SAAK,YAAY,GAAG;AAAA,EACtB,OAAO;AACL,QAAI,CAAC,OAAO,KAAK,KAAK,MAAM,GAAG;AAC7B,YAAM,IAAI,MAAM,qBAAqB,MAAM,CAAC;AAAA,IAC9C;AAEA,UAAM,OAAO,IAAI;AACjB,UAAM;AAEN,QAAI,OAAO,KAAK,KAAK,QAAQ,GAAG;AAC9B,eAAS,IAAI;AAEb,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,yBAAyB,IAAI,CAAC;AAAA,MAChD;AAAA,IACF;AAEA,WAAO,cAAc,IAAI;AACzB,SAAK,YAAY,IAAI;AACrB,YAAQ,IAAI;AAAA,EACd;AAEA,SAAO,EAAE,MAAM,IAAI,QAAQ,KAAK,MAAM;AACxC;AAEA,SAAS,cAAc,KAAK;AAC1B,SAAO,QAAQ,GAAG,IAAI,MAAM,IAAI,MAAM,GAAG;AAC3C;AAEA,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI;AACxC;AAEA,SAAS,IAAI,KAAK,MAAM;AACtB,MAAI,OAAO,CAAC;AACZ,MAAI,MAAM;AAEV,QAAM,UAAU,CAACC,MAAKC,OAAM,UAAU;AACpC,QAAI,CAAC,UAAUD,IAAG,GAAG;AACnB;AAAA,IACF;AACA,QAAI,CAACC,MAAK,KAAK,GAAG;AAEhB,WAAK,KAAKD,IAAG;AAAA,IACf,OAAO;AACL,UAAI,MAAMC,MAAK,KAAK;AAEpB,YAAM,QAAQD,KAAI,GAAG;AAErB,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB;AAAA,MACF;AAIA,UACE,UAAUC,MAAK,SAAS,MACvB,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU,KAAK,IACtD;AACA,aAAK,KAAK,SAAS,KAAK,CAAC;AAAA,MAC3B,WAAW,QAAQ,KAAK,GAAG;AACzB,cAAM;AAEN,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACnD,kBAAQ,MAAM,CAAC,GAAGA,OAAM,QAAQ,CAAC;AAAA,QACnC;AAAA,MACF,WAAWA,MAAK,QAAQ;AAEtB,gBAAQ,OAAOA,OAAM,QAAQ,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC;AAEvD,SAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAwEA,SAAS,KAAK,SAAS,GAAG,WAAW,GAAG;AACtC,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,IAAI,KAAK,IAAI,IAAI,QAAQ;AAE/B,SAAO;AAAA,IACL,IAAI,OAAO;AACT,YAAM,YAAY,MAAM,MAAM,KAAK,EAAE;AAErC,UAAI,MAAM,IAAI,SAAS,GAAG;AACxB,eAAO,MAAM,IAAI,SAAS;AAAA,MAC5B;AAGA,YAAMC,QAAO,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM;AAGjD,YAAM,IAAI,WAAW,KAAK,MAAMA,QAAO,CAAC,IAAI,CAAC;AAE7C,YAAM,IAAI,WAAW,CAAC;AAEtB,aAAO;AAAA,IACT;AAAA,IACA,QAAQ;AACN,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACF;AAiJA,SAAS,YACP,MACA,MACA,EAAE,QAAQ,OAAO,OAAO,kBAAkB,OAAO,gBAAgB,IAAI,CAAC,GACtE;AACA,QAAM,UAAU,IAAI,UAAU,EAAE,OAAO,gBAAgB,CAAC;AACxD,UAAQ,QAAQ,KAAK,IAAI,SAAS,CAAC;AACnC,UAAQ,WAAW,IAAI;AACvB,UAAQ,OAAO;AACf,SAAO;AACT;AAEA,SAAS,WACP,MACA,EAAE,QAAQ,OAAO,OAAO,kBAAkB,OAAO,gBAAgB,IAAI,CAAC,GACtE;AACA,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,QAAM,UAAU,IAAI,UAAU,EAAE,OAAO,gBAAgB,CAAC;AACxD,UAAQ,QAAQ,IAAI;AACpB,UAAQ,gBAAgB,OAAO;AAC/B,SAAO;AACT;AAEA,SAAS,eACP,SACA;AAAA,EACE,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,WAAW,OAAO;AAAA,EAClB,iBAAiB,OAAO;AAC1B,IAAI,CAAC,GACL;AACA,QAAM,WAAW,SAAS,QAAQ;AAElC,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,KAAK,IAAI,mBAAmB,eAAe;AAE7D,MAAI,CAAC,UAAU;AAEb,WAAO,YAAY,IAAM;AAAA,EAC3B;AAEA,SAAO,WAAW,YAAY;AAChC;AAEA,SAAS,qBACP,YAAY,CAAC,GACb,qBAAqB,OAAO,oBAC5B;AACA,MAAI,UAAU,CAAC;AACf,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,IAAI;AAER,WAAS,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK,GAAG;AAChD,QAAI,QAAQ,UAAU,CAAC;AACvB,QAAI,SAAS,UAAU,IAAI;AACzB,cAAQ;AAAA,IACV,WAAW,CAAC,SAAS,UAAU,IAAI;AACjC,YAAM,IAAI;AACV,UAAI,MAAM,QAAQ,KAAK,oBAAoB;AACzC,gBAAQ,KAAK,CAAC,OAAO,GAAG,CAAC;AAAA,MAC3B;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AAGA,MAAI,UAAU,IAAI,CAAC,KAAK,IAAI,SAAS,oBAAoB;AACvD,YAAQ,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;AAAA,EAC7B;AAEA,SAAO;AACT;AAKA,SAAS,OACP,MACA,SACA,iBACA;AAAA,EACE,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,YAAY,OAAO;AAAA,EACnB,iBAAiB,OAAO;AAAA,EACxB,qBAAqB,OAAO;AAAA,EAC5B,iBAAiB,OAAO;AAAA,EACxB,iBAAiB,OAAO;AAC1B,IAAI,CAAC,GACL;AACA,MAAI,QAAQ,SAAS,UAAU;AAC7B,UAAM,IAAI,MAAM,yBAAyB,QAAQ,CAAC;AAAA,EACpD;AAEA,QAAM,aAAa,QAAQ;AAE3B,QAAM,UAAU,KAAK;AAErB,QAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI,UAAU,OAAO,CAAC;AAEhE,MAAI,mBAAmB;AAEvB,MAAI,eAAe;AAInB,QAAM,iBAAiB,qBAAqB,KAAK;AAEjD,QAAM,YAAY,iBAAiB,MAAM,OAAO,IAAI,CAAC;AAErD,MAAI;AAGJ,UAAQ,QAAQ,KAAK,QAAQ,SAAS,YAAY,KAAK,IAAI;AACzD,QAAI,QAAQ,eAAe,SAAS;AAAA,MAClC,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,uBAAmB,KAAK,IAAI,OAAO,gBAAgB;AACnD,mBAAe,QAAQ;AAEvB,QAAI,gBAAgB;AAClB,UAAI,IAAI;AACR,aAAO,IAAI,YAAY;AACrB,kBAAU,QAAQ,CAAC,IAAI;AACvB,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAGA,iBAAe;AAEf,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa;AACjB,MAAI,SAAS,aAAa;AAE1B,QAAM,OAAO,KAAM,aAAa;AAEhC,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK,GAAG;AAItC,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,WAAO,SAAS,QAAQ;AACtB,YAAMC,SAAQ,eAAe,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,iBAAiB,mBAAmB;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAIA,UAAS,kBAAkB;AAC7B,iBAAS;AAAA,MACX,OAAO;AACL,iBAAS;AAAA,MACX;AAEA,eAAS,KAAK,OAAO,SAAS,UAAU,IAAI,MAAM;AAAA,IACpD;AAGA,aAAS;AAET,QAAI,QAAQ,KAAK,IAAI,GAAG,mBAAmB,SAAS,CAAC;AACrD,QAAI,SAAS,iBACT,UACA,KAAK,IAAI,mBAAmB,QAAQ,OAAO,IAAI;AAGnD,QAAI,SAAS,MAAM,SAAS,CAAC;AAE7B,WAAO,SAAS,CAAC,KAAK,KAAK,KAAK;AAEhC,aAAS,IAAI,QAAQ,KAAK,OAAO,KAAK,GAAG;AACvC,UAAI,kBAAkB,IAAI;AAC1B,UAAI,YAAY,gBAAgB,KAAK,OAAO,eAAe,CAAC;AAE5D,UAAI,gBAAgB;AAElB,kBAAU,eAAe,IAAI,CAAC,CAAC,CAAC;AAAA,MAClC;AAGA,aAAO,CAAC,KAAM,OAAO,IAAI,CAAC,KAAK,IAAK,KAAK;AAGzC,UAAI,GAAG;AACL,eAAO,CAAC,MACJ,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,IAAK,IAAI,WAAW,IAAI,CAAC;AAAA,MACrE;AAEA,UAAI,OAAO,CAAC,IAAI,MAAM;AACpB,qBAAa,eAAe,SAAS;AAAA,UACnC,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAID,YAAI,cAAc,kBAAkB;AAElC,6BAAmB;AACnB,yBAAe;AAGf,cAAI,gBAAgB,kBAAkB;AACpC;AAAA,UACF;AAGA,kBAAQ,KAAK,IAAI,GAAG,IAAI,mBAAmB,YAAY;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAGA,UAAM,QAAQ,eAAe,SAAS;AAAA,MACpC,QAAQ,IAAI;AAAA,MACZ,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,QAAQ,kBAAkB;AAC5B;AAAA,IACF;AAEA,iBAAa;AAAA,EACf;AAEA,QAAM,SAAS;AAAA,IACb,SAAS,gBAAgB;AAAA;AAAA,IAEzB,OAAO,KAAK,IAAI,MAAO,UAAU;AAAA,EACnC;AAEA,MAAI,gBAAgB;AAClB,UAAM,UAAU,qBAAqB,WAAW,kBAAkB;AAClE,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO,UAAU;AAAA,IACnB,WAAW,gBAAgB;AACzB,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS;AACtC,MAAI,OAAO,CAAC;AAEZ,WAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK,GAAG;AACrD,UAAM,OAAO,QAAQ,OAAO,CAAC;AAC7B,SAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAM,KAAM,MAAM,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;AAmJA,SAAS,SAAS,SAAS,KAAK;AAC9B,QAAM,UAAU,QAAQ,MAAM,GAAG;AACjC,SAAO,UAAU,QAAQ,CAAC,IAAI;AAChC;AAiQA,SAAS,WAAW,SAAS,UAAU,CAAC,GAAG;AACzC,SAAO,QAAQ,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC3C,QAAI,QAAQ,KACT,KAAK,EACL,MAAM,QAAQ,EACd,OAAO,CAACC,UAASA,SAAQ,CAAC,CAACA,MAAK,KAAK,CAAC;AAEzC,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACnD,YAAM,YAAY,MAAM,CAAC;AAGzB,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,aAAO,CAAC,SAAS,EAAE,MAAM,cAAc;AACrC,cAAM,WAAW,UAAU,GAAG;AAC9B,YAAI,QAAQ,SAAS,aAAa,SAAS;AAC3C,YAAI,OAAO;AACT,kBAAQ,KAAK,IAAI,SAAS,OAAO,OAAO,CAAC;AACzC,kBAAQ;AAAA,QACV;AAAA,MACF;AAEA,UAAI,OAAO;AACT;AAAA,MACF;AAGA,YAAM;AACN,aAAO,EAAE,MAAM,cAAc;AAC3B,cAAM,WAAW,UAAU,GAAG;AAC9B,YAAI,QAAQ,SAAS,cAAc,SAAS;AAC5C,YAAI,OAAO;AACT,kBAAQ,KAAK,IAAI,SAAS,OAAO,OAAO,CAAC;AACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AA+IA,SAAS,YAAY,MAAM;AACzB,sBAAoB,KAAK,GAAG,IAAI;AAClC;AAEA,SAAS,eAAe,SAAS,SAAS;AACxC,WAAS,IAAI,GAAG,MAAM,oBAAoB,QAAQ,IAAI,KAAK,KAAK,GAAG;AACjE,QAAI,gBAAgB,oBAAoB,CAAC;AACzC,QAAI,cAAc,UAAU,SAAS,OAAO,GAAG;AAC7C,aAAO,IAAI,cAAc,SAAS,OAAO;AAAA,IAC3C;AAAA,EACF;AAEA,SAAO,IAAI,YAAY,SAAS,OAAO;AACzC;AA4BA,SAAS,MAAM,OAAO,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG;AACnD,QAAM,OAAO,CAACC,WAAU;AACtB,QAAI,OAAO,OAAO,KAAKA,MAAK;AAE5B,UAAM,cAAc,OAAOA,MAAK;AAEhC,QAAI,CAAC,eAAe,KAAK,SAAS,KAAK,CAAC,aAAaA,MAAK,GAAG;AAC3D,aAAO,KAAK,kBAAkBA,MAAK,CAAC;AAAA,IACtC;AAEA,QAAI,OAAOA,MAAK,GAAG;AACjB,YAAM,MAAM,cAAcA,OAAM,QAAQ,IAAI,IAAI,KAAK,CAAC;AAEtD,YAAM,UAAU,cAAcA,OAAM,QAAQ,OAAO,IAAIA,OAAM,GAAG;AAEhE,UAAI,CAAC,SAAS,OAAO,GAAG;AACtB,cAAM,IAAI,MAAM,qCAAqC,GAAG,CAAC;AAAA,MAC3D;AAEA,YAAM,MAAM;AAAA,QACV,OAAO,YAAY,GAAG;AAAA,QACtB;AAAA,MACF;AAEA,UAAI,MAAM;AACR,YAAI,WAAW,eAAe,SAAS,OAAO;AAAA,MAChD;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AAAA,MACT,UAAU,CAAC;AAAA,MACX,UAAU,KAAK,CAAC;AAAA,IAClB;AAEA,SAAK,QAAQ,CAAC,QAAQ;AACpB,YAAM,QAAQA,OAAM,GAAG;AAEvB,UAAI,QAAQ,KAAK,GAAG;AAClB,cAAM,QAAQ,CAAC,SAAS;AACtB,eAAK,SAAS,KAAK,KAAK,IAAI,CAAC;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,aAAa,KAAK,GAAG;AACxB,YAAQ,kBAAkB,KAAK;AAAA,EACjC;AAEA,SAAO,KAAK,KAAK;AACnB;AAGA,SAAS,aACP,SACA,EAAE,kBAAkB,OAAO,gBAAgB,GAC3C;AACA,UAAQ,QAAQ,CAAC,WAAW;AAC1B,QAAI,aAAa;AAEjB,WAAO,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAAH,OAAM,MAAM,MAAM;AAC/C,YAAM,SAAS,MAAM,IAAI,SAAS;AAElC,oBAAc,KAAK;AAAA,QACjB,UAAU,KAAK,SAAS,OAAO,UAAU;AAAA,SACxC,UAAU,MAAM,kBAAkB,IAAIA;AAAA,MACzC;AAAA,IACF,CAAC;AAED,WAAO,QAAQ;AAAA,EACjB,CAAC;AACH;AAEA,SAAS,iBAAiB,QAAQ,MAAM;AACtC,QAAM,UAAU,OAAO;AACvB,OAAK,UAAU,CAAC;AAEhB,MAAI,CAAC,UAAU,OAAO,GAAG;AACvB;AAAA,EACF;AAEA,UAAQ,QAAQ,CAAC,UAAU;AACzB,QAAI,CAAC,UAAU,MAAM,OAAO,KAAK,CAAC,MAAM,QAAQ,QAAQ;AACtD;AAAA,IACF;AAEA,UAAM,EAAE,SAAS,MAAM,IAAI;AAE3B,QAAI,MAAM;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAEA,QAAI,MAAM,KAAK;AACb,UAAI,MAAM,MAAM,IAAI;AAAA,IACtB;AAEA,QAAI,MAAM,MAAM,IAAI;AAClB,UAAI,WAAW,MAAM;AAAA,IACvB;AAEA,SAAK,QAAQ,KAAK,GAAG;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,eAAe,QAAQ,MAAM;AACpC,OAAK,QAAQ,OAAO;AACtB;AAEA,SAAS,OACP,SACA,MACA;AAAA,EACE,iBAAiB,OAAO;AAAA,EACxB,eAAe,OAAO;AACxB,IAAI,CAAC,GACL;AACA,QAAM,eAAe,CAAC;AAEtB,MAAI,eAAgB,cAAa,KAAK,gBAAgB;AACtD,MAAI,aAAc,cAAa,KAAK,cAAc;AAElD,SAAO,QAAQ,IAAI,CAAC,WAAW;AAC7B,UAAM,EAAE,IAAI,IAAI;AAEhB,UAAM,OAAO;AAAA,MACX,MAAM,KAAK,GAAG;AAAA,MACd,UAAU;AAAA,IACZ;AAEA,QAAI,aAAa,QAAQ;AACvB,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,oBAAY,QAAQ,IAAI;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AA/9CA,IAgBM,UA4DA,sBAEA,sCAGA,0BAGA,sBAEA,0BAGA,QAEA,UA2HA,cAYA,cAeA,cAcA,iBAkBF,QAOE,OAgCA,WA+NA,UAoMA,aAoIA,WAoBA,YA0BA,mBA2BA,kBA0BA,yBA0BA,kBA0BA,yBAuBA,YA0CA,cAqCA,WAWA,cAGA,UACA,UAkDA,eA8BA,gBA2GA,qBAiBA,iBAKA,SAKA,cAGA,QAEA,QAGA,mBAwJA;AAj+CN;AAAA;AAgBA,IAAM,WAAW,IAAI;AA4DrB,IAAM,uBAAuB;AAE7B,IAAM,uCAAuC,CAAC,QAC5C,yBAAyB,GAAG;AAE9B,IAAM,2BAA2B,CAAC,QAChC,iCAAiC,GAAG;AAEtC,IAAM,uBAAuB,CAAC,SAAS,WAAW,IAAI;AAEtD,IAAM,2BAA2B,CAAC,QAChC,6BAA6B,GAAG;AAElC,IAAM,SAAS,OAAO,UAAU;AAEhC,IAAM,WAAN,MAAe;AAAA,MACb,YAAY,MAAM;AAChB,aAAK,QAAQ,CAAC;AACd,aAAK,UAAU,CAAC;AAEhB,YAAI,cAAc;AAElB,aAAK,QAAQ,CAAC,QAAQ;AACpB,cAAI,MAAM,UAAU,GAAG;AAEvB,yBAAe,IAAI;AAEnB,eAAK,MAAM,KAAK,GAAG;AACnB,eAAK,QAAQ,IAAI,EAAE,IAAI;AAEvB,yBAAe,IAAI;AAAA,QACrB,CAAC;AAGD,aAAK,MAAM,QAAQ,CAAC,QAAQ;AAC1B,cAAI,UAAU;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,IAAI,OAAO;AACT,eAAO,KAAK,QAAQ,KAAK;AAAA,MAC3B;AAAA,MACA,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AACP,eAAO,KAAK,UAAU,KAAK,KAAK;AAAA,MAClC;AAAA,IACF;AA2FA,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA,MAInB,gBAAgB;AAAA;AAAA;AAAA,MAGhB,gBAAgB;AAAA;AAAA,MAEhB,oBAAoB;AAAA,IACtB;AAEA,IAAM,eAAe;AAAA;AAAA;AAAA,MAGnB,iBAAiB;AAAA;AAAA,MAEjB,cAAc;AAAA;AAAA,MAEd,MAAM,CAAC;AAAA;AAAA,MAEP,YAAY;AAAA;AAAA,MAEZ,QAAQ,CAAC,GAAG,MACV,EAAE,UAAU,EAAE,QAAS,EAAE,MAAM,EAAE,MAAM,KAAK,IAAK,EAAE,QAAQ,EAAE,QAAQ,KAAK;AAAA,IAC9E;AAEA,IAAM,eAAe;AAAA;AAAA,MAEnB,UAAU;AAAA;AAAA;AAAA,MAGV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMX,UAAU;AAAA,IACZ;AAEA,IAAM,kBAAkB;AAAA;AAAA,MAEtB,mBAAmB;AAAA;AAAA;AAAA,MAGnB,OAAO;AAAA;AAAA;AAAA;AAAA,MAIP,gBAAgB;AAAA;AAAA;AAAA;AAAA,MAIhB,iBAAiB;AAAA;AAAA,MAEjB,iBAAiB;AAAA,IACnB;AAEA,IAAI,SAAS;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,IAAM,QAAQ;AAgCd,IAAM,YAAN,MAAgB;AAAA,MACd,YAAY;AAAA,QACV,QAAQ,OAAO;AAAA,QACf,kBAAkB,OAAO;AAAA,MAC3B,IAAI,CAAC,GAAG;AACN,aAAK,OAAO,KAAK,iBAAiB,CAAC;AACnC,aAAK,QAAQ;AACb,aAAK,YAAY;AAEjB,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,WAAW,OAAO,CAAC,GAAG;AACpB,aAAK,OAAO;AAAA,MACd;AAAA,MACA,gBAAgB,UAAU,CAAC,GAAG;AAC5B,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,QAAQ,OAAO,CAAC,GAAG;AACjB,aAAK,OAAO;AACZ,aAAK,WAAW,CAAC;AACjB,aAAK,QAAQ,CAAC,KAAK,QAAQ;AACzB,eAAK,SAAS,IAAI,EAAE,IAAI;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AACP,YAAI,KAAK,aAAa,CAAC,KAAK,KAAK,QAAQ;AACvC;AAAA,QACF;AAEA,aAAK,YAAY;AAGjB,YAAI,SAAS,KAAK,KAAK,CAAC,CAAC,GAAG;AAC1B,eAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,iBAAK,WAAW,KAAK,QAAQ;AAAA,UAC/B,CAAC;AAAA,QACH,OAAO;AAEL,eAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,iBAAK,WAAW,KAAK,QAAQ;AAAA,UAC/B,CAAC;AAAA,QACH;AAEA,aAAK,KAAK,MAAM;AAAA,MAClB;AAAA;AAAA,MAEA,IAAI,KAAK;AACP,cAAM,MAAM,KAAK,KAAK;AAEtB,YAAI,SAAS,GAAG,GAAG;AACjB,eAAK,WAAW,KAAK,GAAG;AAAA,QAC1B,OAAO;AACL,eAAK,WAAW,KAAK,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA;AAAA,MAEA,SAAS,KAAK;AACZ,aAAK,QAAQ,OAAO,KAAK,CAAC;AAG1B,iBAAS,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG;AACpD,eAAK,QAAQ,CAAC,EAAE,KAAK;AAAA,QACvB;AAAA,MACF;AAAA,MACA,uBAAuB,MAAM,OAAO;AAClC,eAAO,KAAK,KAAK,SAAS,KAAK,CAAC;AAAA,MAClC;AAAA,MACA,OAAO;AACL,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA,MACA,WAAW,KAAK,UAAU;AACxB,YAAI,CAAC,UAAU,GAAG,KAAK,QAAQ,GAAG,GAAG;AACnC;AAAA,QACF;AAEA,YAAI,SAAS;AAAA,UACX,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,QACtB;AAEA,aAAK,QAAQ,KAAK,MAAM;AAAA,MAC1B;AAAA,MACA,WAAW,KAAK,UAAU;AACxB,YAAI,SAAS,EAAE,GAAG,UAAU,GAAG,CAAC,EAAE;AAGlC,aAAK,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,cAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI;AAEjE,cAAI,CAAC,UAAU,KAAK,GAAG;AACrB;AAAA,UACF;AAEA,cAAI,QAAQ,KAAK,GAAG;AAClB,gBAAI,aAAa,CAAC;AAClB,kBAAM,QAAQ,CAAC,EAAE,gBAAgB,IAAI,MAAM,CAAC;AAE5C,mBAAO,MAAM,QAAQ;AACnB,oBAAM,EAAE,gBAAgB,OAAAI,OAAM,IAAI,MAAM,IAAI;AAE5C,kBAAI,CAAC,UAAUA,MAAK,GAAG;AACrB;AAAA,cACF;AAEA,kBAAI,SAASA,MAAK,KAAK,CAAC,QAAQA,MAAK,GAAG;AACtC,oBAAI,YAAY;AAAA,kBACd,GAAGA;AAAA,kBACH,GAAG;AAAA,kBACH,GAAG,KAAK,KAAK,IAAIA,MAAK;AAAA,gBACxB;AAEA,2BAAW,KAAK,SAAS;AAAA,cAC3B,WAAW,QAAQA,MAAK,GAAG;AACzB,gBAAAA,OAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,wBAAM,KAAK;AAAA,oBACT,gBAAgB;AAAA,oBAChB,OAAO;AAAA,kBACT,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,MAAO;AAAA,YACT;AACA,mBAAO,EAAE,QAAQ,IAAI;AAAA,UACvB,WAAW,SAAS,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AAC7C,gBAAI,YAAY;AAAA,cACd,GAAG;AAAA,cACH,GAAG,KAAK,KAAK,IAAI,KAAK;AAAA,YACxB;AAEA,mBAAO,EAAE,QAAQ,IAAI;AAAA,UACvB;AAAA,QACF,CAAC;AAED,aAAK,QAAQ,KAAK,MAAM;AAAA,MAC1B;AAAA,MACA,SAAS;AACP,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAkFA,IAAM,WAAW;AAoMjB,IAAM,cAAN,MAAkB;AAAA,MAChB,YACE,SACA;AAAA,QACE,WAAW,OAAO;AAAA,QAClB,YAAY,OAAO;AAAA,QACnB,WAAW,OAAO;AAAA,QAClB,iBAAiB,OAAO;AAAA,QACxB,iBAAiB,OAAO;AAAA,QACxB,qBAAqB,OAAO;AAAA,QAC5B,kBAAkB,OAAO;AAAA,QACzB,iBAAiB,OAAO;AAAA,MAC1B,IAAI,CAAC,GACL;AACA,aAAK,UAAU;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,aAAK,UAAU,kBAAkB,UAAU,QAAQ,YAAY;AAE/D,aAAK,SAAS,CAAC;AAEf,YAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB;AAAA,QACF;AAEA,cAAM,WAAW,CAACC,UAAS,eAAe;AACxC,eAAK,OAAO,KAAK;AAAA,YACf,SAAAA;AAAA,YACA,UAAU,sBAAsBA,QAAO;AAAA,YACvC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,cAAM,MAAM,KAAK,QAAQ;AAEzB,YAAI,MAAM,UAAU;AAClB,cAAI,IAAI;AACR,gBAAM,YAAY,MAAM;AACxB,gBAAM,MAAM,MAAM;AAElB,iBAAO,IAAI,KAAK;AACd,qBAAS,KAAK,QAAQ,OAAO,GAAG,QAAQ,GAAG,CAAC;AAC5C,iBAAK;AAAA,UACP;AAEA,cAAI,WAAW;AACb,kBAAM,aAAa,MAAM;AACzB,qBAAS,KAAK,QAAQ,OAAO,UAAU,GAAG,UAAU;AAAA,UACtD;AAAA,QACF,OAAO;AACL,mBAAS,KAAK,SAAS,CAAC;AAAA,QAC1B;AAAA,MACF;AAAA,MAEA,SAAS,MAAM;AACb,cAAM,EAAE,iBAAiB,eAAe,IAAI,KAAK;AAEjD,YAAI,CAAC,iBAAiB;AACpB,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAGA,YAAI,KAAK,YAAY,MAAM;AACzB,cAAIC,UAAS;AAAA,YACX,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAEA,cAAI,gBAAgB;AAClB,YAAAA,QAAO,UAAU,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,UACxC;AAEA,iBAAOA;AAAA,QACT;AAGA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AAET,YAAI,aAAa,CAAC;AAClB,YAAI,aAAa;AACjB,YAAI,aAAa;AAEjB,aAAK,OAAO,QAAQ,CAAC,EAAE,SAAS,UAAU,WAAW,MAAM;AACzD,gBAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,OAAO,MAAM,SAAS,UAAU;AAAA,YAClE,UAAU,WAAW;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAED,cAAI,SAAS;AACX,yBAAa;AAAA,UACf;AAEA,wBAAc;AAEd,cAAI,WAAW,SAAS;AACtB,yBAAa,CAAC,GAAG,YAAY,GAAG,OAAO;AAAA,UACzC;AAAA,QACF,CAAC;AAED,YAAI,SAAS;AAAA,UACX,SAAS;AAAA,UACT,OAAO,aAAa,aAAa,KAAK,OAAO,SAAS;AAAA,QACxD;AAEA,YAAI,cAAc,gBAAgB;AAChC,iBAAO,UAAU;AAAA,QACnB;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,IAAM,YAAN,MAAgB;AAAA,MACd,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,OAAO,aAAa,SAAS;AAC3B,eAAO,SAAS,SAAS,KAAK,UAAU;AAAA,MAC1C;AAAA,MACA,OAAO,cAAc,SAAS;AAC5B,eAAO,SAAS,SAAS,KAAK,WAAW;AAAA,MAC3C;AAAA,MACA,SAAiB;AAAA,MAAC;AAAA,IACpB;AASA,IAAM,aAAN,cAAyB,UAAU;AAAA,MACjC,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,UAAU,SAAS,KAAK;AAE9B,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,GAAG,KAAK,QAAQ,SAAS,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAIA,IAAM,oBAAN,cAAgC,UAAU;AAAA,MACxC,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,QAAQ,KAAK,QAAQ,KAAK,OAAO;AACvC,cAAM,UAAU,UAAU;AAE1B,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAIA,IAAM,mBAAN,cAA+B,UAAU;AAAA,MACvC,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,UAAU,KAAK,WAAW,KAAK,OAAO;AAE5C,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,GAAG,KAAK,QAAQ,SAAS,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAIA,IAAM,0BAAN,cAAsC,UAAU;AAAA,MAC9C,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,UAAU,CAAC,KAAK,WAAW,KAAK,OAAO;AAE7C,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAIA,IAAM,mBAAN,cAA+B,UAAU;AAAA,MACvC,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,UAAU,KAAK,SAAS,KAAK,OAAO;AAE1C,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,KAAK,SAAS,KAAK,QAAQ,QAAQ,KAAK,SAAS,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AAIA,IAAM,0BAAN,cAAsC,UAAU;AAAA,MAC9C,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,cAAM,UAAU,CAAC,KAAK,SAAS,KAAK,OAAO;AAC3C,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAEA,IAAM,aAAN,cAAyB,UAAU;AAAA,MACjC,YACE,SACA;AAAA,QACE,WAAW,OAAO;AAAA,QAClB,YAAY,OAAO;AAAA,QACnB,WAAW,OAAO;AAAA,QAClB,iBAAiB,OAAO;AAAA,QACxB,iBAAiB,OAAO;AAAA,QACxB,qBAAqB,OAAO;AAAA,QAC5B,kBAAkB,OAAO;AAAA,QACzB,iBAAiB,OAAO;AAAA,MAC1B,IAAI,CAAC,GACL;AACA,cAAM,OAAO;AACb,aAAK,eAAe,IAAI,YAAY,SAAS;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,eAAO,KAAK,aAAa,SAAS,IAAI;AAAA,MACxC;AAAA,IACF;AAIA,IAAM,eAAN,cAA2B,UAAU;AAAA,MACnC,YAAY,SAAS;AACnB,cAAM,OAAO;AAAA,MACf;AAAA,MACA,WAAW,OAAO;AAChB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MACA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,MAAM;AACX,YAAI,WAAW;AACf,YAAI;AAEJ,cAAM,UAAU,CAAC;AACjB,cAAM,aAAa,KAAK,QAAQ;AAGhC,gBAAQ,QAAQ,KAAK,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI;AAC1D,qBAAW,QAAQ;AACnB,kBAAQ,KAAK,CAAC,OAAO,WAAW,CAAC,CAAC;AAAA,QACpC;AAEA,cAAM,UAAU,CAAC,CAAC,QAAQ;AAE1B,eAAO;AAAA,UACL;AAAA,UACA,OAAO,UAAU,IAAI;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,IAAM,YAAY;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,IAAM,eAAe,UAAU;AAG/B,IAAM,WAAW;AACjB,IAAM,WAAW;AAkDjB,IAAM,gBAAgB,oBAAI,IAAI,CAAC,WAAW,MAAM,aAAa,IAAI,CAAC;AA8BlE,IAAM,iBAAN,MAAqB;AAAA,MACnB,YACE,SACA;AAAA,QACE,kBAAkB,OAAO;AAAA,QACzB,iBAAiB,OAAO;AAAA,QACxB,qBAAqB,OAAO;AAAA,QAC5B,iBAAiB,OAAO;AAAA,QACxB,iBAAiB,OAAO;AAAA,QACxB,WAAW,OAAO;AAAA,QAClB,YAAY,OAAO;AAAA,QACnB,WAAW,OAAO;AAAA,MACpB,IAAI,CAAC,GACL;AACA,aAAK,QAAQ;AACb,aAAK,UAAU;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,aAAK,UAAU,kBAAkB,UAAU,QAAQ,YAAY;AAC/D,aAAK,QAAQ,WAAW,KAAK,SAAS,KAAK,OAAO;AAAA,MACpD;AAAA,MAEA,OAAO,UAAU,GAAG,SAAS;AAC3B,eAAO,QAAQ;AAAA,MACjB;AAAA,MAEA,SAAS,MAAM;AACb,cAAM,QAAQ,KAAK;AAEnB,YAAI,CAAC,OAAO;AACV,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AAEA,cAAM,EAAE,gBAAgB,gBAAgB,IAAI,KAAK;AAEjD,eAAO,kBAAkB,OAAO,KAAK,YAAY;AAEjD,YAAI,aAAa;AACjB,YAAI,aAAa,CAAC;AAClB,YAAI,aAAa;AAGjB,iBAAS,IAAI,GAAG,OAAO,MAAM,QAAQ,IAAI,MAAM,KAAK,GAAG;AACrD,gBAAMC,aAAY,MAAM,CAAC;AAGzB,qBAAW,SAAS;AACpB,uBAAa;AAGb,mBAAS,IAAI,GAAG,OAAOA,WAAU,QAAQ,IAAI,MAAM,KAAK,GAAG;AACzD,kBAAM,WAAWA,WAAU,CAAC;AAC5B,kBAAM,EAAE,SAAS,SAAS,MAAM,IAAI,SAAS,OAAO,IAAI;AAExD,gBAAI,SAAS;AACX,4BAAc;AACd,4BAAc;AACd,kBAAI,gBAAgB;AAClB,sBAAM,OAAO,SAAS,YAAY;AAClC,oBAAI,cAAc,IAAI,IAAI,GAAG;AAC3B,+BAAa,CAAC,GAAG,YAAY,GAAG,OAAO;AAAA,gBACzC,OAAO;AACL,6BAAW,KAAK,OAAO;AAAA,gBACzB;AAAA,cACF;AAAA,YACF,OAAO;AACL,2BAAa;AACb,2BAAa;AACb,yBAAW,SAAS;AACpB;AAAA,YACF;AAAA,UACF;AAGA,cAAI,YAAY;AACd,gBAAI,SAAS;AAAA,cACX,SAAS;AAAA,cACT,OAAO,aAAa;AAAA,YACtB;AAEA,gBAAI,gBAAgB;AAClB,qBAAO,UAAU;AAAA,YACnB;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,IAAM,sBAAsB,CAAC;AAiB7B,IAAM,kBAAkB;AAAA,MACtB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAEA,IAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAEA,IAAM,eAAe,CAAC,UACpB,CAAC,EAAE,MAAM,gBAAgB,GAAG,KAAK,MAAM,gBAAgB,EAAE;AAE3D,IAAM,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,QAAQ,IAAI;AAE9C,IAAM,SAAS,CAAC,UACd,CAAC,QAAQ,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC,aAAa,KAAK;AAE3D,IAAM,oBAAoB,CAAC,WAAW;AAAA,MACpC,CAAC,gBAAgB,GAAG,GAAG,OAAO,KAAK,KAAK,EAAE,IAAI,CAAC,SAAS;AAAA,QACtD,CAAC,GAAG,GAAG,MAAM,GAAG;AAAA,MAClB,EAAE;AAAA,IACJ;AAoJA,IAAM,OAAN,MAAW;AAAA,MACT,YAAY,MAAM,UAAU,CAAC,GAAG,OAAO;AACrC,aAAK,UAAU,EAAE,GAAG,QAAQ,GAAG,QAAQ;AAEvC,YACE,KAAK,QAAQ,qBACb,OACA;AACA,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AAEA,aAAK,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI;AAE/C,aAAK,cAAc,MAAM,KAAK;AAAA,MAChC;AAAA,MAEA,cAAc,MAAM,OAAO;AACzB,aAAK,QAAQ;AAEb,YAAI,SAAS,EAAE,iBAAiB,YAAY;AAC1C,gBAAM,IAAI,MAAM,oBAAoB;AAAA,QACtC;AAEA,aAAK,WACH,SACA,YAAY,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,UACzC,OAAO,KAAK,QAAQ;AAAA,UACpB,iBAAiB,KAAK,QAAQ;AAAA,QAChC,CAAC;AAAA,MACL;AAAA,MAEA,IAAI,KAAK;AACP,YAAI,CAAC,UAAU,GAAG,GAAG;AACnB;AAAA,QACF;AAEA,aAAK,MAAM,KAAK,GAAG;AACnB,aAAK,SAAS,IAAI,GAAG;AAAA,MACvB;AAAA,MAEA,OAAO,YAAY,MAAoB,OAAO;AAC5C,cAAM,UAAU,CAAC;AAEjB,iBAAS,IAAI,GAAG,MAAM,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,GAAG;AACxD,gBAAM,MAAM,KAAK,MAAM,CAAC;AACxB,cAAI,UAAU,KAAK,CAAC,GAAG;AACrB,iBAAK,SAAS,CAAC;AACf,iBAAK;AACL,mBAAO;AAEP,oBAAQ,KAAK,GAAG;AAAA,UAClB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,SAAS,KAAK;AACZ,aAAK,MAAM,OAAO,KAAK,CAAC;AACxB,aAAK,SAAS,SAAS,GAAG;AAAA,MAC5B;AAAA,MAEA,WAAW;AACT,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,OAAO,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG;AACjC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AAET,YAAI,UAAU,SAAS,KAAK,IACxB,SAAS,KAAK,MAAM,CAAC,CAAC,IACpB,KAAK,kBAAkB,KAAK,IAC5B,KAAK,kBAAkB,KAAK,IAC9B,KAAK,eAAe,KAAK;AAE7B,qBAAa,SAAS,EAAE,gBAAgB,CAAC;AAEzC,YAAI,YAAY;AACd,kBAAQ,KAAK,MAAM;AAAA,QACrB;AAEA,YAAI,SAAS,KAAK,KAAK,QAAQ,IAAI;AACjC,oBAAU,QAAQ,MAAM,GAAG,KAAK;AAAA,QAClC;AAEA,eAAO,OAAO,SAAS,KAAK,OAAO;AAAA,UACjC;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,kBAAkB,OAAO;AACvB,cAAM,WAAW,eAAe,OAAO,KAAK,OAAO;AACnD,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,UAAU,CAAC;AAGjB,gBAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,KAAK,GAAGP,MAAK,MAAM;AAChD,cAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,UACF;AAEA,gBAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,cAAI,SAAS;AACX,oBAAQ,KAAK;AAAA,cACX,MAAM;AAAA,cACN;AAAA,cACA,SAAS,CAAC,EAAE,OAAO,OAAO,MAAM,MAAAA,OAAM,QAAQ,CAAC;AAAA,YACjD,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MAEA,eAAe,OAAO;AAEpB,cAAM,aAAa,MAAM,OAAO,KAAK,OAAO;AAE5C,cAAM,WAAW,CAAC,MAAM,MAAM,QAAQ;AACpC,cAAI,CAAC,KAAK,UAAU;AAClB,kBAAM,EAAE,OAAO,SAAS,IAAI;AAE5B,kBAAM,UAAU,KAAK,aAAa;AAAA,cAChC,KAAK,KAAK,UAAU,IAAI,KAAK;AAAA,cAC7B,OAAO,KAAK,SAAS,uBAAuB,MAAM,KAAK;AAAA,cACvD;AAAA,YACF,CAAC;AAED,gBAAI,WAAW,QAAQ,QAAQ;AAC7B,qBAAO;AAAA,gBACL;AAAA,kBACE;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,mBAAO,CAAC;AAAA,UACV;AAEA,gBAAM,MAAM,CAAC;AACb,mBAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK,GAAG;AAC3D,kBAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,kBAAM,SAAS,SAAS,OAAO,MAAM,GAAG;AACxC,gBAAI,OAAO,QAAQ;AACjB,kBAAI,KAAK,GAAG,MAAM;AAAA,YACpB,WAAW,KAAK,aAAa,gBAAgB,KAAK;AAChD,qBAAO,CAAC;AAAA,YACV;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,cAAM,UAAU,KAAK,SAAS;AAC9B,cAAM,YAAY,CAAC;AACnB,cAAM,UAAU,CAAC;AAEjB,gBAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI,MAAM;AACvC,cAAI,UAAU,IAAI,GAAG;AACnB,gBAAI,aAAa,SAAS,YAAY,MAAM,GAAG;AAE/C,gBAAI,WAAW,QAAQ;AAErB,kBAAI,CAAC,UAAU,GAAG,GAAG;AACnB,0BAAU,GAAG,IAAI,EAAE,KAAK,MAAM,SAAS,CAAC,EAAE;AAC1C,wBAAQ,KAAK,UAAU,GAAG,CAAC;AAAA,cAC7B;AACA,yBAAW,QAAQ,CAAC,EAAE,QAAQ,MAAM;AAClC,0BAAU,GAAG,EAAE,QAAQ,KAAK,GAAG,OAAO;AAAA,cACxC,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MAEA,kBAAkB,OAAO;AACvB,cAAM,WAAW,eAAe,OAAO,KAAK,OAAO;AACnD,cAAM,EAAE,MAAM,QAAQ,IAAI,KAAK;AAC/B,cAAM,UAAU,CAAC;AAGjB,gBAAQ,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI,MAAM;AACvC,cAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,UACF;AAEA,cAAI,UAAU,CAAC;AAGf,eAAK,QAAQ,CAAC,KAAK,aAAa;AAC9B,oBAAQ;AAAA,cACN,GAAG,KAAK,aAAa;AAAA,gBACnB;AAAA,gBACA,OAAO,KAAK,QAAQ;AAAA,gBACpB;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAED,cAAI,QAAQ,QAAQ;AAClB,oBAAQ,KAAK;AAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,aAAa,EAAE,KAAK,OAAO,SAAS,GAAG;AACrC,YAAI,CAAC,UAAU,KAAK,GAAG;AACrB,iBAAO,CAAC;AAAA,QACV;AAEA,YAAI,UAAU,CAAC;AAEf,YAAI,QAAQ,KAAK,GAAG;AAClB,gBAAM,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,KAAK,GAAGA,MAAK,MAAM;AAC9C,gBAAI,CAAC,UAAU,IAAI,GAAG;AACpB;AAAA,YACF;AAEA,kBAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,gBAAI,SAAS;AACX,sBAAQ,KAAK;AAAA,gBACX;AAAA,gBACA;AAAA,gBACA,OAAO;AAAA,gBACP;AAAA,gBACA,MAAAA;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,EAAE,GAAG,MAAM,GAAGA,MAAK,IAAI;AAE7B,gBAAM,EAAE,SAAS,OAAO,QAAQ,IAAI,SAAS,SAAS,IAAI;AAE1D,cAAI,SAAS;AACX,oBAAQ,KAAK,EAAE,OAAO,KAAK,OAAO,MAAM,MAAAA,OAAM,QAAQ,CAAC;AAAA,UACzD;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,SAAS;AAEd;AACE,WAAK,aAAa;AAAA,IACpB;AAEA;AACE,eAAS,cAAc;AAAA,IACzB;AAAA;AAAA;;;ACjvDA;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB,QAAQ,aAAa,QAAQ,WAAW;AACjE,QAAIQ,SAAQ,aAAa,eAAgB;AACzC,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,YAAY,gBAAgB,iDAAkB;AAClD,YAAQ,WAAW;AAAA,MACf,MAAM;AAAA,MACN,UAAU,QAAQ,SAAS;AAAA,IAC/B;AACA,QAAI,cAAc;AAAA,MACd,MAAM;AAAA,QACF;AAAA,UACI,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,OAAO,SAAU,MAAM;AAAE,gBAAI;AAAI,qBAAS,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,IAAI,MAAM,GAAG;AAAA,UAAG;AAAA,UAC/G,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,oBAAoB;AAAA,IACxB;AACA,aAAS,MAAM,GAAG,GAAG;AAIjB,aAAO,EAAE,WAAW,EAAE;AAAA,IAC1B;AAIA,aAAS,aAAa;AAClB,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,QAAQ,MAAM;AAAA,UACd,SAAS,MAAM;AAAA,UACf,cAAc,MAAM;AAAA,QACxB;AAAA,MAAI,CAAC,GAAGC,UAAS,GAAG,QAAQ,UAAU,GAAG,SAAS,eAAe,GAAG;AACpE,UAAI,cAAcD,OAAM,QAAQ,WAAY;AACxC,eAAO,OAAO,KAAK,OAAO,EACrB,OAAO,SAAU,KAAK,UAAU;AACjC,cAAI,SAAS,QAAQ,QAAQ;AAC7B,cAAI,CAAC,OAAO,UAAU,CAAC,cAAc;AACjC,gBAAI,KAAK,MAAM;AAAA,UACnB;AACA,cAAI,OAAO,OAAO,cAAc;AAC5B,qBAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAK;AAC7C,kBAAI,KAAK,OAAO,SAAS,CAAC,CAAC;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC,EACA,KAAK,KAAK;AAAA,MACnB,GAAG,CAAC,SAAS,YAAY,CAAC;AAC1B,UAAI,iBAAiBA,OAAM,YAAY,SAAUE,UAAS;AACtD,YAAI,eAAe,CAAC;AACpB,iBAAS,IAAI,GAAG,IAAIA,SAAQ,QAAQ,KAAK;AACrC,uBAAa,KAAKA,SAAQ,CAAC,CAAC;AAAA,QAChC;AACA,eAAQ,SAAS,gBAAgBA,UAAS,KAAK;AAC3C,cAAI,QAAQ,QAAQ;AAAE,kBAAM;AAAA,UAAc;AAC1C,mBAASC,KAAI,GAAGA,KAAID,SAAQ,QAAQC,MAAK;AACrC,gBAAID,SAAQC,EAAC,EAAE,SAAS,SAAS,GAAG;AAChC,kBAAI,iBAAiBD,SAAQC,EAAC,EAAE;AAChC,uBAAS,MAAM,GAAG,MAAM,eAAe,QAAQ,OAAO;AAClD,oBAAI,KAAK,eAAe,GAAG,CAAC;AAAA,cAChC;AACA,8BAAgBD,SAAQC,EAAC,EAAE,UAAU,GAAG;AAAA,YAC5C;AAAA,UACJ;AACA,iBAAO;AAAA,QACX,EAAGD,QAAO;AAAA,MACd,GAAG,CAAC,CAAC;AACL,UAAI,cAAc,CAACD;AACnB,UAAI,WAAWD,OAAM,QAAQ,WAAY;AACrC,YAAI;AACA,iBAAO;AACX,eAAO,eAAe,WAAW;AAAA,MACrC,GAAG,CAAC,gBAAgB,aAAa,WAAW,CAAC;AAC7C,UAAI,OAAOA,OAAM,QAAQ,WAAY;AAAE,eAAO,IAAI,UAAU,QAAQ,UAAU,WAAW;AAAA,MAAG,GAAG,CAAC,QAAQ,CAAC;AACzG,UAAI,UAAU,mBAAmB,UAAUC,SAAQ,IAAI;AACvD,UAAI,UAAUD,OAAM,QAAQ,WAAY;AACpC,YAAII,KAAI;AAOR,YAAI,MAAM,CAAC;AAKX,YAAI,OAAO,CAAC;AAMZ,YAAI,UAAU,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI,SAAS,MAAM;AACnB,cAAI,QAAQ,MAAM,SAAS,QAAQ,SAAS;AAC5C,cAAI,UAAU;AAAA,YACV,MAAM,OAAO,OAAO,YAAY,WAC1B,OAAO,YACLA,MAAK,OAAO,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,QAAQ,SAAS;AAAA,YAC/F,UAAU,OAAO,OAAO,YAAY,WAC9B,UACE,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,IAAI;AAAA,UAC1F;AACA,cAAI,CAAC,IAAI,QAAQ,IAAI,GAAG;AACpB,gBAAI,QAAQ,IAAI,IAAI,CAAC;AACrB,iBAAK,KAAK,OAAO;AAAA,UACrB;AACA,cAAI,QAAQ,IAAI,EAAE,KAAK;AAAA,YACnB,UAAU,OAAO,WAAW;AAAA,YAC5B;AAAA,UACJ,CAAC;AAAA,QACL;AACA,kBAAU,KAAK,KAAK,KAAK,EAAE,IAAI,SAAUC,QAAO;AAAE,iBAAQ;AAAA,YACtD,MAAMA,OAAM;AAAA,YACZ,SAAS,IAAIA,OAAM,IAAI,EAAE,KAAK,KAAK,EAAE,IAAI,SAAU,MAAM;AAAE,qBAAO,KAAK;AAAA,YAAQ,CAAC;AAAA,UACpF;AAAA,QAAI,CAAC;AAKL,YAAIC,WAAU,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI,MAAM,SAAS,QAAQ,SAAS;AAChC,YAAAA,SAAQ,KAAK,MAAM,IAAI;AAC3B,mBAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,QAAQ,OAAO;AACjD,YAAAA,SAAQ,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,UACnC;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,GAAG,CAAC,OAAO,CAAC;AAIZ,UAAI,mBAAmBN,OAAM,QAAQ,WAAY;AAAE,eAAO;AAAA,MAAc,GAAG,CAAC,OAAO,CAAC;AACpF,aAAOA,OAAM,QAAQ,WAAY;AAAE,eAAQ;AAAA,UACvC;AAAA,UACA,cAAc;AAAA,QAClB;AAAA,MAAI,GAAG,CAAC,kBAAkB,OAAO,CAAC;AAAA,IACtC;AACA,YAAQ,aAAa;AACrB,aAAS,mBAAmB,UAAUC,SAAQ,MAAM;AAChD,UAAI,QAAQD,OAAM,QAAQ,WAAY;AAAE,eAAQ;AAAA,UAC5C;AAAA,UACA,QAAQC;AAAA,QACZ;AAAA,MAAI,GAAG,CAAC,UAAUA,OAAM,CAAC;AACzB,UAAI,MAAM,GAAG,QAAQ,mBAAmB,KAAK,GAAG,oBAAoB,GAAG,UAAU,kBAAkB,GAAG;AACtG,aAAOD,OAAM,QAAQ,WAAY;AAC7B,YAAI,gBAAgB,KAAK,MAAM,IAAI;AAC/B,iBAAO,kBAAkB,IAAI,SAAU,QAAQ;AAAE,mBAAQ,EAAE,OAAO,GAAG,OAAe;AAAA,UAAI,CAAC;AAAA,QAC7F;AACA,YAAI,UAAU,CAAC;AAEf,YAAI,gBAAgB,KAAK,OAAO,eAAe;AAE/C,kBAAU,cAAc,IAAI,SAAUI,KAAI;AACtC,cAAI,SAASA,IAAG,MAAM,QAAQA,IAAG;AACjC,iBAAQ;AAAA,YACJ,OAAO,MAAM,UAAU,QAAQ,UAAU,SAAS,QAAQ,KAAK;AAAA,YAC/D;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX,GAAG,CAAC,mBAAmB,iBAAiB,IAAI,CAAC;AAAA,IACjD;AAIA,YAAQ,iBAAiB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/MzB,QAAA,gBAAA,CAAA;AAAA,IAAAG,UAAA,eAAA;MAAA,aAAA,MAAA;MAAA,iBAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAAC,cAAA,aAAA;ACAA,QAAAC,SAAuBC,SAAA,eAAA;AAQvB,aAAS,OAAU,KAAqB,OAAU;AAChD,UAAI,OAAO,QAAQ,YAAY;AAC7B,eAAO,IAAI,KAAK;MAClB,WAAW,QAAQ,QAAQ,QAAQ,QAAW;AAC5C,YAAI,UAAU;MAChB;IACF;AAMA,aAAS,eAAkB,MAA8C;AACvE,aAAO,CAAC,SAAS;AACf,YAAI,aAAa;AACjB,cAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACjC,gBAAM,UAAU,OAAO,KAAK,IAAI;AAChC,cAAI,CAAC,cAAc,OAAO,WAAW,YAAY;AAC/C,yBAAa;UACf;AACA,iBAAO;QACT,CAAC;AAMD,YAAI,YAAY;AACd,iBAAO,MAAM;AACX,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAM,UAAU,SAAS,CAAC;AAC1B,kBAAI,OAAO,WAAW,YAAY;AAChC,wBAAQ;cACV,OAAO;AACL,uBAAO,KAAK,CAAC,GAAG,IAAI;cACtB;YACF;UACF;QACF;MACF;IACF;AAMA,aAAS,mBAAsB,MAA8C;AAE3E,aAAaD,OAAA,YAAY,YAAY,GAAG,IAAI,GAAG,IAAI;IACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA,QAAA,gBAAA,CAAA;AAAA,IAAAE,UAAA,eAAA;MAAA,MAAA,MAAA;MAAA,MAAA,MAAA;MAAA,WAAA,MAAA;MAAA,YAAA,MAAA;MAAA,iBAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAAC,cAAA,aAAA;ACAA,QAAAC,SAAuBC,SAAA,eAAA;AACvB,QAAA,4BAA4B;AAmCpB,QAAA,qBAAA;AAzB0B,aAAS,WAAW,WAAmB;AACvE,YAAM,YAAY,gBAAgB,SAAS;AAC3C,YAAMC,QAAaF,OAAA,WAAmC,CAACG,QAAO,iBAAiB;AAC7E,cAAM,EAAE,UAAU,GAAG,UAAU,IAAIA;AACnC,cAAM,gBAAsBH,OAAA,SAAS,QAAQ,QAAQ;AACrD,cAAM,YAAY,cAAc,KAAK,WAAW;AAEhD,YAAI,WAAW;AAEb,gBAAM,aAAa,UAAU,MAAM;AAEnC,gBAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,gBAAI,UAAU,WAAW;AAGvB,kBAAUA,OAAA,SAAS,MAAM,UAAU,IAAI,EAAG,QAAaA,OAAA,SAAS,KAAK,IAAI;AACzE,qBAAaA,OAAA,eAAe,UAAU,IACjC,WAAW,MAAwC,WACpD;YACN,OAAO;AACL,qBAAO;YACT;UACF,CAAC;AAED,kBACE,GAAA,mBAAA,KAAC,WAAA,EAAW,GAAG,WAAW,KAAK,cAC5B,UAAMA,OAAA,eAAe,UAAU,IACtBA,OAAA,aAAa,YAAY,QAAW,WAAW,IACrD,KAAA,CACN;QAEJ;AAEA,gBACE,GAAA,mBAAA,KAAC,WAAA,EAAW,GAAG,WAAW,KAAK,cAC5B,SAAA,CACH;MAEJ,CAAC;AAEDE,YAAK,cAAc,GAAG,SAAS;AAC/B,aAAOA;IACT;AAEA,QAAM,OAAO,WAAW,MAAM;AAUH,aAAS,gBAAgB,WAAmB;AACrE,YAAM,YAAkBF,OAAA,WAAgC,CAACG,QAAO,iBAAiB;AAC/E,cAAM,EAAE,UAAU,GAAG,UAAU,IAAIA;AAEnC,YAAUH,OAAA,eAAe,QAAQ,GAAG;AAClC,gBAAM,cAAc,cAAc,QAAQ;AAC1C,gBAAMG,UAAQ,WAAW,WAAW,SAAS,KAAiB;AAE9D,cAAI,SAAS,SAAeH,OAAA,UAAU;AACpCG,YAAAA,QAAM,MAAM,gBAAA,GAAe,0BAAA,aAAY,cAAc,WAAW,IAAI;UACtE;AACA,iBAAaH,OAAA,aAAa,UAAUG,OAAK;QAC3C;AAEA,eAAaH,OAAA,SAAS,MAAM,QAAQ,IAAI,IAAUA,OAAA,SAAS,KAAK,IAAI,IAAI;MAC1E,CAAC;AAED,gBAAU,cAAc,GAAG,SAAS;AACpC,aAAO;IACT;AAMA,QAAM,uBAAuB,OAAO,iBAAiB;AAUnB,aAAS,gBAAgB,WAAmB;AAC5E,YAAMI,aAAgC,CAAC,EAAE,SAAS,MAAM;AACtD,gBAAO,GAAA,mBAAA,KAAA,mBAAA,UAAA,EAAG,SAAA,CAAS;MACrB;AACAA,iBAAU,cAAc,GAAG,SAAS;AACpCA,iBAAU,YAAY;AACtB,aAAOA;IACT;AAEA,QAAM,YAAY,gBAAgB,WAAW;AAM7C,aAAS,YACP,OAC+D;AAC/D,aACQJ,OAAA,eAAe,KAAK,KAC1B,OAAO,MAAM,SAAS,cACtB,eAAe,MAAM,QACrB,MAAM,KAAK,cAAc;IAE7B;AAEA,aAAS,WAAW,WAAqB,YAAsB;AAE7D,YAAM,gBAAgB,EAAE,GAAG,WAAW;AAEtC,iBAAW,YAAY,YAAY;AACjC,cAAM,gBAAgB,UAAU,QAAQ;AACxC,cAAM,iBAAiB,WAAW,QAAQ;AAE1C,cAAM,YAAY,WAAW,KAAK,QAAQ;AAC1C,YAAI,WAAW;AAEb,cAAI,iBAAiB,gBAAgB;AACnC,0BAAc,QAAQ,IAAI,IAAI,SAAoB;AAChD,oBAAM,SAAS,eAAe,GAAG,IAAI;AACrC,4BAAc,GAAG,IAAI;AACrB,qBAAO;YACT;UACF,WAES,eAAe;AACtB,0BAAc,QAAQ,IAAI;UAC5B;QACF,WAES,aAAa,SAAS;AAC7B,wBAAc,QAAQ,IAAI,EAAE,GAAG,eAAe,GAAG,eAAe;QAClE,WAAW,aAAa,aAAa;AACnC,wBAAc,QAAQ,IAAI,CAAC,eAAe,cAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;QACpF;MACF;AAEA,aAAO,EAAE,GAAG,WAAW,GAAG,cAAc;IAC1C;AAOA,aAAS,cAAc,SAA6B;AAElD,UAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,KAAK,GAAG;AACpE,UAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,UAAI,SAAS;AACX,eAAQ,QAAgB;MAC1B;AAGA,eAAS,OAAO,yBAAyB,SAAS,KAAK,GAAG;AAC1D,gBAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,UAAI,SAAS;AACX,eAAQ,QAAQ,MAAuC;MACzD;AAGA,aAAQ,QAAQ,MAAuC,OAAQ,QAAgB;IACjF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtLA,QAAA,gBAAA,CAAA;AAAA,IAAAK,UAAA,eAAA;MAAA,WAAA,MAAA;MAAA,MAAA,MAAA;MAAA,6BAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAAC,cAAA,aAAA;ACAA,QAAAC,SAAuBC,SAAA,eAAA;AACvB,QAAA,WAA0BA,SAAA,mBAAA;AAC1B,QAAA,oBAA2B;AA4ChB,QAAA,qBAAA;AA1CX,QAAM,QAAQ;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;AAcA,QAAM,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;AAClD,YAAM,QAAA,GAAO,kBAAA,YAAW,aAAa,IAAI,EAAE;AAC3C,YAAM,OAAaD,OAAA,WAAW,CAACE,QAA2C,iBAAsB;AAC9F,cAAM,EAAE,SAAS,GAAG,eAAe,IAAIA;AACvC,cAAM,OAAY,UAAU,OAAO;AAEnC,YAAI,OAAO,WAAW,aAAa;AAChC,iBAAe,OAAO,IAAI,UAAU,CAAC,IAAI;QAC5C;AAEA,gBAAO,GAAA,mBAAA,KAAC,MAAA,EAAM,GAAG,gBAAgB,KAAK,aAAA,CAAc;MACtD,CAAC;AAED,WAAK,cAAc,aAAa,IAAI;AAEpC,aAAO,EAAE,GAAG,WAAW,CAAC,IAAI,GAAG,KAAK;IACtC,GAAG,CAAC,CAAe;AA2CnB,aAAS,4BAAmD,QAAqB,OAAU;AACzF,UAAI,OAAiB,UAAA,UAAU,MAAM,OAAO,cAAc,KAAK,CAAC;IAClE;AAIA,QAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrGb,QAAA,gBAAA,CAAA;AAAA,IAAAC,UAAA,eAAA;MAAA,iBAAA,MAAAC;IAAA,CAAA;AAAA,WAAA,UAAAC,cAAA,aAAA;ACAA,QAAAC,SAAuBC,SAAA,eAAA;AASvB,QAAMH,mBAAkB,YAAY,WAAiBE,OAAA,kBAAkB,MAAM;IAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACT9E,QAAA,gBAAA,CAAA;AAAA,IAAAE,UAAA,eAAA;MAAA,QAAA,MAAA;MAAA,MAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAAC,cAAA,aAAA;ACAA,QAAAC,SAAuBC,SAAA,eAAA;AACvB,QAAA,mBAAqBA,SAAA,mBAAA;AACrB,QAAA,yBAA0B;AAC1B,QAAA,iCAAgC;AAuBJ,QAAA,qBAAA;AAjB5B,QAAM,cAAc;AAWpB,QAAM,SAAeD,OAAA,WAAuC,CAACE,QAAO,iBAAiB;AACnF,YAAM,EAAE,WAAW,eAAe,GAAG,YAAY,IAAIA;AACrD,YAAM,CAAC,SAAS,UAAU,IAAUF,OAAA,SAAS,KAAK;AAClD,OAAA,GAAA,+BAAA,iBAAgB,MAAM,WAAW,IAAI,GAAG,CAAC,CAAC;AAC1C,YAAM,YAAY,iBAAkB,WAAW,YAAY,UAAU;AACrE,aAAO,YACH,iBAAAG,QAAS,cAAa,GAAA,mBAAA,KAAC,uBAAA,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc,GAAI,SAAS,IACtF;IACN,CAAC;AAED,WAAO,cAAc;AAIrB,QAAM,OAAO;;;;;AClCb;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,QAAI,iBAAiB;AACrB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,aAAS,WAAW,IAAI;AACpB,UAAI,WAAW,GAAG,UAAU,YAAY,GAAG;AAC3C,UAAI,WAAW,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UAC5D,SAAS,MAAM,gBAAgB,QAAQ,YAAY;AAAA,QACvD;AAAA,MAAI,CAAC,EAAE;AACP,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAOA,OAAM,cAAc,eAAe,QAAQ,EAAE,UAAqB,GAAG,QAAQ;AAAA,IACxF;AACA,YAAQ,aAAa;AAAA;AAAA;;;ACpCrB;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,SAAU,WAAQ,QAAK,UAAW,SAAU,GAAG,GAAG;AAClD,UAAI,IAAI,CAAC;AACT,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,UAAE,CAAC,IAAI,EAAE,CAAC;AACd,UAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,iBAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,cAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,cAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,QACxB;AACJ,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,eAAe;AAAA,MACf,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,IACb;AACA,aAAS,SAAS,OAAO;AACrB,aAAO,QAAQ,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG,KAAK,IAAI;AAAA,IACjE;AACA,YAAQ,iBAAiBA,OAAM,WAAW,SAAU,IAAI,KAAK;AACzD,UAAI,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAUC,SAAQ,OAAO,IAAI,CAAC,SAAS,UAAU,CAAC;AACtF,aAAQD,OAAM,cAAc,OAAO,SAAS,EAAE,KAAU,OAAO,SAAS,KAAK,EAAE,GAAGC,MAAK,GAAG,QAAQ;AAAA,IACtG,CAAC;AAAA;AAAA;;;AC5DD;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,SAAU,WAAQ,QAAK,UAAW,SAAU,GAAG,GAAG;AAClD,UAAI,IAAI,CAAC;AACT,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,UAAE,CAAC,IAAI,EAAE,CAAC;AACd,UAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,iBAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,cAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,cAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,QACxB;AACJ,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa,QAAQ,mBAAmB,QAAQ,eAAe;AACvE,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,YAAQ,eAAe;AACvB,QAAI,mBAAmB,SAAU,IAAI;AAAE,aAAO,uBAAuB;AAAA,IAAI;AACzE,YAAQ,mBAAmB;AAC3B,aAAS,WAAWC,QAAO;AACvB,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,QAAQ,MAAM;AAAA,UACd,qBAAqB,MAAM;AAAA,UAC3B,SAAS,MAAM;AAAA,UACf,aAAa,MAAM;AAAA,UACnB,SAAS,MAAM,gBAAgB,QAAQ,YAAY;AAAA,QACvD;AAAA,MAAI,CAAC,GAAG,QAAQ,GAAG,OAAOC,UAAS,GAAG,QAAQ,UAAU,GAAG,SAAS,sBAAsB,GAAG,qBAAqB,cAAc,GAAG,aAAa,UAAU,GAAG,SAAS,UAAU,GAAG;AACnL,UAAI,KAAKF,OAAM,SAASE,OAAM,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACzE,MAAAF,OAAM,UAAU,WAAY;AACxB,cAAM,UAAU,UAAU;AAAA,MAC9B,GAAG,CAAC,YAAY,KAAK,CAAC;AACtB,UAAI,qBAAqBC,OAAM,oBAAoB,OAAO,OAAOA,QAAO,CAAC,oBAAoB,CAAC;AAC9F,MAAAD,OAAM,UAAU,WAAY;AACxB,cAAM,UAAU,EAAE;AAClB,cAAM,SAAS,EAAE,MAAM;AACvB,eAAO,WAAY;AAAE,iBAAO,MAAM,UAAU,EAAE;AAAA,QAAG;AAAA,MACrD,GAAG,CAAC,qBAAqB,KAAK,CAAC;AAC/B,UAAI,cAAcA,OAAM,QAAQ,WAAY;AACxC,YAAI,cAAc,uBAAuB,QAAQ,uBAAuB,SAAS,qBAAqB;AACtG,eAAO,uBAAuB,QAAQ,mBAAmB,IACnD,QAAQ,mBAAmB,EAAE,OAC7B;AAAA,MACV,GAAG,CAAC,SAAS,qBAAqB,kBAAkB,CAAC;AACrD,aAAQA,OAAM,cAAc,SAAS,SAAS,CAAC,GAAG,MAAM,EAAE,KAAK,MAAM,gBAAgB,WAAW,MAAM,cAAc,OAAO,MAAM,YAAY,YAAY,SAAS,iBAAiB,SAAS,iBAAiB,QAAQ,cAAc,0BAA0B,GAAG,QAAQ,kBAAkB,WAAW,GAAG,OAAO,YAAY,aAA0B,UAAU,SAAU,OAAO;AACxW,YAAIG,KAAIC,KAAI;AACZ,SAACD,MAAKF,OAAM,cAAc,QAAQE,QAAO,SAAS,SAASA,IAAG,KAAKF,QAAO,KAAK;AAC/E,sBAAc,MAAM,OAAO,KAAK;AAChC,SAAC,MAAMG,MAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKA,KAAI,MAAM,OAAO,KAAK;AAAA,MAC/M,GAAG,WAAW,SAAU,OAAO;AAC3B,YAAID;AACJ,SAACA,MAAKF,OAAM,eAAe,QAAQE,QAAO,SAAS,SAASA,IAAG,KAAKF,QAAO,KAAK;AAChF,YAAI,uBAAuB,CAACC,WAAU,MAAM,QAAQ,aAAa;AAC7D,cAAI,WAAW,QAAQ,mBAAmB,EAAE;AAC5C,gBAAM,qBAAqB,QAAQ;AAAA,QACvC;AAAA,MACJ,EAAE,CAAC,CAAC;AAAA,IACZ;AACA,YAAQ,aAAa;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SCxDGG,YACtBC,MACAC,IAAAA;AAEA,SAAO;IACLC,SAAAA,SAAAA,UAAAA;AACE,UAAIC,WAAWC,cAAAA,SAAf;AACA,UAAIA,cAAAA,IAAJ,IAAIA,GAAyB;AAC3BA,sBAAAA,IAAAA,IAAAA,EAAAA,UAAAA,KAAAA,EAAAA;MADF,OAEO;AACLA,sBAAAA,IAAAA,MAAwB;UACtBC,MADsB;UAEtBC,gBAFsB;UAGtBC,WAAW,CAAA,EAAA;QAHW,CAAxBH;MAKD;AACD,UAAA,SAAcI,KAAG;IAZd;IAeLC,WAAS,SAAA,YAAA;AACP,UAAIC,QAAQN,cAAAA,IAAZ,IAAYA;AACZ,UAAA,OAAW;AAET,YAAMO,QAAQD,MAAAA,UAAAA,QAAd,EAAcA;AACd,YAAIC,SAAJ,EAAgBD,OAAAA,UAAAA,OAAAA,OAHP,CAGOA;AAGhB,YAAI,CAACA,MAAAA,UAAL,OAA6BN,eAAAA,QAAa,EANjC,IAMoBA;AAG7B,YAAI,CAACA,cAAL,KAAyBQ,sBAAAA,KAAoB;MAC9C;IACF;EA5BI;AA8BR;AE9Dc,SAASC,QACtBC,SACAC,aACA;AAAA,MADAA,gBACA,QAAA;AADAA,kBAAc;MAAEC,OAAO;MAAGC,QAAQ;IAApB;EACd;AAAA,MAAA,kBAC8BC,aAAAA,QAAMC,SAASL,QAAQM,OAAvB,GAAvBC,UADP,gBAAA,CAAA,GACgBC,aADhB,gBAAA,CAAA;AAAA,MAAA,oBAEyBJ,aAAAA,QAAMK,WAAWC,aAAaT,WAA9B,GAAlBV,OAFP,kBAAA,CAAA,GAEaoB,WAFb,kBAAA,CAAA;AAGA,MAAMC,iBAAiBR,aAAAA,QAAMS,OAAO,KAAb;AAEvBC,4BAA0B,WAAM;AAC9B,QAAId,QAAQM,YAAYC,SAAS;AAC/BC,iBAAWR,QAAQM,OAAT;IACX;EACF,CAJwB;AAMzBQ,4BAA0B,WAAM;AAC9B,QAAIP,WAAW,CAACK,eAAeN,SAAS;AACtCM,qBAAeN,UAAU;AACzB,UAAMf,QAAOgB,QAAQQ,sBAAR;AACbJ,eAAS;QAAEpB,MAAAA;MAAF,CAAD;IACT;EACF,GAAE,CAACgB,OAAD,CANsB;AAQzBH,eAAAA,QAAMY,UAAU,WAAM;AACpB,QAAI,CAACT,SAAS;AACZ;IACD;AAED,QAAMU,WAAWhC,YAAYsB,SAAS,SAAAhB,OAAQ;AAC5CoB,eAAS;QAAEpB,MAAAA;MAAF,CAAD;IACT,CAF2B;AAI5B0B,aAAS7B,QAAT;AAEA,WAAO,WAAM;AACX6B,eAAStB,UAAT;IACD;EACF,GAAE,CAACY,OAAD,CAdH;AAgBA,SAAOhB;AACR;AAED,SAASmB,YAAYd,OAAOsB,QAAQ;AAClC,MAAM3B,OAAO2B,OAAO3B;AACpB,MAAIK,MAAMO,WAAWZ,KAAKY,UAAUP,MAAMM,UAAUX,KAAKW,OAAO;AAC9D,WAAOX;EACR;AACD,SAAOK;AACR;ACxBM,SAASuB,WAAT,MAgBJ;AAAA,MAAA;AAAA,MAAA,YAAA,KAfDC,MAAAA,OAeC,cAAA,SAfM,IAeN,WAAA,oBAAA,KAdDC,cAAAA,eAcC,sBAAA,SAdcC,sBAcd,mBAAA,gBAAA,KAbDC,UAAAA,WAaC,kBAAA,SAbU,IAaV,eAAA,oBAAA,KAZDC,cAAAA,eAYC,sBAAA,SAZc,IAYd,mBAAA,kBAAA,KAXDC,YAAAA,aAWC,oBAAA,SAXY,IAWZ,iBAVDC,YAUC,KAVDA,WACAC,aASC,KATDA,YACAC,aAQC,KARDA,YACAC,cAOC,KAPDA,aACA5B,cAMC,KANDA,aACA6B,kBAKC,KALDA,iBACAC,iBAIC,KAJDA,gBAIC,oBAAA,KAHDC,cAAAA,eAGC,sBAAA,SAHcC,sBAGd,mBAAA,mBAAA,KAFDC,aAAAA,cAEC,qBAAA,SAFaC,qBAEb,kBAAA,sBAAA,KADDC,gBAAAA,iBACC,wBAAA,SADgBC,wBAChB;AACD,MAAMC,UAAUX,aAAa,UAAU;AACvC,MAAMY,YAAYZ,aAAa,eAAe;AAE9C,MAAMa,YAAYpC,aAAAA,QAAMS,OAAO;IAC7B4B,cAAc;IACdC,cAAc,CAAA;EAFe,CAAb;AAJjB,MAAA,kBASuCtC,aAAAA,QAAMC,SAAS,CAAf,GAAjCoC,eATN,gBAAA,CAAA,GASoBE,kBATpB,gBAAA,CAAA;AAUDH,YAAUlC,QAAQmC,eAAeA;AAEjC,MAAMG,mBAAmBf,eAAe9B;AAZvC,MAAA,oBAcgC6C,iBAAiBlB,WAAWzB,WAAZ,GAA9B4C,YAdlB,kBAcQP,OAdR;AAgBDE,YAAUlC,QAAQuC,YAAYA;AAE9B,MAAMC,oBAAoB1C,aAAAA,QAAM2C,YAC9B,SAAAC,QAAU;AACR,QAAItB,UAAUpB,SAAS;AACrBoB,gBAAUpB,QAAQiC,SAAlB,IAA+BS;IAChC;EACF,GACD,CAACtB,WAAWa,SAAZ,CANwB;AAS1B,MAAMU,qBAAqBrB,cAAckB;AAEzClB,eAAaxB,aAAAA,QAAM2C,YACjB,SAAAC,QAAU;AACRC,uBAAmBD,QAAQF,iBAAT;EACnB,GACD,CAACA,mBAAmBG,kBAApB,CAJW;AA7BZ,MAAA,mBAoCyC7C,aAAAA,QAAMC,SAAS,CAAA,CAAf,GAAnC6C,gBApCN,iBAAA,CAAA,GAoCqBC,mBApCrB,iBAAA,CAAA;AAsCD,MAAMC,UAAUhD,aAAAA,QAAM2C,YAAY,WAAA;AAAA,WAAMI,iBAAiB,CAAA,CAAD;EAAtB,GAA4B,CAAA,CAA9C;AAEhB,MAAME,iCAAiCjD,aAAAA,QAAMS,OAAO,CAAA,CAAb;AAEvC,MAAM6B,eAAetC,aAAAA,QAAMkD,QAAQ,WAAM;AACvC,QAAMC,MACJF,+BAA+B/C,QAAQkD,SAAS,IAC5CC,KAAKF,IAAL,MAAAE,MAAYJ,+BAA+B/C,OAAvC,IACJ;AACN+C,mCAA+B/C,UAAU,CAAA;AAEzC,QAAMoC,gBAAeF,UAAUlC,QAAQoC,aAAagB,MAAM,GAAGH,GAAxC;AAErB,aAASI,IAAIJ,KAAKI,IAAIvC,MAAMuC,KAAK;AAC/B,UAAMC,MAAM5B,aAAa2B,CAAD;AACxB,UAAME,eAAeX,cAAcU,GAAD;AAClC,UAAME,SAAQpB,cAAaiB,IAAI,CAAL,IAAUjB,cAAaiB,IAAI,CAAL,EAAQI,MAAMvC;AAC9D,UAAMJ,QACJ,OAAOyC,iBAAiB,WAAWA,eAAexC,aAAasC,CAAD;AAChE,UAAMI,OAAMD,SAAQ1C;AACpBsB,MAAAA,cAAaiB,CAAD,IAAM;QAAE9D,OAAO8D;QAAGG,OAAAA;QAAO1C,MAAAA;QAAM2C,KAAAA;QAAKH;MAA9B;IACnB;AACD,WAAOlB;EACR,GAAE,CAACrB,cAAc6B,eAAe1B,cAAcJ,MAAMY,YAAlD,CAnBkB;AAqBrB,MAAMgC,eAAa,gBAAAtB,aAAatB,OAAO,CAAR,MAAZ,OAAA,SAAA,cAAwB2C,QAAOvC,gBAAgBC;AAElEe,YAAUlC,QAAQoC,eAAeA;AACjCF,YAAUlC,QAAQ0D,YAAYA;AAE9B,MAAMzD,UAAUuB,kBAAkBA,gBAAgBxB,UAAUoB,UAAUpB;AAEtE,MAAM2D,oBAAoB7D,aAAAA,QAAMS,OAAOkB,cAAb;AAC1BkC,oBAAkB3D,UAAUyB;AAE5BjB,4BAA0B,WAAM;AAC9B,QAAI,CAACP,SAAS;AACZoC,sBAAgB,CAAD;AAEf;IACD;AAED,QAAMuB,WAAW,SAAXA,UAAWC,OAAS;AACxB,UAAMnB,SAASiB,kBAAkB3D,UAC7B2D,kBAAkB3D,QAAQ6D,KAA1B,IACA5D,QAAQgC,SAAD;AAEXI,sBAAgBK,MAAD;IAChB;AAEDkB,aAAQ;AAER3D,YAAQ6D,iBAAiB,UAAUF,UAAU;MAC3CG,SAAS;MACTC,SAAS;IAFkC,CAA7C;AAKA,WAAO,WAAM;AACX/D,cAAQgE,oBAAoB,UAAUL,QAAtC;IACD;EACF,GAAE,CAAC3D,SAASgC,SAAV,CAzBsB;AAzExB,MAAA,kBAoGsBiC,eAAehC,UAAUlC,OAAX,GAA7BwD,QApGP,gBAoGOA,OAAOC,MApGd,gBAoGcA;AAEf,MAAMU,UAAUrE,aAAAA,QAAMkD,QACpB,WAAA;AAAA,WACElB,eAAe;MACb0B;MACAC;MACAxC;MACAH,MAAMsB,aAAac;IAJN,CAAD;EADhB,GAOA,CAACM,OAAOC,KAAKxC,UAAUmB,aAAac,QAAQpB,cAA5C,CARc;AAWhB,MAAMsC,iBAAiBtE,aAAAA,QAAMS,OAAOqB,WAAb;AACvBwC,iBAAepE,UAAU4B;AAEzB,MAAMyC,eAAevE,aAAAA,QAAMkD,QAAQ,WAAM;AACvC,QAAMqB,gBAAe,CAAA;AADkB,QAAA,QAAA,SAAAC,OAG9BC,IAAOC,MAHuB;AAIrC,UAAMnB,IAAIc,QAAQI,EAAD;AACjB,UAAME,cAAcrC,aAAaiB,CAAD;AAEhC,UAAMqB,OAAI,SAAA,SAAA,CAAA,GACLD,WADK,GAAA,CAAA,GAAA;QAERE,YAAY,SAAA,WAAAC,IAAM;AAChB,cAAIA,IAAI;AACN,gBAAMrB,eAAea,eAAepE,QAAQ4E,IAAIvD,UAA3B;AAErB,gBAAIkC,iBAAiBmB,KAAK5D,MAAM;AAAA,kBACtBqB,gBAAiBD,UAAUlC,QAA3BmC;AAER,kBAAIuC,KAAKlB,QAAQrB,eAAc;AAC7BK,kCAAkBL,iBAAgBoB,eAAemB,KAAK5D,KAArC;cAClB;AAEDiC,6CAA+B/C,QAAQ6E,KAAKxB,CAA5C;AAEAR,+BAAiB,SAAAiC,KAAG;AAAA,oBAAA;AAAA,uBAAA,SAAA,SAAA,CAAA,GACfA,GADe,GAAA,CAAA,IAAA,YAAA,CAAA,GAAA,UAEjBJ,KAAKpB,GAFY,IAENC,cAFM,UAAA;cAAA,CAAJ;YAIjB;UACF;QACF;MArBO,CAAA;AAwBVc,MAAAA,cAAaQ,KAAKH,IAAlB;IA/BqC;AAGvC,aAASH,IAAI,GAAGC,MAAML,QAAQjB,QAAQqB,IAAIC,KAAKD,KAAK;AAAA,YAA3CA,CAA2C;IA6BnD;AAED,WAAOF;EACR,GAAE,CAACF,SAAS3B,mBAAmBnB,YAAYe,YAAzC,CAnCkB;AAqCrB,MAAM2C,aAAajF,aAAAA,QAAMS,OAAO,KAAb;AAEnBC,4BAA0B,WAAM;AAC9B,QAAIuE,WAAW/E,SAAS;AACtB6C,uBAAiB,CAAA,CAAD;IACjB;AACDkC,eAAW/E,UAAU;EACtB,GAAE,CAACe,YAAD,CALsB;AAOzB,MAAMiE,iBAAiBlF,aAAAA,QAAM2C,YAC3B,SAACwC,UAAD,OAAwC;AAAA,QAAA,QAAA,UAAA,SAAP,CAAA,IAAO,OAAA,cAAA,MAA3BC,OAAAA,QAA2B,gBAAA,SAAnB,UAAmB;AAAA,QAAA,qBACFhD,UAAUlC,SAAtCmC,gBAD8B,mBAC9BA,cAAcI,aADgB,mBAChBA;AAEtB,QAAI2C,UAAU,QAAQ;AACpB,UAAID,YAAY9C,eAAc;AAC5B+C,gBAAQ;MACT,WAAUD,YAAY9C,gBAAeI,YAAW;AAC/C2C,gBAAQ;MACT,OAAM;AACLA,gBAAQ;MACT;IACF;AAED,QAAIA,UAAU,SAAS;AACrB5D,iBAAW2D,QAAD;IACX,WAAUC,UAAU,OAAO;AAC1B5D,iBAAW2D,WAAW1C,UAAZ;IACX,WAAU2C,UAAU,UAAU;AAC7B5D,iBAAW2D,WAAW1C,aAAY,CAAxB;IACX;EACF,GACD,CAACjB,UAAD,CAtBqB;AAyBvB,MAAM6D,mBAAmBrF,aAAAA,QAAM2C,YAC7B,SAAClD,OAAD,QAA6C;AAAA,QAAA,QAAA,WAAA,SAAP,CAAA,IAAO,QAAA,cAAA,MAAnC2F,OAAAA,QAAmC,gBAAA,SAA3B,SAA2B,aAAhBE,OAAgB,8BAAA,OAAA,CAAA,OAAA,CAAA;AAAA,QAAA,sBACOlD,UAAUlC,SAApDoC,gBADmC,oBACnCA,cAAcD,gBADqB,oBACrBA,cAAcI,aADO,oBACPA;AAEpC,QAAMkC,cAAcrC,cAAae,KAAKkC,IAAI,GAAGlC,KAAKF,IAAI1D,OAAOuB,OAAO,CAAvB,CAAZ,CAAD;AAEhC,QAAI,CAAC2D,aAAa;AAChB;IACD;AAED,QAAIS,UAAU,QAAQ;AACpB,UAAIT,YAAYhB,OAAOtB,gBAAeI,YAAW;AAC/C2C,gBAAQ;MACT,WAAUT,YAAYjB,SAASrB,eAAc;AAC5C+C,gBAAQ;MACT,OAAM;AACL;MACD;IACF;AAED,QAAMD,WACJC,UAAU,WACNT,YAAYjB,QAAQiB,YAAY3D,OAAO,IACvCoE,UAAU,QACVT,YAAYhB,MACZgB,YAAYjB;AAElBwB,mBAAeC,UAAD,SAAA;MAAaC;IAAb,GAAuBE,IAAvB,CAAA;EACf,GACD,CAACJ,gBAAgBlE,IAAjB,CA7BuB;AAgCzB,MAAMwE,gBAAgBxF,aAAAA,QAAM2C,YAC1B,WAAa;AAAA,aAAA,OAAA,UAAA,QAAT8C,OAAS,IAAA,MAAA,IAAA,GAAA,OAAA,GAAA,OAAA,MAAA,QAAA;AAATA,WAAS,IAAA,IAAA,UAAA,IAAA;IAAA;AAMXJ,qBAAgB,MAAhB,QAAoBI,IAApB;AACAC,0BAAsB,WAAM;AAC1BL,uBAAgB,MAAhB,QAAoBI,IAApB;IACD,CAFoB;EAGtB,GACD,CAACJ,gBAAD,CAZoB;AAetB,SAAO;IACLd;IACAX;IACAsB;IACAM;IACAxC;EALK;AAOR;AAuBD,SAASoB,eAAT,OAAmE;AAAA,MAAzC9B,eAAyC,MAAzCA,cAAcG,YAA2B,MAA3BA,WAAWJ,eAAgB,MAAhBA;AACjD,MAAMrB,OAAOsB,aAAac,SAAS;AACnC,MAAMuC,YAAY,SAAZA,WAAYlG,OAAK;AAAA,WAAI6C,aAAa7C,KAAD,EAAQiE;EAAxB;AAEvB,MAAIA,QAAQkC,wBAAwB,GAAG5E,MAAM2E,WAAWtD,YAArB;AACnC,MAAIsB,MAAMD;AAEV,SAAOC,MAAM3C,QAAQsB,aAAaqB,GAAD,EAAMA,MAAMtB,eAAeI,WAAW;AACrEkB;EACD;AAED,SAAO;IAAED;IAAOC;EAAT;AACR;kBH/TGkC,OASAC,aAGA5G,eACJ,OAEII,KCbJ,2BEEM4B,qBAEAW,qBAEAE,oBAMOE,uBAgRP2D;;;;AH9RN,IAAIC,QAA2B,CAAA,UAAA,UAAA,QAAA,SAAA,OAA/B,OAA+B;AAS/B,IAAIC,cAAc,SAAdA,aAAc,GAAA,GAAA;AAAC,UAAA,MAAA,QAAA;AAAA,YAAA,CAAA;;AAA4B,UAAA,MAAA,QAAA;AAAA,YAAA,CAAA;;AAC7C,aAAA,MAAA,KAAW,SAAA,MAAA;AAAQ,eAAA,EAAA,IAAC,MAAWC,EAAZ,IAAa;MAAhC,CAAA;IADF;AAGA,IAAI7G,gBAAgB,oBAApB,IAAoB;AAGpB,IAAII,MAAM,SAANA,OAAM;AACR,UAAM0G,gBAAN,CAAA;AACA9G,oBAAAA,QAAsB,SAAA,OAAA,MAAA;AACpB,YAAI+G,UAAUnH,KAAd,sBAAcA;AACd,YAAIgH,YAAW,SAAUtG,MAAzB,IAAe,GAAuB;AACpCA,gBAAAA,OAAAA;AACAwG,wBAAAA,KAAAA,KAAAA;QACD;MALH9G,CAAAA;AAQA8G,oBAAAA,QAAsB,SAAA,OAAA;AACpBxG,cAAAA,UAAAA,QAAwB,SAAA,IAAA;AAAM,iBAAA,GAAGA,MAAH,IAAE;QAAhCA,CAAAA;MADFwG,CAAAA;AAIAE,cAAQC,OAAAA,sBAARD,IAAQC;IAdV;ACbA,IAAA,4BAAe,OAAOA,WAAW,cAC7BnG,aAAAA,QAAMoG,kBACNpG,aAAAA,QAAMY;AEAV,IAAMM,sBAAsB,SAAtBA,uBAAsB;AAAA,aAAM;IAAN;AAE5B,IAAMW,sBAAsB,SAAtBA,qBAAsBpC,OAAK;AAAA,aAAIA;IAAJ;AAEjC,IAAMsC,qBAAqB,SAArBA,oBAAsB+C,IAAIvD,YAAe;AAC7C,UAAMiC,MAAMjC,aAAa,gBAAgB;AAEzC,aAAOuD,GAAGtB,GAAD;IACV;AAED,IAAavB,wBAAwB,SAAxBA,uBAAwBoE,OAAS;AAC5C,UAAM3C,QAAQL,KAAKkC,IAAIc,MAAM3C,QAAQ2C,MAAMlF,UAAU,CAAvC;AACd,UAAMwC,MAAMN,KAAKF,IAAIkD,MAAM1C,MAAM0C,MAAMlF,UAAUkF,MAAMrF,OAAO,CAAlD;AAEZ,UAAMsF,MAAM,CAAA;AAEZ,eAAS/C,IAAIG,OAAOH,KAAKI,KAAKJ,KAAK;AACjC+C,YAAIvB,KAAKxB,CAAT;MACD;AAED,aAAO+C;IACR;AAqQD,IAAMV,0BAA0B,SAA1BA,yBAA2BW,KAAKC,MAAMC,iBAAiBC,OAAU;AACrE,aAAOH,OAAOC,MAAM;AAClB,YAAIG,UAAWJ,MAAMC,QAAQ,IAAK;AAClC,YAAII,eAAeH,gBAAgBE,MAAD;AAElC,YAAIC,eAAeF,OAAO;AACxBH,gBAAMI,SAAS;QAChB,WAAUC,eAAeF,OAAO;AAC/BF,iBAAOG,SAAS;QACjB,OAAM;AACL,iBAAOA;QACR;MACF;AAED,UAAIJ,MAAM,GAAG;AACX,eAAOA,MAAM;MACd,OAAM;AACL,eAAO;MACR;IACF;;;;;ACjTD;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAIM,SAAQ,aAAa,eAAgB;AACzC,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,QAAI,cAAc,SAAUC,QAAO;AAC/B,UAAI,YAAYD,OAAM,OAAO,IAAI;AACjC,UAAI,YAAYA,OAAM,OAAO,IAAI;AAGjC,UAAI,WAAWA,OAAM,OAAOC,OAAM,KAAK;AACvC,eAAS,UAAUA,OAAM;AACzB,UAAI,kBAAkB,GAAG,gBAAgB,YAAY;AAAA,QACjD,MAAM,SAAS,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,QAAQ,MAAM;AAAA,UACd,qBAAqB,MAAM;AAAA,UAC3B,aAAa,MAAM;AAAA,QACvB;AAAA,MAAI,CAAC,GAAG,QAAQ,GAAG,OAAOC,UAAS,GAAG,QAAQ,sBAAsB,GAAG,qBAAqB,cAAc,GAAG,aAAa,UAAU,GAAG;AACvI,MAAAF,OAAM,UAAU,WAAY;AACxB,YAAI,UAAU,SAAU,OAAO;AAC3B,cAAIG;AACJ,cAAI,MAAM,aAAa;AACnB;AAAA,UACJ;AACA,cAAI,MAAM,QAAQ,aAAc,MAAM,WAAW,MAAM,QAAQ,KAAM;AACjE,kBAAM,eAAe;AACrB,kBAAM,gBAAgB;AACtB,kBAAM,eAAe,SAAU,OAAO;AAClC,kBAAI,YAAY,QAAQ,cAAc,QAAQ,IAAI;AAElD,kBAAI,OAAO,SAAS,QAAQ,SAAS,MAAM,UAAU;AACjD,oBAAI,cAAc;AACd,yBAAO;AACX,6BAAa;AAAA,cACjB;AACA,qBAAO;AAAA,YACX,CAAC;AAAA,UACL,WACS,MAAM,QAAQ,eAClB,MAAM,WAAW,MAAM,QAAQ,KAAM;AACtC,kBAAM,eAAe;AACrB,kBAAM,gBAAgB;AACtB,kBAAM,eAAe,SAAU,OAAO;AAClC,kBAAI,YAAY,QAAQ,SAAS,QAAQ,SAAS,IAAI,QAAQ,IAAI;AAElE,kBAAI,OAAO,SAAS,QAAQ,SAAS,MAAM,UAAU;AACjD,oBAAI,cAAc,SAAS,QAAQ,SAAS;AACxC,yBAAO;AACX,6BAAa;AAAA,cACjB;AACA,qBAAO;AAAA,YACX,CAAC;AAAA,UACL,WACS,MAAM,QAAQ,SAAS;AAC5B,kBAAM,eAAe;AACrB,kBAAM,gBAAgB;AAKtB,aAACA,MAAK,UAAU,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM;AAAA,UAC3E;AAAA,QACJ;AACA,eAAO,iBAAiB,WAAW,SAAS,EAAE,SAAS,KAAK,CAAC;AAC7D,eAAO,WAAY;AAAE,iBAAO,OAAO,oBAAoB,WAAW,SAAS,EAAE,SAAS,KAAK,CAAC;AAAA,QAAG;AAAA,MACnG,GAAG,CAAC,KAAK,CAAC;AAGV,UAAI,gBAAgB,eAAe;AACnC,MAAAH,OAAM,UAAU,WAAY;AACxB,sBAAc,aAAa;AAAA;AAAA;AAAA;AAAA,UAIvB,OAAO,eAAe,IAAI,QAAQ;AAAA,QACtC,CAAC;AAAA,MACL,GAAG,CAAC,aAAa,aAAa,CAAC;AAC/B,MAAAA,OAAM,UAAU,WAAY;AAMxB,cAAM;AAAA;AAAA,UAEN,OAAOC,OAAM,MAAM,WAAW,MAAM,WAC9B,cAAc,IACd;AAAA,QAAW;AAAA,MACrB,GAAG,CAACC,SAAQ,qBAAqBD,OAAM,OAAO,KAAK,CAAC;AACpD,UAAI,UAAUD,OAAM,YAAY,SAAU,MAAM;AAC5C,YAAIG,KAAI;AACR,YAAI,OAAO,SAAS;AAChB;AACJ,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,QAAQ,IAAI;AACzB,gBAAM,OAAO;AAAA,QACjB,OACK;AACD,gBAAM,UAAU,EAAE;AAClB,gBAAM,qBAAqB,KAAK,EAAE;AAAA,QACtC;AACA,SAAC,MAAMA,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKA,KAAI,IAAI;AAAA,MAChJ,GAAG,CAAC,OAAO,OAAO,CAAC;AACnB,UAAI,gBAAgB,GAAG,QAAQ,2BAA2B;AAC1D,aAAQH,OAAM;AAAA,QAAc;AAAA,QAAO,EAAE,KAAK,WAAW,OAAO;AAAA,UACpD,WAAWC,OAAM,aAAa;AAAA,UAC9B,UAAU;AAAA,UACV,UAAU;AAAA,QACd,EAAE;AAAA,QACFD,OAAM,cAAc,OAAO,EAAE,MAAM,WAAW,IAAI,aAAa,cAAc,OAAO;AAAA,UAC5E,QAAQ,eAAe,YAAY;AAAA,UACnC,OAAO;AAAA,QACX,EAAE,GAAG,eAAe,aAAa,IAAI,SAAU,YAAY;AAC3D,cAAI,OAAO,SAAS,QAAQ,WAAW,KAAK;AAC5C,cAAI,WAAW,OAAO,SAAS,YAAY;AAAA,YACvC,eAAe,WAAY;AACvB,qBAAO,gBACH,gBAAgB,WAAW,SAC3B,MAAM,eAAe,WAAW,KAAK;AAAA,YAC7C;AAAA,YACA,eAAe,WAAY;AAAE,qBAAO,MAAM,eAAe,WAAW,KAAK;AAAA,YAAG;AAAA,YAC5E,SAAS,WAAY;AAAE,qBAAO,QAAQ,IAAI;AAAA,YAAG;AAAA,UACjD;AACA,cAAI,SAAS,WAAW,UAAU;AAClC,iBAAQA,OAAM,cAAc,OAAO,SAAS,EAAE,KAAK,SAAS,YAAY,MAAM,KAAK,GAAG,aAAa,kBAAkB,WAAW,KAAK,GAAG,MAAM,UAAU,iBAAiB,QAAQ,KAAK,WAAW,OAAO,OAAO;AAAA,YACvM,UAAU;AAAA,YACV,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,YACP,WAAW,gBAAgB,WAAW,QAAQ;AAAA,UAClD,EAAE,GAAG,QAAQ,GAAGA,OAAM,aAAaC,OAAM,SAAS;AAAA,YAClD;AAAA,YACA;AAAA,UACJ,CAAC,GAAG;AAAA,YACA,KAAK,WAAW;AAAA,UACpB,CAAC,CAAC;AAAA,QACN,CAAC,CAAC;AAAA,MAAC;AAAA,IACX;AACA,YAAQ,cAAc;AAAA;AAAA;;;AC/KtB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,qBAAqB;AAC7B,QAAIG,SAAQ,aAAa,eAAgB;AACzC,QAAI,YAAY;AAChB,aAAS,mBAAmB,SAAS,cAAc;AAC/C,UAAI,iBAAiB,QAAQ;AAAE,uBAAe,CAAC;AAAA,MAAG;AAClD,UAAI,SAAS,GAAG,UAAU,SAAS,EAAE;AAErC,UAAI,eAAeA,OAAM,QAAQ,WAAY;AAAE,eAAO;AAAA,MAAS,GAAG,YAAY;AAC9E,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,CAAC,aAAa,QAAQ;AACtB;AAAA,QACJ;AACA,YAAI,aAAa,MAAM,gBAAgB,YAAY;AACnD,eAAO,WAAY;AACf,qBAAW;AAAA,QACf;AAAA,MACJ,GAAG,CAAC,OAAO,YAAY,CAAC;AAAA,IAC5B;AACA,YAAQ,qBAAqB;AAAA;AAAA;;;ACvC7B;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,eAAe;AACvB,QAAIC,SAAQ,aAAa,eAAgB;AACzC,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,+BAA+B;AAAA,MAC/B;AAAA,QACI,SAAS;AAAA,QACT,WAAW;AAAA,MACf;AAAA,MACA,EAAE,SAAS,GAAG,WAAW,cAAc;AAAA,MACvC,EAAE,SAAS,GAAG,WAAW,WAAW;AAAA,IACxC;AACA,QAAI,yBAAyB;AAAA,MACzB;AAAA,QACI,WAAW;AAAA,MACf;AAAA,MACA;AAAA,QACI,WAAW;AAAA,MACf;AAAA,MACA;AAAA,QACI,WAAW;AAAA,MACf;AAAA,IACJ;AACA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,IAAI;AACR,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,YAAY,GAAG,WAAW,2BAA2B,GAAG;AACtG,UAAI,MAAM,GAAG,UAAU,SAAS,SAAU,OAAO;AAAE,eAAQ;AAAA,UACvD,aAAa,MAAM;AAAA,UACnB,qBAAqB,MAAM;AAAA,QAC/B;AAAA,MAAI,CAAC,GAAG,cAAc,GAAG,aAAa,sBAAsB,GAAG,qBAAqB,QAAQ,GAAG,OAAO,UAAU,GAAG;AACnH,UAAI,WAAWA,OAAM,OAAO,IAAI;AAChC,UAAI,WAAWA,OAAM,OAAO,IAAI;AAChC,UAAI,YAAY,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAC/I,UAAI,WAAW,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAE7I,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,gBAAgB,QAAQ,YAAY,SAAS;AAC7C;AAAA,QACJ;AACA,YAAI,WAAW,gBAAgB,QAAQ,YAAY,cAAc,UAAU;AAC3E,YAAI,UAAU,SAAS;AACvB,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,8BAA8B;AAAA,UAC5F;AAAA,UACA;AAAA;AAAA,YAEA,gBAAgB,QAAQ,YAAY,eAAe,YAAY;AAAA;AAAA,UAC/D,WAAW,gBAAgB,QAAQ,YAAY,eAAe,YAAY;AAAA,UAC1E,MAAM;AAAA,QACV,CAAC;AAAA,MACL,GAAG,CAAC,SAAS,aAAa,SAAS,MAAM,CAAC;AAE1C,UAAI,iBAAiBA,OAAM,OAAO;AAClC,MAAAA,OAAM,UAAU,WAAY;AAExB,YAAI,gBAAgB,QAAQ,YAAY,SAAS;AAC7C,cAAI,UAAU,SAAS;AACvB,cAAI,UAAU,SAAS;AACvB,cAAI,CAAC,WAAW,CAAC,SAAS;AACtB;AAAA,UACJ;AACA,cAAI,OAAO,IAAI,eAAe,SAAU,SAAS;AAC7C,qBAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,kBAAI,QAAQ,UAAU,EAAE;AACxB,kBAAI,KAAK,MAAM;AACf,kBAAI,CAAC,eAAe,SAAS;AACzB,+BAAe,UAAU,GAAG;AAAA,cAChC;AACA,sBAAQ,QAAQ;AAAA,gBACZ;AAAA,kBACI,QAAQ,eAAe,UAAU;AAAA,gBACrC;AAAA,gBACA;AAAA,kBACI,QAAQ,GAAG,SAAS;AAAA,gBACxB;AAAA,cACJ,GAAG;AAAA,gBACC,UAAU,UAAU;AAAA;AAAA,gBAEpB,QAAQ;AAAA,gBACR,MAAM;AAAA,cACV,CAAC;AACD,6BAAe,UAAU,GAAG;AAAA,YAChC;AAAA,UACJ,CAAC;AACD,eAAK,QAAQ,OAAO;AACpB,iBAAO,WAAY;AACf,iBAAK,UAAU,OAAO;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,aAAa,SAAS,SAAS,MAAM,CAAC;AAE1C,UAAI,cAAcA,OAAM,OAAO,IAAI;AACnC,MAAAA,OAAM,UAAU,WAAY;AACxB,YAAI,YAAY,SAAS;AACrB,sBAAY,UAAU;AACtB;AAAA,QACJ;AACA,YAAI,UAAU,SAAS;AACvB,YAAI,SAAS;AACT,kBAAQ,QAAQ,wBAAwB;AAAA,YACpC,UAAU;AAAA,YACV,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,qBAAqB,OAAO,CAAC;AACjC,OAAC,GAAG,QAAQ,eAAe,UAAU,WAAY;AAC7C,YAAIC,KAAIC;AACR,YAAI,0BAA0B;AAC1B;AAAA,QACJ;AACA,cAAM,eAAe,QAAQ,YAAY,YAAY;AACrD,SAACA,OAAMD,MAAK,QAAQ,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,GAAE;AAAA,MACnI,CAAC;AACD,aAAQD,OAAM;AAAA,QAAc;AAAA,QAAO,EAAE,KAAK,UAAU,OAAO,SAAS,SAAS,SAAS,CAAC,GAAG,6BAA6B,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE,eAAe,OAAO,CAAC,GAAG,UAAqB;AAAA,QACjLA,OAAM,cAAc,OAAO,EAAE,KAAK,SAAS,GAAG,QAAQ;AAAA,MAAC;AAAA,IAC/D;AACA,YAAQ,eAAe;AAAA;AAAA;;;ACpJvB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGG,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,2BAA8B,OAAO;AAClD,iBAAa,sBAAyB,OAAO;AAAA;AAAA;;;ACb7C;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,aAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG,EAAE,CAAC;AAAA,IACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,eAAe;AAC1C,QAAI,UAAU;AACd,WAAO,eAAe,SAAS,gBAAgB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,QAAQ;AAAA,IAAc,EAAE,CAAC;AACtH,WAAO,eAAe,SAAS,YAAY,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,QAAQ;AAAA,IAAU,EAAE,CAAC;AAC9G,iBAAa,sBAAyB,OAAO;AAC7C,iBAAa,sBAAyB,OAAO;AAC7C,iBAAa,0BAA6B,OAAO;AACjD,iBAAa,sBAAyB,OAAO;AAC7C,iBAAa,uBAA0B,OAAO;AAC9C,iBAAa,mBAAsB,OAAO;AAC1C,iBAAa,8BAAiC,OAAO;AACrD,iBAAa,+BAAkC,OAAO;AACtD,iBAAa,wBAA2B,OAAO;AAC/C,iBAAa,iBAAoB,OAAO;AACxC,iBAAa,kBAAqB,OAAO;AAAA;AAAA;", "names": ["React", "Command", "ActionImpl", "ActionInterface", "HistoryItemImpl", "HistoryImpl", "VisualState", "React", "props", "state", "Publisher", "Subscriber", "parse", "event", "React", "_a", "_b", "_c", "action", "React", "props", "React", "collected", "obj", "path", "norm", "score", "item", "query", "value", "pattern", "result", "searchers", "React", "search", "actions", "i", "_a", "group", "results", "__export", "__toCommonJS", "React", "__toESM", "__export", "__toCommonJS", "React", "__toESM", "Slot", "props", "Slottable", "__export", "__toCommonJS", "React", "__toESM", "props", "__export", "useLayoutEffect", "__toCommonJS", "React", "__toESM", "__export", "__toCommonJS", "React", "__toESM", "props", "ReactDOM", "React", "React", "props", "React", "props", "search", "_a", "_b", "observeRect", "node", "cb", "observe", "wasEmpty", "observedNodes", "rect", "hasRectChanged", "callbacks", "run", "unobserve", "state", "index", "cancelAnimationFrame", "useRect", "nodeRef", "initialRect", "width", "height", "React", "useState", "current", "element", "setElement", "useReducer", "rectReducer", "dispatch", "initialRectSet", "useRef", "useIsomorphicLayoutEffect", "getBoundingClientRect", "useEffect", "observer", "action", "useVirtual", "size", "estimateSize", "defaultEstimateSize", "overscan", "paddingStart", "paddingEnd", "parentRef", "horizontal", "scrollToFn", "useObserver", "onScrollElement", "scrollOffsetFn", "keyExtractor", "defaultKeyExtractor", "measureSize", "defaultMeasureSize", "rangeExtractor", "defaultRangeExtractor", "sizeKey", "scroll<PERSON>ey", "latestRef", "scrollOffset", "measurements", "setScrollOffset", "useMeasureParent", "outerSize", "defaultScrollToFn", "useCallback", "offset", "resolvedScrollToFn", "measuredCache", "setMeasuredCache", "measure", "pendingMeasuredCacheIndexesRef", "useMemo", "min", "length", "Math", "slice", "i", "key", "measuredSize", "start", "end", "totalSize", "scrollOffsetFnRef", "onScroll", "event", "addEventListener", "capture", "passive", "removeEventListener", "calculateRange", "indexes", "measureSizeRef", "virtualItems", "_loop", "k", "len", "measurement", "item", "measureRef", "el", "push", "old", "mountedRef", "scrollToOffset", "toOffset", "align", "tryScrollToIndex", "rest", "max", "scrollToIndex", "args", "requestAnimationFrame", "getOffset", "findNearestBinarySearch", "props", "rectChanged", "b", "changedStates", "newRect", "rafId", "window", "useLayoutEffect", "range", "arr", "low", "high", "getCurrentValue", "value", "middle", "currentValue", "React", "props", "search", "_a", "React", "React", "_a", "_b", "exports", "exports"]}