import Decimal from "decimal.js";
import { useState, useEffect, Fragment } from "react";
import { useTranslation } from "react-i18next";
import StepFormWizardSessionBadge from "~/custom/modules/stepFormWizard/components/StepFormWizardSessionBadge";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowWithValues } from "~/utils/db/entities/rows.db.server";
import RowHelper from "~/utils/helpers/RowHelper";

export default function RowTitle({ entity, item, isOverViewPage = false,rowData }: { entity: EntityWithDetails; item: RowWithValues; isOverViewPage?: boolean; rowData?:any }) {
  const { t } = useTranslation();
  const [folio, setFolio] = useState<string | undefined>(undefined);
  const [description, setDescription] = useState<string | undefined>(undefined);
  const [metaValues, setMetaValues] = useState<(string | Date | Decimal | null)[]>([]);
  useEffect(() => {
    setFolio(RowHelper.getRowFolio(entity, item));
    let description = RowHelper.getTextDescription({ entity, item, t }) ?? "";
    if (description.length > 160) {
      description = description.substring(0, 160) + "...";
    }
    setDescription(description);
  }, [entity, item, t]);

  function getTitleFromProperty(overviewHeaderPropertyId: string, item: RowWithValues) {
    const val = item.values.find((v) => v.propertyId === overviewHeaderPropertyId);
    if (!val) return "N/A";

    if (val.textValue !== null) return val.textValue;
    if (val.numberValue !== null) return val.numberValue;
    if (val.dateValue !== null) return val.dateValue;

    if (val.booleanValue === false) return "No";
    if (val.booleanValue === true) return "Yes";

    if (val.multiple.length) {
      let s = "";
      for (let i = 0; i < val.multiple.length; i++) {
        if (val.multiple[i]) {
          s += val.multiple[i].value + (i === val.multiple.length - 1 ? "" : ", ");
        }
      }
      return s;
    }

    return "N/A";
  }

  useEffect(() => {
    if (!isOverViewPage) return;

    const overviewHeaderProperty = entity.properties.find((p) => p.isOverviewHeaderProperty);
    const overviewHeaderPropertyId = overviewHeaderProperty ? overviewHeaderProperty.id : undefined;

    setDescription(overviewHeaderProperty ? getTitleFromProperty(overviewHeaderPropertyId, item) : "");

    const metaProperties = entity.properties.filter((p) => p.isMetaProperty);

    let metaPropertyValues: (string | Date | Decimal | null)[] = [];

    for (const metaProperty of metaProperties) {
      const metaPropertyValue = item.values.find((v) => v.propertyId === metaProperty.id);
      if (metaPropertyValue) {
        metaPropertyValues.push(getTitleFromProperty(metaProperty.id, item));
      }
    }

    setMetaValues(metaPropertyValues);
  }, []);
  if (!isOverViewPage) {
    return (
      <Fragment>
        {!description || folio === description ? (
          <div className="truncate">{RowHelper.getRowFolio(entity, item)}</div>
        ) : (
          <div className="flex items-baseline space-x-1 truncate">
            <div className="truncate">
              {description} <span className="text-muted-foreground text-xs font-medium uppercase">({RowHelper.getRowFolio(entity, item)})</span>
            </div>
          </div>
        )}
      </Fragment>
    );
  } else
    return (
      <>
        <div className="flex items-center justify-center gap-[10px]" >
          {description && (<div
            className="w-[28px] text-[14px] flex justify-center items-center text-white h-[28px] min-w-[28px] gap-[7px] pt-[7px] pr-[5.6px] pb-[7px] pl-[5.6px] rounded-[6px] bg-[#202229]"
          >
            {description?.charAt(0)}
          </div>)}
          <div className="flex flex-col items-baseline gap-[6px] space-x-1 truncate">

            <div className={`flex items-baseline ${!description?'':"gap-[6px]"} `}>
            <div className="text-foreground truncate text-[18px] leading-[18px] font-medium tracking-[0]">{description}</div>
            <div className="flex"> <div>{rowData?.item?.onboardingSession ? <OnboardingSessionBadge item={rowData?.item?.onboardingSession} /> : ""}</div></div>
            </div>
            <div className="flex items-baseline space-x-1 truncate">
              <span className="text-muted-foreground align-middle text-[12px] leading-[12px] font-normal tracking-[0]">
                {RowHelper.getRowFolio(entity, item)} {metaValues.length > 0 && "|"}
              </span>
              {metaValues.map((value, index) => (
                <span key={index} className="text-muted-foreground align-middle text-[12px] leading-[12px] font-normal tracking-[0] uppercase">
                  <>
                    {value} {index === metaValues.length - 1 ? <></> : "|"}
                  </>
                </span>
              ))}
            </div>
          </div>
        </div>
      </>
    );
}
