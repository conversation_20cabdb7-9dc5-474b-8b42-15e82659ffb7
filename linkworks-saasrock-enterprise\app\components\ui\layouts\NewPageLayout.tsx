import { ReactNode } from "react";
import { cn } from "~/lib/utils";
import Stepper from "~/custom/components/CreatePageStepper";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { useLocation, useSearchParams, useNavigate } from "react-router";

interface Props {
  title: string;
  menu?: {
    title: string;
    routePath?: string;
  }[];
  buttons?: ReactNode;
  children: ReactNode;
  className?: string;
  entity?: any;
  item?: RowWithDetails;
}
export default function NewPageLayout({ title, menu, buttons, children, className, entity, item }: Props) {
  const parseStep = (step: any) => {
    try {
      return JSON.parse(step.block);
    } catch {
      return null;
    }
  };

  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const steps = entity?.isStepFormWizard && entity?.stepFormWizard ? entity?.stepFormWizard.steps.map(parseStep).filter(Boolean) : [];
  const currentStepIndexStr = searchParams.get("step") ?? "0";
  const currentStepIndex = Number(currentStepIndexStr);
  const currentStep = steps[currentStepIndex];
  const stepFormWizardSession = item?.stepFormWizardSession ?? null;
  const sessionSteps = stepFormWizardSession?.sessionSteps ?? [];
  const location = useLocation();
  const isAdminPage = location.pathname.includes("admin");

  const handleCurrentStep = (stepIndex: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("step", stepIndex.toString());
    params.set("creating", "true"); // Always set this during creation
    navigate(`${location.pathname}?${params.toString()}`);
  };

  return (
    <div className={cn("", entity?.isStepFormWizard ? "flex-col" : "")}>
      <div className="bg-muted-background">
        {!isAdminPage && (
          <>
            <div className="mx-auto flex w-full max-w-[943px] items-center !justify-between px-4 pt-[24px] pb-[24px] text-[18px] font-bold text-[#202229] sm:px-6 lg:px-8">
              {location?.pathname?.includes("new") ? `Add New ${entity?.name}` : `Edit ${entity?.name}`}
            </div>
          </>
        )}
      </div>
      <div className={cn("mx-auto flex w-full max-w-[943px] items-center !justify-between px-4 sm:px-6 lg:px-8", entity?.isStepFormWizard ? "" : "")}>
        {entity?.isStepFormWizard ? (
          <>
            <div
              style={{ boxShadow: "0px 4px 14.9px 0px #B7B7B740" }}
              className="bg-card border-input sticky top-0 z-50 flex h-[57px] w-full justify-center rounded-[8px] border-[1px] p-4"
            >
              <Stepper steps={steps} sessionSteps={sessionSteps} currentStep={currentStepIndex} handleCurrentStep={handleCurrentStep} />
            </div>
          </>
        ) : (
          <></>
        )}
      </div>

      <div className={entity?.isStepFormWizard ? "flex-1 overflow-y-auto" : ""}>
        <div className={cn(className, "mx-auto max-w-[943px] px-4 pb-6 sm:px-6 lg:px-8")}>
          <div className="space-y-1">
            <div className="flex items-center justify-between space-x-2">
              {isAdminPage && <h1 className="flex flex-1 items-center truncate text-xl font-medium">{title}</h1>}
              <div className="flex items-center space-x-2">{buttons}</div>
            </div>

            {/* {menu && <BreadcrumbSimple menu={menu} />} */}
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
