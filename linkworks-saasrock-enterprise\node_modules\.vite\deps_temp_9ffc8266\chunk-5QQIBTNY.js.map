{"version": 3, "sources": ["../../deepmerge/dist/cjs.js"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAI,oBAAoB,SAASA,mBAAkB,OAAO;AACzD,aAAO,gBAAgB,KAAK,KACxB,CAAC,UAAU,KAAK;AAAA,IACrB;AAEA,aAAS,gBAAgB,OAAO;AAC/B,aAAO,CAAC,CAAC,SAAS,OAAO,UAAU;AAAA,IACpC;AAEA,aAAS,UAAU,OAAO;AACzB,UAAI,cAAc,OAAO,UAAU,SAAS,KAAK,KAAK;AAEtD,aAAO,gBAAgB,qBACnB,gBAAgB,mBAChB,eAAe,KAAK;AAAA,IACzB;AAGA,QAAI,eAAe,OAAO,WAAW,cAAc,OAAO;AAC1D,QAAI,qBAAqB,eAAe,OAAO,IAAI,eAAe,IAAI;AAEtE,aAAS,eAAe,OAAO;AAC9B,aAAO,MAAM,aAAa;AAAA,IAC3B;AAEA,aAAS,YAAY,KAAK;AACzB,aAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAAA,IACnC;AAEA,aAAS,8BAA8B,OAAO,SAAS;AACtD,aAAQ,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,KAAK,IAC/D,UAAU,YAAY,KAAK,GAAG,OAAO,OAAO,IAC5C;AAAA,IACJ;AAEA,aAAS,kBAAkB,QAAQ,QAAQ,SAAS;AACnD,aAAO,OAAO,OAAO,MAAM,EAAE,IAAI,SAAS,SAAS;AAClD,eAAO,8BAA8B,SAAS,OAAO;AAAA,MACtD,CAAC;AAAA,IACF;AAEA,aAAS,iBAAiB,KAAK,SAAS;AACvC,UAAI,CAAC,QAAQ,aAAa;AACzB,eAAO;AAAA,MACR;AACA,UAAI,cAAc,QAAQ,YAAY,GAAG;AACzC,aAAO,OAAO,gBAAgB,aAAa,cAAc;AAAA,IAC1D;AAEA,aAAS,gCAAgC,QAAQ;AAChD,aAAO,OAAO,wBACX,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAS,QAAQ;AAC9D,eAAO,OAAO,qBAAqB,KAAK,QAAQ,MAAM;AAAA,MACvD,CAAC,IACC,CAAC;AAAA,IACL;AAEA,aAAS,QAAQ,QAAQ;AACxB,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,gCAAgC,MAAM,CAAC;AAAA,IAC1E;AAEA,aAAS,mBAAmB,QAAQ,UAAU;AAC7C,UAAI;AACH,eAAO,YAAY;AAAA,MACpB,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACD;AAGA,aAAS,iBAAiB,QAAQ,KAAK;AACtC,aAAO,mBAAmB,QAAQ,GAAG,KACjC,EAAE,OAAO,eAAe,KAAK,QAAQ,GAAG,KACvC,OAAO,qBAAqB,KAAK,QAAQ,GAAG;AAAA,IAClD;AAEA,aAAS,YAAY,QAAQ,QAAQ,SAAS;AAC7C,UAAI,cAAc,CAAC;AACnB,UAAI,QAAQ,kBAAkB,MAAM,GAAG;AACtC,gBAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE,CAAC;AAAA,MACF;AACA,cAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,YAAI,iBAAiB,QAAQ,GAAG,GAAG;AAClC;AAAA,QACD;AAEA,YAAI,mBAAmB,QAAQ,GAAG,KAAK,QAAQ,kBAAkB,OAAO,GAAG,CAAC,GAAG;AAC9E,sBAAY,GAAG,IAAI,iBAAiB,KAAK,OAAO,EAAE,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,OAAO;AAAA,QACpF,OAAO;AACN,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACR;AAEA,aAAS,UAAU,QAAQ,QAAQ,SAAS;AAC3C,gBAAU,WAAW,CAAC;AACtB,cAAQ,aAAa,QAAQ,cAAc;AAC3C,cAAQ,oBAAoB,QAAQ,qBAAqB;AAGzD,cAAQ,gCAAgC;AAExC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,4BAA4B,kBAAkB;AAElD,UAAI,CAAC,2BAA2B;AAC/B,eAAO,8BAA8B,QAAQ,OAAO;AAAA,MACrD,WAAW,eAAe;AACzB,eAAO,QAAQ,WAAW,QAAQ,QAAQ,OAAO;AAAA,MAClD,OAAO;AACN,eAAO,YAAY,QAAQ,QAAQ,OAAO;AAAA,MAC3C;AAAA,IACD;AAEA,cAAU,MAAM,SAAS,aAAa,OAAO,SAAS;AACrD,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1B,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACpD;AAEA,aAAO,MAAM,OAAO,SAAS,MAAM,MAAM;AACxC,eAAO,UAAU,MAAM,MAAM,OAAO;AAAA,MACrC,GAAG,CAAC,CAAC;AAAA,IACN;AAEA,QAAI,cAAc;AAElB,WAAO,UAAU;AAAA;AAAA;", "names": ["isMergeableObject"]}