{"version": 3, "sources": ["../../@epic-web/cachified/src/common.ts", "../../@epic-web/cachified/src/reporter.ts", "../../@epic-web/cachified/src/adapters.ts", "../../@epic-web/cachified/src/createBatch.ts", "../../@epic-web/cachified/src/assertCacheEntry.ts", "../../@epic-web/cachified/src/shouldRefresh.ts", "../../@epic-web/cachified/src/checkValue.ts", "../../@epic-web/cachified/src/getCachedValue.ts", "../../@epic-web/cachified/src/getFreshValue.ts", "../../@epic-web/cachified/src/cachified.ts", "../../@epic-web/cachified/src/softPurge.ts"], "sourcesContent": ["import type { Create<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from './reporter';\n\nexport interface CacheMetadata {\n  createdTime: number;\n  ttl?: number | null;\n  swr?: number | null;\n  /** @deprecated use swr instead */\n  readonly swv?: number | null;\n}\n\nexport interface CacheEntry<Value = unknown> {\n  metadata: CacheMetadata;\n  value: Value;\n}\n\nexport type Eventually<Value> =\n  | Value\n  | null\n  | undefined\n  | Promise<Value | null | undefined>;\n\nexport interface Cache {\n  name?: string;\n  get: (key: string) => Eventually<CacheEntry<unknown>>;\n  set: (key: string, value: CacheEntry<unknown>) => unknown | Promise<unknown>;\n  delete: (key: string) => unknown | Promise<unknown>;\n}\n\nexport interface GetFreshValueContext {\n  readonly metadata: CacheMetadata;\n  readonly background: boolean;\n}\nexport const HANDLE = Symbol();\nexport type GetFreshValue<Value> = {\n  (context: GetFreshValueContext): Promise<Value> | Value;\n  [HANDLE]?: () => void;\n};\nexport const MIGRATED = Symbol();\nexport type MigratedValue<Value> = {\n  [MIGRATED]: boolean;\n  value: Value;\n};\n\nexport type ValueCheckResultOk<Value> =\n  | true\n  | undefined\n  | null\n  | void\n  | MigratedValue<Value>;\nexport type ValueCheckResultInvalid = false | string;\nexport type ValueCheckResult<Value> =\n  | ValueCheckResultOk<Value>\n  | ValueCheckResultInvalid;\nexport type CheckValue<Value> = (\n  value: unknown,\n  migrate: (value: Value, updateCache?: boolean) => MigratedValue<Value>,\n) => ValueCheckResult<Value> | Promise<ValueCheckResult<Value>>;\nexport interface Schema<Value, InputValue> {\n  _input: InputValue;\n  parseAsync(value: unknown): Promise<Value>;\n}\n\nexport interface CachifiedOptions<Value> {\n  /**\n   * Required\n   *\n   * The key this value is cached by\n   * Must be unique for each value\n   */\n  key: string;\n  /**\n   * Required\n   *\n   * Cache implementation to use\n   *\n   * Must conform with signature\n   *  - set(key: string, value: object): void | Promise<void>\n   *  - get(key: string): object | Promise<object>\n   *  - delete(key: string): void | Promise<void>\n   */\n  cache: Cache;\n  /**\n   * Required\n   *\n   * Function that is called when no valid value is in cache for given key\n   * Basically what we would do if we wouldn't use a cache\n   *\n   * Can be async and must return fresh value or throw\n   *\n   * receives context object as argument\n   *  - context.metadata.ttl?: number\n   *  - context.metadata.swr?: number\n   *  - context.metadata.createdTime: number\n   *  - context.background: boolean\n   */\n  getFreshValue: GetFreshValue<Value>;\n  /**\n   * Time To Live; often also referred to as max age\n   *\n   * Amount of milliseconds the value should stay in cache\n   * before we get a fresh one\n   *\n   * Setting any negative value will disable caching\n   * Can be infinite\n   *\n   * Default: `Infinity`\n   */\n  ttl?: number;\n  /**\n   * Amount of milliseconds that a value with exceeded ttl is still returned\n   * while a fresh value is refreshed in the background\n   *\n   * Should be positive, can be infinite\n   *\n   * Default: `0`\n   */\n  staleWhileRevalidate?: number;\n  /**\n   * Alias for staleWhileRevalidate\n   */\n  swr?: number;\n  /**\n   * Validator that checks every cached and fresh value to ensure type safety\n   *\n   * Can be a zod schema or a custom validator function\n   *\n   * Value considered ok when:\n   *  - zod schema.parseAsync succeeds\n   *  - validator returns\n   *    - true\n   *    - migrate(newValue)\n   *    - undefined\n   *    - null\n   *\n   * Value considered bad when:\n   *  - zod schema.parseAsync throws\n   *  - validator:\n   *    - returns false\n   *    - returns reason as string\n   *    - throws\n   *\n   * A validator function receives two arguments:\n   *  1. the value\n   *  2. a migrate callback, see https://github.com/Xiphe/cachified#migrating-values\n   *\n   * Default: `undefined` - no validation\n   */\n  checkValue?: CheckValue<Value> | Schema<Value, unknown>;\n  /**\n   * Set true to not even try reading the currently cached value\n   *\n   * Will write new value to cache even when cached value is\n   * still valid.\n   *\n   * Default: `false`\n   */\n  forceFresh?: boolean;\n  /**\n   * Whether or not to fall back to cache when getting a forced fresh value\n   * fails\n   *\n   * Can also be a positive number as the maximum age in milliseconds that a\n   * fallback value might have\n   *\n   * Default: `Infinity`\n   */\n  fallbackToCache?: boolean | number;\n  /**\n   * Amount of time in milliseconds before revalidation of a stale\n   * cache entry is started\n   *\n   * Must be positive and finite\n   *\n   * Default: `0`\n   */\n  staleRefreshTimeout?: number;\n  /**\n   * A reporter receives events during the runtime of\n   * cachified and can be used for debugging and monitoring\n   *\n   * Default: `undefined` - no reporting\n   */\n  reporter?: CreateReporter<Value> | null;\n}\n\n/* When using a schema validator, a strongly typed getFreshValue is not required\n   and sometimes even sub-optimal */\nexport type CachifiedOptionsWithSchema<Value, InternalValue> = Omit<\n  CachifiedOptions<Value>,\n  'checkValue' | 'getFreshValue'\n> & {\n  checkValue: Schema<Value, InternalValue>;\n  getFreshValue: GetFreshValue<InternalValue>;\n};\n\nexport interface Context<Value>\n  extends Omit<\n    Required<CachifiedOptions<Value>>,\n    'fallbackToCache' | 'reporter' | 'checkValue' | 'swr'\n  > {\n  checkValue: CheckValue<Value>;\n  report: Reporter<Value>;\n  fallbackToCache: number;\n  metadata: CacheMetadata;\n}\n\nexport function createContext<Value>({\n  fallbackToCache,\n  reporter,\n  checkValue,\n  ...options\n}: CachifiedOptions<Value>): Context<Value> {\n  const ttl = options.ttl ?? Infinity;\n  const staleWhileRevalidate = options.swr ?? options.staleWhileRevalidate ?? 0;\n  const checkValueCompat: CheckValue<Value> =\n    typeof checkValue === 'function'\n      ? checkValue\n      : typeof checkValue === 'object'\n      ? (value, migrate) =>\n          checkValue.parseAsync(value).then((v) => migrate(v, false))\n      : () => true;\n\n  const contextWithoutReport = {\n    checkValue: checkValueCompat,\n    ttl,\n    staleWhileRevalidate,\n    fallbackToCache:\n      fallbackToCache === false\n        ? 0\n        : fallbackToCache === true || fallbackToCache === undefined\n        ? Infinity\n        : fallbackToCache,\n    staleRefreshTimeout: 0,\n    forceFresh: false,\n    ...options,\n    metadata: createCacheMetaData({ ttl, swr: staleWhileRevalidate }),\n  };\n\n  const report =\n    reporter?.(contextWithoutReport) ||\n    (() => {\n      /* ¯\\_(ツ)_/¯ */\n    });\n\n  return {\n    ...contextWithoutReport,\n    report,\n  };\n}\n\nexport function staleWhileRevalidate(metadata: CacheMetadata): number | null {\n  return (\n    (typeof metadata.swr === 'undefined' ? metadata.swv : metadata.swr) || null\n  );\n}\n\nexport function totalTtl(metadata?: CacheMetadata): number {\n  if (!metadata) {\n    return 0;\n  }\n  if (metadata.ttl === null) {\n    return Infinity;\n  }\n  return (metadata.ttl || 0) + (staleWhileRevalidate(metadata) || 0);\n}\n\nexport function createCacheMetaData({\n  ttl = null,\n  swr = 0,\n  createdTime = Date.now(),\n}: Partial<Omit<CacheMetadata, 'swv'>> = {}) {\n  return {\n    ttl: ttl === Infinity ? null : ttl,\n    swr: swr === Infinity ? null : swr,\n    createdTime,\n  };\n}\n\nexport function createCacheEntry<Value>(\n  value: Value,\n  metadata?: Partial<Omit<CacheMetadata, 'swv'>>,\n): CacheEntry<Value> {\n  return {\n    value,\n    metadata: createCacheMetaData(metadata),\n  };\n}\n", "import { CacheMetadata, Context, staleWhileRevalidate } from './common';\n\nexport type GetFreshValueStartEvent = {\n  name: 'getFreshValueStart';\n};\nexport type GetFreshValueHookPendingEvent = {\n  name: 'getFreshValueHookPending';\n};\nexport type GetFreshValueSuccessEvent<Value> = {\n  name: 'getFreshValueSuccess';\n  value: Value;\n};\nexport type GetFreshValueErrorEvent = {\n  name: 'getFreshValueError';\n  error: unknown;\n};\nexport type GetFreshValueCacheFallbackEvent = {\n  name: 'getFreshValueCacheFallback';\n  value: unknown;\n};\n/** @deprecated this event will be removed in favour of `CheckFreshValueErrorObjEvent` */\nexport type CheckFreshValueErrorEvent<Value> = {\n  name: 'checkFreshValueError';\n  reason: string;\n};\nexport type CheckFreshValueErrorObjEvent = {\n  name: 'checkFreshValueErrorObj';\n  reason: unknown;\n};\nexport type WriteFreshValueSuccessEvent<Value> = {\n  name: 'writeFreshValueSuccess';\n  metadata: CacheMetadata;\n  /**\n   * Value might not actually be written to cache in case getting fresh\n   * value took longer then ttl */\n  written: boolean;\n  migrated: boolean;\n};\nexport type WriteFreshValueErrorEvent = {\n  name: 'writeFreshValueError';\n  error: unknown;\n};\n\nexport type GetCachedValueStartEvent = {\n  name: 'getCachedValueStart';\n};\nexport type GetCachedValueReadEvent = {\n  name: 'getCachedValueRead';\n  entry: unknown;\n};\nexport type GetCachedValueEmptyEvent = {\n  name: 'getCachedValueEmpty';\n};\nexport type GetCachedValueOutdatedEvent = {\n  name: 'getCachedValueOutdated';\n  value: unknown;\n  metadata: CacheMetadata;\n};\nexport type GetCachedValueSuccessEvent<Value> = {\n  name: 'getCachedValueSuccess';\n  value: Value;\n  migrated: boolean;\n};\n/** @deprecated this event will be removed in favour of `CheckCachedValueErrorObjEvent` */\nexport type CheckCachedValueErrorEvent = {\n  name: 'checkCachedValueError';\n  reason: string;\n};\nexport type CheckCachedValueErrorObjEvent = {\n  name: 'checkCachedValueErrorObj';\n  reason: unknown;\n};\nexport type GetCachedValueErrorEvent = {\n  name: 'getCachedValueError';\n  error: unknown;\n};\n\nexport type RefreshValueStartEvent = {\n  name: 'refreshValueStart';\n};\nexport type RefreshValueSuccessEvent<Value> = {\n  name: 'refreshValueSuccess';\n  value: Value;\n};\nexport type RefreshValueErrorEvent = {\n  name: 'refreshValueError';\n  error: unknown;\n};\nexport type DoneEvent<Value> = {\n  name: 'done';\n  value: Value;\n};\n\nexport type CacheEvent<Value> =\n  | GetFreshValueStartEvent\n  | GetFreshValueHookPendingEvent\n  | GetFreshValueSuccessEvent<Value>\n  | GetFreshValueErrorEvent\n  | GetFreshValueCacheFallbackEvent\n  | CheckFreshValueErrorEvent<Value>\n  | CheckFreshValueErrorObjEvent\n  | WriteFreshValueSuccessEvent<Value>\n  | WriteFreshValueErrorEvent\n  | GetCachedValueStartEvent\n  | GetCachedValueReadEvent\n  | GetCachedValueEmptyEvent\n  | GetCachedValueOutdatedEvent\n  | GetCachedValueSuccessEvent<Value>\n  | CheckCachedValueErrorEvent\n  | CheckCachedValueErrorObjEvent\n  | GetCachedValueErrorEvent\n  | RefreshValueStartEvent\n  | RefreshValueSuccessEvent<Value>\n  | RefreshValueErrorEvent\n  | DoneEvent<Value>;\n\nexport type Reporter<Value> = (event: CacheEvent<Value>) => void;\n\nexport type CreateReporter<Value> = (\n  context: Omit<Context<Value>, 'report'>,\n) => Reporter<Value>;\n\nconst defaultFormatDuration = (ms: number) => `${Math.round(ms)}ms`;\nfunction formatCacheTime(\n  metadata: CacheMetadata,\n  formatDuration: (duration: number) => string,\n) {\n  const swr = staleWhileRevalidate(metadata);\n  if (metadata.ttl == null || swr == null) {\n    return `forever${\n      metadata.ttl != null\n        ? ` (revalidation after ${formatDuration(metadata.ttl)})`\n        : ''\n    }`;\n  }\n\n  return `${formatDuration(metadata.ttl)} + ${formatDuration(swr)} stale`;\n}\n\ninterface ReporterOpts {\n  formatDuration?: (ms: number) => string;\n  logger?: Pick<typeof console, 'log' | 'warn' | 'error'>;\n  performance?: Pick<typeof Date, 'now'>;\n}\nexport function verboseReporter<Value>({\n  formatDuration = defaultFormatDuration,\n  logger = console,\n  performance = global.performance || Date,\n}: ReporterOpts = {}): CreateReporter<Value> {\n  return ({ key, fallbackToCache, forceFresh, metadata, cache }) => {\n    const cacheName =\n      cache.name ||\n      cache\n        .toString()\n        .toString()\n        .replace(/^\\[object (.*?)]$/, '$1');\n    let cached: unknown;\n    let freshValue: unknown;\n    let getFreshValueStartTs: number;\n    let refreshValueStartTS: number;\n\n    return (event) => {\n      switch (event.name) {\n        case 'getCachedValueRead':\n          cached = event.entry;\n          break;\n        case 'checkCachedValueError':\n          logger.warn(\n            `check failed for cached value of ${key}\\nReason: ${event.reason}.\\nDeleting the cache key and trying to get a fresh value.`,\n            cached,\n          );\n          break;\n        case 'getCachedValueError':\n          logger.error(\n            `error with cache at ${key}. Deleting the cache key and trying to get a fresh value.`,\n            event.error,\n          );\n          break;\n        case 'getFreshValueError':\n          logger.error(\n            `getting a fresh value for ${key} failed`,\n            { fallbackToCache, forceFresh },\n            event.error,\n          );\n          break;\n        case 'getFreshValueStart':\n          getFreshValueStartTs = performance.now();\n          break;\n        case 'writeFreshValueSuccess': {\n          const totalTime = performance.now() - getFreshValueStartTs;\n          if (event.written) {\n            logger.log(\n              `Updated the cache value for ${key}.`,\n              `Getting a fresh value for this took ${formatDuration(\n                totalTime,\n              )}.`,\n              `Caching for ${formatCacheTime(\n                metadata,\n                formatDuration,\n              )} in ${cacheName}.`,\n            );\n          } else {\n            logger.log(\n              `Not updating the cache value for ${key}.`,\n              `Getting a fresh value for this took ${formatDuration(\n                totalTime,\n              )}.`,\n              `Thereby exceeding caching time of ${formatCacheTime(\n                metadata,\n                formatDuration,\n              )}`,\n            );\n          }\n          break;\n        }\n        case 'writeFreshValueError':\n          logger.error(`error setting cache: ${key}`, event.error);\n          break;\n        case 'getFreshValueSuccess':\n          freshValue = event.value;\n          break;\n        case 'checkFreshValueError':\n          logger.error(\n            `check failed for fresh value of ${key}\\nReason: ${event.reason}.`,\n            freshValue,\n          );\n          break;\n        case 'refreshValueStart':\n          refreshValueStartTS = performance.now();\n          break;\n        case 'refreshValueSuccess':\n          logger.log(\n            `Background refresh for ${key} successful.`,\n            `Getting a fresh value for this took ${formatDuration(\n              performance.now() - refreshValueStartTS,\n            )}.`,\n            `Caching for ${formatCacheTime(\n              metadata,\n              formatDuration,\n            )} in ${cacheName}.`,\n          );\n          break;\n        case 'refreshValueError':\n          logger.log(`Background refresh for ${key} failed.`, event.error);\n          break;\n      }\n    };\n  };\n}\n\nexport function mergeReporters<Value = unknown>(\n  ...reporters: (CreateReporter<Value> | null | undefined)[]\n): CreateReporter<Value> {\n  return (context) => {\n    const reporter = reporters.map((r) => r?.(context));\n    return (event) => {\n      reporter.forEach((r) => r?.(event));\n    };\n  };\n}\n", "import { Cache, CacheEntry, totalTtl } from './common';\n\nexport interface LRUishCache extends Omit<Cache, 'set'> {\n  set(\n    key: string,\n    value: CacheEntry<unknown>,\n    options?: { ttl?: number; start?: number },\n  ): void;\n}\n\nexport function lruCacheAdapter(lruCache: LRUishCache): Cache {\n  return {\n    name: lruCache.name || 'LRU',\n    set(key, value) {\n      const ttl = totalTtl(value?.metadata);\n      return lruCache.set(key, value, {\n        ttl: ttl === Infinity ? undefined : ttl,\n        start: value?.metadata?.createdTime,\n      });\n    },\n    get(key) {\n      return lruCache.get(key);\n    },\n    delete(key) {\n      return lruCache.delete(key);\n    },\n  };\n}\n\ninterface Redis3Multi {\n  set(key: string, value: string): Redis3Multi;\n  expireat(key: string, timestamp: number): Redis3Multi;\n  exec(cb: (err: Error | null, replies: (number | 'OK')[]) => void): unknown;\n}\nexport interface Redis3LikeCache {\n  name?: string;\n  set(\n    key: string,\n    value: string,\n    cb: (err: Error | null, reply: 'OK') => void,\n  ): unknown;\n  get(\n    key: string,\n    cb?: (err: Error | null, reply: string | null) => void,\n  ): unknown;\n  del(key: string, cb?: (err: Error | null, reply: number) => void): unknown;\n  multi(): Redis3Multi;\n}\n\nexport function redis3CacheAdapter(redisCache: Redis3LikeCache): Cache {\n  return {\n    name: redisCache.name || 'Redis3',\n    set(key, value) {\n      return new Promise<void>((res, rej) => {\n        const ttl = totalTtl(value?.metadata);\n        const createdTime = value?.metadata?.createdTime;\n        const cb = (err: unknown) => {\n          if (err) {\n            return rej(err);\n          }\n          res();\n        };\n\n        if (ttl > 0 && ttl < Infinity && typeof createdTime === 'number') {\n          redisCache\n            .multi()\n            .set(key, JSON.stringify(value))\n            .expireat(key, (ttl + createdTime) / 1000)\n            .exec(cb);\n        } else {\n          redisCache.set(key, JSON.stringify(value), cb);\n        }\n      });\n    },\n    get(key) {\n      return new Promise<CacheEntry | null | undefined>((res, rej) => {\n        redisCache.get(key, (err, reply) => {\n          if (err) {\n            rej(err);\n          } else if (reply == null) {\n            res(null);\n          } else {\n            try {\n              res(JSON.parse(reply));\n            } catch (err) {\n              rej(err);\n            }\n          }\n        });\n      });\n    },\n    delete(key) {\n      return new Promise<void>((res, rej) => {\n        redisCache.del(key, (err) => {\n          if (err) {\n            rej(err);\n          }\n          res();\n        });\n      });\n    },\n  };\n}\n\nexport interface RedisLikeCache {\n  name?: string;\n  set(\n    key: string,\n    value: string,\n    options?: { EXAT: number },\n  ): Promise<string | null>;\n  get(key: string): Promise<string | null>;\n  del(key: string): Promise<unknown>;\n}\n\nexport function redisCacheAdapter(redisCache: RedisLikeCache): Cache {\n  return {\n    name: redisCache.name || 'Redis',\n    set(key, value) {\n      const ttl = totalTtl(value?.metadata);\n      const createdTime = value?.metadata?.createdTime;\n\n      return redisCache.set(\n        key,\n        JSON.stringify(value),\n        ttl > 0 && ttl < Infinity && typeof createdTime === 'number'\n          ? {\n              EXAT: Math.ceil((ttl + createdTime) / 1000),\n            }\n          : undefined,\n      );\n    },\n    async get(key) {\n      const value = await redisCache.get(key);\n      if (value == null) {\n        return null;\n      }\n      return JSON.parse(value);\n    },\n    delete(key) {\n      return redisCache.del(key);\n    },\n  };\n}\n", "import type { GetFreshValue, GetFreshValueContext } from './common';\nimport { HANDLE } from './common';\n\ntype OnValueCallback<Value> = (\n  context: GetFreshValueContext & {\n    value: Value;\n  },\n) => void;\n\nexport type AddFn<Value, Param> = (\n  param: Param,\n  onValue?: OnValueCallback<Value>,\n) => GetFreshValue<Value>;\n\nexport function createBatch<Value, Param>(\n  getFreshValues: (params: Param[]) => Value[] | Promise<Value[]>,\n  autoSubmit: false,\n): {\n  submit: () => Promise<void>;\n  add: AddFn<Value, Param>;\n};\nexport function createBatch<Value, Param>(\n  getFreshValues: (params: Param[]) => Value[] | Promise<Value[]>,\n): {\n  add: AddFn<Value, Param>;\n};\nexport function createBatch<Value, Param>(\n  getFreshValues: (params: Param[]) => Value[] | Promise<Value[]>,\n  autoSubmit: boolean = true,\n): {\n  submit?: () => Promise<void>;\n  add: AddFn<Value, Param>;\n} {\n  const requests: [\n    param: Param,\n    res: (value: Value) => void,\n    rej: (reason: unknown) => void,\n  ][] = [];\n\n  let count = 0;\n  let submitted = false;\n  const submission = new Deferred<void>();\n\n  const checkSubmission = () => {\n    if (submitted) {\n      throw new Error('Can not add to batch after submission');\n    }\n  };\n\n  const submit = async () => {\n    if (count !== 0) {\n      autoSubmit = true;\n      return submission.promise;\n    }\n    checkSubmission();\n    submitted = true;\n\n    if (requests.length === 0) {\n      submission.resolve();\n      return;\n    }\n\n    try {\n      const results = await Promise.resolve(\n        getFreshValues(requests.map(([param]) => param)),\n      );\n      results.forEach((value, index) => requests[index][1](value));\n      submission.resolve();\n    } catch (err) {\n      requests.forEach(([_, __, rej]) => rej(err));\n      submission.resolve();\n    }\n  };\n\n  const trySubmitting = () => {\n    count--;\n    if (autoSubmit === false) {\n      return;\n    }\n    submit();\n  };\n\n  return {\n    ...(autoSubmit === false ? { submit } : {}),\n    add(param, onValue) {\n      checkSubmission();\n      count++;\n      let handled = false;\n\n      return Object.assign(\n        (context: GetFreshValueContext) => {\n          return new Promise<Value>((res, rej) => {\n            requests.push([\n              param,\n              (value) => {\n                onValue?.({ ...context, value });\n                res(value);\n              },\n              rej,\n            ]);\n            if (!handled) {\n              handled = true;\n              trySubmitting();\n            }\n          });\n        },\n        {\n          [HANDLE]: () => {\n            if (!handled) {\n              handled = true;\n              trySubmitting();\n            }\n          },\n        },\n      );\n    },\n  };\n}\n\nexport class Deferred<Value> {\n  readonly promise: Promise<Value>;\n  // @ts-ignore\n  readonly resolve: (value: Value | Promise<Value>) => void;\n  // @ts-ignore\n  readonly reject: (reason: unknown) => void;\n  constructor() {\n    this.promise = new Promise((res, rej) => {\n      // @ts-ignore\n      this.resolve = res;\n      // @ts-ignore\n      this.reject = rej;\n    });\n  }\n}\n", "import type { CacheMetadata } from './common';\n\nexport function logKey(key?: string) {\n  return key ? `for ${key} ` : '';\n}\n\nexport function assertCacheEntry(\n  entry: unknown,\n  key?: string,\n): asserts entry is {\n  metadata: CacheMetadata;\n  value: unknown;\n} {\n  if (!isRecord(entry)) {\n    throw new Error(\n      `Cache entry ${logKey(\n        key,\n      )}is not a cache entry object, it's a ${typeof entry}`,\n    );\n  }\n  if (\n    !isRecord(entry.metadata) ||\n    typeof entry.metadata.createdTime !== 'number' ||\n    (entry.metadata.ttl != null && typeof entry.metadata.ttl !== 'number') ||\n    (entry.metadata.swr != null && typeof entry.metadata.swr !== 'number')\n  ) {\n    throw new Error(\n      `Cache entry ${logKey(key)}does not have valid metadata property`,\n    );\n  }\n\n  if (!('value' in entry)) {\n    throw new Error(\n      `Cache entry for ${logKey(key)}does not have a value property`,\n    );\n  }\n}\n\nfunction isRecord(entry: unknown): entry is Record<string, unknown> {\n  return typeof entry === 'object' && entry !== null && !Array.isArray(entry);\n}\n", "import { CacheMetadata, staleWhileRevalidate } from './common';\n\nexport function shouldRefresh(\n  metadata: CacheMetadata,\n): 'now' | 'stale' | false {\n  if (metadata.ttl !== null) {\n    const valid = metadata.createdTime + (metadata.ttl || 0);\n    const stale = valid + (staleWhileRevalidate(metadata) || 0);\n    const now = Date.now();\n    if (now <= valid) {\n      return false;\n    }\n    if (now <= stale) {\n      return 'stale';\n    }\n\n    return 'now';\n  }\n  return false;\n}\n", "import type { Context } from './common';\nimport { MIGRATED } from './common';\n\nexport async function checkValue<Value>(\n  context: Context<Value>,\n  value: unknown,\n): Promise<\n  | { success: true; value: Value; migrated: boolean }\n  | { success: false; reason: unknown }\n> {\n  try {\n    const checkResponse = await context.checkValue(\n      value,\n      (value, updateCache = true) => ({\n        [MIGRATED]: updateCache,\n        value,\n      }),\n    );\n\n    if (typeof checkResponse === 'string') {\n      return { success: false, reason: checkResponse };\n    }\n\n    if (checkResponse == null || checkResponse === true) {\n      return {\n        success: true,\n        value: value as Value,\n        migrated: false,\n      };\n    }\n\n    if (checkResponse && typeof checkResponse[MIGRATED] === 'boolean') {\n      return {\n        success: true,\n        migrated: checkResponse[MIGRATED],\n        value: checkResponse.value,\n      };\n    }\n\n    return { success: false, reason: 'unknown' };\n  } catch (err) {\n    return {\n      success: false,\n      reason: err,\n    };\n  }\n}\n", "import { Context, CacheEntry } from './common';\nimport { assertCacheEntry } from './assertCacheEntry';\nimport { HANDLE } from './common';\nimport { shouldRefresh } from './shouldRefresh';\nimport { cachified } from './cachified';\nimport { Reporter } from './reporter';\nimport { checkValue } from './checkValue';\n\nexport const CACHE_EMPTY = Symbol();\nexport async function getCacheEntry<Value>(\n  { key, cache }: Pick<Context<Value>, 'key' | 'cache'>,\n  report: Reporter<Value>,\n): Promise<CacheEntry<unknown> | typeof CACHE_EMPTY> {\n  report({ name: 'getCachedValueStart' });\n  const cached = await cache.get(key);\n  report({ name: 'getCachedValueRead', entry: cached });\n  if (cached) {\n    assertCacheEntry(cached, key);\n    return cached;\n  }\n  return CACHE_EMPTY;\n}\n\nexport async function getCachedValue<Value>(\n  context: Context<Value>,\n  report: Reporter<Value>,\n  hasPendingValue: () => boolean,\n): Promise<Value | typeof CACHE_EMPTY> {\n  const {\n    key,\n    cache,\n    staleWhileRevalidate,\n    staleRefreshTimeout,\n    metadata,\n    getFreshValue: { [HANDLE]: handle },\n  } = context;\n  try {\n    const cached = await getCacheEntry(context, report);\n\n    if (cached === CACHE_EMPTY) {\n      report({ name: 'getCachedValueEmpty' });\n      return CACHE_EMPTY;\n    }\n\n    const refresh = shouldRefresh(cached.metadata);\n    const staleRefresh =\n      refresh === 'stale' ||\n      (refresh === 'now' && staleWhileRevalidate === Infinity);\n\n    if (refresh === 'now') {\n      report({ name: 'getCachedValueOutdated', ...cached });\n    }\n\n    if (staleRefresh) {\n      // refresh cache in background so future requests are faster\n      setTimeout(() => {\n        report({ name: 'refreshValueStart' });\n        void cachified({\n          ...context,\n          reporter: () => () => {},\n          getFreshValue({ metadata }) {\n            return context.getFreshValue({ metadata, background: true });\n          },\n          forceFresh: true,\n          fallbackToCache: false,\n        })\n          .then((value) => {\n            report({ name: 'refreshValueSuccess', value });\n          })\n          .catch((error) => {\n            report({ name: 'refreshValueError', error });\n          });\n      }, staleRefreshTimeout);\n    }\n\n    if (!refresh || staleRefresh) {\n      const valueCheck = await checkValue(context, cached.value);\n      if (valueCheck.success) {\n        report({\n          name: 'getCachedValueSuccess',\n          value: valueCheck.value,\n          migrated: valueCheck.migrated,\n        });\n        if (!staleRefresh) {\n          // Notify batch that we handled this call using cached value\n          handle?.();\n        }\n\n        if (valueCheck.migrated) {\n          setTimeout(async () => {\n            try {\n              const cached = await context.cache.get(context.key);\n\n              // Unless cached value was changed in the meantime or is about to\n              // change\n              if (\n                cached &&\n                cached.metadata.createdTime === metadata.createdTime &&\n                !hasPendingValue()\n              ) {\n                // update with migrated value\n                await context.cache.set(context.key, {\n                  ...cached,\n                  value: valueCheck.value,\n                });\n              }\n            } catch (err) {\n              /* ¯\\_(ツ)_/¯ */\n            }\n          }, 0);\n        }\n\n        return valueCheck.value;\n      } else {\n        report({ name: 'checkCachedValueErrorObj', reason: valueCheck.reason });\n        report({\n          name: 'checkCachedValueError',\n          reason:\n            valueCheck.reason instanceof Error\n              ? valueCheck.reason.message\n              : String(valueCheck.reason),\n        });\n\n        await cache.delete(key);\n      }\n    }\n  } catch (error: unknown) {\n    report({ name: 'getCachedValueError', error });\n\n    await cache.delete(key);\n  }\n\n  return CACHE_EMPTY;\n}\n", "import { Context, CacheMetadata, createCacheEntry } from './common';\nimport { getCacheEntry, CACHE_EMPTY } from './getCachedValue';\nimport { shouldRefresh } from './shouldRefresh';\nimport { Reporter } from './reporter';\nimport { checkValue } from './checkValue';\n\nexport async function getFreshValue<Value>(\n  context: Context<Value>,\n  metadata: CacheMetadata,\n  report: Reporter<Value>,\n): Promise<Value> {\n  const { fallbackToCache, key, getFreshValue, forceFresh, cache } = context;\n\n  let value: unknown;\n  try {\n    report({ name: 'getFreshValueStart' });\n    const freshValue = await getFreshValue({\n      metadata: context.metadata,\n      background: false,\n    });\n    value = freshValue;\n    report({ name: 'getFreshValueSuccess', value: freshValue });\n  } catch (error) {\n    report({ name: 'getFreshValueError', error });\n\n    // in case a fresh value was forced (and errored) we might be able to\n    // still get one from cache\n    if (forceFresh && fallbackToCache > 0) {\n      const entry = await getCacheEntry(context, report);\n      if (\n        entry === CACHE_EMPTY ||\n        entry.metadata.createdTime + fallbackToCache < Date.now()\n      ) {\n        throw error;\n      }\n      value = entry.value;\n      report({ name: 'getFreshValueCacheFallback', value });\n    } else {\n      // we are either not allowed to check the cache or already checked it\n      // nothing we can do anymore\n      throw error;\n    }\n  }\n\n  const valueCheck = await checkValue(context, value);\n  if (!valueCheck.success) {\n    report({ name: 'checkFreshValueErrorObj', reason: valueCheck.reason });\n    report({\n      name: 'checkFreshValueError',\n      reason:\n        valueCheck.reason instanceof Error\n          ? valueCheck.reason.message\n          : String(valueCheck.reason),\n    });\n\n    throw new Error(`check failed for fresh value of ${key}`, {\n      cause: valueCheck.reason,\n    });\n  }\n\n  try {\n    const write = shouldRefresh(metadata) !== 'now';\n    if (write) {\n      await cache.set(key, createCacheEntry(value, metadata));\n    }\n    report({\n      name: 'writeFreshValueSuccess',\n      metadata,\n      migrated: valueCheck.migrated,\n      written: write,\n    });\n  } catch (error: unknown) {\n    report({ name: 'writeFreshValueError', error });\n  }\n\n  return valueCheck.value;\n}\n", "import {\n  CachifiedOptions,\n  CachifiedOptionsWithSchema,\n  Cache,\n  CacheEntry,\n  createContext,\n} from './common';\nimport { CACHE_EMPTY, getCachedValue } from './getCachedValue';\nimport { getFreshValue } from './getFreshValue';\nimport { shouldRefresh } from './shouldRefresh';\n\n// This is to prevent requesting multiple fresh values in parallel\n// while revalidating or getting first value\n// Keys are unique per cache but may be used by multiple caches\nconst pendingValuesByCache = new WeakMap<Cache, Map<string, any>>();\n\nexport async function cachified<Value, InternalValue>(\n  options: CachifiedOptionsWithSchema<Value, InternalValue>,\n): Promise<Value>;\nexport async function cachified<Value>(\n  options: CachifiedOptions<Value>,\n): Promise<Value>;\nexport async function cachified<Value>(\n  options: CachifiedOptions<Value>,\n): Promise<Value> {\n  const context = createContext(options);\n  const { key, cache, forceFresh, report, metadata } = context;\n\n  // Register this cache\n  if (!pendingValuesByCache.has(cache)) {\n    pendingValuesByCache.set(cache, new Map());\n  }\n  const pendingValues: Map<\n    string,\n    CacheEntry<Promise<Value>> & { resolve: (value: Value) => void }\n  > = pendingValuesByCache.get(cache)!;\n\n  const hasPendingValue = () => {\n    return pendingValues.has(key);\n  };\n  const cachedValue = !forceFresh\n    ? await getCachedValue(context, report, hasPendingValue)\n    : CACHE_EMPTY;\n  if (cachedValue !== CACHE_EMPTY) {\n    report({ name: 'done', value: cachedValue });\n    return cachedValue;\n  }\n\n  if (pendingValues.has(key)) {\n    const { value: pendingRefreshValue, metadata } = pendingValues.get(key)!;\n    if (!shouldRefresh(metadata)) {\n      report({ name: 'getFreshValueHookPending' });\n      const value = await pendingRefreshValue;\n      report({ name: 'done', value });\n      return value;\n    }\n  }\n\n  let resolveFromFuture: (value: Value) => void;\n  const freshValue = Promise.race([\n    // try to get a fresh value\n    getFreshValue(context, metadata, report),\n    // or when a future call is faster, we'll take it's value\n    // this happens when getting value of first call takes longer then ttl + second response\n    new Promise<Value>((r) => {\n      resolveFromFuture = r;\n    }),\n  ]).finally(() => {\n    pendingValues.delete(key);\n  });\n\n  // here we inform past calls that we got a response\n  if (pendingValues.has(key)) {\n    const { resolve } = pendingValues.get(key)!;\n    freshValue.then((value) => resolve(value));\n  }\n\n  pendingValues.set(key, {\n    metadata,\n    value: freshValue,\n    // here we receive a fresh value from a future call\n    resolve: resolveFromFuture!,\n  });\n\n  const value = await freshValue;\n  report({ name: 'done', value });\n  return value;\n}\n", "import { Cache, createCacheEntry, staleWhileRevalidate } from './common';\nimport { CACHE_EMPTY, getCacheEntry } from './getCachedValue';\nimport { shouldRefresh } from './shouldRefresh';\n\ninterface SoftPurgeOpts {\n  cache: Cache;\n  key: string;\n  /**\n   * Force the entry to outdate after ms\n   */\n  staleWhileRevalidate?: number;\n  /**\n   * Force the entry to outdate after ms\n   */\n  swr?: number;\n}\n\nexport async function softPurge({\n  cache,\n  key,\n  ...swrOverwrites\n}: SoftPurgeOpts) {\n  const swrOverwrite = swrOverwrites.swr ?? swrOverwrites.staleWhileRevalidate;\n  const entry = await getCacheEntry({ cache, key }, () => {});\n\n  if (entry === CACHE_EMPTY || shouldRefresh(entry.metadata)) {\n    return;\n  }\n\n  const ttl = entry.metadata.ttl || Infinity;\n  const swr = staleWhileRevalidate(entry.metadata) || 0;\n  const lt = Date.now() - entry.metadata.createdTime;\n\n  await cache.set(\n    key,\n    createCacheEntry(entry.value, {\n      ttl: 0,\n      swr: swrOverwrite === undefined ? ttl + swr : swrOverwrite + lt,\n      createdTime: entry.metadata.createdTime,\n    }),\n  );\n}\n"], "mappings": ";;;AAgCO,IAAMA,IAAS,OAAO;AAAtB,IAKMC,IAAW,OAAO;AAyKxB,SAASC,EAAqB,EACnC,iBAAAC,GACA,UAAAC,GACA,YAAAC,GACA,GAAGC,EACL,GAA4C;AAC1C,MAAMC,IAAMD,EAAQ,OAAO,IAAA,GACrBE,IAAuBF,EAAQ,OAAOA,EAAQ,wBAAwB,GAStEG,IAAuB,EAC3B,YARA,OAAOJ,KAAe,aAClBA,IACA,OAAOA,KAAe,WACtB,CAACK,GAAOC,MACNN,EAAW,WAAWK,CAAK,EAAE,KAAME,OAAMD,EAAQC,GAAG,KAAK,CAAC,IAC5D,MAAM,MAIV,KAAAL,GACA,sBAAAC,GACA,iBACEL,MAAoB,QAChB,IACAA,MAAoB,QAAQA,MAAoB,SAChD,IAAA,IACAA,GACN,qBAAqB,GACrB,YAAY,OACZ,GAAGG,GACH,UAAUO,EAAoB,EAAE,KAAAN,GAAK,KAAKC,EAAqB,CAAC,EAClE,GAEMM,IACJV,IAAWK,CAAoB,MAC9B,MAAM;EAEP;AAEF,SAAO,EACL,GAAGA,GACH,QAAAK,EACF;AACF;AAEO,SAASN,EAAqBO,GAAwC;AAC3E,UACG,OAAOA,EAAS,MAAQ,MAAcA,EAAS,MAAMA,EAAS,QAAQ;AAE3E;AAEO,SAASC,EAASD,GAAkC;AACzD,SAAKA,IAGDA,EAAS,QAAQ,OACZ,IAAA,KAEDA,EAAS,OAAO,MAAMP,EAAqBO,CAAQ,KAAK,KALvD;AAMX;AAEO,SAASF,EAAoB,EAClC,KAAAN,IAAM,MACN,KAAAU,IAAM,GACN,aAAAC,IAAc,KAAK,IAAI,EACzB,IAAyC,CAAC,GAAG;AAC3C,SAAO,EACL,KAAKX,MAAQ,IAAA,IAAW,OAAOA,GAC/B,KAAKU,MAAQ,IAAA,IAAW,OAAOA,GAC/B,aAAAC,EACF;AACF;AAEO,SAASC,EACdT,GACAK,GACmB;AACnB,SAAO,EACL,OAAAL,GACA,UAAUG,EAAoBE,CAAQ,EACxC;AACF;ACpKA,IAAMK,IAAyBC,OAAe,GAAG,KAAK,MAAMA,CAAE,CAAC;AAC/D,SAASC,EACPP,GACAQ,GACA;AACA,MAAMN,IAAMT,EAAqBO,CAAQ;AACzC,SAAIA,EAAS,OAAO,QAAQE,KAAO,OAC1B,UACLF,EAAS,OAAO,OACZ,wBAAwBQ,EAAeR,EAAS,GAAG,CAAC,MACpD,EACN,KAGK,GAAGQ,EAAeR,EAAS,GAAG,CAAC,MAAMQ,EAAeN,CAAG,CAAC;AACjE;AAOO,SAASO,EAAuB,EACrC,gBAAAD,IAAiBH,GACjB,QAAAK,IAAS,SACT,aAAAC,IAAc,OAAO,eAAe,KACtC,IAAkB,CAAC,GAA0B;AAC3C,SAAO,CAAC,EAAE,KAAAC,GAAK,iBAAAxB,GAAiB,YAAAyB,GAAY,UAAAb,GAAU,OAAAc,EAAM,MAAM;AAChE,QAAMC,IACJD,EAAM,QACNA,EACG,SAAS,EACT,SAAS,EACT,QAAQ,qBAAqB,IAAI,GAClCE,GACAC,GACAC,GACAC;AAEJ,WAAQC,OAAU;AAChB,cAAQA,EAAM,MAAM;QAClB,KAAK;AACHJ,cAASI,EAAM;AACf;QACF,KAAK;AACHV,YAAO,KACL,oCAAoCE,CAAG;UAAaQ,EAAM,MAAM;0DAChEJ,CACF;AACA;QACF,KAAK;AACHN,YAAO,MACL,uBAAuBE,CAAG,6DAC1BQ,EAAM,KACR;AACA;QACF,KAAK;AACHV,YAAO,MACL,6BAA6BE,CAAG,WAChC,EAAE,iBAAAxB,GAAiB,YAAAyB,EAAW,GAC9BO,EAAM,KACR;AACA;QACF,KAAK;AACHF,cAAuBP,EAAY,IAAI;AACvC;QACF,KAAK,0BAA0B;AAC7B,cAAMU,IAAYV,EAAY,IAAI,IAAIO;AAClCE,YAAM,UACRV,EAAO,IACL,+BAA+BE,CAAG,KAClC,uCAAuCJ,EACrCa,CACF,CAAC,KACD,eAAed,EACbP,GACAQ,CACF,CAAC,OAAOO,CAAS,GACnB,IAEAL,EAAO,IACL,oCAAoCE,CAAG,KACvC,uCAAuCJ,EACrCa,CACF,CAAC,KACD,qCAAqCd,EACnCP,GACAQ,CACF,CAAC,EACH;AAEF;QACF;QACA,KAAK;AACHE,YAAO,MAAM,wBAAwBE,CAAG,IAAIQ,EAAM,KAAK;AACvD;QACF,KAAK;AACHH,cAAaG,EAAM;AACnB;QACF,KAAK;AACHV,YAAO,MACL,mCAAmCE,CAAG;UAAaQ,EAAM,MAAM,KAC/DH,CACF;AACA;QACF,KAAK;AACHE,cAAsBR,EAAY,IAAI;AACtC;QACF,KAAK;AACHD,YAAO,IACL,0BAA0BE,CAAG,gBAC7B,uCAAuCJ,EACrCG,EAAY,IAAI,IAAIQ,CACtB,CAAC,KACD,eAAeZ,EACbP,GACAQ,CACF,CAAC,OAAOO,CAAS,GACnB;AACA;QACF,KAAK;AACHL,YAAO,IAAI,0BAA0BE,CAAG,YAAYQ,EAAM,KAAK;AAC/D;MACJ;IACF;EACF;AACF;AAEO,SAASE,KACXC,GACoB;AACvB,SAAQC,OAAY;AAClB,QAAMnC,IAAWkC,EAAU,IAAK,OAAM,IAAIC,CAAO,CAAC;AAClD,WAAQJ,OAAU;AAChB/B,QAAS,QAASoC,OAAMA,IAAIL,CAAK,CAAC;IACpC;EACF;AACF;ACzPO,SAASM,EAAgBC,GAA8B;AAC5D,SAAO,EACL,MAAMA,EAAS,QAAQ,OACvB,IAAIf,GAAKjB,GAAO;AACd,QAAMH,IAAMS,EAASN,GAAO,QAAQ;AACpC,WAAOgC,EAAS,IAAIf,GAAKjB,GAAO,EAC9B,KAAKH,MAAQ,IAAA,IAAW,SAAYA,GACpC,OAAOG,GAAO,UAAU,YAC1B,CAAC;EACH,GACA,IAAIiB,GAAK;AACP,WAAOe,EAAS,IAAIf,CAAG;EACzB,GACA,OAAOA,GAAK;AACV,WAAOe,EAAS,OAAOf,CAAG;EAC5B,EACF;AACF;AAsBO,SAASgB,EAAmBC,GAAoC;AACrE,SAAO,EACL,MAAMA,EAAW,QAAQ,UACzB,IAAIjB,GAAKjB,GAAO;AACd,WAAO,IAAI,QAAc,CAACmC,GAAKC,MAAQ;AACrC,UAAMvC,IAAMS,EAASN,GAAO,QAAQ,GAC9BQ,IAAcR,GAAO,UAAU,aAC/BqC,IAAMC,OAAiB;AAC3B,YAAIA,EACF,QAAOF,EAAIE,CAAG;AAEhBH,UAAI;MACN;AAEItC,UAAM,KAAKA,IAAM,IAAA,KAAY,OAAOW,KAAgB,WACtD0B,EACG,MAAM,EACN,IAAIjB,GAAK,KAAK,UAAUjB,CAAK,CAAC,EAC9B,SAASiB,IAAMpB,IAAMW,KAAe,GAAI,EACxC,KAAK6B,CAAE,IAEVH,EAAW,IAAIjB,GAAK,KAAK,UAAUjB,CAAK,GAAGqC,CAAE;IAEjD,CAAC;EACH,GACA,IAAIpB,GAAK;AACP,WAAO,IAAI,QAAuC,CAACkB,GAAKC,MAAQ;AAC9DF,QAAW,IAAIjB,GAAK,CAACqB,GAAKC,MAAU;AAClC,YAAID,EACFF,GAAIE,CAAG;iBACEC,KAAS,KAClBJ,GAAI,IAAI;YAER,KAAI;AACFA,YAAI,KAAK,MAAMI,CAAK,CAAC;QACvB,SAASD,GAAK;AACZF,YAAIE,CAAG;QACT;MAEJ,CAAC;IACH,CAAC;EACH,GACA,OAAOrB,GAAK;AACV,WAAO,IAAI,QAAc,CAACkB,GAAKC,MAAQ;AACrCF,QAAW,IAAIjB,GAAMqB,OAAQ;AACvBA,aACFF,EAAIE,CAAG,GAETH,EAAI;MACN,CAAC;IACH,CAAC;EACH,EACF;AACF;AAaO,SAASK,EAAkBN,GAAmC;AACnE,SAAO,EACL,MAAMA,EAAW,QAAQ,SACzB,IAAIjB,GAAKjB,GAAO;AACd,QAAMH,IAAMS,EAASN,GAAO,QAAQ,GAC9BQ,IAAcR,GAAO,UAAU;AAErC,WAAOkC,EAAW,IAChBjB,GACA,KAAK,UAAUjB,CAAK,GACpBH,IAAM,KAAKA,IAAM,IAAA,KAAY,OAAOW,KAAgB,WAChD,EACE,MAAM,KAAK,MAAMX,IAAMW,KAAe,GAAI,EAC5C,IACA,MACN;EACF,GACA,MAAM,IAAIS,GAAK;AACb,QAAMjB,IAAQ,MAAMkC,EAAW,IAAIjB,CAAG;AACtC,WAAIjB,KAAS,OACJ,OAEF,KAAK,MAAMA,CAAK;EACzB,GACA,OAAOiB,GAAK;AACV,WAAOiB,EAAW,IAAIjB,CAAG;EAC3B,EACF;AACF;ACrHO,SAASwB,EACdC,GACAC,IAAsB,MAItB;AACA,MAAMC,IAIA,CAAC,GAEHC,IAAQ,GACRC,IAAY,OACVC,IAAa,IAAIC,KAEjBC,IAAkB,MAAM;AAC5B,QAAIH,EACF,OAAM,IAAI,MAAM,uCAAuC;EAE3D,GAEMI,IAAS,YAAY;AACzB,QAAIL,MAAU,EACZ,QAAAF,IAAa,MACNI,EAAW;AAKpB,QAHAE,EAAgB,GAChBH,IAAY,MAERF,EAAS,WAAW,GAAG;AACzBG,QAAW,QAAQ;AACnB;IACF;AAEA,QAAI;AAAA,OACc,MAAM,QAAQ,QAC5BL,EAAeE,EAAS,IAAI,CAAC,CAACO,CAAK,MAAMA,CAAK,CAAC,CACjD,GACQ,QAAQ,CAACnD,GAAOoD,MAAUR,EAASQ,CAAK,EAAE,CAAC,EAAEpD,CAAK,CAAC,GAC3D+C,EAAW,QAAQ;IACrB,SAAST,GAAK;AACZM,QAAS,QAAQ,CAAC,CAACS,GAAGC,GAAIlB,CAAG,MAAMA,EAAIE,CAAG,CAAC,GAC3CS,EAAW,QAAQ;IACrB;EACF,GAEMQ,IAAgB,MAAM;AAC1BV,SACIF,MAAe,SAGnBO,EAAO;EACT;AAEA,SAAO,EACL,GAAIP,MAAe,QAAQ,EAAE,QAAAO,EAAO,IAAI,CAAC,GACzC,IAAIC,GAAOK,GAAS;AAClBP,MAAgB,GAChBJ;AACA,QAAIY,IAAU;AAEd,WAAO,OAAO,OACX5B,OACQ,IAAI,QAAe,CAACM,GAAKC,MAAQ;AACtCQ,QAAS,KAAK,CACZO,GACCnD,OAAU;AACTwD,YAAU,EAAE,GAAG3B,GAAS,OAAA7B,EAAM,CAAC,GAC/BmC,EAAInC,CAAK;MACX,GACAoC,CACF,CAAC,GACIqB,MACHA,IAAU,MACVF,EAAc;IAElB,CAAC,GAEH,EACE,CAACjE,CAAM,GAAG,MAAM;AACTmE,YACHA,IAAU,MACVF,EAAc;IAElB,EACF,CACF;EACF,EACF;AACF;AAEO,IAAMP,IAAN,MAAsB;EAM3B,cAAc;AACZ,SAAK,UAAU,IAAI,QAAQ,CAACb,GAAKC,MAAQ;AAEvC,WAAK,UAAUD,GAEf,KAAK,SAASC;IAChB,CAAC;EACH;AACF;ACnIO,SAASsB,EAAOzC,GAAc;AACnC,SAAOA,IAAM,OAAOA,CAAG,MAAM;AAC/B;AAEO,SAAS0C,EACdC,GACA3C,GAIA;AACA,MAAI,CAAC4C,EAASD,CAAK,EACjB,OAAM,IAAI,MACR,eAAeF,EACbzC,CACF,CAAC,uCAAuC,OAAO2C,CAAK,EACtD;AAEF,MACE,CAACC,EAASD,EAAM,QAAQ,KACxB,OAAOA,EAAM,SAAS,eAAgB,YACrCA,EAAM,SAAS,OAAO,QAAQ,OAAOA,EAAM,SAAS,OAAQ,YAC5DA,EAAM,SAAS,OAAO,QAAQ,OAAOA,EAAM,SAAS,OAAQ,SAE7D,OAAM,IAAI,MACR,eAAeF,EAAOzC,CAAG,CAAC,uCAC5B;AAGF,MAAI,EAAE,WAAW2C,GACf,OAAM,IAAI,MACR,mBAAmBF,EAAOzC,CAAG,CAAC,gCAChC;AAEJ;AAEA,SAAS4C,EAASD,GAAkD;AAClE,SAAO,OAAOA,KAAU,YAAYA,MAAU,QAAQ,CAAC,MAAM,QAAQA,CAAK;AAC5E;ACtCO,SAASE,EACdzD,GACyB;AACzB,MAAIA,EAAS,QAAQ,MAAM;AACzB,QAAM0D,IAAQ1D,EAAS,eAAeA,EAAS,OAAO,IAChD2D,IAAQD,KAASjE,EAAqBO,CAAQ,KAAK,IACnD4D,IAAM,KAAK,IAAI;AACrB,WAAIA,KAAOF,IACF,QAELE,KAAOD,IACF,UAGF;EACT;AACA,SAAO;AACT;AChBA,eAAsBrE,EACpBkC,GACA7B,GAIA;AACA,MAAI;AACF,QAAMkE,IAAgB,MAAMrC,EAAQ,WAClC7B,GACA,CAACA,GAAOmE,IAAc,UAAU,EAC9B,CAAC5E,CAAQ,GAAG4E,GACZ,OAAAnE,EACF,EACF;AAEA,WAAI,OAAOkE,KAAkB,WACpB,EAAE,SAAS,OAAO,QAAQA,EAAc,IAG7CA,KAAiB,QAAQA,MAAkB,OACtC,EACL,SAAS,MACT,OAAOlE,GACP,UAAU,MACZ,IAGEkE,KAAiB,OAAOA,EAAc3E,CAAQ,KAAM,YAC/C,EACL,SAAS,MACT,UAAU2E,EAAc3E,CAAQ,GAChC,OAAO2E,EAAc,MACvB,IAGK,EAAE,SAAS,OAAO,QAAQ,UAAU;EAC7C,SAAS5B,GAAK;AACZ,WAAO,EACL,SAAS,OACT,QAAQA,EACV;EACF;AACF;ACtCO,IAAM8B,IAAc,OAAO;AAClC,eAAsBC,EACpB,EAAE,KAAApD,GAAK,OAAAE,EAAM,GACbf,GACmD;AACnDA,IAAO,EAAE,MAAM,sBAAsB,CAAC;AACtC,MAAMiB,IAAS,MAAMF,EAAM,IAAIF,CAAG;AAElC,SADAb,EAAO,EAAE,MAAM,sBAAsB,OAAOiB,EAAO,CAAC,GAChDA,KACFsC,EAAiBtC,GAAQJ,CAAG,GACrBI,KAEF+C;AACT;AAEA,eAAsBE,EACpBzC,GACAzB,GACAmE,GACqC;AACrC,MAAM,EACJ,KAAAtD,GACA,OAAAE,GACA,sBAAArB,GACA,qBAAA0E,GACA,UAAAnE,GACA,eAAe,EAAE,CAACf,CAAM,GAAGmF,EAAO,EACpC,IAAI5C;AACJ,MAAI;AACF,QAAMR,IAAS,MAAMgD,EAAcxC,GAASzB,CAAM;AAElD,QAAIiB,MAAW+C,EACb,QAAAhE,EAAO,EAAE,MAAM,sBAAsB,CAAC,GAC/BgE;AAGT,QAAMM,IAAUZ,EAAczC,EAAO,QAAQ,GACvCsD,IACJD,MAAY,WACXA,MAAY,SAAS5E,MAAyB,IAAA;AA4BjD,QA1BI4E,MAAY,SACdtE,EAAO,EAAE,MAAM,0BAA0B,GAAGiB,EAAO,CAAC,GAGlDsD,KAEF,WAAW,MAAM;AACfvE,QAAO,EAAE,MAAM,oBAAoB,CAAC,GAC/BwE,EAAU,EACb,GAAG/C,GACH,UAAU,MAAM,MAAM;MAAC,GACvB,cAAc,EAAE,UAAAxB,EAAS,GAAG;AAC1B,eAAOwB,EAAQ,cAAc,EAAE,UAAAxB,GAAU,YAAY,KAAK,CAAC;MAC7D,GACA,YAAY,MACZ,iBAAiB,MACnB,CAAC,EACE,KAAML,OAAU;AACfI,UAAO,EAAE,MAAM,uBAAuB,OAAAJ,EAAM,CAAC;MAC/C,CAAC,EACA,MAAO6E,OAAU;AAChBzE,UAAO,EAAE,MAAM,qBAAqB,OAAAyE,EAAM,CAAC;MAC7C,CAAC;IACL,GAAGL,CAAmB,GAGpB,CAACE,KAAWC,GAAc;AAC5B,UAAMG,IAAa,MAAMnF,EAAWkC,GAASR,EAAO,KAAK;AACzD,UAAIyD,EAAW,QACb,QAAA1E,EAAO,EACL,MAAM,yBACN,OAAO0E,EAAW,OAClB,UAAUA,EAAW,SACvB,CAAC,GACIH,KAEHF,IAAS,GAGPK,EAAW,YACb,WAAW,YAAY;AACrB,YAAI;AACF,cAAMzD,IAAS,MAAMQ,EAAQ,MAAM,IAAIA,EAAQ,GAAG;AAKhDR,eACAA,EAAO,SAAS,gBAAgBhB,EAAS,eACzC,CAACkE,EAAgB,KAGjB,MAAM1C,EAAQ,MAAM,IAAIA,EAAQ,KAAK,EACnC,GAAGR,GACH,OAAOyD,EAAW,MACpB,CAAC;QAEL,QAAc;QAEd;MACF,GAAG,CAAC,GAGCA,EAAW;AAElB1E,QAAO,EAAE,MAAM,4BAA4B,QAAQ0E,EAAW,OAAO,CAAC,GACtE1E,EAAO,EACL,MAAM,yBACN,QACE0E,EAAW,kBAAkB,QACzBA,EAAW,OAAO,UAClB,OAAOA,EAAW,MAAM,EAChC,CAAC,GAED,MAAM3D,EAAM,OAAOF,CAAG;IAE1B;EACF,SAAS4D,GAAgB;AACvBzE,MAAO,EAAE,MAAM,uBAAuB,OAAAyE,EAAM,CAAC,GAE7C,MAAM1D,EAAM,OAAOF,CAAG;EACxB;AAEA,SAAOmD;AACT;AC/HA,eAAsBW,EACpBlD,GACAxB,GACAD,GACgB;AAChB,MAAM,EAAE,iBAAAX,GAAiB,KAAAwB,GAAK,eAAA8D,GAAe,YAAA7D,GAAY,OAAAC,EAAM,IAAIU,GAE/D7B;AACJ,MAAI;AACFI,MAAO,EAAE,MAAM,qBAAqB,CAAC;AACrC,QAAMkB,IAAa,MAAMyD,EAAc,EACrC,UAAUlD,EAAQ,UAClB,YAAY,MACd,CAAC;AACD7B,QAAQsB,GACRlB,EAAO,EAAE,MAAM,wBAAwB,OAAOkB,EAAW,CAAC;EAC5D,SAASuD,GAAO;AAKd,QAJAzE,EAAO,EAAE,MAAM,sBAAsB,OAAAyE,EAAM,CAAC,GAIxC3D,KAAczB,IAAkB,GAAG;AACrC,UAAMmE,IAAQ,MAAMS,EAAcxC,GAASzB,CAAM;AACjD,UACEwD,MAAUQ,KACVR,EAAM,SAAS,cAAcnE,IAAkB,KAAK,IAAI,EAExD,OAAMoF;AAER7E,UAAQ4D,EAAM,OACdxD,EAAO,EAAE,MAAM,8BAA8B,OAAAJ,EAAM,CAAC;IACtD,MAGE,OAAM6E;EAEV;AAEA,MAAMC,IAAa,MAAMnF,EAAWkC,GAAS7B,CAAK;AAClD,MAAI,CAAC8E,EAAW,QACd,OAAA1E,EAAO,EAAE,MAAM,2BAA2B,QAAQ0E,EAAW,OAAO,CAAC,GACrE1E,EAAO,EACL,MAAM,wBACN,QACE0E,EAAW,kBAAkB,QACzBA,EAAW,OAAO,UAClB,OAAOA,EAAW,MAAM,EAChC,CAAC,GAEK,IAAI,MAAM,mCAAmC7D,CAAG,IAAI,EACxD,OAAO6D,EAAW,OACpB,CAAC;AAGH,MAAI;AACF,QAAME,IAAQlB,EAAczD,CAAQ,MAAM;AACtC2E,SACF,MAAM7D,EAAM,IAAIF,GAAKR,EAAiBT,GAAOK,CAAQ,CAAC,GAExDD,EAAO,EACL,MAAM,0BACN,UAAAC,GACA,UAAUyE,EAAW,UACrB,SAASE,EACX,CAAC;EACH,SAASH,GAAgB;AACvBzE,MAAO,EAAE,MAAM,wBAAwB,OAAAyE,EAAM,CAAC;EAChD;AAEA,SAAOC,EAAW;AACpB;AC9DA,IAAMG,IAAuB,oBAAI;AAQjC,eAAsBL,EACpBhF,GACgB;AAChB,MAAMiC,IAAUrC,EAAcI,CAAO,GAC/B,EAAE,KAAAqB,GAAK,OAAAE,GAAO,YAAAD,GAAY,QAAAd,GAAQ,UAAAC,EAAS,IAAIwB;AAGhDoD,IAAqB,IAAI9D,CAAK,KACjC8D,EAAqB,IAAI9D,GAAO,oBAAI,KAAK;AAE3C,MAAM+D,IAGFD,EAAqB,IAAI9D,CAAK,GAK5BgE,IAAejE,IAEjBkD,IADA,MAAME,EAAezC,GAASzB,GAJV,MACf8E,EAAc,IAAIjE,CAAG,CAG2B;AAEzD,MAAIkE,MAAgBf,EAClB,QAAAhE,EAAO,EAAE,MAAM,QAAQ,OAAO+E,EAAY,CAAC,GACpCA;AAGT,MAAID,EAAc,IAAIjE,CAAG,GAAG;AAC1B,QAAM,EAAE,OAAOmE,GAAqB,UAAA/E,EAAS,IAAI6E,EAAc,IAAIjE,CAAG;AACtE,QAAI,CAAC6C,EAAczD,CAAQ,GAAG;AAC5BD,QAAO,EAAE,MAAM,2BAA2B,CAAC;AAC3C,UAAMJ,IAAQ,MAAMoF;AACpB,aAAAhF,EAAO,EAAE,MAAM,QAAQ,OAAAJ,EAAM,CAAC,GACvBA;IACT;EACF;AAEA,MAAIqF,GACE/D,IAAa,QAAQ,KAAK,CAE9ByD,EAAclD,GAASxB,GAAUD,CAAM,GAGvC,IAAI,QAAgB0B,OAAM;AACxBuD,QAAoBvD;EACtB,CAAC,CACH,CAAC,EAAE,QAAQ,MAAM;AACfoD,MAAc,OAAOjE,CAAG;EAC1B,CAAC;AAGD,MAAIiE,EAAc,IAAIjE,CAAG,GAAG;AAC1B,QAAM,EAAE,SAAAqE,EAAQ,IAAIJ,EAAc,IAAIjE,CAAG;AACzCK,MAAW,KAAMtB,OAAUsF,EAAQtF,CAAK,CAAC;EAC3C;AAEAkF,IAAc,IAAIjE,GAAK,EACrB,UAAAZ,GACA,OAAOiB,GAEP,SAAS+D,EACX,CAAC;AAED,MAAMrF,IAAQ,MAAMsB;AACpB,SAAAlB,EAAO,EAAE,MAAM,QAAQ,OAAAJ,EAAM,CAAC,GACvBA;AACT;ACtEA,eAAsBuF,EAAU,EAC9B,OAAApE,GACA,KAAAF,GACA,GAAGuE,EACL,GAAkB;AAChB,MAAMC,IAAeD,EAAc,OAAOA,EAAc,sBAClD5B,IAAQ,MAAMS,EAAc,EAAE,OAAAlD,GAAO,KAAAF,EAAI,GAAG,MAAM;EAAC,CAAC;AAE1D,MAAI2C,MAAUQ,KAAeN,EAAcF,EAAM,QAAQ,EACvD;AAGF,MAAM/D,IAAM+D,EAAM,SAAS,OAAO,IAAA,GAC5BrD,IAAMT,EAAqB8D,EAAM,QAAQ,KAAK,GAC9C8B,IAAK,KAAK,IAAI,IAAI9B,EAAM,SAAS;AAEvC,QAAMzC,EAAM,IACVF,GACAR,EAAiBmD,EAAM,OAAO,EAC5B,KAAK,GACL,KAAK6B,MAAiB,SAAY5F,IAAMU,IAAMkF,IAAeC,GAC7D,aAAa9B,EAAM,SAAS,YAC9B,CAAC,CACH;AACF;", "names": ["HANDLE", "MIGRATED", "createContext", "fallback<PERSON><PERSON><PERSON><PERSON>", "reporter", "checkValue", "options", "ttl", "staleWhileRevalidate", "contextWithoutReport", "value", "migrate", "v", "createCacheMetaData", "report", "metadata", "totalTtl", "swr", "createdTime", "createCacheEntry", "defaultFormatDuration", "ms", "formatCacheTime", "formatDuration", "verbose<PERSON><PERSON><PERSON><PERSON>", "logger", "performance", "key", "forceFresh", "cache", "cacheName", "cached", "freshValue", "getFreshValueStartTs", "refreshValueStartTS", "event", "totalTime", "mergeReporters", "reporters", "context", "r", "lruCacheAdapter", "lruCache", "redis3CacheAdapter", "redisCache", "res", "rej", "cb", "err", "reply", "redisCacheAdapter", "createBatch", "getFreshV<PERSON>ues", "autoSubmit", "requests", "count", "submitted", "submission", "Deferred", "checkSubmission", "submit", "param", "index", "_", "__", "trySubmitting", "onValue", "handled", "log<PERSON><PERSON>", "assertCacheEntry", "entry", "isRecord", "shouldRefresh", "valid", "stale", "now", "checkResponse", "updateCache", "CACHE_EMPTY", "getCacheEntry", "getCachedValue", "hasPendingValue", "staleRefreshTimeout", "handle", "refresh", "staleRefresh", "cachified", "error", "valueCheck", "getFreshValue", "write", "pendingValuesByCache", "pendingValues", "cachedValue", "pendingRefreshValue", "resolveFromFuture", "resolve", "softPurge", "swrOverwrites", "swrOverwrite", "lt"]}