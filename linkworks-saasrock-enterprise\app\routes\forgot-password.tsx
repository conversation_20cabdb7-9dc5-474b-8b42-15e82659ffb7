import Logo from "~/components/brand/Logo";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import ErrorModal, { RefErrorModal } from "~/components/ui/modals/ErrorModal";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, Form, useActionData, useNavigation } from "react-router";
import { ActionFunction, LoaderFunctionArgs, MetaFunction } from "react-router";
import crypto from "crypto";
import { getUserByEmail, updateUserVerifyToken } from "~/utils/db/users.db.server";
import { getTranslations } from "~/locale/i18next.server";
import { sendEmail } from "~/modules/emails/services/EmailService";
import SuccessModal, { RefSuccessModal } from "~/components/ui/modals/SuccessModal";
import { getBaseURL } from "~/utils/url.server";
import ExclamationTriangleIcon from "~/components/ui/icons/ExclamationTriangleIcon";
import SuccessBanner from "~/components/ui/banners/SuccessBanner";
import { getLinkTags } from "~/modules/pageBlocks/services/.server/pagesService";
import { getDefaultSiteTags } from "~/modules/pageBlocks/utils/defaultSeoMetaTags";
import InputText from "~/components/ui/input/InputText";
import EmailTemplates from "~/modules/emails/utils/EmailTemplates";
import { getAppConfiguration } from "~/utils/db/appConfiguration.db.server";
import { BackArrowIcon, BoxSvg, LockIconSvg } from "~/custom/components/OnBoarding/Svg";
import { LeftSection } from "~/custom/components/OnBoarding/LeftSection";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.metatags || [];
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { t } = await getTranslations(request);

  return {
    metatags: [{ title: `${t("account.forgot.title")} | ${getDefaultSiteTags().title}` }, ...getLinkTags(request)],
  };
};

type ActionData = {
  success?: string;
  error?: string;
};
const badRequest = (data: ActionData) => Response.json(data, { status: 400 });
export const action: ActionFunction = async ({ request }) => {
  const form = await request.formData();
  const email = form.get("email")?.toString();

  if (!email) {
    return badRequest({
      error: "Email required",
    });
  }

  const user = await getUserByEmail(email);
  // const ipError = await IpAddressServiceServer.log(request, {
  //   action: "forgot-password",
  //   description: email,
  //   block: user === null ? "User not found" : undefined,
  // }).catch((e) => e.message);
  // if (ipError) {
  //   return Response.json({ error: ipError }, { status: 400 });
  // }
  if (!user) {
    // Do not show that the email was not found, fake wait
    await new Promise((resolve) => setTimeout(resolve, 1500));
    return Response.json({ success: "Email sent" });
  }

  var verifyToken = crypto.randomBytes(20).toString("hex");
  await updateUserVerifyToken({ verifyToken }, user.id);
  await sendEmail({
    request,
    to: email,
    alias: "password-reset",
    ...EmailTemplates.PASSWORD_RESET_EMAIL.parse({
      appConfiguration: await getAppConfiguration({ request }),
      data: {
        name: user.firstName,
        action_url: new URL(getBaseURL(request) + `/reset?e=${encodeURIComponent(email)}&t=${verifyToken}`).toString(),
      },
    }),
  });

  return Response.json({
    success: "Email sent",
  });
};

export default function ForgotPasswordRoute() {
  const { t } = useTranslation();
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();

  const errorModal = useRef<RefErrorModal>(null);
  const successModal = useRef<RefSuccessModal>(null);

  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    if (actionData?.error) {
      errorModal.current?.show(actionData.error);
    }
    if (actionData?.success) {
      setEmailSent(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actionData]);

  return (
    <div>
      <div className="flex w-full flex-col md:space-x-4 lg:flex-row">
        {/* Left Section - Hidden on mobile */}
        <div className="hidden lg:block lg:w-[60%]">
          <LeftSection />
        </div>

        {/* Right Section */}
        <div className="flex min-h-screen w-full flex-col justify-between py-6 lg:w-[40%] lg:py-10">
          <BoxSvg className="rotate-180" />

          {/* Form Container */}
          <div className="flex flex-1 items-center justify-center px-4 lg:px-0">
            <div className="flex w-full max-w-[365px] flex-col gap-6 lg:max-w-[500px] lg:gap-7">
              {/* Header */}
              <div className="flex w-full flex-col items-start gap-3">
                <LockIconSvg className="h-6 w-6" />
                <h1 className="text-foreground w-full text-2xl font-medium lg:text-[28px] lg:leading-[100%]">Forgot Password</h1>
              </div>

              {/* Form */}
              <Form method="post" className="flex w-full flex-col items-center gap-4 lg:min-h-[207px] lg:gap-4.5">
                {/* Email Input */}
                <div className="flex w-full flex-col items-start gap-1">
                  <InputText
                    title={t("account.shared.email")}
                    id="email"
                    name="email"
                    type="email"
                    className="placeholder:text-muted-foreground/45 focus:ring-foreground mt-2 h-10 w-full rounded-[4px] border border-none text-sm focus:ring-1 focus:outline-none lg:h-8 lg:text-xs lg:leading-4 lg:tracking-[0.12px]"
                    placeholder="John@"
                    required
                    defaultValue=""
                  />
                </div>

                {/* Continue Button */}
                <div className="bg-primary mt-10 w-full rounded-[4px] lg:mt-10">
                  <LoadingButton
                    type="submit"
                    className="bg-primary text-background hover:bg-primary flex h-10 w-full items-center justify-center text-sm font-bold transition-all duration-200 lg:h-8 lg:text-xs"
                  >
                    {t("account.newPassword.continueButton")}
                  </LoadingButton>
                </div>

                {/* Divider */}
                <div className="mt-4 flex h-5 items-center justify-center gap-2.5 lg:mt-4.5 lg:h-[21px]">
                  <div className="bg-input h-px w-10 lg:w-[45px]" />
                  <span className="text-foreground text-xs lg:text-[13px] lg:leading-[21px]">(Or)</span>
                  <div className="bg-input h-px w-10 lg:w-[45px]" />
                </div>

                {/* Back to Login Button */}
                <button
                  aria-label="Go back to login page"
                  className="bg-background text-primary mt-3 flex h-10 w-full items-center justify-center gap-2 rounded px-2 transition-all duration-150 focus:ring-0 lg:mt-4 lg:h-8"
                >
                  <BackArrowIcon className="h-3 w-3" />
                  <Link to="/login" className="text-primary text-xs lg:leading-[21px]">
                    {t("account.register.clickHereToLogin")}
                  </Link>
                </button>

                {/* Error Message */}
                <div id="form-error-message">
                  {actionData?.error && navigation.state === "idle" ? (
                    <div className="text-destructive flex items-center justify-center space-x-2 text-sm">
                      <ExclamationTriangleIcon className="h-4 w-4" />
                      <div>{actionData.error}</div>
                    </div>
                  ) : null}
                </div>
              </Form>

              {/* Success Message */}
              {emailSent && (
                <div className="mt-6 lg:mt-8">
                  <SuccessBanner title={t("account.reset.resetSuccess")} text={t("account.reset.emailSent")} />
                </div>
              )}
            </div>
          </div>

          <BoxSvg className="rotate-180 lg:-ml-22" />
        </div>
      </div>

      <SuccessModal ref={successModal} />
      <ErrorModal ref={errorModal} />
    </div>
  );
}
