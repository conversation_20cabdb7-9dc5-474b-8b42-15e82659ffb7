import { StepFormWizardFilter, StepFormWizardSession } from "@prisma/client";
import { getAppConfiguration } from "~/utils/db/appConfiguration.db.server";
import { getAllEntities } from "~/utils/db/entities/entities.db.server";
import { getAllRoles } from "~/utils/db/permissions/roles.db.server";
import { getAllSubscriptionProducts } from "~/utils/db/subscriptionProducts.db.server";
import { adminGetAllTenants, getAllTenantUsers } from "~/utils/db/tenants.db.server";
import { adminGetAllUsers, UserSimple } from "~/utils/db/users.db.server";
import { createStepFormWizardSession, getStepFormWizard, getStepFormWizards, StepFormWizardWithDetails, updateStepFormWizard } from "../db/stepFormWizard.db.server";
import { getStepFormWizardSession, getStepFormWizardSessionsByUser, StepFormWizardSessionWithDetails } from "../db/stepFormWizardSessions.db.server";
import { StepFormWizardCandidateDto } from "../dtos/StepFormWizardCandidateDto";
import { StepFormWizardFilterMetadataDto } from "../dtos/StepFormWizardFilterMetadataDto";
import { StepFormWizardFilterType } from "../dtos/StepFormWizardFilterTypes";
import StepFormWizardFiltersService from "./StepFormWizardFiltersService";
import StepFormWizardSessionService from "./StepFormWizardSessionService";

async function getUserActiveStepFormWizard({
  userId,
  tenantId,
  request,
}: {
  userId: string;
  tenantId: string | null;
  request: Request;
}): Promise<StepFormWizardSessionWithDetails | null> {
  const appConfiguration = await getAppConfiguration({ request });
  if (!appConfiguration.stepFormWizard.enabled) {
    return null;
  }
  let currentSession: StepFormWizardSessionWithDetails | null = null;

  const allUserStepFormWizards = await getStepFormWizardSessionsByUser({ userId, tenantId });
  const activeStepFormWizards = allUserStepFormWizards.filter((f) => f.status === "active" || f.status === "started");
  // If there is an active stepFormWizard, return it
  if (activeStepFormWizards.length > 0) {
    currentSession = activeStepFormWizards[0];
    if (currentSession.status === "active") {
      await StepFormWizardSessionService.started({ session: currentSession, request });
    }
  }
  // If there is no active stepFormWizard, check if this user belongs to an active stepFormWizard
  else {
    const stepFormWizardSessions = await generateMatchingStepFormWizards({ userId, tenantId });
    await Promise.all(
      stepFormWizardSessions.map(async (session) => {
        if (!currentSession && (session?.status === "active" || session?.status === "started")) {
          currentSession = await getStepFormWizardSession(session.id);
          if (currentSession && currentSession.status === "active") {
            await StepFormWizardSessionService.started({ session: currentSession, request });
          }
        }
      })
    );
  }

  if (!currentSession?.stepFormWizard.active) {
    return null;
  }
  return currentSession;
}

async function generateMatchingStepFormWizards({ userId, tenantId }: { userId: string; tenantId: string | null }): Promise<(StepFormWizardSession | undefined)[]> {
  const allStepFormWizards = await getStepFormWizards({
    realtime: true,
    active: true,
  });
  return await Promise.all(
    allStepFormWizards.map(async (stepFormWizard) => {
      const existingSession = stepFormWizard.sessions.find((f) => f.userId === userId);
      if (existingSession) {
        return;
      }
      if (stepFormWizard.filters.length === 0) {
        return;
      }
      const matchingFilters = await getMatchingFilters({ userId, tenantId, filters: stepFormWizard.filters });
      if (matchingFilters.length === stepFormWizard.filters.length) {
        return await createStepFormWizardSession(stepFormWizard, {
          userId,
          tenantId,
          status: "active",
          matchingFilters,
          createdRealtime: true,
        });
      }
    })
  );
}

async function getMatchingFilters({
  userId,
  tenantId,
  filters,
}: {
  userId: string;
  tenantId: string | null;
  filters: StepFormWizardFilter[];
}): Promise<StepFormWizardFilter[]> {
  const matchedFilters: StepFormWizardFilter[] = [];
  await Promise.all(
    filters.map(async (filter) => {
      if (await StepFormWizardFiltersService.matches({ userId, tenantId, filter })) {
        matchedFilters.push(filter);
      }
    })
  );
  return matchedFilters;
}

async function setStepFormWizardStatus(id: string, active: boolean): Promise<void> {
  const stepFormWizard = await getStepFormWizard(id);
  if (!stepFormWizard || stepFormWizard.active === active) {
    return;
  }
  await updateStepFormWizard(id, { active });
  // await updateStepFormWizardSessions(
  //   stepFormWizard.sessions.map((m) => m.id),
  //   { status: "active" }
  // );
}

async function getCandidates(stepFormWizard: StepFormWizardWithDetails): Promise<StepFormWizardCandidateDto[]> {
  if (stepFormWizard.filters.length === 0) {
    return [];
  }
  const candidates: StepFormWizardCandidateDto[] = [];
  const allUsers = await adminGetAllUsers();
  const allTenantUsers = await getAllTenantUsers();
  const allTenants = await adminGetAllTenants();
  await Promise.all(
    allUsers.items.map(async (user) => {
      if (!user.admin) {
        return;
      }
      const matchingFilters = await getMatchingFilters({ userId: user.id, tenantId: null, filters: stepFormWizard.filters });
      if (matchingFilters.length === stepFormWizard.filters.length) {
        candidates.push({
          user: user as UserSimple,
          tenant: null,
          matchingFilters: matchingFilters.map((m) => {
            return {
              type: m.type as StepFormWizardFilterType,
              value: m.value,
            };
          }),
        });
      }
    })
  );

  await Promise.all(
    allTenantUsers.map(async (tenantUser) => {
      const tenant = allTenants.find((f) => f.id === tenantUser.tenantId);
      if (!tenant) {
        return;
      }
      const matchingFilters = await getMatchingFilters({ userId: tenantUser.userId, tenantId: tenantUser.tenantId, filters: stepFormWizard.filters });
      if (matchingFilters.length === stepFormWizard.filters.length) {
        candidates.push({
          user: tenantUser.user as UserSimple,
          tenant: { id: tenant.id, name: tenant.name, slug: tenant.slug },
          matchingFilters: matchingFilters.map((m) => {
            return {
              type: m.type as StepFormWizardFilterType,
              value: m.value,
            };
          }),
        });
      }
    })
  );

  return candidates.sort((a, b) => {
    if (a.tenant && !b.tenant) {
      return 1;
    }
    if (!a.tenant && b.tenant) {
      return -1;
    }
    if (a.tenant && b.tenant) {
      return a.tenant.name.localeCompare(b.tenant.name);
    }
    return a.user.email.localeCompare(b.user.email);
  });
}

async function getMetadata(): Promise<StepFormWizardFilterMetadataDto> {
  return {
    users: (await adminGetAllUsers()).items,
    tenants: await adminGetAllTenants(),
    entities: await getAllEntities({ tenantId: null }),
    subscriptionProducts: await getAllSubscriptionProducts(),
    roles: await getAllRoles(),
  };
}

export type StepFormWizardSummaryDto = {
  stepFormWizards: { all: number; active: number };
  sessions: { all: number; active: number; dismissed: number; completed: number };
};
async function getSummary(): Promise<StepFormWizardSummaryDto> {
  const allStepFormWizards = await getStepFormWizards({});
  const summary: StepFormWizardSummaryDto = {
    stepFormWizards: { all: 0, active: 0 },
    sessions: { all: 0, active: 0, dismissed: 0, completed: 0 },
  };
  allStepFormWizards.forEach((stepFormWizard) => {
    summary.stepFormWizards.all++;
    if (stepFormWizard.active) {
      summary.stepFormWizards.active++;
    }
    stepFormWizard.sessions.forEach((session) => {
      summary.sessions.all++;
      if (session.status === "active" || session.status === "started") {
        summary.sessions.active++;
      } else if (session.status === "completed") {
        summary.sessions.completed++;
      } else if (session.status === "dismissed") {
        summary.sessions.dismissed++;
      }
    });
  });
  return summary;
}

export default {
  getUserActiveStepFormWizard,
  getMatchingFilters,
  setStepFormWizardStatus,
  getCandidates,
  getMetadata,
  getSummary,
};
