
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  email: 'email',
  passwordHash: 'passwordHash',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  phone: 'phone',
  defaultTenantId: 'defaultTenantId',
  verifyToken: 'verifyToken',
  githubId: 'githubId',
  azureId: 'azureId',
  googleId: 'googleId',
  locale: 'locale',
  active: 'active'
};

exports.Prisma.AdminUserScalarFieldEnum = {
  userId: 'userId'
};

exports.Prisma.TenantScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  slug: 'slug',
  name: 'name',
  icon: 'icon',
  theme: 'theme',
  subscriptionId: 'subscriptionId',
  active: 'active',
  deactivatedReason: 'deactivatedReason'
};

exports.Prisma.TenantUserScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  userId: 'userId',
  type: 'type',
  joined: 'joined',
  status: 'status'
};

exports.Prisma.TenantUserInvitationScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  type: 'type',
  pending: 'pending',
  createdUserId: 'createdUserId',
  fromUserId: 'fromUserId'
};

exports.Prisma.RegistrationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  slug: 'slug',
  token: 'token',
  ipAddress: 'ipAddress',
  company: 'company',
  selectedSubscriptionPriceId: 'selectedSubscriptionPriceId',
  createdTenantId: 'createdTenantId'
};

exports.Prisma.BlacklistScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  type: 'type',
  value: 'value',
  active: 'active',
  registerAttempts: 'registerAttempts'
};

exports.Prisma.TenantIpAddressScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  userId: 'userId',
  apiKeyId: 'apiKeyId',
  ip: 'ip',
  fromUrl: 'fromUrl',
  createdAt: 'createdAt'
};

exports.Prisma.TenantTypeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  title: 'title',
  titlePlural: 'titlePlural',
  description: 'description',
  isDefault: 'isDefault'
};

exports.Prisma.CustomThemeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  name: 'name',
  value: 'value',
  colors: 'colors',
  isActive: 'isActive'
};

exports.Prisma.LoyalityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  mail: 'mail',
  team: 'team'
};

exports.Prisma.MilestoneScalarFieldEnum = {
  id: 'id',
  title: 'title',
  threshold: 'threshold',
  order: 'order',
  rewardId: 'rewardId'
};

exports.Prisma.RewardScalarFieldEnum = {
  id: 'id',
  title: 'title',
  cost: 'cost',
  order: 'order',
  link: 'link',
  couponCode: 'couponCode'
};

exports.Prisma.RowMilestoneScalarFieldEnum = {
  loyalityId: 'loyalityId',
  milestoneId: 'milestoneId',
  createdAt: 'createdAt'
};

exports.Prisma.RowRewardScalarFieldEnum = {
  loyalityId: 'loyalityId',
  rewardId: 'rewardId',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.RowCoinsScalarFieldEnum = {
  id: 'id',
  loyalityId: 'loyalityId',
  coins: 'coins',
  message: 'message',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId'
};

exports.Prisma.AnalyticsSettingsScalarFieldEnum = {
  id: 'id',
  public: 'public',
  ignorePages: 'ignorePages',
  onlyPages: 'onlyPages'
};

exports.Prisma.AnalyticsUniqueVisitorScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  cookie: 'cookie',
  via: 'via',
  httpReferrer: 'httpReferrer',
  browser: 'browser',
  browserVersion: 'browserVersion',
  os: 'os',
  osVersion: 'osVersion',
  device: 'device',
  source: 'source',
  medium: 'medium',
  campaign: 'campaign',
  content: 'content',
  term: 'term',
  country: 'country',
  city: 'city',
  fromUrl: 'fromUrl',
  fromRoute: 'fromRoute',
  userId: 'userId',
  portalId: 'portalId',
  portalUserId: 'portalUserId'
};

exports.Prisma.AnalyticsPageViewScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  uniqueVisitorId: 'uniqueVisitorId',
  url: 'url',
  route: 'route',
  portalId: 'portalId',
  portalUserId: 'portalUserId'
};

exports.Prisma.AnalyticsEventScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  uniqueVisitorId: 'uniqueVisitorId',
  action: 'action',
  category: 'category',
  label: 'label',
  value: 'value',
  url: 'url',
  route: 'route',
  featureFlagId: 'featureFlagId',
  portalId: 'portalId',
  portalUserId: 'portalUserId'
};

exports.Prisma.BlogCategoryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  name: 'name',
  color: 'color'
};

exports.Prisma.BlogTagScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  name: 'name',
  color: 'color'
};

exports.Prisma.BlogPostTagScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  tagId: 'tagId'
};

exports.Prisma.BlogPostScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  slug: 'slug',
  title: 'title',
  description: 'description',
  date: 'date',
  image: 'image',
  content: 'content',
  readingTime: 'readingTime',
  published: 'published',
  authorId: 'authorId',
  categoryId: 'categoryId',
  contentType: 'contentType'
};

exports.Prisma.AppConfigurationScalarFieldEnum = {
  id: 'id',
  updatedAt: 'updatedAt',
  name: 'name',
  url: 'url',
  theme: 'theme',
  authRequireEmailVerification: 'authRequireEmailVerification',
  authRequireOrganization: 'authRequireOrganization',
  authRequireName: 'authRequireName',
  authRecaptchaSiteKey: 'authRecaptchaSiteKey',
  analyticsEnabled: 'analyticsEnabled',
  analyticsSimpleAnalytics: 'analyticsSimpleAnalytics',
  analyticsPlausibleAnalytics: 'analyticsPlausibleAnalytics',
  analyticsGoogleAnalyticsTrackingId: 'analyticsGoogleAnalyticsTrackingId',
  subscriptionRequired: 'subscriptionRequired',
  subscriptionAllowSubscribeBeforeSignUp: 'subscriptionAllowSubscribeBeforeSignUp',
  subscriptionAllowSignUpBeforeSubscribe: 'subscriptionAllowSignUpBeforeSubscribe',
  cookiesEnabled: 'cookiesEnabled',
  metricsEnabled: 'metricsEnabled',
  metricsLogToConsole: 'metricsLogToConsole',
  metricsSaveToDatabase: 'metricsSaveToDatabase',
  metricsIgnoreUrls: 'metricsIgnoreUrls',
  brandingLogo: 'brandingLogo',
  brandingLogoDarkMode: 'brandingLogoDarkMode',
  brandingIcon: 'brandingIcon',
  brandingIconDarkMode: 'brandingIconDarkMode',
  brandingFavicon: 'brandingFavicon',
  headScripts: 'headScripts',
  bodyScripts: 'bodyScripts',
  emailProvider: 'emailProvider',
  emailFromEmail: 'emailFromEmail',
  emailFromName: 'emailFromName',
  emailSupportEmail: 'emailSupportEmail'
};

exports.Prisma.LogScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  userId: 'userId',
  apiKeyId: 'apiKeyId',
  rowId: 'rowId',
  url: 'url',
  action: 'action',
  details: 'details',
  commentId: 'commentId'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId',
  tenantId: 'tenantId',
  key: 'key',
  alias: 'alias',
  expires: 'expires',
  active: 'active'
};

exports.Prisma.ApiKeyLogScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  apiKeyId: 'apiKeyId',
  tenantId: 'tenantId',
  ip: 'ip',
  endpoint: 'endpoint',
  method: 'method',
  params: 'params',
  status: 'status',
  duration: 'duration',
  error: 'error',
  type: 'type'
};

exports.Prisma.EventScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  userId: 'userId',
  name: 'name',
  data: 'data',
  resource: 'resource',
  description: 'description'
};

exports.Prisma.EventWebhookAttemptScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  startedAt: 'startedAt',
  finishedAt: 'finishedAt',
  eventId: 'eventId',
  endpoint: 'endpoint',
  success: 'success',
  status: 'status',
  message: 'message',
  body: 'body'
};

exports.Prisma.MetricLogScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  env: 'env',
  type: 'type',
  route: 'route',
  url: 'url',
  function: 'function',
  duration: 'duration',
  userId: 'userId',
  tenantId: 'tenantId'
};

exports.Prisma.FileUploadProgressScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  progressServer: 'progressServer',
  progressStorage: 'progressStorage',
  url: 'url',
  error: 'error'
};

exports.Prisma.FileChunkScalarFieldEnum = {
  id: 'id',
  fileUploadId: 'fileUploadId',
  index: 'index',
  data: 'data'
};

exports.Prisma.IpAddressScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ip: 'ip',
  provider: 'provider',
  type: 'type',
  countryCode: 'countryCode',
  countryName: 'countryName',
  regionCode: 'regionCode',
  regionName: 'regionName',
  city: 'city',
  zipCode: 'zipCode',
  latitude: 'latitude',
  longitude: 'longitude',
  metadata: 'metadata'
};

exports.Prisma.IpAddressLogScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  ip: 'ip',
  url: 'url',
  action: 'action',
  description: 'description',
  success: 'success',
  error: 'error',
  metadata: 'metadata',
  ipAddressId: 'ipAddressId'
};

exports.Prisma.WidgetScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  name: 'name',
  appearance: 'appearance',
  metadata: 'metadata'
};

exports.Prisma.CredentialScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  value: 'value'
};

exports.Prisma.TenantInboundAddressScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  address: 'address'
};

exports.Prisma.EmailScalarFieldEnum = {
  id: 'id',
  tenantInboundAddressId: 'tenantInboundAddressId',
  messageId: 'messageId',
  type: 'type',
  date: 'date',
  subject: 'subject',
  fromEmail: 'fromEmail',
  fromName: 'fromName',
  toEmail: 'toEmail',
  toName: 'toName',
  textBody: 'textBody',
  htmlBody: 'htmlBody'
};

exports.Prisma.EmailReadScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  emailId: 'emailId',
  userId: 'userId'
};

exports.Prisma.EmailCcScalarFieldEnum = {
  id: 'id',
  emailId: 'emailId',
  toEmail: 'toEmail',
  toName: 'toName'
};

exports.Prisma.EmailAttachmentScalarFieldEnum = {
  id: 'id',
  emailId: 'emailId',
  name: 'name',
  type: 'type',
  length: 'length',
  content: 'content',
  publicUrl: 'publicUrl',
  storageBucket: 'storageBucket',
  storageProvider: 'storageProvider'
};

exports.Prisma.EmailSenderScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  provider: 'provider',
  stream: 'stream',
  apiKey: 'apiKey',
  fromEmail: 'fromEmail',
  fromName: 'fromName',
  replyToEmail: 'replyToEmail'
};

exports.Prisma.CampaignScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  emailSenderId: 'emailSenderId',
  name: 'name',
  subject: 'subject',
  htmlBody: 'htmlBody',
  textBody: 'textBody',
  status: 'status',
  track: 'track',
  sentAt: 'sentAt'
};

exports.Prisma.OutboundEmailScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  campaignId: 'campaignId',
  contactRowId: 'contactRowId',
  email: 'email',
  fromSenderId: 'fromSenderId',
  isPreview: 'isPreview',
  error: 'error',
  sentAt: 'sentAt',
  deliveredAt: 'deliveredAt',
  bouncedAt: 'bouncedAt',
  spamComplainedAt: 'spamComplainedAt',
  unsubscribedAt: 'unsubscribedAt'
};

exports.Prisma.OutboundEmailOpenScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  firstOpen: 'firstOpen',
  outboundEmailId: 'outboundEmailId'
};

exports.Prisma.OutboundEmailClickScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  link: 'link',
  outboundEmailId: 'outboundEmailId'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  moduleId: 'moduleId',
  name: 'name',
  slug: 'slug',
  order: 'order',
  prefix: 'prefix',
  type: 'type',
  title: 'title',
  titlePlural: 'titlePlural',
  isAutogenerated: 'isAutogenerated',
  isStepFormWizard: 'isStepFormWizard',
  hasApi: 'hasApi',
  icon: 'icon',
  active: 'active',
  showInSidebar: 'showInSidebar',
  hasTags: 'hasTags',
  hasComments: 'hasComments',
  hasTasks: 'hasTasks',
  hasActivity: 'hasActivity',
  hasBulkDelete: 'hasBulkDelete',
  hasViews: 'hasViews',
  defaultVisibility: 'defaultVisibility',
  onCreated: 'onCreated',
  onEdit: 'onEdit',
  promptFlowGroupId: 'promptFlowGroupId',
  stepFormWizardId: 'stepFormWizardId'
};

exports.Prisma.PropertyScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  order: 'order',
  name: 'name',
  title: 'title',
  type: 'type',
  subtype: 'subtype',
  isDefault: 'isDefault',
  isRequired: 'isRequired',
  isHidden: 'isHidden',
  isDisplay: 'isDisplay',
  isUnique: 'isUnique',
  isReadOnly: 'isReadOnly',
  isSortable: 'isSortable',
  isSearchable: 'isSearchable',
  isFilterable: 'isFilterable',
  showInCreate: 'showInCreate',
  canUpdate: 'canUpdate',
  isOverviewHeaderProperty: 'isOverviewHeaderProperty',
  isOverviewSecondaryHeaderProperty: 'isOverviewSecondaryHeaderProperty',
  isMetaProperty: 'isMetaProperty',
  formulaId: 'formulaId',
  tenantId: 'tenantId'
};

exports.Prisma.EntityViewScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdByUserId: 'createdByUserId',
  entityId: 'entityId',
  tenantId: 'tenantId',
  userId: 'userId',
  layout: 'layout',
  order: 'order',
  name: 'name',
  title: 'title',
  pageSize: 'pageSize',
  isDefault: 'isDefault',
  isSystem: 'isSystem',
  gridColumns: 'gridColumns',
  gridColumnsSm: 'gridColumnsSm',
  gridColumnsMd: 'gridColumnsMd',
  gridColumnsLg: 'gridColumnsLg',
  gridColumnsXl: 'gridColumnsXl',
  gridColumns2xl: 'gridColumns2xl',
  gridGap: 'gridGap',
  groupByPropertyId: 'groupByPropertyId'
};

exports.Prisma.EntityViewPropertyScalarFieldEnum = {
  id: 'id',
  entityViewId: 'entityViewId',
  propertyId: 'propertyId',
  name: 'name',
  order: 'order'
};

exports.Prisma.EntityViewFilterScalarFieldEnum = {
  id: 'id',
  entityViewId: 'entityViewId',
  match: 'match',
  name: 'name',
  condition: 'condition',
  value: 'value'
};

exports.Prisma.EntityViewSortScalarFieldEnum = {
  id: 'id',
  entityViewId: 'entityViewId',
  name: 'name',
  asc: 'asc',
  order: 'order'
};

exports.Prisma.PropertyAttributeScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  name: 'name',
  value: 'value'
};

exports.Prisma.PropertyOptionScalarFieldEnum = {
  id: 'id',
  propertyId: 'propertyId',
  order: 'order',
  value: 'value',
  name: 'name',
  color: 'color'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  entityId: 'entityId',
  value: 'value',
  color: 'color'
};

exports.Prisma.EntityTenantUserPermissionScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  level: 'level'
};

exports.Prisma.EntityWebhookScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  action: 'action',
  method: 'method',
  endpoint: 'endpoint'
};

exports.Prisma.EntityWebhookLogScalarFieldEnum = {
  id: 'id',
  webhookId: 'webhookId',
  logId: 'logId',
  status: 'status',
  error: 'error'
};

exports.Prisma.EntityRelationshipScalarFieldEnum = {
  id: 'id',
  parentId: 'parentId',
  childId: 'childId',
  order: 'order',
  title: 'title',
  type: 'type',
  required: 'required',
  cascade: 'cascade',
  distinct: 'distinct',
  readOnly: 'readOnly',
  hiddenIfEmpty: 'hiddenIfEmpty',
  childEntityViewId: 'childEntityViewId',
  parentEntityViewId: 'parentEntityViewId'
};

exports.Prisma.SampleCustomEntityScalarFieldEnum = {
  rowId: 'rowId',
  customText: 'customText',
  customNumber: 'customNumber',
  customDate: 'customDate',
  customBoolean: 'customBoolean',
  customSelect: 'customSelect'
};

exports.Prisma.RowRelationshipScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  relationshipId: 'relationshipId',
  parentId: 'parentId',
  childId: 'childId',
  metadata: 'metadata'
};

exports.Prisma.RowScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  entityId: 'entityId',
  tenantId: 'tenantId',
  folio: 'folio',
  createdByUserId: 'createdByUserId',
  createdByApiKeyId: 'createdByApiKeyId',
  order: 'order',
  stepFormWizardSessionId: 'stepFormWizardSessionId',
  stepFormWizardId: 'stepFormWizardId',
  loyalityId: 'loyalityId'
};

exports.Prisma.RowValueScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  rowId: 'rowId',
  propertyId: 'propertyId',
  textValue: 'textValue',
  numberValue: 'numberValue',
  dateValue: 'dateValue',
  booleanValue: 'booleanValue'
};

exports.Prisma.RowValueMultipleScalarFieldEnum = {
  id: 'id',
  rowValueId: 'rowValueId',
  order: 'order',
  value: 'value'
};

exports.Prisma.RowValueRangeScalarFieldEnum = {
  rowValueId: 'rowValueId',
  numberMin: 'numberMin',
  numberMax: 'numberMax',
  dateMin: 'dateMin',
  dateMax: 'dateMax'
};

exports.Prisma.RowPermissionScalarFieldEnum = {
  id: 'id',
  rowId: 'rowId',
  tenantId: 'tenantId',
  roleId: 'roleId',
  groupId: 'groupId',
  userId: 'userId',
  public: 'public',
  access: 'access'
};

exports.Prisma.RowMediaScalarFieldEnum = {
  id: 'id',
  rowValueId: 'rowValueId',
  title: 'title',
  name: 'name',
  file: 'file',
  type: 'type',
  publicUrl: 'publicUrl',
  storageBucket: 'storageBucket',
  storageProvider: 'storageProvider'
};

exports.Prisma.RowTagScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  rowId: 'rowId',
  tagId: 'tagId'
};

exports.Prisma.RowCommentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId',
  rowId: 'rowId',
  value: 'value',
  isDeleted: 'isDeleted'
};

exports.Prisma.RowCommentReactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId',
  rowCommentId: 'rowCommentId',
  reaction: 'reaction'
};

exports.Prisma.RowTaskScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId',
  rowId: 'rowId',
  title: 'title',
  description: 'description',
  completed: 'completed',
  completedAt: 'completedAt',
  completedByUserId: 'completedByUserId',
  priority: 'priority',
  assignedToUserId: 'assignedToUserId',
  deadline: 'deadline'
};

exports.Prisma.EntityTemplateScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  entityId: 'entityId',
  title: 'title',
  config: 'config'
};

exports.Prisma.EntityGroupScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  order: 'order',
  slug: 'slug',
  title: 'title',
  icon: 'icon',
  collapsible: 'collapsible',
  section: 'section'
};

exports.Prisma.EntityGroupEntityScalarFieldEnum = {
  id: 'id',
  entityGroupId: 'entityGroupId',
  entityId: 'entityId',
  allViewId: 'allViewId',
  selectMin: 'selectMin',
  selectMax: 'selectMax'
};

exports.Prisma.FormulaScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  name: 'name',
  description: 'description',
  resultAs: 'resultAs',
  calculationTrigger: 'calculationTrigger',
  withLogs: 'withLogs'
};

exports.Prisma.FormulaComponentScalarFieldEnum = {
  id: 'id',
  formulaId: 'formulaId',
  order: 'order',
  type: 'type',
  value: 'value'
};

exports.Prisma.FormulaLogScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  formulaId: 'formulaId',
  userId: 'userId',
  tenantId: 'tenantId',
  originalTrigger: 'originalTrigger',
  triggeredBy: 'triggeredBy',
  expression: 'expression',
  result: 'result',
  duration: 'duration',
  error: 'error',
  rowValueId: 'rowValueId'
};

exports.Prisma.FormulaComponentLogScalarFieldEnum = {
  id: 'id',
  order: 'order',
  type: 'type',
  value: 'value',
  rowId: 'rowId',
  formulaLogId: 'formulaLogId'
};

exports.Prisma.EntityGroupConfigurationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  entityGroupId: 'entityGroupId',
  title: 'title'
};

exports.Prisma.EntityGroupConfigurationRowScalarFieldEnum = {
  id: 'id',
  entityGroupConfigurationId: 'entityGroupConfigurationId',
  rowId: 'rowId'
};

exports.Prisma.ApiKeyEntityScalarFieldEnum = {
  id: 'id',
  apiKeyId: 'apiKeyId',
  entityId: 'entityId',
  create: 'create',
  read: 'read',
  update: 'update',
  delete: 'delete'
};

exports.Prisma.TenantSettingsRowScalarFieldEnum = {
  tenantId: 'tenantId',
  rowId: 'rowId'
};

exports.Prisma.FeatureFlagScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  description: 'description',
  enabled: 'enabled'
};

exports.Prisma.FeatureFlagFilterScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  featureFlagId: 'featureFlagId',
  type: 'type',
  value: 'value',
  action: 'action'
};

exports.Prisma.FeedbackScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  userId: 'userId',
  message: 'message',
  fromUrl: 'fromUrl'
};

exports.Prisma.SurveyScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  title: 'title',
  slug: 'slug',
  description: 'description',
  isEnabled: 'isEnabled',
  isPublic: 'isPublic',
  minSubmissions: 'minSubmissions',
  image: 'image'
};

exports.Prisma.SurveyItemScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  surveyId: 'surveyId',
  title: 'title',
  description: 'description',
  shortName: 'shortName',
  type: 'type',
  order: 'order',
  categories: 'categories',
  href: 'href',
  color: 'color',
  options: 'options',
  style: 'style'
};

exports.Prisma.SurveySubmissionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  surveyId: 'surveyId',
  userAnalyticsId: 'userAnalyticsId',
  ipAddress: 'ipAddress'
};

exports.Prisma.SurveySubmissionResultScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  surveySubmissionId: 'surveySubmissionId',
  surveItemTitle: 'surveItemTitle',
  surveItemType: 'surveItemType',
  value: 'value',
  other: 'other'
};

exports.Prisma.KnowledgeBaseScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  basePath: 'basePath',
  slug: 'slug',
  title: 'title',
  description: 'description',
  defaultLanguage: 'defaultLanguage',
  layout: 'layout',
  color: 'color',
  enabled: 'enabled',
  languages: 'languages',
  links: 'links',
  logo: 'logo',
  seoImage: 'seoImage'
};

exports.Prisma.KnowledgeBaseCategoryScalarFieldEnum = {
  id: 'id',
  knowledgeBaseId: 'knowledgeBaseId',
  slug: 'slug',
  order: 'order',
  title: 'title',
  description: 'description',
  icon: 'icon',
  language: 'language',
  seoImage: 'seoImage'
};

exports.Prisma.KnowledgeBaseCategorySectionScalarFieldEnum = {
  id: 'id',
  categoryId: 'categoryId',
  order: 'order',
  title: 'title',
  description: 'description'
};

exports.Prisma.KnowledgeBaseArticleScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  knowledgeBaseId: 'knowledgeBaseId',
  categoryId: 'categoryId',
  sectionId: 'sectionId',
  slug: 'slug',
  title: 'title',
  description: 'description',
  order: 'order',
  contentDraft: 'contentDraft',
  contentPublished: 'contentPublished',
  contentPublishedAsText: 'contentPublishedAsText',
  contentType: 'contentType',
  language: 'language',
  featuredOrder: 'featuredOrder',
  seoImage: 'seoImage',
  publishedAt: 'publishedAt',
  relatedInArticleId: 'relatedInArticleId',
  createdByUserId: 'createdByUserId'
};

exports.Prisma.KnowledgeBaseRelatedArticleScalarFieldEnum = {
  id: 'id',
  articleId: 'articleId',
  relatedArticleId: 'relatedArticleId'
};

exports.Prisma.KnowledgeBaseViewsScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  knowledgeBaseId: 'knowledgeBaseId',
  userAnalyticsId: 'userAnalyticsId'
};

exports.Prisma.KnowledgeBaseArticleViewsScalarFieldEnum = {
  knowledgeBaseArticleId: 'knowledgeBaseArticleId',
  userAnalyticsId: 'userAnalyticsId'
};

exports.Prisma.KnowledgeBaseArticleUpvotesScalarFieldEnum = {
  createdAt: 'createdAt',
  knowledgeBaseArticleId: 'knowledgeBaseArticleId',
  userAnalyticsId: 'userAnalyticsId'
};

exports.Prisma.KnowledgeBaseArticleDownvotesScalarFieldEnum = {
  createdAt: 'createdAt',
  knowledgeBaseArticleId: 'knowledgeBaseArticleId',
  userAnalyticsId: 'userAnalyticsId'
};

exports.Prisma.OnboardingScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  title: 'title',
  type: 'type',
  realtime: 'realtime',
  active: 'active',
  canBeDismissed: 'canBeDismissed',
  height: 'height'
};

exports.Prisma.OnboardingFilterScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  onboardingId: 'onboardingId',
  type: 'type',
  value: 'value'
};

exports.Prisma.OnboardingStepScalarFieldEnum = {
  id: 'id',
  onboardingId: 'onboardingId',
  order: 'order',
  block: 'block'
};

exports.Prisma.OnboardingSessionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  onboardingId: 'onboardingId',
  userId: 'userId',
  tenantId: 'tenantId',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  dismissedAt: 'dismissedAt',
  createdRealtime: 'createdRealtime'
};

exports.Prisma.OnboardingSessionActionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  onboardingSessionId: 'onboardingSessionId',
  type: 'type',
  name: 'name',
  value: 'value'
};

exports.Prisma.OnboardingSessionFilterMatchScalarFieldEnum = {
  id: 'id',
  onboardingFilterId: 'onboardingFilterId',
  onboardingSessionId: 'onboardingSessionId'
};

exports.Prisma.OnboardingSessionStepScalarFieldEnum = {
  id: 'id',
  onboardingSessionId: 'onboardingSessionId',
  stepId: 'stepId',
  seenAt: 'seenAt',
  completedAt: 'completedAt'
};

exports.Prisma.PageScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  slug: 'slug',
  isPublished: 'isPublished',
  isPublic: 'isPublic'
};

exports.Prisma.PageMetaTagScalarFieldEnum = {
  id: 'id',
  pageId: 'pageId',
  order: 'order',
  name: 'name',
  value: 'value'
};

exports.Prisma.PageBlockScalarFieldEnum = {
  id: 'id',
  pageId: 'pageId',
  order: 'order',
  type: 'type',
  value: 'value'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  description: 'description',
  type: 'type',
  assignToNewUsers: 'assignToNewUsers',
  isDefault: 'isDefault',
  order: 'order'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  description: 'description',
  type: 'type',
  isDefault: 'isDefault',
  order: 'order',
  entityId: 'entityId'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  id: 'id',
  roleId: 'roleId',
  permissionId: 'permissionId'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  userId: 'userId',
  roleId: 'roleId',
  tenantId: 'tenantId'
};

exports.Prisma.GroupScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  createdByUserId: 'createdByUserId',
  tenantId: 'tenantId',
  name: 'name',
  description: 'description',
  color: 'color'
};

exports.Prisma.GroupUserScalarFieldEnum = {
  id: 'id',
  groupId: 'groupId',
  userId: 'userId'
};

exports.Prisma.PortalScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  createdByUserId: 'createdByUserId',
  subdomain: 'subdomain',
  domain: 'domain',
  title: 'title',
  isPublished: 'isPublished',
  stripeAccountId: 'stripeAccountId',
  themeColor: 'themeColor',
  themeScheme: 'themeScheme',
  seoTitle: 'seoTitle',
  seoDescription: 'seoDescription',
  seoImage: 'seoImage',
  seoThumbnail: 'seoThumbnail',
  seoTwitterCreator: 'seoTwitterCreator',
  seoTwitterSite: 'seoTwitterSite',
  seoKeywords: 'seoKeywords',
  authRequireEmailVerification: 'authRequireEmailVerification',
  authRequireOrganization: 'authRequireOrganization',
  authRequireName: 'authRequireName',
  analyticsSimpleAnalytics: 'analyticsSimpleAnalytics',
  analyticsPlausibleAnalytics: 'analyticsPlausibleAnalytics',
  analyticsGoogleAnalyticsTrackingId: 'analyticsGoogleAnalyticsTrackingId',
  brandingLogo: 'brandingLogo',
  brandingLogoDarkMode: 'brandingLogoDarkMode',
  brandingIcon: 'brandingIcon',
  brandingIconDarkMode: 'brandingIconDarkMode',
  brandingFavicon: 'brandingFavicon',
  affiliatesRewardfulApiKey: 'affiliatesRewardfulApiKey',
  affiliatesRewardfulUrl: 'affiliatesRewardfulUrl',
  metadata: 'metadata'
};

exports.Prisma.PortalUserScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  portalId: 'portalId',
  email: 'email',
  passwordHash: 'passwordHash',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  phone: 'phone',
  verifyToken: 'verifyToken',
  githubId: 'githubId',
  googleId: 'googleId',
  locale: 'locale'
};

exports.Prisma.PortalPageScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  portalId: 'portalId',
  name: 'name',
  attributes: 'attributes'
};

exports.Prisma.PortalUserRegistrationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  portalId: 'portalId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  slug: 'slug',
  token: 'token',
  ipAddress: 'ipAddress',
  company: 'company',
  selectedSubscriptionPriceId: 'selectedSubscriptionPriceId',
  createdPortalUserId: 'createdPortalUserId'
};

exports.Prisma.PortalSubscriptionProductScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  stripeId: 'stripeId',
  order: 'order',
  title: 'title',
  active: 'active',
  model: 'model',
  public: 'public',
  groupTitle: 'groupTitle',
  groupDescription: 'groupDescription',
  description: 'description',
  badge: 'badge',
  billingAddressCollection: 'billingAddressCollection',
  hasQuantity: 'hasQuantity',
  canBuyAgain: 'canBuyAgain'
};

exports.Prisma.PortalSubscriptionPriceScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  subscriptionProductId: 'subscriptionProductId',
  stripeId: 'stripeId',
  type: 'type',
  billingPeriod: 'billingPeriod',
  price: 'price',
  currency: 'currency',
  trialDays: 'trialDays',
  active: 'active'
};

exports.Prisma.PortalSubscriptionFeatureScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  subscriptionProductId: 'subscriptionProductId',
  order: 'order',
  title: 'title',
  name: 'name',
  type: 'type',
  value: 'value',
  href: 'href',
  badge: 'badge',
  accumulate: 'accumulate'
};

exports.Prisma.PortalUserSubscriptionScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  portalUserId: 'portalUserId',
  stripeCustomerId: 'stripeCustomerId'
};

exports.Prisma.PortalUserSubscriptionProductScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  createdAt: 'createdAt',
  portalUserSubscriptionId: 'portalUserSubscriptionId',
  subscriptionProductId: 'subscriptionProductId',
  cancelledAt: 'cancelledAt',
  endsAt: 'endsAt',
  stripeSubscriptionId: 'stripeSubscriptionId',
  quantity: 'quantity',
  fromCheckoutSessionId: 'fromCheckoutSessionId',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd'
};

exports.Prisma.PortalUserSubscriptionProductPriceScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  portalUserSubscriptionProductId: 'portalUserSubscriptionProductId',
  subscriptionPriceId: 'subscriptionPriceId'
};

exports.Prisma.PortalCheckoutSessionStatusScalarFieldEnum = {
  id: 'id',
  portalId: 'portalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  pending: 'pending',
  email: 'email',
  fromUrl: 'fromUrl',
  fromUserId: 'fromUserId',
  createdUserId: 'createdUserId'
};

exports.Prisma.PromptFlowGroupScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  order: 'order',
  title: 'title',
  description: 'description'
};

exports.Prisma.PromptFlowGroupTemplateScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  order: 'order',
  title: 'title',
  promptFlowGroupId: 'promptFlowGroupId'
};

exports.Prisma.PromptFlowGroupEntityScalarFieldEnum = {
  entityId: 'entityId',
  promptFlowGroupId: 'promptFlowGroupId'
};

exports.Prisma.PromptFlowScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  model: 'model',
  title: 'title',
  description: 'description',
  actionTitle: 'actionTitle',
  executionType: 'executionType',
  promptFlowGroupId: 'promptFlowGroupId',
  stream: 'stream',
  public: 'public',
  inputEntityId: 'inputEntityId'
};

exports.Prisma.PromptFlowInputVariableScalarFieldEnum = {
  id: 'id',
  promptFlowId: 'promptFlowId',
  type: 'type',
  name: 'name',
  title: 'title',
  isRequired: 'isRequired'
};

exports.Prisma.PromptTemplateScalarFieldEnum = {
  id: 'id',
  flowId: 'flowId',
  order: 'order',
  title: 'title',
  template: 'template',
  temperature: 'temperature',
  maxTokens: 'maxTokens',
  generations: 'generations'
};

exports.Prisma.PromptFlowOutputScalarFieldEnum = {
  id: 'id',
  promptFlowId: 'promptFlowId',
  type: 'type',
  entityId: 'entityId'
};

exports.Prisma.PromptFlowOutputMappingScalarFieldEnum = {
  id: 'id',
  promptFlowOutputId: 'promptFlowOutputId',
  promptTemplateId: 'promptTemplateId',
  propertyId: 'propertyId'
};

exports.Prisma.PromptFlowExecutionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  flowId: 'flowId',
  model: 'model',
  userId: 'userId',
  tenantId: 'tenantId',
  status: 'status',
  error: 'error',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration'
};

exports.Prisma.PromptTemplateResultScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  flowExecutionId: 'flowExecutionId',
  templateId: 'templateId',
  order: 'order',
  status: 'status',
  prompt: 'prompt',
  response: 'response',
  error: 'error',
  startedAt: 'startedAt',
  completedAt: 'completedAt'
};

exports.Prisma.StepFormWizardScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  title: 'title',
  type: 'type',
  realtime: 'realtime',
  active: 'active',
  canBeDismissed: 'canBeDismissed',
  height: 'height',
  progressBar: 'progressBar',
  entity: 'entity'
};

exports.Prisma.StepFormWizardFilterScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  stepFormWizardId: 'stepFormWizardId',
  type: 'type',
  value: 'value'
};

exports.Prisma.StepFormWizardStepScalarFieldEnum = {
  id: 'id',
  stepFormWizardId: 'stepFormWizardId',
  order: 'order',
  block: 'block'
};

exports.Prisma.StepFormWizardSessionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  stepFormWizardId: 'stepFormWizardId',
  userId: 'userId',
  tenantId: 'tenantId',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  dismissedAt: 'dismissedAt',
  createdRealtime: 'createdRealtime',
  logicAppRunId: 'logicAppRunId',
  callBackURL: 'callBackURL',
  currentStepIndex: 'currentStepIndex',
  error: 'error'
};

exports.Prisma.StepFormWizardSessionActionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  stepFormWizardSessionId: 'stepFormWizardSessionId',
  type: 'type',
  name: 'name',
  value: 'value'
};

exports.Prisma.StepFormWizardSessionFilterMatchScalarFieldEnum = {
  id: 'id',
  stepFormWizardFilterId: 'stepFormWizardFilterId',
  stepFormWizardSessionId: 'stepFormWizardSessionId'
};

exports.Prisma.StepFormWizardSessionStepScalarFieldEnum = {
  id: 'id',
  stepFormWizardSessionId: 'stepFormWizardSessionId',
  stepId: 'stepId',
  seenAt: 'seenAt',
  completedAt: 'completedAt',
  status: 'status',
  webHookTriggeredAt: 'webHookTriggeredAt',
  error: 'error'
};

exports.Prisma.SubscriptionProductScalarFieldEnum = {
  id: 'id',
  stripeId: 'stripeId',
  order: 'order',
  title: 'title',
  active: 'active',
  model: 'model',
  public: 'public',
  groupTitle: 'groupTitle',
  groupDescription: 'groupDescription',
  description: 'description',
  badge: 'badge',
  billingAddressCollection: 'billingAddressCollection',
  hasQuantity: 'hasQuantity',
  canBuyAgain: 'canBuyAgain'
};

exports.Prisma.SubscriptionPriceScalarFieldEnum = {
  id: 'id',
  subscriptionProductId: 'subscriptionProductId',
  stripeId: 'stripeId',
  type: 'type',
  billingPeriod: 'billingPeriod',
  price: 'price',
  currency: 'currency',
  trialDays: 'trialDays',
  active: 'active'
};

exports.Prisma.SubscriptionUsageBasedPriceScalarFieldEnum = {
  id: 'id',
  subscriptionProductId: 'subscriptionProductId',
  stripeId: 'stripeId',
  billingPeriod: 'billingPeriod',
  currency: 'currency',
  unit: 'unit',
  unitTitle: 'unitTitle',
  unitTitlePlural: 'unitTitlePlural',
  usageType: 'usageType',
  aggregateUsage: 'aggregateUsage',
  tiersMode: 'tiersMode',
  billingScheme: 'billingScheme'
};

exports.Prisma.SubscriptionUsageBasedTierScalarFieldEnum = {
  id: 'id',
  subscriptionUsageBasedPriceId: 'subscriptionUsageBasedPriceId',
  from: 'from',
  to: 'to',
  perUnitPrice: 'perUnitPrice',
  flatFeePrice: 'flatFeePrice'
};

exports.Prisma.SubscriptionFeatureScalarFieldEnum = {
  id: 'id',
  subscriptionProductId: 'subscriptionProductId',
  order: 'order',
  title: 'title',
  name: 'name',
  type: 'type',
  value: 'value',
  href: 'href',
  badge: 'badge',
  accumulate: 'accumulate'
};

exports.Prisma.TenantSubscriptionScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  stripeCustomerId: 'stripeCustomerId'
};

exports.Prisma.TenantSubscriptionProductScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantSubscriptionId: 'tenantSubscriptionId',
  subscriptionProductId: 'subscriptionProductId',
  cancelledAt: 'cancelledAt',
  endsAt: 'endsAt',
  stripeSubscriptionId: 'stripeSubscriptionId',
  quantity: 'quantity',
  fromCheckoutSessionId: 'fromCheckoutSessionId',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd'
};

exports.Prisma.TenantSubscriptionProductPriceScalarFieldEnum = {
  id: 'id',
  tenantSubscriptionProductId: 'tenantSubscriptionProductId',
  subscriptionPriceId: 'subscriptionPriceId',
  subscriptionUsageBasedPriceId: 'subscriptionUsageBasedPriceId'
};

exports.Prisma.TenantSubscriptionUsageRecordScalarFieldEnum = {
  id: 'id',
  tenantSubscriptionProductPriceId: 'tenantSubscriptionProductPriceId',
  timestamp: 'timestamp',
  quantity: 'quantity',
  stripeSubscriptionItemId: 'stripeSubscriptionItemId'
};

exports.Prisma.CheckoutSessionStatusScalarFieldEnum = {
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  id: 'id',
  pending: 'pending',
  email: 'email',
  fromUrl: 'fromUrl',
  fromUserId: 'fromUserId',
  fromTenantId: 'fromTenantId',
  createdUserId: 'createdUserId',
  createdTenantId: 'createdTenantId'
};

exports.Prisma.CreditScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  tenantId: 'tenantId',
  userId: 'userId',
  amount: 'amount',
  type: 'type',
  objectId: 'objectId'
};

exports.Prisma.WorkflowScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  description: 'description',
  status: 'status',
  tenantId: 'tenantId',
  createdByUserId: 'createdByUserId',
  appliesToAllTenants: 'appliesToAllTenants'
};

exports.Prisma.WorkflowBlockScalarFieldEnum = {
  id: 'id',
  workflowId: 'workflowId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  description: 'description',
  isTrigger: 'isTrigger',
  isBlock: 'isBlock',
  input: 'input'
};

exports.Prisma.WorkflowBlockConditionGroupScalarFieldEnum = {
  id: 'id',
  workflowBlockId: 'workflowBlockId',
  index: 'index',
  type: 'type'
};

exports.Prisma.WorkflowBlockConditionScalarFieldEnum = {
  id: 'id',
  workflowBlockConditionGroupId: 'workflowBlockConditionGroupId',
  index: 'index',
  variable: 'variable',
  operator: 'operator',
  value: 'value'
};

exports.Prisma.WorkflowBlockToBlockScalarFieldEnum = {
  id: 'id',
  fromBlockId: 'fromBlockId',
  toBlockId: 'toBlockId',
  condition: 'condition'
};

exports.Prisma.WorkflowExecutionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  workflowId: 'workflowId',
  tenantId: 'tenantId',
  type: 'type',
  status: 'status',
  input: 'input',
  output: 'output',
  duration: 'duration',
  endedAt: 'endedAt',
  error: 'error',
  waitingBlockId: 'waitingBlockId',
  createdByUserId: 'createdByUserId',
  appliesToAllTenants: 'appliesToAllTenants'
};

exports.Prisma.WorkflowInputExampleScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  workflowId: 'workflowId',
  title: 'title',
  input: 'input'
};

exports.Prisma.WorkflowBlockExecutionScalarFieldEnum = {
  id: 'id',
  workflowExecutionId: 'workflowExecutionId',
  workflowBlockId: 'workflowBlockId',
  fromWorkflowBlockId: 'fromWorkflowBlockId',
  status: 'status',
  startedAt: 'startedAt',
  input: 'input',
  output: 'output',
  duration: 'duration',
  endedAt: 'endedAt',
  error: 'error'
};

exports.Prisma.WorkflowVariableScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  name: 'name',
  value: 'value',
  createdByUserId: 'createdByUserId',
  userId: 'userId'
};

exports.Prisma.WorkflowCredentialScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  name: 'name',
  value: 'value',
  createdByUserId: 'createdByUserId',
  userId: 'userId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.LogicAppStatus = exports.$Enums.LogicAppStatus = {
  PENDING: 'PENDING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED'
};

exports.Prisma.ModelName = {
  User: 'User',
  AdminUser: 'AdminUser',
  Tenant: 'Tenant',
  TenantUser: 'TenantUser',
  TenantUserInvitation: 'TenantUserInvitation',
  Registration: 'Registration',
  Blacklist: 'Blacklist',
  TenantIpAddress: 'TenantIpAddress',
  TenantType: 'TenantType',
  CustomTheme: 'CustomTheme',
  Loyality: 'Loyality',
  Milestone: 'Milestone',
  Reward: 'Reward',
  RowMilestone: 'RowMilestone',
  RowReward: 'RowReward',
  RowCoins: 'RowCoins',
  AnalyticsSettings: 'AnalyticsSettings',
  AnalyticsUniqueVisitor: 'AnalyticsUniqueVisitor',
  AnalyticsPageView: 'AnalyticsPageView',
  AnalyticsEvent: 'AnalyticsEvent',
  BlogCategory: 'BlogCategory',
  BlogTag: 'BlogTag',
  BlogPostTag: 'BlogPostTag',
  BlogPost: 'BlogPost',
  AppConfiguration: 'AppConfiguration',
  Log: 'Log',
  ApiKey: 'ApiKey',
  ApiKeyLog: 'ApiKeyLog',
  Event: 'Event',
  EventWebhookAttempt: 'EventWebhookAttempt',
  MetricLog: 'MetricLog',
  FileUploadProgress: 'FileUploadProgress',
  FileChunk: 'FileChunk',
  IpAddress: 'IpAddress',
  IpAddressLog: 'IpAddressLog',
  Widget: 'Widget',
  Credential: 'Credential',
  TenantInboundAddress: 'TenantInboundAddress',
  Email: 'Email',
  EmailRead: 'EmailRead',
  EmailCc: 'EmailCc',
  EmailAttachment: 'EmailAttachment',
  EmailSender: 'EmailSender',
  Campaign: 'Campaign',
  OutboundEmail: 'OutboundEmail',
  OutboundEmailOpen: 'OutboundEmailOpen',
  OutboundEmailClick: 'OutboundEmailClick',
  Entity: 'Entity',
  Property: 'Property',
  EntityView: 'EntityView',
  EntityViewProperty: 'EntityViewProperty',
  EntityViewFilter: 'EntityViewFilter',
  EntityViewSort: 'EntityViewSort',
  PropertyAttribute: 'PropertyAttribute',
  PropertyOption: 'PropertyOption',
  EntityTag: 'EntityTag',
  EntityTenantUserPermission: 'EntityTenantUserPermission',
  EntityWebhook: 'EntityWebhook',
  EntityWebhookLog: 'EntityWebhookLog',
  EntityRelationship: 'EntityRelationship',
  SampleCustomEntity: 'SampleCustomEntity',
  RowRelationship: 'RowRelationship',
  Row: 'Row',
  RowValue: 'RowValue',
  RowValueMultiple: 'RowValueMultiple',
  RowValueRange: 'RowValueRange',
  RowPermission: 'RowPermission',
  RowMedia: 'RowMedia',
  RowTag: 'RowTag',
  RowComment: 'RowComment',
  RowCommentReaction: 'RowCommentReaction',
  RowTask: 'RowTask',
  EntityTemplate: 'EntityTemplate',
  EntityGroup: 'EntityGroup',
  EntityGroupEntity: 'EntityGroupEntity',
  Formula: 'Formula',
  FormulaComponent: 'FormulaComponent',
  FormulaLog: 'FormulaLog',
  FormulaComponentLog: 'FormulaComponentLog',
  EntityGroupConfiguration: 'EntityGroupConfiguration',
  EntityGroupConfigurationRow: 'EntityGroupConfigurationRow',
  ApiKeyEntity: 'ApiKeyEntity',
  TenantSettingsRow: 'TenantSettingsRow',
  FeatureFlag: 'FeatureFlag',
  FeatureFlagFilter: 'FeatureFlagFilter',
  Feedback: 'Feedback',
  Survey: 'Survey',
  SurveyItem: 'SurveyItem',
  SurveySubmission: 'SurveySubmission',
  SurveySubmissionResult: 'SurveySubmissionResult',
  KnowledgeBase: 'KnowledgeBase',
  KnowledgeBaseCategory: 'KnowledgeBaseCategory',
  KnowledgeBaseCategorySection: 'KnowledgeBaseCategorySection',
  KnowledgeBaseArticle: 'KnowledgeBaseArticle',
  KnowledgeBaseRelatedArticle: 'KnowledgeBaseRelatedArticle',
  KnowledgeBaseViews: 'KnowledgeBaseViews',
  KnowledgeBaseArticleViews: 'KnowledgeBaseArticleViews',
  KnowledgeBaseArticleUpvotes: 'KnowledgeBaseArticleUpvotes',
  KnowledgeBaseArticleDownvotes: 'KnowledgeBaseArticleDownvotes',
  Onboarding: 'Onboarding',
  OnboardingFilter: 'OnboardingFilter',
  OnboardingStep: 'OnboardingStep',
  OnboardingSession: 'OnboardingSession',
  OnboardingSessionAction: 'OnboardingSessionAction',
  OnboardingSessionFilterMatch: 'OnboardingSessionFilterMatch',
  OnboardingSessionStep: 'OnboardingSessionStep',
  Page: 'Page',
  PageMetaTag: 'PageMetaTag',
  PageBlock: 'PageBlock',
  Role: 'Role',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  UserRole: 'UserRole',
  Group: 'Group',
  GroupUser: 'GroupUser',
  Portal: 'Portal',
  PortalUser: 'PortalUser',
  PortalPage: 'PortalPage',
  PortalUserRegistration: 'PortalUserRegistration',
  PortalSubscriptionProduct: 'PortalSubscriptionProduct',
  PortalSubscriptionPrice: 'PortalSubscriptionPrice',
  PortalSubscriptionFeature: 'PortalSubscriptionFeature',
  PortalUserSubscription: 'PortalUserSubscription',
  PortalUserSubscriptionProduct: 'PortalUserSubscriptionProduct',
  PortalUserSubscriptionProductPrice: 'PortalUserSubscriptionProductPrice',
  PortalCheckoutSessionStatus: 'PortalCheckoutSessionStatus',
  PromptFlowGroup: 'PromptFlowGroup',
  PromptFlowGroupTemplate: 'PromptFlowGroupTemplate',
  PromptFlowGroupEntity: 'PromptFlowGroupEntity',
  PromptFlow: 'PromptFlow',
  PromptFlowInputVariable: 'PromptFlowInputVariable',
  PromptTemplate: 'PromptTemplate',
  PromptFlowOutput: 'PromptFlowOutput',
  PromptFlowOutputMapping: 'PromptFlowOutputMapping',
  PromptFlowExecution: 'PromptFlowExecution',
  PromptTemplateResult: 'PromptTemplateResult',
  StepFormWizard: 'StepFormWizard',
  StepFormWizardFilter: 'StepFormWizardFilter',
  StepFormWizardStep: 'StepFormWizardStep',
  StepFormWizardSession: 'StepFormWizardSession',
  StepFormWizardSessionAction: 'StepFormWizardSessionAction',
  StepFormWizardSessionFilterMatch: 'StepFormWizardSessionFilterMatch',
  StepFormWizardSessionStep: 'StepFormWizardSessionStep',
  SubscriptionProduct: 'SubscriptionProduct',
  SubscriptionPrice: 'SubscriptionPrice',
  SubscriptionUsageBasedPrice: 'SubscriptionUsageBasedPrice',
  SubscriptionUsageBasedTier: 'SubscriptionUsageBasedTier',
  SubscriptionFeature: 'SubscriptionFeature',
  TenantSubscription: 'TenantSubscription',
  TenantSubscriptionProduct: 'TenantSubscriptionProduct',
  TenantSubscriptionProductPrice: 'TenantSubscriptionProductPrice',
  TenantSubscriptionUsageRecord: 'TenantSubscriptionUsageRecord',
  CheckoutSessionStatus: 'CheckoutSessionStatus',
  Credit: 'Credit',
  Workflow: 'Workflow',
  WorkflowBlock: 'WorkflowBlock',
  WorkflowBlockConditionGroup: 'WorkflowBlockConditionGroup',
  WorkflowBlockCondition: 'WorkflowBlockCondition',
  WorkflowBlockToBlock: 'WorkflowBlockToBlock',
  WorkflowExecution: 'WorkflowExecution',
  WorkflowInputExample: 'WorkflowInputExample',
  WorkflowBlockExecution: 'WorkflowBlockExecution',
  WorkflowVariable: 'WorkflowVariable',
  WorkflowCredential: 'WorkflowCredential'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
