{"version": 3, "sources": ["../../marked/lib/marked.esm.js"], "sourcesContent": ["/**\n * marked v4.3.0 - a markdown parser\n * Copyright (c) 2011-2023, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\nfunction getDefaults() {\n  return {\n    async: false,\n    baseUrl: null,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    headerIds: true,\n    headerPrefix: '',\n    highlight: null,\n    hooks: null,\n    langPrefix: 'language-',\n    mangle: true,\n    pedantic: false,\n    renderer: null,\n    sanitize: false,\n    sanitizer: null,\n    silent: false,\n    smartypants: false,\n    tokenizer: null,\n    walkTokens: null,\n    xhtml: false\n  };\n}\n\nlet defaults = getDefaults();\n\nfunction changeDefaults(newDefaults) {\n  defaults = newDefaults;\n}\n\n/**\n * Helpers\n */\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n  if (encode) {\n    if (escapeTest.test(html)) {\n      return html.replace(escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (escapeTestNoEncode.test(html)) {\n      return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n\nconst unescapeTest = /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig;\n\n/**\n * @param {string} html\n */\nfunction unescape(html) {\n  // explicitly match decimal, hex, and named HTML entities\n  return html.replace(unescapeTest, (_, n) => {\n    n = n.toLowerCase();\n    if (n === 'colon') return ':';\n    if (n.charAt(0) === '#') {\n      return n.charAt(1) === 'x'\n        ? String.fromCharCode(parseInt(n.substring(2), 16))\n        : String.fromCharCode(+n.substring(1));\n    }\n    return '';\n  });\n}\n\nconst caret = /(^|[^\\[])\\^/g;\n\n/**\n * @param {string | RegExp} regex\n * @param {string} opt\n */\nfunction edit(regex, opt) {\n  regex = typeof regex === 'string' ? regex : regex.source;\n  opt = opt || '';\n  const obj = {\n    replace: (name, val) => {\n      val = val.source || val;\n      val = val.replace(caret, '$1');\n      regex = regex.replace(name, val);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(regex, opt);\n    }\n  };\n  return obj;\n}\n\nconst nonWordAndColonTest = /[^\\w:]/g;\nconst originIndependentUrl = /^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;\n\n/**\n * @param {boolean} sanitize\n * @param {string} base\n * @param {string} href\n */\nfunction cleanUrl(sanitize, base, href) {\n  if (sanitize) {\n    let prot;\n    try {\n      prot = decodeURIComponent(unescape(href))\n        .replace(nonWordAndColonTest, '')\n        .toLowerCase();\n    } catch (e) {\n      return null;\n    }\n    if (prot.indexOf('javascript:') === 0 || prot.indexOf('vbscript:') === 0 || prot.indexOf('data:') === 0) {\n      return null;\n    }\n  }\n  if (base && !originIndependentUrl.test(href)) {\n    href = resolveUrl(base, href);\n  }\n  try {\n    href = encodeURI(href).replace(/%25/g, '%');\n  } catch (e) {\n    return null;\n  }\n  return href;\n}\n\nconst baseUrls = {};\nconst justDomain = /^[^:]+:\\/*[^/]*$/;\nconst protocol = /^([^:]+:)[\\s\\S]*$/;\nconst domain = /^([^:]+:\\/*[^/]*)[\\s\\S]*$/;\n\n/**\n * @param {string} base\n * @param {string} href\n */\nfunction resolveUrl(base, href) {\n  if (!baseUrls[' ' + base]) {\n    // we can ignore everything in base after the last slash of its path component,\n    // but we might need to add _that_\n    // https://tools.ietf.org/html/rfc3986#section-3\n    if (justDomain.test(base)) {\n      baseUrls[' ' + base] = base + '/';\n    } else {\n      baseUrls[' ' + base] = rtrim(base, '/', true);\n    }\n  }\n  base = baseUrls[' ' + base];\n  const relativeBase = base.indexOf(':') === -1;\n\n  if (href.substring(0, 2) === '//') {\n    if (relativeBase) {\n      return href;\n    }\n    return base.replace(protocol, '$1') + href;\n  } else if (href.charAt(0) === '/') {\n    if (relativeBase) {\n      return href;\n    }\n    return base.replace(domain, '$1') + href;\n  } else {\n    return base + href;\n  }\n}\n\nconst noopTest = { exec: function noopTest() {} };\n\nfunction splitCells(tableRow, count) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(/\\|/g, (match, offset, str) => {\n      let escaped = false,\n        curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(/ \\|/);\n  let i = 0;\n\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) { cells.shift(); }\n  if (cells.length > 0 && !cells[cells.length - 1].trim()) { cells.pop(); }\n\n  if (cells.length > count) {\n    cells.splice(count);\n  } else {\n    while (cells.length < count) cells.push('');\n  }\n\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n  }\n  return cells;\n}\n\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param {string} str\n * @param {string} c\n * @param {boolean} invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n\n  return str.slice(0, l - suffLen);\n}\n\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  const l = str.length;\n  let level = 0,\n    i = 0;\n  for (; i < l; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  return -1;\n}\n\nfunction checkSanitizeDeprecation(opt) {\n  if (opt && opt.sanitize && !opt.silent) {\n    console.warn('marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options');\n  }\n}\n\n// copied from https://stackoverflow.com/a/5450113/806777\n/**\n * @param {string} pattern\n * @param {number} count\n */\nfunction repeatString(pattern, count) {\n  if (count < 1) {\n    return '';\n  }\n  let result = '';\n  while (count > 1) {\n    if (count & 1) {\n      result += pattern;\n    }\n    count >>= 1;\n    pattern += pattern;\n  }\n  return result + pattern;\n}\n\nfunction outputLink(cap, link, raw, lexer) {\n  const href = link.href;\n  const title = link.title ? escape(link.title) : null;\n  const text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n\n  if (cap[0].charAt(0) !== '!') {\n    lexer.state.inLink = true;\n    const token = {\n      type: 'link',\n      raw,\n      href,\n      title,\n      text,\n      tokens: lexer.inlineTokens(text)\n    };\n    lexer.state.inLink = false;\n    return token;\n  }\n  return {\n    type: 'image',\n    raw,\n    href,\n    title,\n    text: escape(text)\n  };\n}\n\nfunction indentCodeCompensation(raw, text) {\n  const matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n\n  if (matchIndentToCode === null) {\n    return text;\n  }\n\n  const indentToCode = matchIndentToCode[1];\n\n  return text\n    .split('\\n')\n    .map(node => {\n      const matchIndentInNode = node.match(/^\\s+/);\n      if (matchIndentInNode === null) {\n        return node;\n      }\n\n      const [indentInNode] = matchIndentInNode;\n\n      if (indentInNode.length >= indentToCode.length) {\n        return node.slice(indentToCode.length);\n      }\n\n      return node;\n    })\n    .join('\\n');\n}\n\n/**\n * Tokenizer\n */\nclass Tokenizer {\n  constructor(options) {\n    this.options = options || defaults;\n  }\n\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0]\n      };\n    }\n  }\n\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(/^ {1,4}/gm, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic\n          ? rtrim(text, '\\n')\n          : text\n      };\n    }\n  }\n\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '');\n\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline._escapes, '$1') : cap[2],\n        text\n      };\n    }\n  }\n\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n\n      // remove trailing #s\n      if (/#$/.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || / $/.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: cap[0]\n      };\n    }\n  }\n\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      const text = cap[0].replace(/^ *>[ \\t]?/gm, '');\n      const top = this.lexer.state.top;\n      this.lexer.state.top = true;\n      const tokens = this.lexer.blockTokens(text);\n      this.lexer.state.top = top;\n      return {\n        type: 'blockquote',\n        raw: cap[0],\n        tokens,\n        text\n      };\n    }\n  }\n\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let raw, istask, ischecked, indent, i, blankLine, endsWithBlankLine,\n        line, nextLine, rawLine, itemContents, endEarly;\n\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n\n      const list = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: []\n      };\n\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n\n      // Get next list item\n      const itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        endEarly = false;\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n\n        if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n\n        raw = cap[0];\n        src = src.substring(raw.length);\n\n        line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, (t) => ' '.repeat(3 * t.length));\n        nextLine = src.split('\\n', 1)[0];\n\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimLeft();\n        } else {\n          indent = cap[2].search(/[^ ]/); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n\n        blankLine = false;\n\n        if (!line && /^ *$/.test(nextLine)) { // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n\n        if (!endEarly) {\n          const nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n          const hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n          const fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n          const headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n\n          // Check if following lines should be included in List Item\n          while (src) {\n            rawLine = src.split('\\n', 1)[0];\n            nextLine = rawLine;\n\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n            }\n\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n\n            // Horizontal rule found\n            if (hrRegex.test(src)) {\n              break;\n            }\n\n            if (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) { // Dedent if possible\n              itemContents += '\\n' + nextLine.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n\n              // paragraph continuation unless last line was a different block level element\n              if (line.search(/[^ ]/) >= 4) { // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n\n              itemContents += '\\n' + nextLine;\n            }\n\n            if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n              blankLine = true;\n            }\n\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLine.slice(indent);\n          }\n        }\n\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (/\\n *\\n *$/.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = /^\\[[ xX]\\] /.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n          }\n        }\n\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents\n        });\n\n        list.raw += raw;\n      }\n\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      list.items[list.items.length - 1].raw = raw.trimRight();\n      list.items[list.items.length - 1].text = itemContents.trimRight();\n      list.raw = list.raw.trimRight();\n\n      const l = list.items.length;\n\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (i = 0; i < l; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (i = 0; i < l; i++) {\n          list.items[i].loose = true;\n        }\n      }\n\n      return list;\n    }\n  }\n\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: 'html',\n        raw: cap[0],\n        pre: !this.options.sanitizer\n          && (cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style'),\n        text: cap[0]\n      };\n      if (this.options.sanitize) {\n        const text = this.options.sanitizer ? this.options.sanitizer(cap[0]) : escape(cap[0]);\n        token.type = 'paragraph';\n        token.text = text;\n        token.tokens = this.lexer.inline(text);\n      }\n      return token;\n    }\n  }\n\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n      const href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline._escapes, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline._escapes, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (cap) {\n      const item = {\n        type: 'table',\n        header: splitCells(cap[1]).map(c => { return { text: c }; }),\n        align: cap[2].replace(/^ *|\\| *$/g, '').split(/ *\\| */),\n        rows: cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : []\n      };\n\n      if (item.header.length === item.align.length) {\n        item.raw = cap[0];\n\n        let l = item.align.length;\n        let i, j, k, row;\n        for (i = 0; i < l; i++) {\n          if (/^ *-+: *$/.test(item.align[i])) {\n            item.align[i] = 'right';\n          } else if (/^ *:-+: *$/.test(item.align[i])) {\n            item.align[i] = 'center';\n          } else if (/^ *:-+ *$/.test(item.align[i])) {\n            item.align[i] = 'left';\n          } else {\n            item.align[i] = null;\n          }\n        }\n\n        l = item.rows.length;\n        for (i = 0; i < l; i++) {\n          item.rows[i] = splitCells(item.rows[i], item.header.length).map(c => { return { text: c }; });\n        }\n\n        // parse child tokens inside headers and cells\n\n        // header child tokens\n        l = item.header.length;\n        for (j = 0; j < l; j++) {\n          item.header[j].tokens = this.lexer.inline(item.header[j].text);\n        }\n\n        // cell child tokens\n        l = item.rows.length;\n        for (j = 0; j < l; j++) {\n          row = item.rows[j];\n          for (k = 0; k < row.length; k++) {\n            row[k].tokens = this.lexer.inline(row[k].text);\n          }\n        }\n\n        return item;\n      }\n    }\n  }\n\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n        ? cap[1].slice(0, -1)\n        : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: escape(cap[1])\n      };\n    }\n  }\n\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n\n      return {\n        type: this.options.sanitize\n          ? 'text'\n          : 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        text: this.options.sanitize\n          ? (this.options.sanitizer\n            ? this.options.sanitizer(cap[0])\n            : escape(cap[0]))\n          : cap[0]\n      };\n    }\n  }\n\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && /^</.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!(/>$/.test(trimmedUrl))) {\n          return;\n        }\n\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n\n      href = href.trim();\n      if (/^</.test(href)) {\n        if (this.options.pedantic && !(/>$/.test(trimmedUrl))) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline._escapes, '$1') : href,\n        title: title ? title.replace(this.rules.inline._escapes, '$1') : title\n      }, cap[0], this.lexer);\n    }\n  }\n\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src))\n        || (cap = this.rules.inline.nolink.exec(src))) {\n      let link = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n      link = links[link.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer);\n    }\n  }\n\n  emStrong(src, maskedSrc, prevChar = '') {\n    let match = this.rules.inline.emStrong.lDelim.exec(src);\n    if (!match) return;\n\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u)) return;\n\n    const nextChar = match[1] || match[2] || '';\n\n    if (!nextChar || (nextChar && (prevChar === '' || this.rules.inline.punctuation.exec(prevChar)))) {\n      const lLength = match[0].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrong.rDelimAst : this.rules.inline.emStrong.rDelimUnd;\n      endReg.lastIndex = 0;\n\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\n        if (!rDelim) continue; // skip single * in __abc*abc__\n\n        rLength = rDelim.length;\n\n        if (match[3] || match[4]) { // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) { // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n\n        delimTotal -= rLength;\n\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n\n        const raw = src.slice(0, lLength + match.index + (match[0].length - rDelim.length) + rLength);\n\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text)\n          };\n        }\n\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(/\\n/g, ' ');\n      const hasNonSpaceChars = /[^ ]/.test(text);\n      const hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      text = escape(text, true);\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text\n      };\n    }\n  }\n\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0]\n      };\n    }\n  }\n\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n\n  autolink(src, mangle) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = escape(this.options.mangle ? mangle(cap[1]) : cap[1]);\n        href = 'mailto:' + text;\n      } else {\n        text = escape(cap[1]);\n        href = text;\n      }\n\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n\n  url(src, mangle) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = escape(this.options.mangle ? mangle(cap[0]) : cap[0]);\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])[0];\n        } while (prevCapZero !== cap[0]);\n        text = escape(cap[0]);\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n\n  inlineText(src, smartypants) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      let text;\n      if (this.lexer.state.inRawBlock) {\n        text = this.options.sanitize ? (this.options.sanitizer ? this.options.sanitizer(cap[0]) : escape(cap[0])) : cap[0];\n      } else {\n        text = escape(this.options.smartypants ? smartypants(cap[0]) : cap[0]);\n      }\n      return {\n        type: 'text',\n        raw: cap[0],\n        text\n      };\n    }\n  }\n}\n\n/**\n * Block-Level Grammar\n */\nconst block = {\n  newline: /^(?: *(?:\\n|$))+/,\n  code: /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/,\n  fences: /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/,\n  hr: /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/,\n  heading: /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/,\n  blockquote: /^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/,\n  list: /^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/,\n  html: '^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n    + ')',\n  def: /^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/,\n  table: noopTest,\n  lheading: /^((?:.|\\n(?!\\n))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  // regex template, placeholders will be replaced according to different paragraph\n  // interruption rules of commonmark and the original markdown spec:\n  _paragraph: /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/,\n  text: /^[^\\n]+/\n};\n\nblock._label = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nblock._title = /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/;\nblock.def = edit(block.def)\n  .replace('label', block._label)\n  .replace('title', block._title)\n  .getRegex();\n\nblock.bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nblock.listItemStart = edit(/^( *)(bull) */)\n  .replace('bull', block.bullet)\n  .getRegex();\n\nblock.list = edit(block.list)\n  .replace(/bull/g, block.bullet)\n  .replace('hr', '\\\\n+(?=\\\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$))')\n  .replace('def', '\\\\n+(?=' + block.def.source + ')')\n  .getRegex();\n\nblock._tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n  + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n  + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n  + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n  + '|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr'\n  + '|track|ul';\nblock._comment = /<!--(?!-?>)[\\s\\S]*?(?:-->|$)/;\nblock.html = edit(block.html, 'i')\n  .replace('comment', block._comment)\n  .replace('tag', block._tag)\n  .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nblock.paragraph = edit(block._paragraph)\n  .replace('hr', block.hr)\n  .replace('heading', ' {0,3}#{1,6} ')\n  .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n  .replace('|table', '')\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', block._tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n\nblock.blockquote = edit(block.blockquote)\n  .replace('paragraph', block.paragraph)\n  .getRegex();\n\n/**\n * Normal Block Grammar\n */\n\nblock.normal = { ...block };\n\n/**\n * GFM Block Grammar\n */\n\nblock.gfm = {\n  ...block.normal,\n  table: '^ *([^\\\\n ].*\\\\|.*)\\\\n' // Header\n    + ' {0,3}(?:\\\\| *)?(:?-+:? *(?:\\\\| *:?-+:? *)*)(?:\\\\| *)?' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)' // Cells\n};\n\nblock.gfm.table = edit(block.gfm.table)\n  .replace('hr', block.hr)\n  .replace('heading', ' {0,3}#{1,6} ')\n  .replace('blockquote', ' {0,3}>')\n  .replace('code', ' {4}[^\\\\n]')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', block._tag) // tables can be interrupted by type (6) html blocks\n  .getRegex();\n\nblock.gfm.paragraph = edit(block._paragraph)\n  .replace('hr', block.hr)\n  .replace('heading', ' {0,3}#{1,6} ')\n  .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n  .replace('table', block.gfm.table) // interrupt paragraphs with table\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', block._tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\n\nblock.pedantic = {\n  ...block.normal,\n  html: edit(\n    '^ *(?:comment *(?:\\\\n|\\\\s*$)'\n    + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n    + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n    .replace('comment', block._comment)\n    .replace(/tag/g, '(?!(?:'\n      + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n      + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n      + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n    .getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest, // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(block.normal._paragraph)\n    .replace('hr', block.hr)\n    .replace('heading', ' *#{1,6} *[^\\n]')\n    .replace('lheading', block.lheading)\n    .replace('blockquote', ' {0,3}>')\n    .replace('|fences', '')\n    .replace('|list', '')\n    .replace('|html', '')\n    .getRegex()\n};\n\n/**\n * Inline-Level Grammar\n */\nconst inline = {\n  escape: /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/,\n  autolink: /^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/,\n  url: noopTest,\n  tag: '^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>', // CDATA section\n  link: /^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/,\n  reflink: /^!?\\[(label)\\]\\[(ref)\\]/,\n  nolink: /^!?\\[(ref)\\](?:\\[\\])?/,\n  reflinkSearch: 'reflink|nolink(?!\\\\()',\n  emStrong: {\n    lDelim: /^(?:\\*+(?:([punct_])|[^\\s*]))|^_+(?:([punct*])|([^\\s_]))/,\n    //        (1) and (2) can only be a Right Delimiter. (3) and (4) can only be Left.  (5) and (6) can be either Left or Right.\n    //          () Skip orphan inside strong                                      () Consume to delim     (1) #***                (2) a***#, a***                             (3) #***a, ***a                 (4) ***#              (5) #***#                 (6) a***a\n    rDelimAst: /^(?:[^_*\\\\]|\\\\.)*?\\_\\_(?:[^_*\\\\]|\\\\.)*?\\*(?:[^_*\\\\]|\\\\.)*?(?=\\_\\_)|(?:[^*\\\\]|\\\\.)+(?=[^*])|[punct_](\\*+)(?=[\\s]|$)|(?:[^punct*_\\s\\\\]|\\\\.)(\\*+)(?=[punct_\\s]|$)|[punct_\\s](\\*+)(?=[^punct*_\\s])|[\\s](\\*+)(?=[punct_])|[punct_](\\*+)(?=[punct_])|(?:[^punct*_\\s\\\\]|\\\\.)(\\*+)(?=[^punct*_\\s])/,\n    rDelimUnd: /^(?:[^_*\\\\]|\\\\.)*?\\*\\*(?:[^_*\\\\]|\\\\.)*?\\_(?:[^_*\\\\]|\\\\.)*?(?=\\*\\*)|(?:[^_\\\\]|\\\\.)+(?=[^_])|[punct*](\\_+)(?=[\\s]|$)|(?:[^punct*_\\s\\\\]|\\\\.)(\\_+)(?=[punct*\\s]|$)|[punct*\\s](\\_+)(?=[^punct*_\\s])|[\\s](\\_+)(?=[punct*])|[punct*](\\_+)(?=[punct*])/ // ^- Not allowed for _\n  },\n  code: /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/,\n  br: /^( {2,}|\\\\)\\n(?!\\s*$)/,\n  del: noopTest,\n  text: /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/,\n  punctuation: /^([\\spunctuation])/\n};\n\n// list of punctuation marks from CommonMark spec\n// without * and _ to handle the different emphasis markers * and _\ninline._punctuation = '!\"#$%&\\'()+\\\\-.,/:;<=>?@\\\\[\\\\]`^{|}~';\ninline.punctuation = edit(inline.punctuation).replace(/punctuation/g, inline._punctuation).getRegex();\n\n// sequences em should skip over [title](link), `code`, <html>\ninline.blockSkip = /\\[[^\\]]*?\\]\\([^\\)]*?\\)|`[^`]*?`|<[^>]*?>/g;\n// lookbehind is not available on Safari as of version 16\n// inline.escapedEmSt = /(?<=(?:^|[^\\\\)(?:\\\\[^])*)\\\\[*_]/g;\ninline.escapedEmSt = /(?:^|[^\\\\])(?:\\\\\\\\)*\\\\[*_]/g;\n\ninline._comment = edit(block._comment).replace('(?:-->|$)', '-->').getRegex();\n\ninline.emStrong.lDelim = edit(inline.emStrong.lDelim)\n  .replace(/punct/g, inline._punctuation)\n  .getRegex();\n\ninline.emStrong.rDelimAst = edit(inline.emStrong.rDelimAst, 'g')\n  .replace(/punct/g, inline._punctuation)\n  .getRegex();\n\ninline.emStrong.rDelimUnd = edit(inline.emStrong.rDelimUnd, 'g')\n  .replace(/punct/g, inline._punctuation)\n  .getRegex();\n\ninline._escapes = /\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/g;\n\ninline._scheme = /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;\ninline._email = /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;\ninline.autolink = edit(inline.autolink)\n  .replace('scheme', inline._scheme)\n  .replace('email', inline._email)\n  .getRegex();\n\ninline._attribute = /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/;\n\ninline.tag = edit(inline.tag)\n  .replace('comment', inline._comment)\n  .replace('attribute', inline._attribute)\n  .getRegex();\n\ninline._label = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\ninline._href = /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/;\ninline._title = /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/;\n\ninline.link = edit(inline.link)\n  .replace('label', inline._label)\n  .replace('href', inline._href)\n  .replace('title', inline._title)\n  .getRegex();\n\ninline.reflink = edit(inline.reflink)\n  .replace('label', inline._label)\n  .replace('ref', block._label)\n  .getRegex();\n\ninline.nolink = edit(inline.nolink)\n  .replace('ref', block._label)\n  .getRegex();\n\ninline.reflinkSearch = edit(inline.reflinkSearch, 'g')\n  .replace('reflink', inline.reflink)\n  .replace('nolink', inline.nolink)\n  .getRegex();\n\n/**\n * Normal Inline Grammar\n */\n\ninline.normal = { ...inline };\n\n/**\n * Pedantic Inline Grammar\n */\n\ninline.pedantic = {\n  ...inline.normal,\n  strong: {\n    start: /^__|\\*\\*/,\n    middle: /^__(?=\\S)([\\s\\S]*?\\S)__(?!_)|^\\*\\*(?=\\S)([\\s\\S]*?\\S)\\*\\*(?!\\*)/,\n    endAst: /\\*\\*(?!\\*)/g,\n    endUnd: /__(?!_)/g\n  },\n  em: {\n    start: /^_|\\*/,\n    middle: /^()\\*(?=\\S)([\\s\\S]*?\\S)\\*(?!\\*)|^_(?=\\S)([\\s\\S]*?\\S)_(?!_)/,\n    endAst: /\\*(?!\\*)/g,\n    endUnd: /_(?!_)/g\n  },\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n    .replace('label', inline._label)\n    .getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n    .replace('label', inline._label)\n    .getRegex()\n};\n\n/**\n * GFM Inline Grammar\n */\n\ninline.gfm = {\n  ...inline.normal,\n  escape: edit(inline.escape).replace('])', '~|])').getRegex(),\n  _extended_email: /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,\n  url: /^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/,\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\n\ninline.gfm.url = edit(inline.gfm.url, 'i')\n  .replace('email', inline.gfm._extended_email)\n  .getRegex();\n/**\n * GFM + Line Breaks Inline Grammar\n */\n\ninline.breaks = {\n  ...inline.gfm,\n  br: edit(inline.br).replace('{2,}', '*').getRegex(),\n  text: edit(inline.gfm.text)\n    .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n    .replace(/\\{2,\\}/g, '*')\n    .getRegex()\n};\n\n/**\n * smartypants text replacement\n * @param {string} text\n */\nfunction smartypants(text) {\n  return text\n    // em-dashes\n    .replace(/---/g, '\\u2014')\n    // en-dashes\n    .replace(/--/g, '\\u2013')\n    // opening singles\n    .replace(/(^|[-\\u2014/(\\[{\"\\s])'/g, '$1\\u2018')\n    // closing singles & apostrophes\n    .replace(/'/g, '\\u2019')\n    // opening doubles\n    .replace(/(^|[-\\u2014/(\\[{\\u2018\\s])\"/g, '$1\\u201c')\n    // closing doubles\n    .replace(/\"/g, '\\u201d')\n    // ellipses\n    .replace(/\\.{3}/g, '\\u2026');\n}\n\n/**\n * mangle email addresses\n * @param {string} text\n */\nfunction mangle(text) {\n  let out = '',\n    i,\n    ch;\n\n  const l = text.length;\n  for (i = 0; i < l; i++) {\n    ch = text.charCodeAt(i);\n    if (Math.random() > 0.5) {\n      ch = 'x' + ch.toString(16);\n    }\n    out += '&#' + ch + ';';\n  }\n\n  return out;\n}\n\n/**\n * Block Lexer\n */\nclass Lexer {\n  constructor(options) {\n    this.tokens = [];\n    this.tokens.links = Object.create(null);\n    this.options = options || defaults;\n    this.options.tokenizer = this.options.tokenizer || new Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n\n    const rules = {\n      block: block.normal,\n      inline: inline.normal\n    };\n\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options) {\n    const lexer = new Lexer(options);\n    return lexer.lex(src);\n  }\n\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options) {\n    const lexer = new Lexer(options);\n    return lexer.inlineTokens(src);\n  }\n\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src\n      .replace(/\\r\\n|\\r/g, '\\n');\n\n    this.blockTokens(src, this.tokens);\n\n    let next;\n    while (next = this.inlineQueue.shift()) {\n      this.inlineTokens(next.src, next.tokens);\n    }\n\n    return this.tokens;\n  }\n\n  /**\n   * Lexing\n   */\n  blockTokens(src, tokens = []) {\n    if (this.options.pedantic) {\n      src = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n    } else {\n      src = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n        return leading + '    '.repeat(tabs.length);\n      });\n    }\n\n    let token, lastToken, cutSrc, lastParagraphClipped;\n\n    while (src) {\n      if (this.options.extensions\n        && this.options.extensions.block\n        && this.options.extensions.block.some((extTokenizer) => {\n          if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            return true;\n          }\n          return false;\n        })) {\n        continue;\n      }\n\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.length === 1 && tokens.length > 0) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unecessary paragraph tags\n          tokens[tokens.length - 1].raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        lastToken = tokens[tokens.length - 1];\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        lastToken = tokens[tokens.length - 1];\n        if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      cutSrc = src;\n      if (this.options.extensions && this.options.extensions.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach(function(getStartIndex) {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) { startIndex = Math.min(startIndex, tempStart); }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        lastToken = tokens[tokens.length - 1];\n        if (lastParagraphClipped && lastToken.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = (cutSrc.length !== src.length);\n        src = src.substring(token.raw.length);\n        continue;\n      }\n\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        lastToken = tokens[tokens.length - 1];\n        if (lastToken && lastToken.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    this.state.top = true;\n    return tokens;\n  }\n\n  inline(src, tokens = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    let token, lastToken, cutSrc;\n\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match;\n    let keepPrevChar, prevChar;\n\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + repeatString('a', match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + repeatString('a', match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n\n    // Mask out escaped em & strong delimiters\n    while ((match = this.tokenizer.rules.inline.escapedEmSt.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index + match[0].length - 2) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex);\n      this.tokenizer.rules.inline.escapedEmSt.lastIndex--;\n    }\n\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n\n      // extensions\n      if (this.options.extensions\n        && this.options.extensions.inline\n        && this.options.extensions.inline.some((extTokenizer) => {\n          if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            return true;\n          }\n          return false;\n        })) {\n        continue;\n      }\n\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        lastToken = tokens[tokens.length - 1];\n        if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        lastToken = tokens[tokens.length - 1];\n        if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // autolink\n      if (token = this.tokenizer.autolink(src, mangle)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src, mangle))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      cutSrc = src;\n      if (this.options.extensions && this.options.extensions.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach(function(getStartIndex) {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) { startIndex = Math.min(startIndex, tempStart); }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc, smartypants)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        lastToken = tokens[tokens.length - 1];\n        if (lastToken && lastToken.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    return tokens;\n  }\n}\n\n/**\n * Renderer\n */\nclass Renderer {\n  constructor(options) {\n    this.options = options || defaults;\n  }\n\n  code(code, infostring, escaped) {\n    const lang = (infostring || '').match(/\\S*/)[0];\n    if (this.options.highlight) {\n      const out = this.options.highlight(code, lang);\n      if (out != null && out !== code) {\n        escaped = true;\n        code = out;\n      }\n    }\n\n    code = code.replace(/\\n$/, '') + '\\n';\n\n    if (!lang) {\n      return '<pre><code>'\n        + (escaped ? code : escape(code, true))\n        + '</code></pre>\\n';\n    }\n\n    return '<pre><code class=\"'\n      + this.options.langPrefix\n      + escape(lang)\n      + '\">'\n      + (escaped ? code : escape(code, true))\n      + '</code></pre>\\n';\n  }\n\n  /**\n   * @param {string} quote\n   */\n  blockquote(quote) {\n    return `<blockquote>\\n${quote}</blockquote>\\n`;\n  }\n\n  html(html) {\n    return html;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} level\n   * @param {string} raw\n   * @param {any} slugger\n   */\n  heading(text, level, raw, slugger) {\n    if (this.options.headerIds) {\n      const id = this.options.headerPrefix + slugger.slug(raw);\n      return `<h${level} id=\"${id}\">${text}</h${level}>\\n`;\n    }\n\n    // ignore IDs\n    return `<h${level}>${text}</h${level}>\\n`;\n  }\n\n  hr() {\n    return this.options.xhtml ? '<hr/>\\n' : '<hr>\\n';\n  }\n\n  list(body, ordered, start) {\n    const type = ordered ? 'ol' : 'ul',\n      startatt = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n    return '<' + type + startatt + '>\\n' + body + '</' + type + '>\\n';\n  }\n\n  /**\n   * @param {string} text\n   */\n  listitem(text) {\n    return `<li>${text}</li>\\n`;\n  }\n\n  checkbox(checked) {\n    return '<input '\n      + (checked ? 'checked=\"\" ' : '')\n      + 'disabled=\"\" type=\"checkbox\"'\n      + (this.options.xhtml ? ' /' : '')\n      + '> ';\n  }\n\n  /**\n   * @param {string} text\n   */\n  paragraph(text) {\n    return `<p>${text}</p>\\n`;\n  }\n\n  /**\n   * @param {string} header\n   * @param {string} body\n   */\n  table(header, body) {\n    if (body) body = `<tbody>${body}</tbody>`;\n\n    return '<table>\\n'\n      + '<thead>\\n'\n      + header\n      + '</thead>\\n'\n      + body\n      + '</table>\\n';\n  }\n\n  /**\n   * @param {string} content\n   */\n  tablerow(content) {\n    return `<tr>\\n${content}</tr>\\n`;\n  }\n\n  tablecell(content, flags) {\n    const type = flags.header ? 'th' : 'td';\n    const tag = flags.align\n      ? `<${type} align=\"${flags.align}\">`\n      : `<${type}>`;\n    return tag + content + `</${type}>\\n`;\n  }\n\n  /**\n   * span level renderer\n   * @param {string} text\n   */\n  strong(text) {\n    return `<strong>${text}</strong>`;\n  }\n\n  /**\n   * @param {string} text\n   */\n  em(text) {\n    return `<em>${text}</em>`;\n  }\n\n  /**\n   * @param {string} text\n   */\n  codespan(text) {\n    return `<code>${text}</code>`;\n  }\n\n  br() {\n    return this.options.xhtml ? '<br/>' : '<br>';\n  }\n\n  /**\n   * @param {string} text\n   */\n  del(text) {\n    return `<del>${text}</del>`;\n  }\n\n  /**\n   * @param {string} href\n   * @param {string} title\n   * @param {string} text\n   */\n  link(href, title, text) {\n    href = cleanUrl(this.options.sanitize, this.options.baseUrl, href);\n    if (href === null) {\n      return text;\n    }\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + title + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out;\n  }\n\n  /**\n   * @param {string} href\n   * @param {string} title\n   * @param {string} text\n   */\n  image(href, title, text) {\n    href = cleanUrl(this.options.sanitize, this.options.baseUrl, href);\n    if (href === null) {\n      return text;\n    }\n\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${title}\"`;\n    }\n    out += this.options.xhtml ? '/>' : '>';\n    return out;\n  }\n\n  text(text) {\n    return text;\n  }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass TextRenderer {\n  // no need for block level renderers\n  strong(text) {\n    return text;\n  }\n\n  em(text) {\n    return text;\n  }\n\n  codespan(text) {\n    return text;\n  }\n\n  del(text) {\n    return text;\n  }\n\n  html(text) {\n    return text;\n  }\n\n  text(text) {\n    return text;\n  }\n\n  link(href, title, text) {\n    return '' + text;\n  }\n\n  image(href, title, text) {\n    return '' + text;\n  }\n\n  br() {\n    return '';\n  }\n}\n\n/**\n * Slugger generates header id\n */\nclass Slugger {\n  constructor() {\n    this.seen = {};\n  }\n\n  /**\n   * @param {string} value\n   */\n  serialize(value) {\n    return value\n      .toLowerCase()\n      .trim()\n      // remove html tags\n      .replace(/<[!\\/a-z].*?>/ig, '')\n      // remove unwanted chars\n      .replace(/[\\u2000-\\u206F\\u2E00-\\u2E7F\\\\'!\"#$%&()*+,./:;<=>?@[\\]^`{|}~]/g, '')\n      .replace(/\\s/g, '-');\n  }\n\n  /**\n   * Finds the next safe (unique) slug to use\n   * @param {string} originalSlug\n   * @param {boolean} isDryRun\n   */\n  getNextSafeSlug(originalSlug, isDryRun) {\n    let slug = originalSlug;\n    let occurenceAccumulator = 0;\n    if (this.seen.hasOwnProperty(slug)) {\n      occurenceAccumulator = this.seen[originalSlug];\n      do {\n        occurenceAccumulator++;\n        slug = originalSlug + '-' + occurenceAccumulator;\n      } while (this.seen.hasOwnProperty(slug));\n    }\n    if (!isDryRun) {\n      this.seen[originalSlug] = occurenceAccumulator;\n      this.seen[slug] = 0;\n    }\n    return slug;\n  }\n\n  /**\n   * Convert string to unique id\n   * @param {object} [options]\n   * @param {boolean} [options.dryrun] Generates the next unique slug without\n   * updating the internal accumulator.\n   */\n  slug(value, options = {}) {\n    const slug = this.serialize(value);\n    return this.getNextSafeSlug(slug, options.dryrun);\n  }\n}\n\n/**\n * Parsing & Compiling\n */\nclass Parser {\n  constructor(options) {\n    this.options = options || defaults;\n    this.options.renderer = this.options.renderer || new Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.textRenderer = new TextRenderer();\n    this.slugger = new Slugger();\n  }\n\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options) {\n    const parser = new Parser(options);\n    return parser.parse(tokens);\n  }\n\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options) {\n    const parser = new Parser(options);\n    return parser.parseInline(tokens);\n  }\n\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = '',\n      i,\n      j,\n      k,\n      l2,\n      l3,\n      row,\n      cell,\n      header,\n      body,\n      token,\n      ordered,\n      start,\n      loose,\n      itemBody,\n      item,\n      checked,\n      task,\n      checkbox,\n      ret;\n\n    const l = tokens.length;\n    for (i = 0; i < l; i++) {\n      token = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n        ret = this.options.extensions.renderers[token.type].call({ parser: this }, token);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(token.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      switch (token.type) {\n        case 'space': {\n          continue;\n        }\n        case 'hr': {\n          out += this.renderer.hr();\n          continue;\n        }\n        case 'heading': {\n          out += this.renderer.heading(\n            this.parseInline(token.tokens),\n            token.depth,\n            unescape(this.parseInline(token.tokens, this.textRenderer)),\n            this.slugger);\n          continue;\n        }\n        case 'code': {\n          out += this.renderer.code(token.text,\n            token.lang,\n            token.escaped);\n          continue;\n        }\n        case 'table': {\n          header = '';\n\n          // header\n          cell = '';\n          l2 = token.header.length;\n          for (j = 0; j < l2; j++) {\n            cell += this.renderer.tablecell(\n              this.parseInline(token.header[j].tokens),\n              { header: true, align: token.align[j] }\n            );\n          }\n          header += this.renderer.tablerow(cell);\n\n          body = '';\n          l2 = token.rows.length;\n          for (j = 0; j < l2; j++) {\n            row = token.rows[j];\n\n            cell = '';\n            l3 = row.length;\n            for (k = 0; k < l3; k++) {\n              cell += this.renderer.tablecell(\n                this.parseInline(row[k].tokens),\n                { header: false, align: token.align[k] }\n              );\n            }\n\n            body += this.renderer.tablerow(cell);\n          }\n          out += this.renderer.table(header, body);\n          continue;\n        }\n        case 'blockquote': {\n          body = this.parse(token.tokens);\n          out += this.renderer.blockquote(body);\n          continue;\n        }\n        case 'list': {\n          ordered = token.ordered;\n          start = token.start;\n          loose = token.loose;\n          l2 = token.items.length;\n\n          body = '';\n          for (j = 0; j < l2; j++) {\n            item = token.items[j];\n            checked = item.checked;\n            task = item.task;\n\n            itemBody = '';\n            if (item.task) {\n              checkbox = this.renderer.checkbox(checked);\n              if (loose) {\n                if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n                  item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                  if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                    item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n                  }\n                } else {\n                  item.tokens.unshift({\n                    type: 'text',\n                    text: checkbox\n                  });\n                }\n              } else {\n                itemBody += checkbox;\n              }\n            }\n\n            itemBody += this.parse(item.tokens, loose);\n            body += this.renderer.listitem(itemBody, task, checked);\n          }\n\n          out += this.renderer.list(body, ordered, start);\n          continue;\n        }\n        case 'html': {\n          // TODO parse inline content if parameter markdown=1\n          out += this.renderer.html(token.text);\n          continue;\n        }\n        case 'paragraph': {\n          out += this.renderer.paragraph(this.parseInline(token.tokens));\n          continue;\n        }\n        case 'text': {\n          body = token.tokens ? this.parseInline(token.tokens) : token.text;\n          while (i + 1 < l && tokens[i + 1].type === 'text') {\n            token = tokens[++i];\n            body += '\\n' + (token.tokens ? this.parseInline(token.tokens) : token.text);\n          }\n          out += top ? this.renderer.paragraph(body) : body;\n          continue;\n        }\n\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n\n    return out;\n  }\n\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer) {\n    renderer = renderer || this.renderer;\n    let out = '',\n      i,\n      token,\n      ret;\n\n    const l = tokens.length;\n    for (i = 0; i < l; i++) {\n      token = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n        ret = this.options.extensions.renderers[token.type].call({ parser: this }, token);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(token.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      switch (token.type) {\n        case 'escape': {\n          out += renderer.text(token.text);\n          break;\n        }\n        case 'html': {\n          out += renderer.html(token.text);\n          break;\n        }\n        case 'link': {\n          out += renderer.link(token.href, token.title, this.parseInline(token.tokens, renderer));\n          break;\n        }\n        case 'image': {\n          out += renderer.image(token.href, token.title, token.text);\n          break;\n        }\n        case 'strong': {\n          out += renderer.strong(this.parseInline(token.tokens, renderer));\n          break;\n        }\n        case 'em': {\n          out += renderer.em(this.parseInline(token.tokens, renderer));\n          break;\n        }\n        case 'codespan': {\n          out += renderer.codespan(token.text);\n          break;\n        }\n        case 'br': {\n          out += renderer.br();\n          break;\n        }\n        case 'del': {\n          out += renderer.del(this.parseInline(token.tokens, renderer));\n          break;\n        }\n        case 'text': {\n          out += renderer.text(token.text);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n}\n\nclass Hooks {\n  constructor(options) {\n    this.options = options || defaults;\n  }\n\n  static passThroughHooks = new Set([\n    'preprocess',\n    'postprocess'\n  ]);\n\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html) {\n    return html;\n  }\n}\n\nfunction onError(silent, async, callback) {\n  return (e) => {\n    e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\n    if (silent) {\n      const msg = '<p>An error occurred:</p><pre>'\n        + escape(e.message + '', true)\n        + '</pre>';\n      if (async) {\n        return Promise.resolve(msg);\n      }\n      if (callback) {\n        callback(null, msg);\n        return;\n      }\n      return msg;\n    }\n\n    if (async) {\n      return Promise.reject(e);\n    }\n    if (callback) {\n      callback(e);\n      return;\n    }\n    throw e;\n  };\n}\n\nfunction parseMarkdown(lexer, parser) {\n  return (src, opt, callback) => {\n    if (typeof opt === 'function') {\n      callback = opt;\n      opt = null;\n    }\n\n    const origOpt = { ...opt };\n    opt = { ...marked.defaults, ...origOpt };\n    const throwError = onError(opt.silent, opt.async, callback);\n\n    // throw error in case of non string input\n    if (typeof src === 'undefined' || src === null) {\n      return throwError(new Error('marked(): input parameter is undefined or null'));\n    }\n    if (typeof src !== 'string') {\n      return throwError(new Error('marked(): input parameter is of type '\n        + Object.prototype.toString.call(src) + ', string expected'));\n    }\n\n    checkSanitizeDeprecation(opt);\n\n    if (opt.hooks) {\n      opt.hooks.options = opt;\n    }\n\n    if (callback) {\n      const highlight = opt.highlight;\n      let tokens;\n\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        tokens = lexer(src, opt);\n      } catch (e) {\n        return throwError(e);\n      }\n\n      const done = function(err) {\n        let out;\n\n        if (!err) {\n          try {\n            if (opt.walkTokens) {\n              marked.walkTokens(tokens, opt.walkTokens);\n            }\n            out = parser(tokens, opt);\n            if (opt.hooks) {\n              out = opt.hooks.postprocess(out);\n            }\n          } catch (e) {\n            err = e;\n          }\n        }\n\n        opt.highlight = highlight;\n\n        return err\n          ? throwError(err)\n          : callback(null, out);\n      };\n\n      if (!highlight || highlight.length < 3) {\n        return done();\n      }\n\n      delete opt.highlight;\n\n      if (!tokens.length) return done();\n\n      let pending = 0;\n      marked.walkTokens(tokens, function(token) {\n        if (token.type === 'code') {\n          pending++;\n          setTimeout(() => {\n            highlight(token.text, token.lang, function(err, code) {\n              if (err) {\n                return done(err);\n              }\n              if (code != null && code !== token.text) {\n                token.text = code;\n                token.escaped = true;\n              }\n\n              pending--;\n              if (pending === 0) {\n                done();\n              }\n            });\n          }, 0);\n        }\n      });\n\n      if (pending === 0) {\n        done();\n      }\n\n      return;\n    }\n\n    if (opt.async) {\n      return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n        .then(src => lexer(src, opt))\n        .then(tokens => opt.walkTokens ? Promise.all(marked.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n        .then(tokens => parser(tokens, opt))\n        .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n        .catch(throwError);\n    }\n\n    try {\n      if (opt.hooks) {\n        src = opt.hooks.preprocess(src);\n      }\n      const tokens = lexer(src, opt);\n      if (opt.walkTokens) {\n        marked.walkTokens(tokens, opt.walkTokens);\n      }\n      let html = parser(tokens, opt);\n      if (opt.hooks) {\n        html = opt.hooks.postprocess(html);\n      }\n      return html;\n    } catch (e) {\n      return throwError(e);\n    }\n  };\n}\n\n/**\n * Marked\n */\nfunction marked(src, opt, callback) {\n  return parseMarkdown(Lexer.lex, Parser.parse)(src, opt, callback);\n}\n\n/**\n * Options\n */\n\nmarked.options =\nmarked.setOptions = function(opt) {\n  marked.defaults = { ...marked.defaults, ...opt };\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\nmarked.getDefaults = getDefaults;\n\nmarked.defaults = defaults;\n\n/**\n * Use Extension\n */\n\nmarked.use = function(...args) {\n  const extensions = marked.defaults.extensions || { renderers: {}, childTokens: {} };\n\n  args.forEach((pack) => {\n    // copy options to new object\n    const opts = { ...pack };\n\n    // set async to true if it was set to true before\n    opts.async = marked.defaults.async || opts.async || false;\n\n    // ==-- Parse \"addon\" extensions --== //\n    if (pack.extensions) {\n      pack.extensions.forEach((ext) => {\n        if (!ext.name) {\n          throw new Error('extension name required');\n        }\n        if (ext.renderer) { // Renderer extensions\n          const prevRenderer = extensions.renderers[ext.name];\n          if (prevRenderer) {\n            // Replace extension with func to run new extension but fall back if false\n            extensions.renderers[ext.name] = function(...args) {\n              let ret = ext.renderer.apply(this, args);\n              if (ret === false) {\n                ret = prevRenderer.apply(this, args);\n              }\n              return ret;\n            };\n          } else {\n            extensions.renderers[ext.name] = ext.renderer;\n          }\n        }\n        if (ext.tokenizer) { // Tokenizer Extensions\n          if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n            throw new Error(\"extension level must be 'block' or 'inline'\");\n          }\n          if (extensions[ext.level]) {\n            extensions[ext.level].unshift(ext.tokenizer);\n          } else {\n            extensions[ext.level] = [ext.tokenizer];\n          }\n          if (ext.start) { // Function to check for start of token\n            if (ext.level === 'block') {\n              if (extensions.startBlock) {\n                extensions.startBlock.push(ext.start);\n              } else {\n                extensions.startBlock = [ext.start];\n              }\n            } else if (ext.level === 'inline') {\n              if (extensions.startInline) {\n                extensions.startInline.push(ext.start);\n              } else {\n                extensions.startInline = [ext.start];\n              }\n            }\n          }\n        }\n        if (ext.childTokens) { // Child tokens to be visited by walkTokens\n          extensions.childTokens[ext.name] = ext.childTokens;\n        }\n      });\n      opts.extensions = extensions;\n    }\n\n    // ==-- Parse \"overwrite\" extensions --== //\n    if (pack.renderer) {\n      const renderer = marked.defaults.renderer || new Renderer();\n      for (const prop in pack.renderer) {\n        const prevRenderer = renderer[prop];\n        // Replace renderer with func to run extension, but fall back if false\n        renderer[prop] = (...args) => {\n          let ret = pack.renderer[prop].apply(renderer, args);\n          if (ret === false) {\n            ret = prevRenderer.apply(renderer, args);\n          }\n          return ret;\n        };\n      }\n      opts.renderer = renderer;\n    }\n    if (pack.tokenizer) {\n      const tokenizer = marked.defaults.tokenizer || new Tokenizer();\n      for (const prop in pack.tokenizer) {\n        const prevTokenizer = tokenizer[prop];\n        // Replace tokenizer with func to run extension, but fall back if false\n        tokenizer[prop] = (...args) => {\n          let ret = pack.tokenizer[prop].apply(tokenizer, args);\n          if (ret === false) {\n            ret = prevTokenizer.apply(tokenizer, args);\n          }\n          return ret;\n        };\n      }\n      opts.tokenizer = tokenizer;\n    }\n\n    // ==-- Parse Hooks extensions --== //\n    if (pack.hooks) {\n      const hooks = marked.defaults.hooks || new Hooks();\n      for (const prop in pack.hooks) {\n        const prevHook = hooks[prop];\n        if (Hooks.passThroughHooks.has(prop)) {\n          hooks[prop] = (arg) => {\n            if (marked.defaults.async) {\n              return Promise.resolve(pack.hooks[prop].call(hooks, arg)).then(ret => {\n                return prevHook.call(hooks, ret);\n              });\n            }\n\n            const ret = pack.hooks[prop].call(hooks, arg);\n            return prevHook.call(hooks, ret);\n          };\n        } else {\n          hooks[prop] = (...args) => {\n            let ret = pack.hooks[prop].apply(hooks, args);\n            if (ret === false) {\n              ret = prevHook.apply(hooks, args);\n            }\n            return ret;\n          };\n        }\n      }\n      opts.hooks = hooks;\n    }\n\n    // ==-- Parse WalkTokens extensions --== //\n    if (pack.walkTokens) {\n      const walkTokens = marked.defaults.walkTokens;\n      opts.walkTokens = function(token) {\n        let values = [];\n        values.push(pack.walkTokens.call(this, token));\n        if (walkTokens) {\n          values = values.concat(walkTokens.call(this, token));\n        }\n        return values;\n      };\n    }\n\n    marked.setOptions(opts);\n  });\n};\n\n/**\n * Run callback for every token\n */\n\nmarked.walkTokens = function(tokens, callback) {\n  let values = [];\n  for (const token of tokens) {\n    values = values.concat(callback.call(marked, token));\n    switch (token.type) {\n      case 'table': {\n        for (const cell of token.header) {\n          values = values.concat(marked.walkTokens(cell.tokens, callback));\n        }\n        for (const row of token.rows) {\n          for (const cell of row) {\n            values = values.concat(marked.walkTokens(cell.tokens, callback));\n          }\n        }\n        break;\n      }\n      case 'list': {\n        values = values.concat(marked.walkTokens(token.items, callback));\n        break;\n      }\n      default: {\n        if (marked.defaults.extensions && marked.defaults.extensions.childTokens && marked.defaults.extensions.childTokens[token.type]) { // Walk any extensions\n          marked.defaults.extensions.childTokens[token.type].forEach(function(childTokens) {\n            values = values.concat(marked.walkTokens(token[childTokens], callback));\n          });\n        } else if (token.tokens) {\n          values = values.concat(marked.walkTokens(token.tokens, callback));\n        }\n      }\n    }\n  }\n  return values;\n};\n\n/**\n * Parse Inline\n * @param {string} src\n */\nmarked.parseInline = parseMarkdown(Lexer.lexInline, Parser.parseInline);\n\n/**\n * Expose\n */\nmarked.Parser = Parser;\nmarked.parser = Parser.parse;\nmarked.Renderer = Renderer;\nmarked.TextRenderer = TextRenderer;\nmarked.Lexer = Lexer;\nmarked.lexer = Lexer.lex;\nmarked.Tokenizer = Tokenizer;\nmarked.Slugger = Slugger;\nmarked.Hooks = Hooks;\nmarked.parse = marked;\n\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = Parser.parse;\nconst lexer = Lexer.lex;\n\nexport { Hooks, Lexer, Parser, Renderer, Slugger, TextRenderer, Tokenizer, defaults, getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };\n"], "mappings": ";;;AAWA,SAAS,cAAc;AACrB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AACF;AAEA,IAAI,WAAW,YAAY;AAE3B,SAAS,eAAe,aAAa;AACnC,aAAW;AACb;AAKA,IAAM,aAAa;AACnB,IAAM,gBAAgB,IAAI,OAAO,WAAW,QAAQ,GAAG;AACvD,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB,IAAI,OAAO,mBAAmB,QAAQ,GAAG;AACvE,IAAM,qBAAqB;AAAA,EACzB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,uBAAuB,CAAC,OAAO,mBAAmB,EAAE;AAC1D,SAAS,OAAO,MAAM,QAAQ;AAC5B,MAAI,QAAQ;AACV,QAAI,WAAW,KAAK,IAAI,GAAG;AACzB,aAAO,KAAK,QAAQ,eAAe,oBAAoB;AAAA,IACzD;AAAA,EACF,OAAO;AACL,QAAI,mBAAmB,KAAK,IAAI,GAAG;AACjC,aAAO,KAAK,QAAQ,uBAAuB,oBAAoB;AAAA,IACjE;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAM,eAAe;AAKrB,SAAS,SAAS,MAAM;AAEtB,SAAO,KAAK,QAAQ,cAAc,CAAC,GAAG,MAAM;AAC1C,QAAI,EAAE,YAAY;AAClB,QAAI,MAAM,QAAS,QAAO;AAC1B,QAAI,EAAE,OAAO,CAAC,MAAM,KAAK;AACvB,aAAO,EAAE,OAAO,CAAC,MAAM,MACnB,OAAO,aAAa,SAAS,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,IAChD,OAAO,aAAa,CAAC,EAAE,UAAU,CAAC,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAM,QAAQ;AAMd,SAAS,KAAK,OAAO,KAAK;AACxB,UAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,QAAM,OAAO;AACb,QAAM,MAAM;AAAA,IACV,SAAS,CAAC,MAAM,QAAQ;AACtB,YAAM,IAAI,UAAU;AACpB,YAAM,IAAI,QAAQ,OAAO,IAAI;AAC7B,cAAQ,MAAM,QAAQ,MAAM,GAAG;AAC/B,aAAO;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,aAAO,IAAI,OAAO,OAAO,GAAG;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAO7B,SAAS,SAAS,UAAU,MAAM,MAAM;AACtC,MAAI,UAAU;AACZ,QAAI;AACJ,QAAI;AACF,aAAO,mBAAmB,SAAS,IAAI,CAAC,EACrC,QAAQ,qBAAqB,EAAE,EAC/B,YAAY;AAAA,IACjB,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ,aAAa,MAAM,KAAK,KAAK,QAAQ,WAAW,MAAM,KAAK,KAAK,QAAQ,OAAO,MAAM,GAAG;AACvG,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,QAAQ,CAAC,qBAAqB,KAAK,IAAI,GAAG;AAC5C,WAAO,WAAW,MAAM,IAAI;AAAA,EAC9B;AACA,MAAI;AACF,WAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ,GAAG;AAAA,EAC5C,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,WAAW,CAAC;AAClB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,SAAS;AAMf,SAAS,WAAW,MAAM,MAAM;AAC9B,MAAI,CAAC,SAAS,MAAM,IAAI,GAAG;AAIzB,QAAI,WAAW,KAAK,IAAI,GAAG;AACzB,eAAS,MAAM,IAAI,IAAI,OAAO;AAAA,IAChC,OAAO;AACL,eAAS,MAAM,IAAI,IAAI,MAAM,MAAM,KAAK,IAAI;AAAA,IAC9C;AAAA,EACF;AACA,SAAO,SAAS,MAAM,IAAI;AAC1B,QAAM,eAAe,KAAK,QAAQ,GAAG,MAAM;AAE3C,MAAI,KAAK,UAAU,GAAG,CAAC,MAAM,MAAM;AACjC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,UAAU,IAAI,IAAI;AAAA,EACxC,WAAW,KAAK,OAAO,CAAC,MAAM,KAAK;AACjC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAAA,EACtC,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,IAAM,WAAW,EAAE,MAAM,SAASA,YAAW;AAAC,EAAE;AAEhD,SAAS,WAAW,UAAU,OAAO;AAGnC,QAAM,MAAM,SAAS,QAAQ,OAAO,CAAC,OAAO,QAAQ,QAAQ;AACxD,QAAI,UAAU,OACZ,OAAO;AACT,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAM,WAAU,CAAC;AACrD,QAAI,SAAS;AAGX,aAAO;AAAA,IACT,OAAO;AAEL,aAAO;AAAA,IACT;AAAA,EACF,CAAC,GACD,QAAQ,IAAI,MAAM,KAAK;AACzB,MAAI,IAAI;AAGR,MAAI,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG;AAAE,UAAM,MAAM;AAAA,EAAG;AACvC,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,EAAE,KAAK,GAAG;AAAE,UAAM,IAAI;AAAA,EAAG;AAExE,MAAI,MAAM,SAAS,OAAO;AACxB,UAAM,OAAO,KAAK;AAAA,EACpB,OAAO;AACL,WAAO,MAAM,SAAS,MAAO,OAAM,KAAK,EAAE;AAAA,EAC5C;AAEA,SAAO,IAAI,MAAM,QAAQ,KAAK;AAE5B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,SAAS,GAAG;AAAA,EACjD;AACA,SAAO;AACT;AAUA,SAAS,MAAM,KAAK,GAAG,QAAQ;AAC7B,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAGA,MAAI,UAAU;AAGd,SAAO,UAAU,GAAG;AAClB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,IACF,WAAW,aAAa,KAAK,QAAQ;AACnC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACjC;AAEA,SAAS,mBAAmB,KAAK,GAAG;AAClC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,IAAI,IAAI;AACd,MAAI,QAAQ,GACV,IAAI;AACN,SAAO,IAAI,GAAG,KAAK;AACjB,QAAI,IAAI,CAAC,MAAM,MAAM;AACnB;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AACA,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,KAAK;AACrC,MAAI,OAAO,IAAI,YAAY,CAAC,IAAI,QAAQ;AACtC,YAAQ,KAAK,yMAAyM;AAAA,EACxN;AACF;AAOA,SAAS,aAAa,SAAS,OAAO;AACpC,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AACb,SAAO,QAAQ,GAAG;AAChB,QAAI,QAAQ,GAAG;AACb,gBAAU;AAAA,IACZ;AACA,cAAU;AACV,eAAW;AAAA,EACb;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,WAAW,KAAK,MAAM,KAAKC,QAAO;AACzC,QAAM,OAAO,KAAK;AAClB,QAAM,QAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK,IAAI;AAChD,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,eAAe,IAAI;AAE/C,MAAI,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AAC5B,IAAAA,OAAM,MAAM,SAAS;AACrB,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQA,OAAM,aAAa,IAAI;AAAA,IACjC;AACA,IAAAA,OAAM,MAAM,SAAS;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,OAAO,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,uBAAuB,KAAK,MAAM;AACzC,QAAM,oBAAoB,IAAI,MAAM,eAAe;AAEnD,MAAI,sBAAsB,MAAM;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,kBAAkB,CAAC;AAExC,SAAO,KACJ,MAAM,IAAI,EACV,IAAI,UAAQ;AACX,UAAM,oBAAoB,KAAK,MAAM,MAAM;AAC3C,QAAI,sBAAsB,MAAM;AAC9B,aAAO;AAAA,IACT;AAEA,UAAM,CAAC,YAAY,IAAI;AAEvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,aAAO,KAAK,MAAM,aAAa,MAAM;AAAA,IACvC;AAEA,WAAO;AAAA,EACT,CAAC,EACA,KAAK,IAAI;AACd;AAKA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAYC,UAAS;AACnB,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,aAAa,EAAE;AAC3C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAM,CAAC,KAAK,QAAQ,WAChB,MAAM,MAAM,IAAI,IAChB;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,EAAE;AAErD,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,QAC9E;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAQ,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AAGvB,UAAI,KAAK,KAAK,IAAI,GAAG;AACnB,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACzB,iBAAO,QAAQ,KAAK;AAAA,QACtB,WAAW,CAAC,WAAW,KAAK,KAAK,OAAO,GAAG;AAEzC,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,KAAK;AACd,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,gBAAgB,EAAE;AAC9C,YAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,WAAK,MAAM,MAAM,MAAM;AACvB,YAAM,SAAS,KAAK,MAAM,YAAY,IAAI;AAC1C,WAAK,MAAM,MAAM,MAAM;AACvB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,UAAI,KAAK,QAAQ,WAAW,QAAQ,GAAG,WAAW,mBAChD,MAAM,UAAU,SAAS,cAAc;AAEzC,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,YAAM,YAAY,KAAK,SAAS;AAEhC,YAAM,OAAO;AAAA,QACX,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,MACV;AAEA,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAE5D,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,YAAY,OAAO;AAAA,MAC5B;AAGA,YAAM,YAAY,IAAI,OAAO,WAAW,IAAI,8BAA+B;AAG3E,aAAO,KAAK;AACV,mBAAW;AACX,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAChC;AAAA,QACF;AAEA,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AACjC;AAAA,QACF;AAEA,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAE9B,eAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,QAAQ,CAAC,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AAC/E,mBAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AAE/B,YAAI,KAAK,QAAQ,UAAU;AACzB,mBAAS;AACT,yBAAe,KAAK,SAAS;AAAA,QAC/B,OAAO;AACL,mBAAS,IAAI,CAAC,EAAE,OAAO,MAAM;AAC7B,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;AAAA,QACnB;AAEA,oBAAY;AAEZ,YAAI,CAAC,QAAQ,OAAO,KAAK,QAAQ,GAAG;AAClC,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;AAAA,QACb;AAEA,YAAI,CAAC,UAAU;AACb,gBAAM,kBAAkB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAqD;AACvH,gBAAM,UAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAC9G,gBAAM,mBAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;AACpF,gBAAM,oBAAoB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;AAGxE,iBAAO,KAAK;AACV,sBAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AAC9B,uBAAW;AAGX,gBAAI,KAAK,QAAQ,UAAU;AACzB,yBAAW,SAAS,QAAQ,2BAA2B,IAAI;AAAA,YAC7D;AAGA,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC;AAAA,YACF;AAGA,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC;AAAA,YACF;AAGA,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC;AAAA,YACF;AAGA,gBAAI,QAAQ,KAAK,GAAG,GAAG;AACrB;AAAA,YACF;AAEA,gBAAI,SAAS,OAAO,MAAM,KAAK,UAAU,CAAC,SAAS,KAAK,GAAG;AACzD,8BAAgB,OAAO,SAAS,MAAM,MAAM;AAAA,YAC9C,OAAO;AAEL,kBAAI,WAAW;AACb;AAAA,cACF;AAGA,kBAAI,KAAK,OAAO,MAAM,KAAK,GAAG;AAC5B;AAAA,cACF;AACA,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,cACF;AACA,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAChC;AAAA,cACF;AACA,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACtB;AAAA,cACF;AAEA,8BAAgB,OAAO;AAAA,YACzB;AAEA,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAK,GAAG;AAClC,0BAAY;AAAA,YACd;AAEA,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,SAAS,MAAM,MAAM;AAAA,UAC9B;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,OAAO;AAEf,cAAI,mBAAmB;AACrB,iBAAK,QAAQ;AAAA,UACf,WAAW,YAAY,KAAK,GAAG,GAAG;AAChC,gCAAoB;AAAA,UACtB;AAAA,QACF;AAGA,YAAI,KAAK,QAAQ,KAAK;AACpB,mBAAS,cAAc,KAAK,YAAY;AACxC,cAAI,QAAQ;AACV,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,gBAAgB,EAAE;AAAA,UACxD;AAAA,QACF;AAEA,aAAK,MAAM,KAAK;AAAA,UACd,MAAM;AAAA,UACN;AAAA,UACA,MAAM,CAAC,CAAC;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,aAAK,OAAO;AAAA,MACd;AAGA,WAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,MAAM,IAAI,UAAU;AACtD,WAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,OAAO,aAAa,UAAU;AAChE,WAAK,MAAM,KAAK,IAAI,UAAU;AAE9B,YAAM,IAAI,KAAK,MAAM;AAGrB,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAY,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAEpE,YAAI,CAAC,KAAK,OAAO;AAEf,gBAAM,UAAU,KAAK,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACnE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,SAAS,KAAK,EAAE,GAAG,CAAC;AAE1F,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAGA,UAAI,KAAK,OAAO;AACd,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,eAAK,MAAM,CAAC,EAAE,QAAQ;AAAA,QACxB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,CAAC,KAAK,QAAQ,cACb,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;AAAA,QAC5D,MAAM,IAAI,CAAC;AAAA,MACb;AACA,UAAI,KAAK,QAAQ,UAAU;AACzB,cAAM,OAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU,IAAI,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC;AACpF,cAAM,OAAO;AACb,cAAM,OAAO;AACb,cAAM,SAAS,KAAK,MAAM,OAAO,IAAI;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,QAAQ,GAAG;AACpD,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,YAAY,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AACnG,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAC/G,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,OAAO;AAAA,QACX,MAAM;AAAA,QACN,QAAQ,WAAW,IAAI,CAAC,CAAC,EAAE,IAAI,OAAK;AAAE,iBAAO,EAAE,MAAM,EAAE;AAAA,QAAG,CAAC;AAAA,QAC3D,OAAO,IAAI,CAAC,EAAE,QAAQ,cAAc,EAAE,EAAE,MAAM,QAAQ;AAAA,QACtD,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,EAAE,QAAQ,aAAa,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;AAAA,MACjF;AAEA,UAAI,KAAK,OAAO,WAAW,KAAK,MAAM,QAAQ;AAC5C,aAAK,MAAM,IAAI,CAAC;AAEhB,YAAI,IAAI,KAAK,MAAM;AACnB,YAAI,GAAG,GAAG,GAAG;AACb,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,cAAI,YAAY,KAAK,KAAK,MAAM,CAAC,CAAC,GAAG;AACnC,iBAAK,MAAM,CAAC,IAAI;AAAA,UAClB,WAAW,aAAa,KAAK,KAAK,MAAM,CAAC,CAAC,GAAG;AAC3C,iBAAK,MAAM,CAAC,IAAI;AAAA,UAClB,WAAW,YAAY,KAAK,KAAK,MAAM,CAAC,CAAC,GAAG;AAC1C,iBAAK,MAAM,CAAC,IAAI;AAAA,UAClB,OAAO;AACL,iBAAK,MAAM,CAAC,IAAI;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,KAAK,KAAK;AACd,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,eAAK,KAAK,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,OAAK;AAAE,mBAAO,EAAE,MAAM,EAAE;AAAA,UAAG,CAAC;AAAA,QAC9F;AAKA,YAAI,KAAK,OAAO;AAChB,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,eAAK,OAAO,CAAC,EAAE,SAAS,KAAK,MAAM,OAAO,KAAK,OAAO,CAAC,EAAE,IAAI;AAAA,QAC/D;AAGA,YAAI,KAAK,KAAK;AACd,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,gBAAM,KAAK,KAAK,CAAC;AACjB,eAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,gBAAI,CAAC,EAAE,SAAS,KAAK,MAAM,OAAO,IAAI,CAAC,EAAE,IAAI;AAAA,UAC/C;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,QACtC,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,KAAK;AACb,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAC9C,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClB,IAAI,CAAC;AACT,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AACpD,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B,WAAW,KAAK,MAAM,MAAM,UAAU,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AAC5D,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,iCAAiC,KAAK,IAAI,CAAC,CAAC,GAAG;AACjF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC,WAAW,KAAK,MAAM,MAAM,cAAc,mCAAmC,KAAK,IAAI,CAAC,CAAC,GAAG;AACzF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC;AAEA,aAAO;AAAA,QACL,MAAM,KAAK,QAAQ,WACf,SACA;AAAA,QACJ,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,KAAK,MAAM,MAAM;AAAA,QACzB,YAAY,KAAK,MAAM,MAAM;AAAA,QAC7B,MAAM,KAAK,QAAQ,WACd,KAAK,QAAQ,YACZ,KAAK,QAAQ,UAAU,IAAI,CAAC,CAAC,IAC7B,OAAO,IAAI,CAAC,CAAC,IACf,IAAI,CAAC;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,aAAa,IAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,KAAK,UAAU,GAAG;AAEnD,YAAI,CAAE,KAAK,KAAK,UAAU,GAAI;AAC5B;AAAA,QACF;AAGA,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACrD;AAAA,QACF;AAAA,MACF,OAAO;AAEL,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,iBAAiB,IAAI;AACvB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,CAAC,IAAI;AAAA,QACX;AAAA,MACF;AACA,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEzB,cAAM,OAAO,gCAAgC,KAAK,IAAI;AAEtD,YAAI,MAAM;AACR,iBAAO,KAAK,CAAC;AACb,kBAAQ,KAAK,CAAC;AAAA,QAChB;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MACzC;AAEA,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,KAAK,IAAI,GAAG;AACnB,YAAI,KAAK,QAAQ,YAAY,CAAE,KAAK,KAAK,UAAU,GAAI;AAErD,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,MAAM,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AACA,aAAO,WAAW,KAAK;AAAA,QACrB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AAAA,QAC9D,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AAAA,MACnE,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,QAAQ,KAAK,OAAO;AAClB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OACrC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AACjD,UAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,QAAQ,GAAG;AACjD,aAAO,MAAM,KAAK,YAAY,CAAC;AAC/B,UAAI,CAAC,MAAM;AACT,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO,WAAW,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK,KAAK;AAAA,IACjD;AAAA,EACF;AAAA,EAEA,SAAS,KAAK,WAAW,WAAW,IAAI;AACtC,QAAI,QAAQ,KAAK,MAAM,OAAO,SAAS,OAAO,KAAK,GAAG;AACtD,QAAI,CAAC,MAAO;AAGZ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,eAAe,EAAG;AAEjD,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AAEzC,QAAI,CAAC,YAAa,aAAa,aAAa,MAAM,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,IAAK;AAChG,YAAM,UAAU,MAAM,CAAC,EAAE,SAAS;AAClC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAE3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,SAAS,YAAY,KAAK,MAAM,OAAO,SAAS;AACvG,aAAO,YAAY;AAGnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AAErD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC/C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAE5E,YAAI,CAAC,OAAQ;AAEb,kBAAU,OAAO;AAEjB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACxB,wBAAc;AACd;AAAA,QACF,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC/B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC7C,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AAEA,sBAAc;AAEd,YAAI,aAAa,EAAG;AAGpB,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAEhE,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,SAAS,MAAM,CAAC,EAAE,SAAS,OAAO,UAAU,OAAO;AAG5F,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAClC,gBAAMC,QAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,MAAAA;AAAA,YACA,QAAQ,KAAK,MAAM,aAAaA,KAAI;AAAA,UACtC;AAAA,QACF;AAGA,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAG;AACpC,YAAM,mBAAmB,OAAO,KAAK,IAAI;AACzC,YAAM,0BAA0B,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AACjE,UAAI,oBAAoB,yBAAyB;AAC/C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO,OAAO,MAAM,IAAI;AACxB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS,KAAKC,SAAQ;AACpB,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,OAAO,KAAK,QAAQ,SAASA,QAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AAC3D,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,KAAKA,SAAQ;AACf,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACzC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,OAAO,KAAK,QAAQ,SAASA,QAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AAC3D,eAAO,YAAY;AAAA,MACrB,OAAO;AAEL,YAAI;AACJ,WAAG;AACD,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,QACtD,SAAS,gBAAgB,IAAI,CAAC;AAC9B,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,YAAI,IAAI,CAAC,MAAM,QAAQ;AACrB,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC1B,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,KAAKC,cAAa;AAC3B,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,UAAI;AACJ,UAAI,KAAK,MAAM,MAAM,YAAY;AAC/B,eAAO,KAAK,QAAQ,WAAY,KAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU,IAAI,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,IAAK,IAAI,CAAC;AAAA,MACnH,OAAO;AACL,eAAO,OAAO,KAAK,QAAQ,cAAcA,aAAY,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,MACvE;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAKA,IAAM,QAAQ;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EAUN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AAAA;AAAA;AAAA,EAGV,YAAY;AAAA,EACZ,MAAM;AACR;AAEA,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,MAAM,KAAK,MAAM,GAAG,EACvB,QAAQ,SAAS,MAAM,MAAM,EAC7B,QAAQ,SAAS,MAAM,MAAM,EAC7B,SAAS;AAEZ,MAAM,SAAS;AACf,MAAM,gBAAgB,KAAK,eAAe,EACvC,QAAQ,QAAQ,MAAM,MAAM,EAC5B,SAAS;AAEZ,MAAM,OAAO,KAAK,MAAM,IAAI,EACzB,QAAQ,SAAS,MAAM,MAAM,EAC7B,QAAQ,MAAM,iEAAiE,EAC/E,QAAQ,OAAO,YAAY,MAAM,IAAI,SAAS,GAAG,EACjD,SAAS;AAEZ,MAAM,OAAO;AAMb,MAAM,WAAW;AACjB,MAAM,OAAO,KAAK,MAAM,MAAM,GAAG,EAC9B,QAAQ,WAAW,MAAM,QAAQ,EACjC,QAAQ,OAAO,MAAM,IAAI,EACzB,QAAQ,aAAa,0EAA0E,EAC/F,SAAS;AAEZ,MAAM,YAAY,KAAK,MAAM,UAAU,EACpC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,eAAe,EAClC,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AAEZ,MAAM,aAAa,KAAK,MAAM,UAAU,EACrC,QAAQ,aAAa,MAAM,SAAS,EACpC,SAAS;AAMZ,MAAM,SAAS,EAAE,GAAG,MAAM;AAM1B,MAAM,MAAM;AAAA,EACV,GAAG,MAAM;AAAA,EACT,OAAO;AAAA;AAGT;AAEA,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,KAAK,EACnC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,eAAe,EAClC,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,YAAY,EAC5B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AAEZ,MAAM,IAAI,YAAY,KAAK,MAAM,UAAU,EACxC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,eAAe,EAClC,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,MAAM,IAAI,KAAK,EAChC,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AAKZ,MAAM,WAAW;AAAA,EACf,GAAG,MAAM;AAAA,EACT,MAAM;AAAA,IACJ;AAAA,EAEwE,EACvE,QAAQ,WAAW,MAAM,QAAQ,EACjC,QAAQ,QAAQ,mKAGkB,EAClC,SAAS;AAAA,EACZ,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA;AAAA,EACR,UAAU;AAAA,EACV,WAAW,KAAK,MAAM,OAAO,UAAU,EACpC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,iBAAiB,EACpC,QAAQ,YAAY,MAAM,QAAQ,EAClC,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,SAAS;AACd;AAKA,IAAM,SAAS;AAAA,EACb,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,KAAK;AAAA,EACL,KAAK;AAAA;AAAA,EAML,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,UAAU;AAAA,IACR,QAAQ;AAAA;AAAA;AAAA,IAGR,WAAW;AAAA,IACX,WAAW;AAAA;AAAA,EACb;AAAA,EACA,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,aAAa;AACf;AAIA,OAAO,eAAe;AACtB,OAAO,cAAc,KAAK,OAAO,WAAW,EAAE,QAAQ,gBAAgB,OAAO,YAAY,EAAE,SAAS;AAGpG,OAAO,YAAY;AAGnB,OAAO,cAAc;AAErB,OAAO,WAAW,KAAK,MAAM,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AAE5E,OAAO,SAAS,SAAS,KAAK,OAAO,SAAS,MAAM,EACjD,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AAEZ,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,WAAW,GAAG,EAC5D,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AAEZ,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,WAAW,GAAG,EAC5D,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AAEZ,OAAO,WAAW;AAElB,OAAO,UAAU;AACjB,OAAO,SAAS;AAChB,OAAO,WAAW,KAAK,OAAO,QAAQ,EACnC,QAAQ,UAAU,OAAO,OAAO,EAChC,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AAEZ,OAAO,aAAa;AAEpB,OAAO,MAAM,KAAK,OAAO,GAAG,EACzB,QAAQ,WAAW,OAAO,QAAQ,EAClC,QAAQ,aAAa,OAAO,UAAU,EACtC,SAAS;AAEZ,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,SAAS;AAEhB,OAAO,OAAO,KAAK,OAAO,IAAI,EAC3B,QAAQ,SAAS,OAAO,MAAM,EAC9B,QAAQ,QAAQ,OAAO,KAAK,EAC5B,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AAEZ,OAAO,UAAU,KAAK,OAAO,OAAO,EACjC,QAAQ,SAAS,OAAO,MAAM,EAC9B,QAAQ,OAAO,MAAM,MAAM,EAC3B,SAAS;AAEZ,OAAO,SAAS,KAAK,OAAO,MAAM,EAC/B,QAAQ,OAAO,MAAM,MAAM,EAC3B,SAAS;AAEZ,OAAO,gBAAgB,KAAK,OAAO,eAAe,GAAG,EAClD,QAAQ,WAAW,OAAO,OAAO,EACjC,QAAQ,UAAU,OAAO,MAAM,EAC/B,SAAS;AAMZ,OAAO,SAAS,EAAE,GAAG,OAAO;AAM5B,OAAO,WAAW;AAAA,EAChB,GAAG,OAAO;AAAA,EACV,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,MAAM,KAAK,yBAAyB,EACjC,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AAAA,EACZ,SAAS,KAAK,+BAA+B,EAC1C,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AACd;AAMA,OAAO,MAAM;AAAA,EACX,GAAG,OAAO;AAAA,EACV,QAAQ,KAAK,OAAO,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,SAAS;AAAA,EAC3D,iBAAiB;AAAA,EACjB,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AACR;AAEA,OAAO,IAAI,MAAM,KAAK,OAAO,IAAI,KAAK,GAAG,EACtC,QAAQ,SAAS,OAAO,IAAI,eAAe,EAC3C,SAAS;AAKZ,OAAO,SAAS;AAAA,EACd,GAAG,OAAO;AAAA,EACV,IAAI,KAAK,OAAO,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS;AAAA,EAClD,MAAM,KAAK,OAAO,IAAI,IAAI,EACvB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAS;AACd;AAMA,SAAS,YAAY,MAAM;AACzB,SAAO,KAEJ,QAAQ,QAAQ,GAAQ,EAExB,QAAQ,OAAO,GAAQ,EAEvB,QAAQ,2BAA2B,KAAU,EAE7C,QAAQ,MAAM,GAAQ,EAEtB,QAAQ,gCAAgC,KAAU,EAElD,QAAQ,MAAM,GAAQ,EAEtB,QAAQ,UAAU,GAAQ;AAC/B;AAMA,SAAS,OAAO,MAAM;AACpB,MAAI,MAAM,IACR,GACA;AAEF,QAAM,IAAI,KAAK;AACf,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,SAAK,KAAK,WAAW,CAAC;AACtB,QAAI,KAAK,OAAO,IAAI,KAAK;AACvB,WAAK,MAAM,GAAG,SAAS,EAAE;AAAA,IAC3B;AACA,WAAO,OAAO,KAAK;AAAA,EACrB;AAEA,SAAO;AACT;AAKA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAAYH,UAAS;AACnB,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI;AACtC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,UAAU;AACjE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AAEA,UAAM,QAAQ;AAAA,MACZ,OAAO,MAAM;AAAA,MACb,QAAQ,OAAO;AAAA,IACjB;AAEA,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;AAAA,IACxB,WAAW,KAAK,QAAQ,KAAK;AAC3B,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,SAAS,OAAO;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,OAAO;AAAA,MACxB;AAAA,IACF;AACA,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,IAAI,KAAKA,UAAS;AACvB,UAAMD,SAAQ,IAAI,OAAMC,QAAO;AAC/B,WAAOD,OAAM,IAAI,GAAG;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU,KAAKC,UAAS;AAC7B,UAAMD,SAAQ,IAAI,OAAMC,QAAO;AAC/B,WAAOD,OAAM,aAAa,GAAG;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACP,UAAM,IACH,QAAQ,YAAY,IAAI;AAE3B,SAAK,YAAY,KAAK,KAAK,MAAM;AAEjC,QAAI;AACJ,WAAO,OAAO,KAAK,YAAY,MAAM,GAAG;AACtC,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;AAAA,IACzC;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK,SAAS,CAAC,GAAG;AAC5B,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,IAAI,QAAQ,OAAO,MAAM,EAAE,QAAQ,UAAU,EAAE;AAAA,IACvD,OAAO;AACL,YAAM,IAAI,QAAQ,gBAAgB,CAAC,GAAG,SAAS,SAAS;AACtD,eAAO,UAAU,OAAO,OAAO,KAAK,MAAM;AAAA,MAC5C,CAAC;AAAA,IACH;AAEA,QAAI,OAAO,WAAW,QAAQ;AAE9B,WAAO,KAAK;AACV,UAAI,KAAK,QAAQ,cACZ,KAAK,QAAQ,WAAW,SACxB,KAAK,QAAQ,WAAW,MAAM,KAAK,CAAC,iBAAiB;AACtD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACJ;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,WAAW,KAAK,OAAO,SAAS,GAAG;AAG/C,iBAAO,OAAO,SAAS,CAAC,EAAE,OAAO;AAAA,QACnC,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AAEpC,YAAI,cAAc,UAAU,SAAS,eAAe,UAAU,SAAS,SAAS;AAC9E,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAChE,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACvC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AAC1C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,cAAc,UAAU,SAAS,eAAe,UAAU,SAAS,SAAS;AAC9E,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAChE,WAAW,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACxC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;AAAA,YAC7B,MAAM,MAAM;AAAA,YACZ,OAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAIA,eAAS;AACT,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,YAAY;AACjE,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,SAAS,eAAe;AACjE,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AAAE,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAAG;AAAA,QACvG,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAChE,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,wBAAwB,UAAU,SAAS,aAAa;AAC1D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAChE,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA,+BAAwB,OAAO,WAAW,IAAI;AAC9C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,UAAU,SAAS,QAAQ;AAC1C,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAChE,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAEA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAEA,SAAK,MAAM,MAAM;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,KAAK,SAAS,CAAC,GAAG;AACvB,SAAK,YAAY,KAAK,EAAE,KAAK,OAAO,CAAC;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAK,SAAS,CAAC,GAAG;AAC7B,QAAI,OAAO,WAAW;AAGtB,QAAI,YAAY;AAChB,QAAI;AACJ,QAAI,cAAc;AAGlB,QAAI,KAAK,OAAO,OAAO;AACrB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAClF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACrE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,aAAa,KAAK,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;AAAA,UACxK;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC9E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,aAAa,KAAK,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAAA,IACpK;AAGA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,YAAY,KAAK,SAAS,MAAM,MAAM;AAChF,kBAAY,UAAU,MAAM,GAAG,MAAM,QAAQ,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,YAAY,SAAS;AAC5I,WAAK,UAAU,MAAM,OAAO,YAAY;AAAA,IAC1C;AAEA,WAAO,KAAK;AACV,UAAI,CAAC,cAAc;AACjB,mBAAW;AAAA,MACb;AACA,qBAAe;AAGf,UAAI,KAAK,QAAQ,cACZ,KAAK,QAAQ,WAAW,UACxB,KAAK,QAAQ,WAAW,OAAO,KAAK,CAAC,iBAAiB;AACvD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACJ;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,MAAM,SAAS,UAAU,UAAU,SAAS,QAAQ;AACnE,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAC1D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,MAAM,SAAS,UAAU,UAAU,SAAS,QAAQ;AACnE,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC7D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,MAAM,GAAG;AAChD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI;AACnE,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AAIA,eAAS;AACT,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa;AAClE,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,SAAS,eAAe;AAClE,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AAAE,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAAG;AAAA,QACvG,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,QAAQ,WAAW,GAAG;AAC1D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC/B,qBAAW,MAAM,IAAI,MAAM,EAAE;AAAA,QAC/B;AACA,uBAAe;AACf,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,UAAU,SAAS,QAAQ;AAC1C,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AAEA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAKA,IAAM,WAAN,MAAe;AAAA,EACb,YAAYC,UAAS;AACnB,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,KAAK,MAAM,YAAY,SAAS;AAC9B,UAAM,QAAQ,cAAc,IAAI,MAAM,KAAK,EAAE,CAAC;AAC9C,QAAI,KAAK,QAAQ,WAAW;AAC1B,YAAM,MAAM,KAAK,QAAQ,UAAU,MAAM,IAAI;AAC7C,UAAI,OAAO,QAAQ,QAAQ,MAAM;AAC/B,kBAAU;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;AAEjC,QAAI,CAAC,MAAM;AACT,aAAO,iBACF,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;AAAA,IACN;AAEA,WAAO,uBACH,KAAK,QAAQ,aACb,OAAO,IAAI,IACX,QACC,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,WAAO;AAAA,EAAiB,KAAK;AAAA;AAAA,EAC/B;AAAA,EAEA,KAAK,MAAM;AACT,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM,OAAO,KAAK,SAAS;AACjC,QAAI,KAAK,QAAQ,WAAW;AAC1B,YAAM,KAAK,KAAK,QAAQ,eAAe,QAAQ,KAAK,GAAG;AACvD,aAAO,KAAK,KAAK,QAAQ,EAAE,KAAK,IAAI,MAAM,KAAK;AAAA;AAAA,IACjD;AAGA,WAAO,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA;AAAA,EACtC;AAAA,EAEA,KAAK;AACH,WAAO,KAAK,QAAQ,QAAQ,YAAY;AAAA,EAC1C;AAAA,EAEA,KAAK,MAAM,SAAS,OAAO;AACzB,UAAM,OAAO,UAAU,OAAO,MAC5B,WAAY,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;AACrE,WAAO,MAAM,OAAO,WAAW,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,WAAO,OAAO,IAAI;AAAA;AAAA,EACpB;AAAA,EAEA,SAAS,SAAS;AAChB,WAAO,aACF,UAAU,gBAAgB,MAC3B,iCACC,KAAK,QAAQ,QAAQ,OAAO,MAC7B;AAAA,EACN;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACd,WAAO,MAAM,IAAI;AAAA;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,MAAM;AAClB,QAAI,KAAM,QAAO,UAAU,IAAI;AAE/B,WAAO,uBAEH,SACA,eACA,OACA;AAAA,EACN;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS;AAChB,WAAO;AAAA,EAAS,OAAO;AAAA;AAAA,EACzB;AAAA,EAEA,UAAU,SAAS,OAAO;AACxB,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAM,MAAM,MAAM,QACd,IAAI,IAAI,WAAW,MAAM,KAAK,OAC9B,IAAI,IAAI;AACZ,WAAO,MAAM,UAAU,KAAK,IAAI;AAAA;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM;AACX,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,MAAM;AACP,WAAO,OAAO,IAAI;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,WAAO,SAAS,IAAI;AAAA,EACtB;AAAA,EAEA,KAAK;AACH,WAAO,KAAK,QAAQ,QAAQ,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM;AACR,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,MAAM,OAAO,MAAM;AACtB,WAAO,SAAS,KAAK,QAAQ,UAAU,KAAK,QAAQ,SAAS,IAAI;AACjE,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ;AAAA,IAC9B;AACA,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,MAAM,OAAO,MAAM;AACvB,WAAO,SAAS,KAAK,QAAQ,UAAU,KAAK,QAAQ,SAAS,IAAI;AACjE,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACT,aAAO,WAAW,KAAK;AAAA,IACzB;AACA,WAAO,KAAK,QAAQ,QAAQ,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM;AACT,WAAO;AAAA,EACT;AACF;AAMA,IAAM,eAAN,MAAmB;AAAA;AAAA,EAEjB,OAAO,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EAEA,GAAG,MAAM;AACP,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,MAAM;AACb,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,MAAM;AACR,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM;AACT,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM;AACT,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM,OAAO,MAAM;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,MAAM,OAAO,MAAM;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,KAAK;AACH,WAAO;AAAA,EACT;AACF;AAKA,IAAM,UAAN,MAAc;AAAA,EACZ,cAAc;AACZ,SAAK,OAAO,CAAC;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,WAAO,MACJ,YAAY,EACZ,KAAK,EAEL,QAAQ,mBAAmB,EAAE,EAE7B,QAAQ,iEAAiE,EAAE,EAC3E,QAAQ,OAAO,GAAG;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,cAAc,UAAU;AACtC,QAAI,OAAO;AACX,QAAI,uBAAuB;AAC3B,QAAI,KAAK,KAAK,eAAe,IAAI,GAAG;AAClC,6BAAuB,KAAK,KAAK,YAAY;AAC7C,SAAG;AACD;AACA,eAAO,eAAe,MAAM;AAAA,MAC9B,SAAS,KAAK,KAAK,eAAe,IAAI;AAAA,IACxC;AACA,QAAI,CAAC,UAAU;AACb,WAAK,KAAK,YAAY,IAAI;AAC1B,WAAK,KAAK,IAAI,IAAI;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,OAAOA,WAAU,CAAC,GAAG;AACxB,UAAM,OAAO,KAAK,UAAU,KAAK;AACjC,WAAO,KAAK,gBAAgB,MAAMA,SAAQ,MAAM;AAAA,EAClD;AACF;AAKA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,YAAYA,UAAS;AACnB,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,SAAS;AAC9D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,QAAQA,UAAS;AAC5B,UAAMI,UAAS,IAAI,QAAOJ,QAAO;AACjC,WAAOI,QAAO,MAAM,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,YAAY,QAAQJ,UAAS;AAClC,UAAMI,UAAS,IAAI,QAAOJ,QAAO;AACjC,WAAOI,QAAO,YAAY,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,MAAM,MAAM;AACxB,QAAI,MAAM,IACR,GACA,GACA,GACA,IACA,IACA,KACA,MACA,QACA,MACA,OACA,SACA,OACA,OACA,UACA,MACA,SACA,MACA,UACA;AAEF,UAAM,IAAI,OAAO;AACjB,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,cAAQ,OAAO,CAAC;AAGhB,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,GAAG;AACjH,cAAM,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,KAAK;AAChF,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,MAAM,IAAI,GAAG;AACzI,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SAAS;AACZ;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,KAAK,SAAS,GAAG;AACxB;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,iBAAO,KAAK,SAAS;AAAA,YACnB,KAAK,YAAY,MAAM,MAAM;AAAA,YAC7B,MAAM;AAAA,YACN,SAAS,KAAK,YAAY,MAAM,QAAQ,KAAK,YAAY,CAAC;AAAA,YAC1D,KAAK;AAAA,UAAO;AACd;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS;AAAA,YAAK,MAAM;AAAA,YAC9B,MAAM;AAAA,YACN,MAAM;AAAA,UAAO;AACf;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,mBAAS;AAGT,iBAAO;AACP,eAAK,MAAM,OAAO;AAClB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,oBAAQ,KAAK,SAAS;AAAA,cACpB,KAAK,YAAY,MAAM,OAAO,CAAC,EAAE,MAAM;AAAA,cACvC,EAAE,QAAQ,MAAM,OAAO,MAAM,MAAM,CAAC,EAAE;AAAA,YACxC;AAAA,UACF;AACA,oBAAU,KAAK,SAAS,SAAS,IAAI;AAErC,iBAAO;AACP,eAAK,MAAM,KAAK;AAChB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,kBAAM,MAAM,KAAK,CAAC;AAElB,mBAAO;AACP,iBAAK,IAAI;AACT,iBAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,sBAAQ,KAAK,SAAS;AAAA,gBACpB,KAAK,YAAY,IAAI,CAAC,EAAE,MAAM;AAAA,gBAC9B,EAAE,QAAQ,OAAO,OAAO,MAAM,MAAM,CAAC,EAAE;AAAA,cACzC;AAAA,YACF;AAEA,oBAAQ,KAAK,SAAS,SAAS,IAAI;AAAA,UACrC;AACA,iBAAO,KAAK,SAAS,MAAM,QAAQ,IAAI;AACvC;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,iBAAO,KAAK,MAAM,MAAM,MAAM;AAC9B,iBAAO,KAAK,SAAS,WAAW,IAAI;AACpC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,oBAAU,MAAM;AAChB,kBAAQ,MAAM;AACd,kBAAQ,MAAM;AACd,eAAK,MAAM,MAAM;AAEjB,iBAAO;AACP,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,mBAAO,MAAM,MAAM,CAAC;AACpB,sBAAU,KAAK;AACf,mBAAO,KAAK;AAEZ,uBAAW;AACX,gBAAI,KAAK,MAAM;AACb,yBAAW,KAAK,SAAS,SAAS,OAAO;AACzC,kBAAI,OAAO;AACT,oBAAI,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,SAAS,aAAa;AACjE,uBAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,sBAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACzG,yBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE;AAAA,kBAC5E;AAAA,gBACF,OAAO;AACL,uBAAK,OAAO,QAAQ;AAAA,oBAClB,MAAM;AAAA,oBACN,MAAM;AAAA,kBACR,CAAC;AAAA,gBACH;AAAA,cACF,OAAO;AACL,4BAAY;AAAA,cACd;AAAA,YACF;AAEA,wBAAY,KAAK,MAAM,KAAK,QAAQ,KAAK;AACzC,oBAAQ,KAAK,SAAS,SAAS,UAAU,MAAM,OAAO;AAAA,UACxD;AAEA,iBAAO,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK;AAC9C;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AAEX,iBAAO,KAAK,SAAS,KAAK,MAAM,IAAI;AACpC;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,iBAAO,KAAK,SAAS,UAAU,KAAK,YAAY,MAAM,MAAM,CAAC;AAC7D;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,MAAM,SAAS,KAAK,YAAY,MAAM,MAAM,IAAI,MAAM;AAC7D,iBAAO,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AACjD,oBAAQ,OAAO,EAAE,CAAC;AAClB,oBAAQ,QAAQ,MAAM,SAAS,KAAK,YAAY,MAAM,MAAM,IAAI,MAAM;AAAA,UACxE;AACA,iBAAO,MAAM,KAAK,SAAS,UAAU,IAAI,IAAI;AAC7C;AAAA,QACF;AAAA,QAEA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,QAAQ,UAAU;AAC5B,eAAW,YAAY,KAAK;AAC5B,QAAI,MAAM,IACR,GACA,OACA;AAEF,UAAM,IAAI,OAAO;AACjB,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,cAAQ,OAAO,CAAC;AAGhB,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,GAAG;AACjH,cAAM,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,KAAK;AAChF,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,MAAM,IAAI,GAAG;AAC/H,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,UAAU;AACb,iBAAO,SAAS,KAAK,MAAM,IAAI;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,MAAM,IAAI;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,MAAM,MAAM,MAAM,OAAO,KAAK,YAAY,MAAM,QAAQ,QAAQ,CAAC;AACtF;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,SAAS,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,IAAI;AACzD;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,iBAAO,SAAS,OAAO,KAAK,YAAY,MAAM,QAAQ,QAAQ,CAAC;AAC/D;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG,KAAK,YAAY,MAAM,QAAQ,QAAQ,CAAC;AAC3D;AAAA,QACF;AAAA,QACA,KAAK,YAAY;AACf,iBAAO,SAAS,SAAS,MAAM,IAAI;AACnC;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG;AACnB;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,iBAAO,SAAS,IAAI,KAAK,YAAY,MAAM,QAAQ,QAAQ,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,MAAM,IAAI;AAC/B;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,QAAN,MAAY;AAAA,EACV,YAAYJ,UAAS;AACnB,SAAK,UAAUA,YAAW;AAAA,EAC5B;AAAA,EAEA,OAAO,mBAAmB,oBAAI,IAAI;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,UAAU;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM;AAChB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO,UAAU;AACxC,SAAO,CAAC,MAAM;AACZ,MAAE,WAAW;AAEb,QAAI,QAAQ;AACV,YAAM,MAAM,mCACR,OAAO,EAAE,UAAU,IAAI,IAAI,IAC3B;AACJ,UAAI,OAAO;AACT,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC5B;AACA,UAAI,UAAU;AACZ,iBAAS,MAAM,GAAG;AAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AACT,aAAO,QAAQ,OAAO,CAAC;AAAA,IACzB;AACA,QAAI,UAAU;AACZ,eAAS,CAAC;AACV;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACF;AAEA,SAAS,cAAcD,QAAOK,SAAQ;AACpC,SAAO,CAAC,KAAK,KAAK,aAAa;AAC7B,QAAI,OAAO,QAAQ,YAAY;AAC7B,iBAAW;AACX,YAAM;AAAA,IACR;AAEA,UAAM,UAAU,EAAE,GAAG,IAAI;AACzB,UAAM,EAAE,GAAG,OAAO,UAAU,GAAG,QAAQ;AACvC,UAAM,aAAa,QAAQ,IAAI,QAAQ,IAAI,OAAO,QAAQ;AAG1D,QAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC9C,aAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;AAAA,IAC/E;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,aAAO,WAAW,IAAI,MAAM,0CACxB,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;AAAA,IAChE;AAEA,6BAAyB,GAAG;AAE5B,QAAI,IAAI,OAAO;AACb,UAAI,MAAM,UAAU;AAAA,IACtB;AAEA,QAAI,UAAU;AACZ,YAAM,YAAY,IAAI;AACtB,UAAI;AAEJ,UAAI;AACF,YAAI,IAAI,OAAO;AACb,gBAAM,IAAI,MAAM,WAAW,GAAG;AAAA,QAChC;AACA,iBAASL,OAAM,KAAK,GAAG;AAAA,MACzB,SAAS,GAAG;AACV,eAAO,WAAW,CAAC;AAAA,MACrB;AAEA,YAAM,OAAO,SAAS,KAAK;AACzB,YAAI;AAEJ,YAAI,CAAC,KAAK;AACR,cAAI;AACF,gBAAI,IAAI,YAAY;AAClB,qBAAO,WAAW,QAAQ,IAAI,UAAU;AAAA,YAC1C;AACA,kBAAMK,QAAO,QAAQ,GAAG;AACxB,gBAAI,IAAI,OAAO;AACb,oBAAM,IAAI,MAAM,YAAY,GAAG;AAAA,YACjC;AAAA,UACF,SAAS,GAAG;AACV,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,YAAI,YAAY;AAEhB,eAAO,MACH,WAAW,GAAG,IACd,SAAS,MAAM,GAAG;AAAA,MACxB;AAEA,UAAI,CAAC,aAAa,UAAU,SAAS,GAAG;AACtC,eAAO,KAAK;AAAA,MACd;AAEA,aAAO,IAAI;AAEX,UAAI,CAAC,OAAO,OAAQ,QAAO,KAAK;AAEhC,UAAI,UAAU;AACd,aAAO,WAAW,QAAQ,SAAS,OAAO;AACxC,YAAI,MAAM,SAAS,QAAQ;AACzB;AACA,qBAAW,MAAM;AACf,sBAAU,MAAM,MAAM,MAAM,MAAM,SAAS,KAAK,MAAM;AACpD,kBAAI,KAAK;AACP,uBAAO,KAAK,GAAG;AAAA,cACjB;AACA,kBAAI,QAAQ,QAAQ,SAAS,MAAM,MAAM;AACvC,sBAAM,OAAO;AACb,sBAAM,UAAU;AAAA,cAClB;AAEA;AACA,kBAAI,YAAY,GAAG;AACjB,qBAAK;AAAA,cACP;AAAA,YACF,CAAC;AAAA,UACH,GAAG,CAAC;AAAA,QACN;AAAA,MACF,CAAC;AAED,UAAI,YAAY,GAAG;AACjB,aAAK;AAAA,MACP;AAEA;AAAA,IACF;AAEA,QAAI,IAAI,OAAO;AACb,aAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAC/D,KAAK,CAAAC,SAAON,OAAMM,MAAK,GAAG,CAAC,EAC3B,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,OAAO,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAClH,KAAK,YAAUD,QAAO,QAAQ,GAAG,CAAC,EAClC,KAAK,UAAQ,IAAI,QAAQ,IAAI,MAAM,YAAY,IAAI,IAAI,IAAI,EAC3D,MAAM,UAAU;AAAA,IACrB;AAEA,QAAI;AACF,UAAI,IAAI,OAAO;AACb,cAAM,IAAI,MAAM,WAAW,GAAG;AAAA,MAChC;AACA,YAAM,SAASL,OAAM,KAAK,GAAG;AAC7B,UAAI,IAAI,YAAY;AAClB,eAAO,WAAW,QAAQ,IAAI,UAAU;AAAA,MAC1C;AACA,UAAI,OAAOK,QAAO,QAAQ,GAAG;AAC7B,UAAI,IAAI,OAAO;AACb,eAAO,IAAI,MAAM,YAAY,IAAI;AAAA,MACnC;AACA,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO,WAAW,CAAC;AAAA,IACrB;AAAA,EACF;AACF;AAKA,SAAS,OAAO,KAAK,KAAK,UAAU;AAClC,SAAO,cAAc,MAAM,KAAK,OAAO,KAAK,EAAE,KAAK,KAAK,QAAQ;AAClE;AAMA,OAAO,UACP,OAAO,aAAa,SAAS,KAAK;AAChC,SAAO,WAAW,EAAE,GAAG,OAAO,UAAU,GAAG,IAAI;AAC/C,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AAEA,OAAO,cAAc;AAErB,OAAO,WAAW;AAMlB,OAAO,MAAM,YAAY,MAAM;AAC7B,QAAM,aAAa,OAAO,SAAS,cAAc,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,EAAE;AAElF,OAAK,QAAQ,CAAC,SAAS;AAErB,UAAM,OAAO,EAAE,GAAG,KAAK;AAGvB,SAAK,QAAQ,OAAO,SAAS,SAAS,KAAK,SAAS;AAGpD,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ,CAAC,QAAQ;AAC/B,YAAI,CAAC,IAAI,MAAM;AACb,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC3C;AACA,YAAI,IAAI,UAAU;AAChB,gBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,cAAI,cAAc;AAEhB,uBAAW,UAAU,IAAI,IAAI,IAAI,YAAYE,OAAM;AACjD,kBAAI,MAAM,IAAI,SAAS,MAAM,MAAMA,KAAI;AACvC,kBAAI,QAAQ,OAAO;AACjB,sBAAM,aAAa,MAAM,MAAMA,KAAI;AAAA,cACrC;AACA,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,uBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;AAAA,UACvC;AAAA,QACF;AACA,YAAI,IAAI,WAAW;AACjB,cAAI,CAAC,IAAI,SAAU,IAAI,UAAU,WAAW,IAAI,UAAU,UAAW;AACnE,kBAAM,IAAI,MAAM,6CAA6C;AAAA,UAC/D;AACA,cAAI,WAAW,IAAI,KAAK,GAAG;AACzB,uBAAW,IAAI,KAAK,EAAE,QAAQ,IAAI,SAAS;AAAA,UAC7C,OAAO;AACL,uBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;AAAA,UACxC;AACA,cAAI,IAAI,OAAO;AACb,gBAAI,IAAI,UAAU,SAAS;AACzB,kBAAI,WAAW,YAAY;AACzB,2BAAW,WAAW,KAAK,IAAI,KAAK;AAAA,cACtC,OAAO;AACL,2BAAW,aAAa,CAAC,IAAI,KAAK;AAAA,cACpC;AAAA,YACF,WAAW,IAAI,UAAU,UAAU;AACjC,kBAAI,WAAW,aAAa;AAC1B,2BAAW,YAAY,KAAK,IAAI,KAAK;AAAA,cACvC,OAAO;AACL,2BAAW,cAAc,CAAC,IAAI,KAAK;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,IAAI,aAAa;AACnB,qBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,QACzC;AAAA,MACF,CAAC;AACD,WAAK,aAAa;AAAA,IACpB;AAGA,QAAI,KAAK,UAAU;AACjB,YAAM,WAAW,OAAO,SAAS,YAAY,IAAI,SAAS;AAC1D,iBAAW,QAAQ,KAAK,UAAU;AAChC,cAAM,eAAe,SAAS,IAAI;AAElC,iBAAS,IAAI,IAAI,IAAIA,UAAS;AAC5B,cAAI,MAAM,KAAK,SAAS,IAAI,EAAE,MAAM,UAAUA,KAAI;AAClD,cAAI,QAAQ,OAAO;AACjB,kBAAM,aAAa,MAAM,UAAUA,KAAI;AAAA,UACzC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,KAAK,WAAW;AAClB,YAAM,YAAY,OAAO,SAAS,aAAa,IAAI,UAAU;AAC7D,iBAAW,QAAQ,KAAK,WAAW;AACjC,cAAM,gBAAgB,UAAU,IAAI;AAEpC,kBAAU,IAAI,IAAI,IAAIA,UAAS;AAC7B,cAAI,MAAM,KAAK,UAAU,IAAI,EAAE,MAAM,WAAWA,KAAI;AACpD,cAAI,QAAQ,OAAO;AACjB,kBAAM,cAAc,MAAM,WAAWA,KAAI;AAAA,UAC3C;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,YAAY;AAAA,IACnB;AAGA,QAAI,KAAK,OAAO;AACd,YAAM,QAAQ,OAAO,SAAS,SAAS,IAAI,MAAM;AACjD,iBAAW,QAAQ,KAAK,OAAO;AAC7B,cAAM,WAAW,MAAM,IAAI;AAC3B,YAAI,MAAM,iBAAiB,IAAI,IAAI,GAAG;AACpC,gBAAM,IAAI,IAAI,CAAC,QAAQ;AACrB,gBAAI,OAAO,SAAS,OAAO;AACzB,qBAAO,QAAQ,QAAQ,KAAK,MAAM,IAAI,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAAC,SAAO;AACpE,uBAAO,SAAS,KAAK,OAAOA,IAAG;AAAA,cACjC,CAAC;AAAA,YACH;AAEA,kBAAM,MAAM,KAAK,MAAM,IAAI,EAAE,KAAK,OAAO,GAAG;AAC5C,mBAAO,SAAS,KAAK,OAAO,GAAG;AAAA,UACjC;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,IAAI,IAAID,UAAS;AACzB,gBAAI,MAAM,KAAK,MAAM,IAAI,EAAE,MAAM,OAAOA,KAAI;AAC5C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,SAAS,MAAM,OAAOA,KAAI;AAAA,YAClC;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAGA,QAAI,KAAK,YAAY;AACnB,YAAME,cAAa,OAAO,SAAS;AACnC,WAAK,aAAa,SAAS,OAAO;AAChC,YAAI,SAAS,CAAC;AACd,eAAO,KAAK,KAAK,WAAW,KAAK,MAAM,KAAK,CAAC;AAC7C,YAAIA,aAAY;AACd,mBAAS,OAAO,OAAOA,YAAW,KAAK,MAAM,KAAK,CAAC;AAAA,QACrD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB,CAAC;AACH;AAMA,OAAO,aAAa,SAAS,QAAQ,UAAU;AAC7C,MAAI,SAAS,CAAC;AACd,aAAW,SAAS,QAAQ;AAC1B,aAAS,OAAO,OAAO,SAAS,KAAK,QAAQ,KAAK,CAAC;AACnD,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,SAAS;AACZ,mBAAW,QAAQ,MAAM,QAAQ;AAC/B,mBAAS,OAAO,OAAO,OAAO,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,QACjE;AACA,mBAAW,OAAO,MAAM,MAAM;AAC5B,qBAAW,QAAQ,KAAK;AACtB,qBAAS,OAAO,OAAO,OAAO,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,UACjE;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,iBAAS,OAAO,OAAO,OAAO,WAAW,MAAM,OAAO,QAAQ,CAAC;AAC/D;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,OAAO,SAAS,cAAc,OAAO,SAAS,WAAW,eAAe,OAAO,SAAS,WAAW,YAAY,MAAM,IAAI,GAAG;AAC9H,iBAAO,SAAS,WAAW,YAAY,MAAM,IAAI,EAAE,QAAQ,SAAS,aAAa;AAC/E,qBAAS,OAAO,OAAO,OAAO,WAAW,MAAM,WAAW,GAAG,QAAQ,CAAC;AAAA,UACxE,CAAC;AAAA,QACH,WAAW,MAAM,QAAQ;AACvB,mBAAS,OAAO,OAAO,OAAO,WAAW,MAAM,QAAQ,QAAQ,CAAC;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAMA,OAAO,cAAc,cAAc,MAAM,WAAW,OAAO,WAAW;AAKtE,OAAO,SAAS;AAChB,OAAO,SAAS,OAAO;AACvB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,MAAM;AACrB,OAAO,YAAY;AACnB,OAAO,UAAU;AACjB,OAAO,QAAQ;AACf,OAAO,QAAQ;AAEf,IAAM,UAAU,OAAO;AACvB,IAAM,aAAa,OAAO;AAC1B,IAAM,MAAM,OAAO;AACnB,IAAM,aAAa,OAAO;AAC1B,IAAM,cAAc,OAAO;AAC3B,IAAM,QAAQ;AACd,IAAM,SAAS,OAAO;AACtB,IAAM,QAAQ,MAAM;", "names": ["noopTest", "lexer", "options", "text", "mangle", "smartypants", "parser", "src", "args", "ret", "walkTokens"]}