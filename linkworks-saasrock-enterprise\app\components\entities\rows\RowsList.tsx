import RowsListAndTable from "./RowsListAndTable";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { EntityWithDetails, PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import { ColumnDto } from "~/application/dtos/data/ColumnDto";
import KanbanSimple, { KanbanColumn } from "~/components/ui/lists/KanbanSimple";
import RowHelper from "~/utils/helpers/RowHelper";
import { useTranslation } from "react-i18next";
import { Colors } from "~/application/enums/shared/Colors";
import { Fragment, useEffect, useState } from "react";
import { Link, useParams } from "react-router";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import GridContainer from "~/components/ui/lists/GridContainer";
import { EntityViewWithDetails } from "~/utils/db/entities/entityViews.db.server";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import clsx from "clsx";
import EntityViewHelper from "~/utils/helpers/EntityViewHelper";
import RowColumnsHelper from "~/utils/helpers/RowColumnsHelper";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { RowHeaderDisplayDto } from "~/application/dtos/data/RowHeaderDisplayDto";
import EmptyState from "~/components/ui/emptyState/EmptyState";
import RenderCard from "./RenderCard";
import RowsLoadMoreCard from "~/components/ui/tables/RowsLoadMoreCard";
import { FilterDto } from "~/components/ui/input/InputFilters";
import { RowDisplayDefaultProperty } from "~/utils/helpers/PropertyHelper";
interface Props {
  view: "table" | "board" | "grid" | "card";
  items: RowWithDetails[];
  routes?: EntitiesApi.Routes;
  pagination?: PaginationDto;
  onEditRow?: (row: RowWithDetails) => void;
  currentView?: EntityViewWithDetails | null;
  selectedRows?: RowWithDetails[];
  onSelected?: (item: RowWithDetails[]) => void;
  readOnly?: boolean;
  onClickRoute?: (row: RowWithDetails) => string;
  onRemove?: (row: RowWithDetails) => void;
  ignoreColumns?: string[];
  columns?: ColumnDto[];
  searchInput?: string;
  entityTitle?: string;
  actions?: (row: RowWithDetails) => {
    title?: string;
    href?: string;
    onClick?: () => void;
    isLoading?: boolean;
    render?: React.ReactNode;
  }[];
  leftHeaders?: RowHeaderDisplayDto<RowWithDetails>[];
  rightHeaders?: RowHeaderDisplayDto<RowWithDetails>[];
  opened?: boolean;
  filters?: FilterDto[];
  // loading?: boolean;
  handleFilter?: any;
  searchInput?: string;
}
export default function RowsList(props: Props & { entity: EntityWithDetails | string }) {
  const appOrAdminData = useAppOrAdminData();

  const [entity, setEntity] = useState<EntityWithDetails>();
  const [columns, setColumns] = useState<ColumnDto[]>([]);
  const [groupBy, setGroupBy] = useState<{ property?: PropertyWithDetails } | undefined>();

  useEffect(() => {
    let entity: EntityWithDetails | undefined = undefined;
    let columns: ColumnDto[] = [];
    let groupBy: { property?: PropertyWithDetails } | undefined = undefined;

    if (typeof props.entity === "string") {
      entity = appOrAdminData.entities.find((e) => e.name === props.entity);
    } else {
      entity = props.entity;
    }

    if (entity) {
      const visibleProperties = entity?.properties?.filter((item) => !item?.isHidden);
      const property =
        visibleProperties && visibleProperties.length > 0 ? visibleProperties.reduce((min, current) => (current?.order < min?.order ? current : min)) : null;
      if (entity.isStepFormWizard) {
        columns = [
          {
            title: property?.title || "Id",
            name: property?.name || RowDisplayDefaultProperty.ID,
            visible: true,
          },
          {
            title: "status",
            name: RowDisplayDefaultProperty.STEP_FORM_WIZARD_STATUS,
            visible: true,
          },
          {
            title: "Steps",
            name: RowDisplayDefaultProperty.STEP_FORM_WIZARD_STEPS,
            visible: true,
          },
          {
            title: "Created at",
            name: RowDisplayDefaultProperty.CREATED_AT,
            visible: true,
          },
          {
            title: "Created by",
            name: RowDisplayDefaultProperty.CREATED_BY,
            visible: true,
          },
        ];
      } else {
        const systemView = entity.views.find((f) => f.isSystem);
        let view = props.currentView ?? systemView;
        if (!view) {
          columns = RowColumnsHelper.getDefaultEntityColumns(entity);
          if (props.view === "board") {
            columns = columns.filter((f) => f.name !== groupBy?.property?.name);
          }
          if (props.ignoreColumns) {
            columns = columns.filter((f) => !props.ignoreColumns?.includes(f.name));
          }

          if (props.view === "board") {
            const property = entity.properties.find((f) => f.type === PropertyType.SELECT && !f.isHidden);
            if (property) {
              groupBy = { property };
            }
          }
        } else {
          columns = view.properties
            .sort((a, b) => a.order - b.order)
            .map((f) => {
              return { name: f.name ?? "", title: "", visible: true };
            });
          if (props.ignoreColumns) {
            columns = columns.filter((f) => !props.ignoreColumns?.includes(f.name));
          }

          if (view.layout === "board") {
            columns = columns.filter((f) => f.name !== groupBy?.property?.name);
          }

          if (view.groupByPropertyId) {
            const property = entity.properties.find((f) => f.id === view?.groupByPropertyId);
            if (property) {
              groupBy = { property };
            }
          }
        }
      }
    }

    // if (props.readOnly) {
    //   columns = columns.filter((f) => ![RowDisplayDefaultProperty.FOLIO.toString()].includes(f.name));
    // }

    if (props.columns !== undefined) {
      columns = props.columns;
    }

    setEntity(entity);
    setColumns(columns);
    setGroupBy(groupBy);
  }, [appOrAdminData.entities, props]);

  if (!entity) {
    return null;
  } else if (columns.length === 0) {
    return null;
  }

  return <RowsListWrapped {...props} entity={entity} columns={columns} groupBy={groupBy} />;
}

function RowsListWrapped({
  view,
  entity,
  items,
  routes,
  columns,
  pagination,
  groupBy,
  onEditRow,
  currentView,
  selectedRows,
  onSelected,
  readOnly,
  onClickRoute,
  onRemove,
  actions,
  leftHeaders,
  rightHeaders,
  opened,
  filters,
  searchInput,
  // loading,
  handleFilter,
}: Props & {
  entity: EntityWithDetails;
  columns: ColumnDto[];
  groupBy?: { property?: PropertyWithDetails };
}) {
  const { t } = useTranslation();
  const params = useParams();
  const appOrAdminData = useAppOrAdminData();

  const [options, setOptions] = useState<KanbanColumn<RowWithDetails>[]>([]);
  useEffect(() => {
    if (groupBy?.property) {
      setOptions(
        groupBy.property.options.map((option) => {
          return {
            name: option.value,
            color: option.color,
            title: (
              <div className="flex items-center space-x-1">
                {option.name ? <div className="font-bold">{option.name}</div> : <div className="font-bold">{option.value}</div>}
              </div>
            ),
            value: (item: RowWithDetails) => (
              <RenderCard layout={view} item={item} entity={entity} columns={columns} allEntities={appOrAdminData.entities} routes={routes} actions={actions} />
            ),
            onClickRoute: (i: RowWithDetails) => EntityHelper.getRoutes({ routes, entity, item: i })?.edit ?? "",
            onNewRoute: (columnValue: string) => {
              let newRoute = EntityHelper.getRoutes({ routes, entity })?.new;
              if (newRoute) {
                return newRoute + `?${groupBy?.property?.name}=${columnValue}`;
              }
              return "";
            },
          };
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupBy]);

  return (
    <Fragment>
      {view == "table" && (
        <RowsListAndTable
          columns={columns}
          entity={entity}
          items={items}
          pagination={pagination}
          routes={routes}
          onFolioClick={onEditRow}
          onEditClick={onEditRow}
          onRelatedRowClick={onEditRow}
          allEntities={appOrAdminData.entities}
          editable={!readOnly}
          selectedRows={selectedRows}
          onSelected={onSelected}
          onRemove={onRemove}
          leftHeaders={leftHeaders}
          rightHeaders={rightHeaders}
          opened={opened}
          filters={filters}
          searchInput={searchInput}
          // loading={loading}
          handleFilter={handleFilter}
        />
      )}
      {view === "board" && groupBy && (
        <KanbanSimple
          className="pt-2"
          items={items}
          classNameWidth={clsx("")}
          filterValue={(item, column) => {
            if (groupBy.property) {
              const value = RowHelper.getPropertyValue({ entity, item, property: groupBy.property });
              if (column === null && !value) {
                return true;
              }
              return value === column?.name;
            }
            return false;
          }}
          columns={options}
          undefinedColumn={{
            name: t("shared.undefined"),
            color: Colors.UNDEFINED,
            title: (
              <div className="flex items-center space-x-1">
                <div className="font-bold">{t("shared.undefined")}</div>
              </div>
            ),
            value: (item: RowWithDetails) => {
              return (
                <div className="bg-background rounded-md">
                  <RenderCard
                    layout={view}
                    item={item}
                    entity={entity}
                    columns={columns}
                    allEntities={appOrAdminData.entities}
                    routes={routes}
                    actions={actions}
                  />
                </div>
              );
            },
            onClickRoute: (i: RowWithDetails) => EntityHelper.getRoutes({ routes, entity, item: i })?.edit ?? "",
          }}
          column={groupBy.property?.name ?? ""}
          renderEmpty={<EmptyCard className="w-full" />}
        />
      )}
      {view === "grid" && (
        <Fragment>
          {items.length === 0 ? (
            <EmptyState
              className="w-full py-8"
              // to={EntityHelper.getRoutes({ routes, entity })?.new ?? ""}
              captions={{
                // new: "Add",
                thereAreNo: "No " + t(entity.titlePlural),
              }}
            />
          ) : (
            <div className="space-y-2">
              {/* {pagination && (
            <GridPagination
              defaultPageSize={currentView?.pageSize ?? undefined}
              totalItems={pagination.totalItems}
              totalPages={pagination.totalPages}
              page={pagination.page}
              pageSize={pagination.pageSize}
            />
          )} */}
              <GridContainer {...(currentView ? EntityViewHelper.getGridLayout(currentView) : { columns: 3, gap: "xs" })}>
                {items.map((item) => {
                  const href = onClickRoute ? onClickRoute(item) : (EntityHelper.getRoutes({ routes, entity, item })?.overview ?? undefined);
                  if (onSelected && selectedRows !== undefined) {
                    return (
                      <ButtonSelectWrapper key={item.id} item={item} onSelected={onSelected} selectedRows={selectedRows}>
                        <RenderCard
                          layout={view}
                          item={item}
                          entity={entity}
                          columns={columns}
                          allEntities={appOrAdminData.entities}
                          routes={routes}
                          actions={actions}
                        />
                      </ButtonSelectWrapper>
                    );
                  }
                  const card = (
                    <div className={clsx("bg-background group relative rounded-md text-left", href && "hover:bg-secondary")}>
                      <RemoveButton item={item} readOnly={readOnly} onRemove={onRemove} />
                      <RenderCard
                        layout={view}
                        item={item}
                        entity={entity}
                        columns={columns}
                        allEntities={appOrAdminData.entities}
                        routes={routes}
                        actions={actions}
                        href={href}
                      />
                    </div>
                  );
                  return href ? (
                    <Link key={item.id} to={href}>
                      {card}
                    </Link>
                  ) : (
                    card
                  );
                  // return (
                  //   <Fragment key={item.id}>
                  //     <Link to={item.id}>
                  //       <div className="group w-full truncate rounded-md border border-border bg-background p-3 text-left shadow-2xs hover:bg-secondary">
                  //         <RenderCard layout={view} item={item} entity={entity} columns={columns} allEntities={appOrAdminData.entities} routes={routes} actions={actions} />
                  //       </div>
                  //     </Link>
                  //   </Fragment>
                  // );
                })}
                {items.length === 0 ? (
                  <Fragment>{readOnly ? <EmptyCard className="w-full" /> : <AddMoreCard entity={entity} routes={routes} />}</Fragment>
                ) : (
                  <Fragment>
                    <RowsLoadMoreCard pagination={pagination} currentView={currentView} />
                  </Fragment>
                )}
              </GridContainer>
            </div>
          )}
        </Fragment>
      )}
      {view === "card" && (
        <Fragment>
          {items.length === 0 ? (
            <EmptyState
              className="w-full py-8"
              // to={EntityHelper.getRoutes({ routes, entity })?.new ?? ""}
              captions={{
                // new: "Add",
                thereAreNo: "No " + t(entity.titlePlural),
              }}
            />
          ) : (
            <div className="flex flex-col gap-4">
              {items.map((item, index) => {
                let className = clsx("w-full");
                if (onSelected && selectedRows !== undefined) {
                  return (
                    <ButtonSelectWrapper className={clsx("group relative")} key={item.id} item={item} onSelected={onSelected} selectedRows={selectedRows}>
                      <div className={className}>
                        <RemoveButton item={item} readOnly={readOnly} onRemove={onRemove} />
                        <RenderCard
                          layout={view}
                          item={item}
                          entity={entity}
                          columns={columns}
                          allEntities={appOrAdminData.entities}
                          routes={routes}
                          actions={actions}
                        />
                      </div>
                    </ButtonSelectWrapper>
                  );
                }
                const href = onClickRoute ? onClickRoute(item) : (EntityHelper.getRoutes({ routes, entity, item })?.overview ?? undefined);
                const card = (
                  <div className={clsx(className, "group relative rounded-md text-left", href && "hover:bg-secondary")}>
                    <div className={className}>
                      <RenderCard
                        title={entity.title + (index + 1)}
                        readOnly={readOnly}
                        onRemove={onRemove}
                        layout={view}
                        item={item}
                        entity={entity}
                        columns={columns}
                        allEntities={appOrAdminData.entities}
                        routes={routes}
                        actions={actions}
                        href={href}
                      />
                    </div>
                  </div>
                );
                return !href ? (
                  <Link to={`${EntityHelper.getEntityRoute({ entity, params, appOrAdminData })}/${item.id}`} key={item.id} className="group relative w-full">
                    {/* <RowLinkButton entityName={entity.name} id={item.id} /> */}
                    {card}
                  </Link>
                ) : (
                    card
                );
              })}
              {/* {items.length === 0 ? (
                <Fragment>{readOnly ? <EmptyCard className="w-full" /> : <AddMoreCard className="w-64" entity={entity} routes={routes} />}</Fragment>
              ) : (
                <Fragment>
                  {!readOnly && <AddMoreCard className="w-64" entity={entity} routes={routes} />}
                  <RowsLoadMoreCard className="w-64" pagination={pagination} currentView={currentView} />
                </Fragment>
              )} */}
            </div>
          )}
        </Fragment>
      )}
    </Fragment>
  );
}

export function AddMoreCard({ entity, routes, className }: { entity: EntityWithDetails; routes?: EntitiesApi.Routes; className?: string }) {
  const { t } = useTranslation();
  return (
    <Fragment>
      <div className={className}>
        {routes && (
          <Link
            className={clsx(
              "border-border hover:border-border group flex h-full items-center rounded-md border-2 border-dashed p-2 text-left align-middle shadow-2xs hover:border-dotted hover:bg-slate-100",
              className
            )}
            to={EntityHelper.getRoutes({ routes, entity })?.new ?? ""}
          >
            <div className="text-foreground/80 mx-auto flex justify-center text-center align-middle text-sm font-medium">{t("shared.add")}</div>
          </Link>
        )}
      </div>
    </Fragment>
  );
}

export function EmptyCard({ className }: { className?: string }) {
  const { t } = useTranslation();
  return (
    <Fragment>
      <div className={className}>
        <div className="bg-background border-border group inline-block h-full w-full truncate rounded-md border-2 border-dashed p-12 text-left align-middle shadow-2xs">
          <div className="text-foreground/80 mx-auto flex justify-center text-center align-middle text-sm font-medium">{t("shared.noRecords")}</div>
        </div>
      </div>
    </Fragment>
  );
}

function ButtonSelectWrapper({
  item,
  onSelected,
  selectedRows,
  children,
  className,
}: {
  item: RowWithDetails;
  selectedRows: RowWithDetails[];
  onSelected: (item: RowWithDetails[]) => void;
  children: React.ReactNode;
  className?: string;
}) {
  const isSelected = selectedRows.find((f) => f.id === item.id);
  return (
    <div className={clsx(className, "group relative rounded-md text-left", isSelected ? "bg-theme-50 hover:bg-theme-50" : "hover:bg-secondary bg-background")}>
      <button
        type="button"
        className="absolute top-0 right-0 mt-2 mr-2 origin-top-right justify-center"
        onClick={() => {
          if (isSelected) {
            onSelected(selectedRows.filter((f) => f.id !== item.id));
          } else {
            onSelected([...(selectedRows ?? []), item]);
          }
        }}
      >
        {isSelected ? (
          <svg
            fill="currentColor"
            className="h-5 w-5 text-teal-700"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            width="50"
            height="50"
            viewBox="0 0 50 50"
          >
            <path d="M 39 4 L 11 4 C 7.140625 4 4 7.140625 4 11 L 4 39 C 4 42.859375 7.140625 46 11 46 L 39 46 C 42.859375 46 46 42.859375 46 39 L 46 11 C 46 7.140625 42.859375 4 39 4 Z M 23.085938 34.445313 L 13.417969 25.433594 L 14.78125 23.96875 L 22.914063 31.554688 L 36.238281 15.832031 L 37.761719 17.125 Z"></path>
          </svg>
        ) : (
          <svg
            fill="currentColor"
            className="h-5 w-5 text-teal-700"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            width="50"
            height="50"
            viewBox="0 0 50 50"
          >
            <path d="M 39 4 L 11 4 C 7.101563 4 4 7.101563 4 11 L 4 39 C 4 42.898438 7.101563 46 11 46 L 39 46 C 42.898438 46 46 42.898438 46 39 L 46 11 C 46 7.101563 42.898438 4 39 4 Z M 42 39 C 42 40.699219 40.699219 42 39 42 L 11 42 C 9.300781 42 8 40.699219 8 39 L 8 11 C 8 9.300781 9.300781 8 11 8 L 39 8 C 40.699219 8 42 9.300781 42 11 Z"></path>
          </svg>
        )}
      </button>
      {children}
    </div>
  );
}

export function RemoveButton({ item, readOnly, onRemove ,className}: { item: RowWithDetails; readOnly?: boolean; onRemove?: (item: RowWithDetails) => void ,className?:any}) {
  return (
    <Fragment>
      {onRemove && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            onRemove(item);
          }}
          type="button"
          disabled={readOnly}
          className={clsx(
            "text-foreground bg-muted-background hover:bg-destructive-lighter absolute top-0 right-0 mt-1.5 mr-2 origin-top-right justify-center rounded-md p-2",
            readOnly ? "cursor-not-allowed" : "hover:text-destructive",
            className
          )}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM8 9H16V19H8V9ZM15.5 4L14.5 3H9.5L8.5 4H5V6H19V4H15.5Z" fill="currentColor" />
          </svg>
        </button>
      )}
    </Fragment>
  );
}
