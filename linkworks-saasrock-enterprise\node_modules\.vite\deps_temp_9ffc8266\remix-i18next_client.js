import "./chunk-PLDDJCW6.js";

// node_modules/remix-i18next/build/client.js
function getInitialNamespaces() {
  let namespaces = Object.values(window.__reactRouterRouteModules).flatMap((route) => {
    if (typeof route?.handle !== "object")
      return [];
    if (!route.handle)
      return [];
    if (!("i18n" in route.handle))
      return [];
    if (typeof route.handle.i18n === "string")
      return [route.handle.i18n];
    if (Array.isArray(route.handle.i18n) && route.handle.i18n.every((value) => typeof value === "string")) {
      return route.handle.i18n;
    }
    return [];
  });
  return [...namespaces];
}
export {
  getInitialNamespaces
};
//# sourceMappingURL=remix-i18next_client.js.map
