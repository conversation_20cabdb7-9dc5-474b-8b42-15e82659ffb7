import { ActionFunction, LoaderFunctionArgs, MetaFunction, redirect, useLoaderData } from "react-router";
import { useActionData, useSubmit } from "react-router";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { Colors } from "~/application/enums/shared/Colors";
import TenantCell from "~/components/core/tenants/TenantCell";
import UserBadge from "~/components/core/users/UserBadge";
import ErrorBanner from "~/components/ui/banners/ErrorBanner";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import ServerError from "~/components/ui/errors/ServerError";
import InputGroup from "~/components/ui/forms/InputGroup";
import InputCheckboxWithDescription from "~/components/ui/input/InputCheckboxWithDescription";
import InputSelector from "~/components/ui/input/InputSelector";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import Modal from "~/components/ui/modals/Modal";
import TableSimple from "~/components/ui/tables/TableSimple";
import { getTranslations } from "~/locale/i18next.server";
import { i18nConfig } from "~/locale/i18n";
import {
  StepFormWizardWithDetails,
  getStepFormWizard,
  updateStepFormWizard,
  createStepFormWizardSession,
} from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import { getStepFormWizardSessions } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { StepFormWizardCandidateDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardCandidateDto";
import { StepFormWizardFilterDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterDto";
import { StepFormWizardFilterMetadataDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterMetadataDto";
import { StepFormWizardFilterType, StepFormWizardFilterTypes } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterTypes";
import StepFormWizardService from "~/custom/modules/stepFormWizard/services/StepFormWizardService";
import StepFormWizardFilterUtils from "~/custom/modules/stepFormWizard/utils/StepFormWizardFilterUtils";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
type LoaderData = {
  meta: MetaTagsDto;
  item: StepFormWizardWithDetails;
  candidates: StepFormWizardCandidateDto[];
  metadata: StepFormWizardFilterMetadataDto;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  const data: LoaderData = {
    meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
    item,
    candidates: await StepFormWizardService.getCandidates(item),
    metadata: await StepFormWizardService.getMetadata(),
  };
  return data;
};

type ActionData = {
  error?: string;
  success?: string;
};
export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  if (action === "set-filters") {
    const filters: { type: StepFormWizardFilterType; value: string | null }[] = form.getAll("filters[]").map((f) => {
      return JSON.parse(f.toString());
    });
    await updateStepFormWizard(item.id, {
      filters,
    });
    return Response.json({ success: "Step form wizard filters updated" });
  } else if (action === "set-realtime") {
    const realtime = form.get("realtime") === "true";
    await updateStepFormWizard(item.id, {
      realtime,
    });
    return Response.json({ success: "Step form wizard filters updated" });
  } else if (action === "save-sessions") {
    const candidates: StepFormWizardCandidateDto[] = form.getAll("candidates[]").map((f) => {
      return JSON.parse(f.toString());
    });
    const existingSessions = await getStepFormWizardSessions({
      stepFormWizardId: item.id,
    });
    await Promise.all(
      candidates.map(async (candidate) => {
        const existing = existingSessions.find((f) => f.tenantId === (candidate.tenant?.id ?? null) && f.userId === candidate.user.id);
        if (existing) {
          return;
        }
        await createStepFormWizardSession(item, {
          userId: candidate.user.id,
          tenantId: candidate.tenant?.id ?? null,
          status: "active",
          matchingFilters: item.filters,
          createdRealtime: false,
        });
      })
    );
    return Response.json({ success: "Step form wizard sessions manually set" });
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();

  const [showModal, setShowModal] = useState<{ item?: StepFormWizardFilterDto; idx?: number }>();

  const [filters, setFilters] = useState<StepFormWizardFilterDto[]>(
    data.item.filters.map((f) => {
      return {
        type: f.type as StepFormWizardFilterDto["type"],
        value: f.value,
      };
    })
  );

  useEffect(() => {
    const form = new FormData();
    form.append("action", "set-filters");
    filters.forEach((filter) => {
      form.append("filters[]", JSON.stringify(filter));
    });
    submit(form, {
      method: "post",
    });
  }, [filters, submit]);

  function onSetRealtime(realtime: boolean) {
    const form = new FormData();
    form.append("action", "set-realtime");
    form.append("realtime", realtime ? "true" : "false");
    submit(form, {
      method: "post",
    });
  }
  function onSaveFilter(item: StepFormWizardFilterDto) {
    const idx = showModal?.idx;
    if (idx !== undefined) {
      filters[idx] = item;
    } else {
      filters.push(item);
    }
    setFilters([...filters]);
    setShowModal(undefined);
  }

  function onDeleteFilter() {
    const idx = showModal?.idx;
    if (idx !== undefined) {
      filters.splice(idx, 1);
      setFilters([...filters]);
    }
    setShowModal(undefined);
  }

  function onSaveSessions() {
    const form = new FormData();
    form.append("action", "save-sessions");
    data.candidates.forEach((c) => {
      form.append("candidates[]", JSON.stringify(c));
    });
    submit(form, {
      method: "post",
    });
  }

  return (
    <div className="mx-auto flex-1 space-y-5 overflow-x-auto px-2 py-2 xl:overflow-y-auto">
      {data.item.active && <ErrorBanner title={t("shared.warning")} text="You cannot edit an active step form wizard." />}

      <InputGroup title={t("stepFormWizard.object.filters")} description="Filters are used to determine if the step form wizard should be shown to the user.">
        <div>
          <InputCheckboxWithDescription
            disabled={data.item.active}
            name="realtime"
            title="Realtime"
            description="Sessions will be created in realtime when the user matches the filters"
            value={data.item.realtime}
            setValue={(e) => onSetRealtime(Boolean(e))}
          />

          <div className="space-y-2">
            {filters.map((filter, idx) => {
              return (
                <button
                  key={idx}
                  type="button"
                  disabled={data.item.active}
                  onClick={() => setShowModal({ item: filter, idx })}
                  className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-3 text-center focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"
                >
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="font-medium">{filter.type}</div>
                    {filter.value !== null && (
                      <>
                        <div>→</div>
                        <div className="text-muted-foreground italic">{StepFormWizardFilterUtils.parseValue({ t, filter, metadata: data.metadata })}</div>
                      </>
                    )}
                  </div>
                </button>
              );
            })}

            <div className="">
              <button
                type="button"
                disabled={data.item.active}
                onClick={() => setShowModal({ item: undefined, idx: undefined })}
                className="border-border hover:border-border relative block w-full rounded-lg border-2 border-dashed p-3 text-center focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"
              >
                <span className="text-foreground block text-sm font-medium">Add filter</span>
              </button>
            </div>
          </div>

          {/* <div className="flex justify-end pt-4">
            <LoadingButton actionName="set-filters" type="submit">
              {t("shared.save")}
            </LoadingButton>
          </div> */}

          <StepFormWizardFilterModal
            open={showModal !== undefined}
            item={showModal?.item}
            idx={showModal?.idx}
            onClose={() => setShowModal(undefined)}
            onSave={onSaveFilter}
            metadata={data.metadata}
            onDelete={onDeleteFilter}
          />
        </div>
      </InputGroup>

      <div className="space-y-3">
        <div className="flex justify-between space-x-2">
          <div>
            <h3 className="text-foreground text-sm leading-3 font-medium">Candidates</h3>
            <p className="text-muted-foreground mt-1 text-sm">Users that match the filters.</p>
          </div>
          <div>
            {!data.item.realtime && (
              <ButtonPrimary disabled={data.item.active} onClick={onSaveSessions}>
                Save {data.candidates.length} sessions
              </ButtonPrimary>
            )}
          </div>
        </div>
        <TableSimple
          items={data.candidates}
          headers={[
            {
              name: "tenant",
              title: t("models.tenant.object"),
              value: (item) => <TenantCell item={item.tenant} />,
            },
            {
              name: "user",
              title: t("models.user.object"),
              value: (i) => <UserBadge item={i.user} />,
            },
            {
              name: "matchingFilters",
              title: t("stepFormWizard.filter.matching"),
              value: (i) => (
                <div className="flex flex-col">
                  {i.matchingFilters.length === 0 && <div className="text-muted-foreground italic">No filters - All users are candidates</div>}
                  {i.matchingFilters.map((filter, idx) => {
                    return (
                      <div key={idx} className="text-sm">
                        <div className="flex items-center space-x-2">
                          <div className="font-medium">{filter.type}</div>
                          {filter.value !== null && (
                            <>
                              <div>→</div>
                              <div className="text-muted-foreground italic">{StepFormWizardFilterUtils.parseValue({ t, filter, metadata: data.metadata })}</div>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ),
            },
          ]}
        />
      </div>

      <ActionResultModal actionData={actionData} showSuccess={false} />
    </div>
  );
}

function StepFormWizardFilterModal({
  item,
  idx,
  open,
  onClose,
  onSave,
  onDelete,
  metadata,
}: {
  item?: StepFormWizardFilterDto;
  idx: number | undefined;
  open: boolean;
  onClose: () => void;
  onSave: (item: StepFormWizardFilterDto) => void;
  onDelete: () => void;
  metadata: StepFormWizardFilterMetadataDto;
}) {
  const { t } = useTranslation();
  const appOrAdminData = useAppOrAdminData();
  const [filter, setFilter] = useState<StepFormWizardFilterDto>();
  useEffect(() => {
    if (item) {
      setFilter(item);
    } else {
      setFilter({ type: "user.is", value: appOrAdminData.user.id });
    }
  }, [appOrAdminData.user.id, item, idx]);

  function onConfirm() {
    if (filter) {
      onSave(filter);
    }
  }

  return (
    <Modal open={open} setOpen={onClose} size="md">
      <div className="inline-block w-full p-1 text-left align-bottom sm:align-middle">
        <input name="action" type="hidden" value="create" readOnly hidden />
        <div className="mt-3 text-center sm:mt-5">
          <h3 className="text-foreground text-lg leading-6 font-medium">{idx === undefined ? "Add filter" : "Edit filter"}</h3>
        </div>
        <div className="mt-4 space-y-2">
          <InputSelector
            name="type"
            title={t("stepFormWizard.object.type")}
            value={filter?.type}
            withSearch={true}
            setValue={(e) => setFilter({ ...filter, type: e as StepFormWizardFilterType, value: null })}
            options={StepFormWizardFilterTypes.map((f) => {
              return { value: f, name: f };
            })}
            required
          />
          {filter?.type === "tenant.is" ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={metadata.tenants.map((t) => {
                return { value: t.id, name: t.name };
              })}
              required
            />
          ) : filter?.type === "user.is" ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={metadata.users.map((t) => {
                return { value: t.id, name: t.email };
              })}
              required
            />
          ) : filter?.type === "user.roles.contains" || filter?.type === "user.roles.notContains" ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              withColors={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={metadata.roles.map((t) => {
                return {
                  value: t.name,
                  name: t.type === "admin" ? `[admin] ${t.name}` : t.name,
                  color: t.type === "admin" ? Colors.RED : Colors.INDIGO,
                };
              })}
              required
            />
          ) : filter?.type === "tenant.subscription.products.has" ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={metadata.subscriptionProducts.map((f) => {
                return { value: f.id, name: t(f.title) };
              })}
              required
            />
          ) : filter?.type.startsWith("tenant.user.entity") ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              withColors={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={appOrAdminData.entities.map((f) => {
                return {
                  value: f.id,
                  name: t(f.titlePlural),
                };
              })}
              required
            />
          ) : filter?.type === "user.language" ? (
            <InputSelector
              name="value"
              title={t("stepFormWizard.filter.value")}
              value={filter.value ?? undefined}
              withSearch={true}
              withColors={true}
              setValue={(e) => setFilter({ ...filter, value: e?.toString() ?? null })}
              options={i18nConfig.supportedLngs.map((f) => {
                return {
                  value: f,
                  name: t("shared.locales." + f),
                };
              })}
              required
            />
          ) : (
            <></>
            // <InputText
            //   name="value"
            //   title={t("stepFormWizard.filter.value")}
            //   value={filter.value ?? undefined}
            //   setValue={(e) => setFilter({ ...filter, value: e.toString() ?? null })}
            //   required
            // />
          )}
        </div>
        <div className="border-border mt-3 flex justify-between border-t pt-3">
          <div>
            <ButtonSecondary destructive type="button" onClick={onDelete} className="flex justify-center" disabled={idx === undefined}>
              {t("shared.delete")}
            </ButtonSecondary>
          </div>
          <div className="flex space-x-2">
            <ButtonSecondary type="button" onClick={onClose} className="flex justify-center">
              {t("shared.cancel")}
            </ButtonSecondary>
            <ButtonPrimary disabled={!getUserHasPermission(appOrAdminData, "admin.stepFormWizard.update")} onClick={onConfirm} className="flex justify-center">
              {t("shared.save")}
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </Modal>
  );
}

export function ErrorBoundary() {
  return <ServerError />;
}
