export default function JourneyIconFilled({ className }: { className?: string }) {
  return (
    <svg className={className} fill="currentColor" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="50" height="50" viewBox="0 0 50 50">
      <path d="M 44 2 C 42.179688 2.003906 40.589844 3.234375 40.132813 5 L 16 5 C 12.257813 5 9.109375 7.59375 8.242188 11.078125 C 6.363281 11.4375 5.003906 13.082031 5 15 C 5.003906 16.839844 6.261719 18.445313 8.050781 18.878906 C 8.492188 22.875 11.890625 26 16 26 L 34 26 C 36.703125 26 38.972656 27.765625 39.730469 30.214844 C 38.101563 30.757813 37.003906 32.28125 37 34 C 37 35.796875 38.203125 37.371094 39.933594 37.851563 C 39.523438 40.769531 37.035156 43 34 43 L 9.867188 43 C 9.414063 41.234375 7.824219 40 6 40 C 3.789063 40 2 41.789063 2 44 C 2 46.210938 3.789063 48 6 48 C 7.820313 47.996094 9.410156 46.765625 9.867188 45 L 34 45 C 38.109375 45 41.507813 41.875 41.949219 37.878906 C 43.738281 37.445313 44.996094 35.839844 45 34 C 45 32.082031 43.636719 30.4375 41.753906 30.074219 C 40.890625 26.59375 37.742188 24 34 24 L 16 24 C 12.964844 24 10.480469 21.769531 10.066406 18.855469 C 11.800781 18.375 13 16.796875 13 15 C 13 13.28125 11.898438 11.757813 10.269531 11.210938 C 11.027344 8.765625 13.296875 7 16 7 L 40.128906 7 C 40.585938 8.765625 42.175781 10 44 10 C 46.210938 10 48 8.210938 48 6 C 48 3.789063 46.210938 2 44 2 Z"></path>
    </svg>
  );
}
