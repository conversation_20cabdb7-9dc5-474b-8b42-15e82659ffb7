import { LoaderFunctionArgs } from "react-router";
import { getTranslations } from "~/locale/i18next.server";
import { getStepFormWizardSessionsWithPagination, StepFormWizardSessionWithDetails } from "../../db/stepFormWizardSessions.db.server";
import { StepFormWizardFilterMetadataDto } from "../../dtos/StepFormWizardFilterMetadataDto";
import StepFormWizardService, { StepFormWizardSummaryDto } from "../../services/StepFormWizardService";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export namespace StepFormWizardSummaryApi {
  export type LoaderData = {
    meta: MetaTagsDto;
    summary: StepFormWizardSummaryDto;
    sessions: {
      items: StepFormWizardSessionWithDetails[];
      metadata: StepFormWizardFilterMetadataDto;
    };
  };
  export const loader = async ({ request }: LoaderFunctionArgs) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.view");
    const { t } = await getTranslations(request);

    const data: LoaderData = {
      meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
      summary: await StepFormWizardService.getSummary(),
      sessions: {
        items: (
          await getStepFormWizardSessionsWithPagination({
            pagination: { page: 1, pageSize: 10 },
          })
        ).items,
        metadata: await StepFormWizardService.getMetadata(),
      },
    };
    return data;
  };
}
