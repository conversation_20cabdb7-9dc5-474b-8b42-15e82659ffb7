import React, { ReactNode, useEffect, useRef, useState } from "react";
import clsx from "clsx";

interface DropdownButtonProps {
  title?: string;
  className?: string;
  icon?: ReactNode;
  onClick?: (e: React.MouseEvent) => void;
  options: any[];
  isStepFormWizard?: boolean;
}

const DropdownButton = ({ title, className = "", icon, onClick, options = [], isStepFormWizard }: DropdownButtonProps) => {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open]);

  const handleToggle = (e) => {
    e.stopPropagation();
    setOpen((prev) => !prev);
    onClick?.(e);
  };

  const handleOptionClick = (option: any) => {
    console.log("Option clicked:", option);
    option.onclick?.();
    setOpen(false);
  };

  return (
    <div className={clsx("relative inline-block text-left", className)} ref={dropdownRef}>
      <button onClick={handleToggle} className="bg-primary inline-flex h-8 w-20 cursor-pointer items-center justify-between rounded px-2 hover:bg-blue-700">
        <div className="text-primary-foreground text-center text-[14px] leading-normal font-medium tracking-[0.14px]">{title}</div>
        <div className="relative h-4 w-4 overflow-hidden">
          <div className="absolute top-0 left-0 h-4 w-4 cursor-pointer opacity-90">{icon}</div>
          {/* <div className="w-2 h-1 left-[4.28px] top-[6px] absolute bg-white" /> */}
          <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_6363_27374)">
              <path
                d="M10.6208 6.695L8.03417 9.28167L5.4475 6.695C5.1875 6.435 4.7675 6.435 4.5075 6.695C4.2475 6.955 4.2475 7.375 4.5075 7.635L7.5675 10.695C7.8275 10.955 8.2475 10.955 8.5075 10.695L11.5675 7.635C11.8275 7.375 11.8275 6.955 11.5675 6.695C11.3075 6.44167 10.8808 6.435 10.6208 6.695Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_6363_27374">
                <rect width="16" height="16" fill="white" transform="translate(0.03125 0.5)" />
              </clipPath>
            </defs>
          </svg>
        </div>
      </button>

      {open && options.length > 0 && (
        <div className="absolute right-[2px] z-50 mt-1 w-40 rounded border border-gray-200 bg-white shadow-md">
          {options.map((option, index) => (
            <div key={index} onClick={() => handleOptionClick(option)} className="hover:bg-primary-light cursor-pointer px-4 py-2 text-sm text-gray-700">
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DropdownButton;
