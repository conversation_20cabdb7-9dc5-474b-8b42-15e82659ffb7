APP_NAME=MyAppName
SESSION_SECRET=
# WEBHOOKS_URL=https://...ngrok-free.app
# PORTAL_SERVER_URL=http://localhost:3001
# PORTAL_SERVER_FLY_TOKEN="FlyV1 ..."

# SQLite
# DATABASE_URL="file:./dev.db?connection_limit=1&connect_timeout=30"
# PostgreSQL
DATABASE_URL="postgres://{USER}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}"

# MASTER ADMIN ACCESS TOKEN
API_ACCESS_TOKEN=1234567890
JWT_SECRET=abc123
CRYPTO_SECRET=abc123

## STORE FILES IN SUPABASE
SUPABASE_API_URL=
SUPABASE_KEY=
SUPABASE_ANON_PUBLIC_KEY=

#PRICING
STRIPE_SK=
STRIPE_WEBHOOK_SIGNING_SECRET=

# EMAILS
SUPPORT_EMAIL=
POSTMARK_SERVER_TOKEN=
POSTMARK_FROM_EMAIL=hello@...
COMPANY_NAME=
COMPANY_ADDRESS=

# CONTACT FORM
INTEGRATIONS_CONTACT_FORMSPREE=

# NEWSLETTER FORM
CONVERTKIT_APIKEY=
CONVERTKIT_FORM=

# CHAT BUBBLE
CRISP_CHAT_WEBSITE_ID=

# ANALYTICS
ANALYTICS_GA_TRACKING_ID=

# AUTH METHOD: GOOGLE
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_SECRET_KEY=

# AUTH METHOD: GITHUB
GITHUB_OAUTH_CLIENT_ID=
GITHUB_OAUTH_CLIENT_SECRET=

# AUTH SECURITY
AUTH_RECAPTCHA_SITE_KEY=
AUTH_RECAPTCHA_SECRET_KEY=

# IN-APP NOTIFICATIONS
NOTIFICATIONS_NOVU_API_KEY=
NOTIFICATIONS_NOVU_APP_ID=

# AI
OPENAI_API_KEY=

# MISC
GITHUB_TOKEN=
