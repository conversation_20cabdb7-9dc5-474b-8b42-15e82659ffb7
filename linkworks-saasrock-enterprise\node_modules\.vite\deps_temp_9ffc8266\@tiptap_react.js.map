{"version": 3, "sources": ["../../@tiptap/extension-bubble-menu/src/bubble-menu-plugin.ts", "../../@tiptap/extension-bubble-menu/src/bubble-menu.ts", "../../@tiptap/extension-floating-menu/src/floating-menu-plugin.ts", "../../@tiptap/extension-floating-menu/src/floating-menu.ts", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../@tiptap/react/src/EditorContent.tsx", "../../node_modules/fast-deep-equal/es6/react.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../node_modules/use-sync-external-store/shim/with-selector.js", "../../@tiptap/react/src/useEditorState.ts", "../../@tiptap/react/src/useEditor.ts", "../../@tiptap/react/src/Context.tsx", "../../@tiptap/react/src/BubbleMenu.tsx", "../../@tiptap/react/src/FloatingMenu.tsx", "../../@tiptap/react/src/useReactNodeView.ts", "../../@tiptap/react/src/NodeViewContent.tsx", "../../@tiptap/react/src/NodeViewWrapper.tsx", "../../@tiptap/react/src/ReactRenderer.tsx", "../../@tiptap/react/src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["import {\n  Editor, isNodeSelection, isTextSelection, posToDOMRect,\n} from '@tiptap/core'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy.js instance.\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  public updateDelay: number\n\n  private updateDebounceTimer: number | undefined\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({\n    view,\n    state,\n    from,\n    to,\n  }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    tippyOptions = {},\n    updateDelay = 250,\n    shouldShow,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    this.element.tabIndex = 0\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'top',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { state, composing } = view\n    const { selection } = state\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect\n        || (() => {\n          if (isNodeSelection(state.selection)) {\n            let node = view.nodeDOM(from) as HTMLElement\n\n            if (node) {\n              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n              if (nodeViewWrapper) {\n                node = nodeViewWrapper.firstChild as HTMLElement\n              }\n\n              if (node) {\n                return node.getBoundingClientRect()\n              }\n            }\n          }\n\n          return posToDOMRect(view, from, to)\n        }),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { BubbleMenuPlugin, BubbleMenuPluginProps } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        updateDelay: this.options.updateDelay,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import {\n  Editor, getText, getTextSerializersFromSchema, posToDOMRect,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy instance.\n   * @default {}\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   * @default null\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n      }) => boolean)\n    | null\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  private getTextContent(node:ProseMirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent)\n\n    if (\n      !view.hasFocus()\n      || !empty\n      || !isRootDepth\n      || !isEmptyTextBlock\n      || !this.editor.isEditable\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor, element, view, tippyOptions = {}, shouldShow,\n  }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    this.element.tabIndex = 0\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'right',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const { doc, selection } = state\n    const { from, to } = selection\n    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection)\n\n    if (isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view,\n      state,\n      oldState,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect || (() => posToDOMRect(view, from, to)),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { FloatingMenuPlugin, FloatingMenuPluginProps } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  ForwardedRef, forwardRef, HTMLProps, LegacyRef, MutableRefObject,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(\n  ...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>\n) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        (ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({\n  contentComponent,\n}) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return (\n    <>\n      {Object.values(renderers)}\n    </>\n  )\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null;\n  innerRef?: ForwardedRef<HTMLDivElement | null>;\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element.firstChild) {\n      return\n    }\n\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n  var envHasBigInt64Array = typeof BigInt64Array !== 'undefined';\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react'\nimport {\n  useDebugValue, useEffect, useLayoutEffect, useState,\n} from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor;\n  transactionNumber: number;\n};\n\nexport type UseEditorStateOptions<\n  TSelectorResult,\n  TEditor extends Editor | null = Editor | null,\n> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor;\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult;\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSelectorR<PERSON>ult, b: TSelectorResult | null) => boolean;\n};\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>\n): TSelectorResult;\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>\n): TSelectorResult | null;\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport {\n  DependencyList,\n  MutableRefObject,\n  useDebugValue,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { useEditorState } from './useEditorState.js'\n\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean;\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default true\n   */\n  shouldRerenderOnTransaction?: boolean;\n};\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        // TODO in the next major release, we should throw an error here\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          console.warn(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual = this.previousDeps.length === deps.length\n        && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: true },\n  deps?: DependencyList\n): Editor;\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options?: UseEditorOptions, deps?: DependencyList): Editor | null;\n\nexport function useEditor(\n  options: UseEditorOptions = {},\n  deps: DependencyList = [],\n): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  createContext, HTMLAttributes, ReactNode, useContext,\n} from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport { useEditor, UseEditorOptions } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null;\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode;\n  slotBefore?: ReactNode;\n  slotAfter?: ReactNode;\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>;\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={{ editor }}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => (\n          <EditorContent editor={currentEditor} {...editorContainerProps} />\n        )}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport React, { useEffect, useState } from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type BubbleMenuProps = Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: BubbleMenuPluginProps['editor'] | null;\n  className?: string;\n  children: React.ReactNode;\n  updateDelay?: number;\n};\n\nexport const BubbleMenu = (props: BubbleMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = BubbleMenuPlugin({\n      updateDelay,\n      editor: menuEditor,\n      element,\n      pluginKey,\n      shouldShow,\n      tippyOptions,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [props.editor, currentEditor, element])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport React, {\n  useEffect, useState,\n} from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null;\n  className?: string,\n  children: React.ReactNode\n}\n\nexport const FloatingMenu = (props: FloatingMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'floatingMenu',\n      editor,\n      tippyOptions = {},\n      shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = FloatingMenuPlugin({\n      pluginKey,\n      editor: menuEditor,\n      element,\n      tippyOptions,\n      shouldShow,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [\n    props.editor,\n    currentEditor,\n    element,\n  ])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { createContext, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart: (event: DragEvent) => void,\n  nodeViewContentRef: (element: HTMLElement | null) => void,\n}\n\nexport const ReactNodeViewContext = createContext<Partial<ReactNodeViewContextProps>>({\n  onDragStart: undefined,\n})\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewContentProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewContent: React.FC<NodeViewContentProps> = props => {\n  const Tag = props.as || 'div'\n  const { nodeViewContentRef } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    />\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "import type { Editor } from '@tiptap/core'\nimport type {\n  ComponentClass,\n  ForwardRefExoticComponent,\n  FunctionComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n} from 'react'\nimport React, { version as reactVersion } from 'react'\nimport { flushSync } from 'react-dom'\n\nimport { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(\n    typeof Component === 'function'\n    && Component.prototype\n    && Component.prototype.isReactComponent\n  )\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof\n    && (Component.$$typeof.toString() === 'Symbol(react.forward_ref)'\n      || Component.$$typeof.description === 'react.forward_ref')\n  )\n}\n\n/**\n * Check if a component is a memoized component.\n * @param Component\n * @returns {boolean}\n */\nfunction isMemoComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof\n    && (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo')\n  )\n}\n\n/**\n * Check if a component can safely receive a ref prop.\n * This includes class components, forwardRef components, and memoized components\n * that wrap forwardRef or class components.\n * @param Component\n * @returns {boolean}\n */\nfunction canReceiveRef(Component: any) {\n  // Check if it's a class component\n  if (isClassComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a forwardRef component\n  if (isForwardRefComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a memoized component\n  if (isMemoComponent(Component)) {\n    // For memoized components, check the wrapped component\n    const wrappedComponent = Component.type\n\n    if (wrappedComponent) {\n      return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent)\n    }\n  }\n\n  return false\n}\n\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus(): boolean {\n  // React 19 is detected by checking React version if available\n  // In practice, we'll use a more conservative approach and assume React 18 behavior\n  // unless we can definitively detect React 19\n  try {\n    // @ts-ignore\n    if (reactVersion) {\n      const majorVersion = parseInt(reactVersion.split('.')[0], 10)\n\n      return majorVersion >= 19\n    }\n  } catch {\n    // Fallback to React 18 behavior if we can't determine version\n  }\n  return false\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor,\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>,\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string,\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string,\n}\n\ntype ComponentType<R, P> =\n  | ComponentClass<P>\n  | FunctionComponent<P>\n  | ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<R>>\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(component: ComponentType<R, P>, {\n    editor,\n    props = {},\n    as = 'div',\n    className = '',\n  }: ReactRendererOptions) {\n    this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    // If the editor is already initialized, we will need to\n    // synchronously render the component to ensure it renders\n    // together with Prosemirror's rendering.\n    if (this.editor.isInitialized) {\n      flushSync(() => {\n        this.render()\n      })\n    } else {\n      queueMicrotask(() => {\n        this.render()\n      })\n    }\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    // Handle ref forwarding with React 18/19 compatibility\n    const isReact19 = isReact19Plus()\n    const componentCanReceiveRef = canReceiveRef(Component)\n\n    const elementProps = { ...props }\n\n    // Always remove ref if the component cannot receive it (unless React 19+)\n    if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {\n      delete elementProps.ref\n    }\n\n    // Only assign our own ref if allowed\n    if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {\n      // @ts-ignore - Setting ref prop for compatible components\n      elementProps.ref = (ref: R) => {\n        this.ref = ref\n      }\n    }\n\n    this.reactElement = <Component {...elementProps} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import type {\n  DecorationWithType,\n  Editor,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n  NodeViewRendererProps,\n} from '@tiptap/core'\nimport { getRenderedAttributes, NodeView } from '@tiptap/core'\nimport type { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { ComponentType, NamedExoticComponent } from 'react'\nimport React, { createElement, createRef, memo } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport type { ReactNodeViewProps } from './types.js'\nimport type { ReactNodeViewContextProps } from './useReactNodeView.js'\nimport { ReactNodeViewContext } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string;\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string;\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: {\n        node: ProseMirrorNode;\n        HTMLAttributes: Record<string, any>;\n      }) => Record<string, string>);\n}\n\nexport class ReactNodeView<\n  T = HTMLElement,\n  Component extends ComponentType<ReactNodeViewProps<T>> = ComponentType<ReactNodeViewProps<T>>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, ReactNodeViewProps<T>>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    super(component, props, options)\n\n    if (!this.node.isLeaf) {\n      if (this.options.contentDOMElementTag) {\n        this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n      } else {\n        this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n      }\n\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      this.contentDOMElement.dataset.nodeViewWrapper = ''\n\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n\n      const contentTarget = this.dom.querySelector('[data-node-view-content]')\n\n      if (!contentTarget) {\n        return\n      }\n\n      contentTarget.appendChild(this.contentDOMElement)\n    }\n  }\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n      ref: createRef<T>(),\n    } satisfies ReactNodeViewProps<T>\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        // remove the nodeViewWrapper attribute from the element\n        if (element.hasAttribute('data-node-view-wrapper')) {\n          element.removeAttribute('data-node-view-wrapper')\n        }\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: NamedExoticComponent<ReactNodeViewProps<T>> = memo(componentProps => {\n      return (\n        <ReactNodeViewContext.Provider value={context}>\n          {createElement(Component, componentProps)}\n        </ReactNodeViewContext.Provider>\n      )\n    })\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild\n      && !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: Node,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (\n      node === this.node\n      && this.decorations === decorations\n      && this.innerDecorations === innerDecorations\n    ) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer<T = HTMLElement>(\n  component: ComponentType<ReactNodeViewProps<T>>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView<T>(component, props, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8Da,uBAAc;EA6CzB,YAAY,EACV,QACA,SACA,MACA,eAAe,CAAA,GACf,cAAc,KACd,WAAU,GACU;AA7Cf,SAAW,cAAG;AAUd,SAAA,aAAiE,CAAC,EACvE,MAAAA,OACA,OACA,MACA,GAAE,MACC;AACH,YAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,YAAM,EAAE,MAAK,IAAK;AAKlB,YAAM,mBAAmB,CAAC,IAAI,YAAY,MAAM,EAAE,EAAE,UAAU,gBAAgB,MAAM,SAAS;AAK7F,YAAM,gBAAgB,KAAK,QAAQ,SAAS,SAAS,aAAa;AAElE,YAAM,iBAAiBA,MAAK,SAAQ,KAAM;AAE1C,UAAI,CAAC,kBAAkB,SAAS,oBAAoB,CAAC,KAAK,OAAO,YAAY;AAC3E,eAAO;;AAGT,aAAO;IACT;AA6BA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAgB,mBAAG,MAAK;AACtB,WAAK,KAAI;IACX;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AA4CA,SAAA,wBAAwB,CAACA,OAAkB,aAA0B;AACnE,YAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAGA,MAAK,MAAM,SAAS;AACrE,YAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAGA,MAAK,MAAM,GAAG;AAEnD,UAAI,CAAC,oBAAoB,CAAC,YAAY;AACpC;;AAGF,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;;AAGvC,WAAK,sBAAsB,OAAO,WAAW,MAAK;AAChD,aAAK,cAAcA,OAAM,kBAAkB,YAAY,QAAQ;MACjE,GAAG,KAAK,WAAW;IACrB;AAEA,SAAa,gBAAG,CAACA,OAAkB,kBAA2B,YAAqB,aAA0B;;AAC3G,YAAM,EAAE,OAAO,UAAS,IAAKA;AAC7B,YAAM,EAAE,UAAS,IAAK;AAEtB,YAAM,SAAS,CAAC,oBAAoB,CAAC;AAErC,UAAI,aAAa,QAAQ;AACvB;;AAGF,WAAK,cAAa;AAGlB,YAAM,EAAE,OAAM,IAAK;AACnB,YAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC;AAC7D,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,IAAI,GAAG,CAAC;AAEzD,YAAMC,eAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;QACnC,QAAQ,KAAK;QACb,SAAS,KAAK;QACd,MAAAD;QACA;QACA;QACA;QACA;MACD,CAAA;AAED,UAAI,CAACC,aAAY;AACf,aAAK,KAAI;AAET;;AAGF,OAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;QACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BACf,MAAK;AACP,cAAI,gBAAgB,MAAM,SAAS,GAAG;AACpC,gBAAI,OAAOD,MAAK,QAAQ,IAAI;AAE5B,gBAAI,MAAM;AACR,oBAAM,kBAAkB,KAAK,QAAQ,kBAAkB,OAAO,KAAK,cAAc,0BAA0B;AAE3G,kBAAI,iBAAiB;AACnB,uBAAO,gBAAgB;;AAGzB,kBAAI,MAAM;AACR,uBAAO,KAAK,sBAAqB;;;;AAKvC,iBAAO,aAAaA,OAAM,MAAM,EAAE;QACpC;MACH,CAAA;AAED,WAAK,KAAI;IACX;AA7KE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AAEnB,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,KAAK,IAAI,iBAAiB,aAAa,KAAK,gBAAgB;AACjE,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAwClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,SAAK,QAAQ,WAAW;AAExB,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,oBAAoB,MAAM,UAAU,SAAS,MAAM,UAAU;AAEnE,QAAI,KAAK,cAAc,KAAK,mBAAmB;AAC7C,WAAK,sBAAsB,MAAM,QAAQ;AACzC;;AAGF,UAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAG,KAAK,MAAM,SAAS;AACrE,UAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAG,KAAK,MAAM,GAAG;AAEnD,SAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;;EAgFjE,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,KAAK,IAAI,oBAAoB,aAAa,KAAK,gBAAgB;AACpE,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,mBAAmB,CAAC,YAAkC;AACjE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,QAAO,CAAE;EACtD,CAAA;AACH;AC9Sa,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,aAAa;MACb,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,iBAAiB;QACf,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;;;;;ICUY,yBAAgB;EAanB,eAAe,MAAoB;AACzC,WAAO,QAAQ,MAAM,EAAE,iBAAiB,6BAA6B,KAAK,OAAO,MAAM,EAAC,CAAE;;EAuB5F,YAAY,EACV,QAAQ,SAAS,MAAM,eAAe,CAAA,GAAI,WAAU,GAC9B;AAhCjB,SAAW,cAAG;AAUd,SAAU,aAAyD,CAAC,EAAE,MAAAE,OAAM,MAAK,MAAM;AAC5F,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,YAAM,cAAc,QAAQ,UAAU;AAEtC,YAAM,mBAAmB,QAAQ,OAAO,eAAe,CAAC,QAAQ,OAAO,KAAK,KAAK,QAAQ,CAAC,QAAQ,OAAO,eAAe,QAAQ,OAAO,eAAe,KAAK,CAAC,KAAK,eAAe,QAAQ,MAAM;AAE9L,UACE,CAACA,MAAK,SAAQ,KACX,CAAC,SACD,CAAC,eACD,CAAC,oBACD,CAAC,KAAK,OAAO,YAChB;AACA,eAAO;;AAGT,aAAO;IACT;AAsBA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AAhDE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAoClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,SAAK,QAAQ,WAAW;AAExB,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,UAAM,EAAE,MAAM,GAAE,IAAK;AACrB,UAAM,SAAS,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,UAAU,GAAG,SAAS;AAElF,QAAI,QAAQ;AACV;;AAGF,SAAK,cAAa;AAElB,UAAM,cAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;MACnC,QAAQ,KAAK;MACb;MACA;MACA;IACD,CAAA;AAED,QAAI,CAAC,YAAY;AACf,WAAK,KAAI;AAET;;AAGF,KAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;MACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BAA2B,MAAM,aAAa,MAAM,MAAM,EAAE;IAClF,CAAA;AAED,SAAK,KAAI;;EAGX,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,qBAAqB,CAAC,YAAoC;AACrE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,iBAAiB,EAAE,MAAM,GAAG,QAAO,CAAE;EACxD,CAAA;AACH;ACzNa,IAAA,eAAe,UAAU,OAA4B;EAChE,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,mBAAmB;QACjB,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;;;;;;;;;;AEhCD,MAAI,MAAuC;AACzC,KAAC,WAAW;AAKd,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,uCAA+B,4BAA4B,IAAI,MAAK,CAAE;;AAE9D,UAAIC,UAAQC,aAAAA;AAEtB,UAAI,uBAAuBD,QAAM;AAEjC,eAAS,MAAM,QAAQ;AACrB;AACE;AACE,qBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,mBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;;AAGnC,yBAAa,SAAS,QAAQ,IAAI;;;;AAKxC,eAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,cAAI,yBAAyB,qBAAqB;AAClD,cAAI,QAAQ,uBAAuB,iBAAgB;AAEnD,cAAI,UAAU,IAAI;AAChB,sBAAU;AACV,mBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;;AAI5B,cAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,mBAAO,OAAO,IAAI;UACxB,CAAK;AAED,yBAAe,QAAQ,cAAc,MAAM;AAI3C,mBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;;;AAQzE,eAAS,GAAG,GAAG,GAAG;AAChB,eAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;;AAIrE,UAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAI7D,UAAIE,YAAWF,QAAM,UACjBG,aAAYH,QAAM,WAClBI,mBAAkBJ,QAAM,iBACxBK,iBAAgBL,QAAM;AAC1B,UAAI,oBAAoB;AACxB,UAAI,6BAA6B;AAWjC,eAAS,qBAAqB,WAAW,aAIzC,mBAAmB;AACjB;AACE,cAAI,CAAC,mBAAmB;AACtB,gBAAIA,QAAM,oBAAoB,QAAW;AACvC,kCAAoB;AAEpB,oBAAM,gMAA+M;;;;AAS3N,YAAI,QAAQ,YAAW;AAEvB;AACE,cAAI,CAAC,4BAA4B;AAC/B,gBAAI,cAAc,YAAW;AAE7B,gBAAI,CAAC,SAAS,OAAO,WAAW,GAAG;AACjC,oBAAM,sEAAsE;AAE5E,2CAA6B;;;;AAmBnC,YAAI,YAAYE,UAAS;UACvB,MAAM;YACJ;YACA;;QAEN,CAAG,GACG,OAAO,UAAU,CAAC,EAAE,MACpB,cAAc,UAAU,CAAC;AAK7B,QAAAE,iBAAgB,WAAY;AAC1B,eAAK,QAAQ;AACb,eAAK,cAAc;AAKnB,cAAI,uBAAuB,IAAI,GAAG;AAEhC,wBAAY;cACV;YACR,CAAO;;WAEF,CAAC,WAAW,OAAO,WAAW,CAAC;AAClC,QAAAD,WAAU,WAAY;AAGpB,cAAI,uBAAuB,IAAI,GAAG;AAEhC,wBAAY;cACV;YACR,CAAO;;AAGH,cAAI,oBAAoB,WAAY;AAOlC,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;gBACV;cACV,CAAS;;UAET;AAGI,iBAAO,UAAU,iBAAiB;QACtC,GAAK,CAAC,SAAS,CAAC;AACd,QAAAE,eAAc,KAAK;AACnB,eAAO;;AAGT,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,YAAI,YAAY,KAAK;AAErB,YAAI;AACF,cAAI,YAAY,kBAAiB;AACjC,iBAAO,CAAC,SAAS,WAAW,SAAS;iBAC9BC,QAAO;AACd,iBAAO;;;AAIX,eAAS,uBAAuB,WAAW,aAAa,mBAAmB;AAKzE,eAAO,YAAW;;AAGpB,UAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AAEvI,UAAI,sBAAsB,CAAC;AAE3B,UAAIC,QAAO,sBAAsB,yBAAyB;AAC1D,UAAI,yBAAyBP,QAAM,yBAAyB,SAAYA,QAAM,uBAAuBO;AAEzE,2CAAA,uBAAG;AAE/B,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,uCAA+B,2BAA2B,IAAI,MAAK,CAAE;;IAGvE,GAAG;EACH;;;AC5OA,IAAI,OAAuC;AACzCC,OAAA,UAAiBP,+CAAA;AACnB,OAAO;AACLO,OAAA,UAAiBC,4CAAA;AACnB;;ACIA,IAAM,YAAY,IACb,SACD;AACF,SAAO,CAAC,SAAW;AACjB,SAAK,QAAQ,SAAM;AACjB,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI;iBACC,KAAK;AACb,YAAmC,UAAU;;IAElD,CAAC;EACH;AACF;AAKA,IAAM,UAA4D,CAAC,EACjE,iBAAgB,MACb;AAEH,QAAM,YAAYC,YAAAA,qBAChB,iBAAiB,WACjB,iBAAiB,aACjB,iBAAiB,iBAAiB;AAIpC,SACE,aAAAV,QACG,cAAA,aAAAA,QAAA,UAAA,MAAA,OAAO,OAAO,SAAS,CAAC;AAG/B;AAOA,SAAS,cAAW;AAClB,QAAM,cAAc,oBAAI,IAAG;AAC3B,MAAI,YAA+C,CAAA;AAEnD,SAAO;;;;IAIL,UAAU,UAAoB;AAC5B,kBAAY,IAAI,QAAQ;AACxB,aAAO,MAAK;AACV,oBAAY,OAAO,QAAQ;MAC7B;;IAEF,cAAW;AACT,aAAO;;IAET,oBAAiB;AACf,aAAO;;;;;IAKT,YAAY,IAAY,UAAuB;AAC7C,kBAAY;QACV,GAAG;QACH,CAAC,EAAE,GAAG,iBAAAW,QAAS,aAAa,SAAS,cAAc,SAAS,SAAS,EAAE;;AAGzE,kBAAY,QAAQ,gBAAc,WAAU,CAAE;;;;;IAKhD,eAAe,IAAU;AACvB,YAAM,gBAAgB,EAAE,GAAG,UAAS;AAEpC,aAAO,cAAc,EAAE;AACvB,kBAAY;AACZ,kBAAY,QAAQ,gBAAc,WAAU,CAAE;;;AAGpD;AAEa,IAAA,oBAAA,cAA0B,aAAAX,QAAM,UAG5C;EAOC,YAAY,OAAyB;;AACnC,UAAM,KAAK;AACX,SAAK,mBAAmB,aAAAA,QAAM,UAAS;AACvC,SAAK,cAAc;AAEnB,SAAK,QAAQ;MACX,gCAAgC,SAAQ,KAAC,MAAM,YAA8C,QAAA,OAAA,SAAA,SAAA,GAAA,gBAAgB;;;EAIjH,oBAAiB;AACf,SAAK,KAAI;;EAGX,qBAAkB;AAChB,SAAK,KAAI;;EAGX,OAAI;AACF,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,UAAU,CAAC,OAAO,eAAe,OAAO,QAAQ,SAAS;AAC3D,UAAI,OAAO,kBAAkB;AAC3B;;AAGF,YAAM,UAAU,KAAK,iBAAiB;AAEtC,cAAQ,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEnD,aAAO,WAAW;QAChB;MACD,CAAA;AAED,aAAO,mBAAmB,YAAW;AAGrC,UAAI,CAAC,KAAK,MAAM,gCAAgC;AAE9C,aAAK,gCAAgC,OAAO,iBAAiB,UAAU,MAAK;AAC1E,eAAK,SAAS,eAAY;AACxB,gBAAI,CAAC,UAAU,gCAAgC;AAC7C,qBAAO;gBACL,gCAAgC;;;AAGpC,mBAAO;UACT,CAAC;AAGD,cAAI,KAAK,+BAA+B;AACtC,iBAAK,8BAA6B;;QAEtC,CAAC;;AAGH,aAAO,gBAAe;AAEtB,WAAK,cAAc;;;EAIvB,uBAAoB;AAClB,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,CAAC,QAAQ;AACX;;AAGF,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,aAAa;AACvB,aAAO,KAAK,SAAS;QACnB,WAAW,CAAA;MACZ,CAAA;;AAGH,QAAI,KAAK,+BAA+B;AACtC,WAAK,8BAA6B;;AAGpC,WAAO,mBAAmB;AAE1B,QAAI,CAAC,OAAO,QAAQ,QAAQ,YAAY;AACtC;;AAGF,UAAM,aAAa,SAAS,cAAc,KAAK;AAE/C,eAAW,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEtD,WAAO,WAAW;MAChB,SAAS;IACV,CAAA;;EAGH,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAU,GAAG,KAAI,IAAK,KAAK;AAE3C,WACE,aAAAA,QAAA;MAAA,aAAAA,QAAA;MAAA;MACE,aAAAA,QAAA,cAAA,OAAA,EAAK,KAAK,UAAU,UAAU,KAAK,gBAAgB,GAAO,GAAA,KAAI,CAAI;OAEjE,WAAA,QAAA,WAAM,SAAA,SAAN,OAAQ,qBAAoB,aAAAA,QAAA,cAAC,SAAQ,EAAA,kBAAkB,OAAO,iBAAgB,CAAI;IAAA;;AAI1F;AAGD,IAAM,2BAAuB,yBAC3B,CAAC,OAA6C,QAAO;AACnD,QAAM,MAAM,aAAAA,QAAM,QAAQ,MAAK;AAC7B,WAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,EAAE,SAAQ;EAExD,GAAG,CAAC,MAAM,MAAM,CAAC;AAGjB,SAAO,aAAAA,QAAM,cAAc,mBAAmB;IAC5C;IACA,UAAU;IACV,GAAG;EACJ,CAAA;AACH,CAAC;AAGU,IAAA,gBAAgB,aAAAA,QAAM,KAAK,oBAAoB;AC9N5D,IAAA,QAAiB,SAAS,MAAM,GAAG,GAAG;AACpC,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,QAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,aAAO;IACb;AAGI,QAAK,aAAa,OAAS,aAAa,KAAM;AAC5C,UAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAG,QAAO;AAC3B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAG,QAAO;AACxC,aAAO;IACb;AAEI,QAAK,aAAa,OAAS,aAAa,KAAM;AAC5C,UAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,WAAK,KAAK,EAAE,QAAO;AACjB,YAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAG,QAAO;AAC3B,aAAO;IACb;AAEI,QAAI,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AAClD,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAC5B,aAAO;IACb;AAGI,QAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,QAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAC5E,QAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEhF,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,MAAM,KAAK,CAAC;AAEhB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAIlC;MACR;AAEM,UAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;IACzC;AAEI,WAAO;EACX;AAGE,SAAO,MAAI,KAAK,MAAI;AACtB;;;;;;;;AElEA,MAAI,MAAuC;AACzC,KAAC,WAAW;AAKd,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,uCAA+B,4BAA4B,IAAI,MAAK,CAAE;;AAE9D,UAAIY,UAAQC,aAAAA;AACtB,UAAIC,QAAOC;AAMX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;;AAIrE,UAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAE7D,UAAI,uBAAuBD,MAAK;AAIhC,UAAIE,UAASJ,QAAM,QACfK,aAAYL,QAAM,WAClB,UAAUA,QAAM,SAChBM,iBAAgBN,QAAM;AAE1B,eAAS,iCAAiC,WAAW,aAAa,mBAAmB,UAAU,SAAS;AAEtG,YAAI,UAAUI,QAAO,IAAI;AACzB,YAAI;AAEJ,YAAI,QAAQ,YAAY,MAAM;AAC5B,iBAAO;YACL,UAAU;YACV,OAAO;UACb;AACI,kBAAQ,UAAU;QACtB,OAAS;AACL,iBAAO,QAAQ;;AAGjB,YAAI,WAAW,QAAQ,WAAY;AAKjC,cAAI,UAAU;AACd,cAAI;AACJ,cAAI;AAEJ,cAAI,mBAAmB,SAAU,cAAc;AAC7C,gBAAI,CAAC,SAAS;AAEZ,wBAAU;AACV,iCAAmB;AAEnB,kBAAI,iBAAiB,SAAS,YAAY;AAE1C,kBAAI,YAAY,QAAW;AAIzB,oBAAI,KAAK,UAAU;AACjB,sBAAI,mBAAmB,KAAK;AAE5B,sBAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC7C,wCAAoB;AACpB,2BAAO;;;;AAKb,kCAAoB;AACpB,qBAAO;;AAKT,gBAAI,eAAe;AACnB,gBAAI,gBAAgB;AAEpB,gBAAI,SAAS,cAAc,YAAY,GAAG;AAExC,qBAAO;;AAKT,gBAAI,gBAAgB,SAAS,YAAY;AASzC,gBAAI,YAAY,UAAa,QAAQ,eAAe,aAAa,GAAG;AAClE,qBAAO;;AAGT,+BAAmB;AACnB,gCAAoB;AACpB,mBAAO;UACb;AAII,cAAI,yBAAyB,sBAAsB,SAAY,OAAO;AAEtE,cAAI,0BAA0B,WAAY;AACxC,mBAAO,iBAAiB,YAAW,CAAE;UAC3C;AAEI,cAAI,gCAAgC,2BAA2B,OAAO,SAAY,WAAY;AAC5F,mBAAO,iBAAiB,uBAAsB,CAAE;UACtD;AACI,iBAAO,CAAC,yBAAyB,6BAA6B;WAC7D,CAAC,aAAa,mBAAmB,UAAU,OAAO,CAAC,GAClD,eAAe,SAAS,CAAC,GACzB,qBAAqB,SAAS,CAAC;AAEnC,YAAI,QAAQ,qBAAqB,WAAW,cAAc,kBAAkB;AAC5E,QAAAC,WAAU,WAAY;AACpB,eAAK,WAAW;AAChB,eAAK,QAAQ;QACjB,GAAK,CAAC,KAAK,CAAC;AACV,QAAAC,eAAc,KAAK;AACnB,eAAO;;AAG+B,+BAAA,mCAAG;AAE3C,UACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,uCAA+B,2BAA2B,IAAI,MAAK,CAAE;;IAGvE,GAAG;EACH;;;AClKA,IAAI,OAAuC;AACzCC,eAAA,UAAiBN,mCAAA;AACnB,OAAO;AACLM,eAAA,UAAiBJ,gCAAA;AACnB;;ACCA,IAAM,4BAA4B,OAAO,WAAW,cAAc,+BAAkB;AA8BpF,IAAM,qBAAN,MAAwB;EAWtB,YAAY,eAAsB;AAV1B,SAAiB,oBAAG;AAEpB,SAAqB,wBAAG;AAMxB,SAAA,cAAc,oBAAI,IAAG;AAG3B,SAAK,SAAS;AACd,SAAK,eAAe,EAAE,QAAQ,eAAe,mBAAmB,EAAC;AAEjE,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;;;;;EAM3C,cAAW;AACT,QAAI,KAAK,sBAAsB,KAAK,uBAAuB;AACzD,aAAO,KAAK;;AAEd,SAAK,wBAAwB,KAAK;AAClC,SAAK,eAAe,EAAE,QAAQ,KAAK,QAAQ,mBAAmB,KAAK,kBAAiB;AACpF,WAAO,KAAK;;;;;EAMd,oBAAiB;AACf,WAAO,EAAE,QAAQ,MAAM,mBAAmB,EAAC;;;;;EAM7C,UAAU,UAAoB;AAC5B,SAAK,YAAY,IAAI,QAAQ;AAC7B,WAAO,MAAK;AACV,WAAK,YAAY,OAAO,QAAQ;IAClC;;;;;EAMF,MAAM,YAAyB;AAC7B,SAAK,SAAS;AAEd,QAAI,KAAK,QAAQ;AAMf,YAAM,KAAK,MAAK;AACd,aAAK,qBAAqB;AAC1B,aAAK,YAAY,QAAQ,cAAY,SAAQ,CAAE;MACjD;AAEA,YAAM,gBAAgB,KAAK;AAE3B,oBAAc,GAAG,eAAe,EAAE;AAClC,aAAO,MAAK;AACV,sBAAc,IAAI,eAAe,EAAE;MACrC;;AAGF,WAAO;;AAEV;AA0CK,SAAU,eACd,SAA+G;;AAE/G,QAAM,CAAC,kBAAkB,QAAI,uBAAS,MAAM,IAAI,mBAAmB,QAAQ,MAAM,CAAC;AAGlF,QAAM,gBAAgBK,oBAAAA,iCACpB,mBAAmB,WACnB,mBAAmB,aACnB,mBAAmB,mBACnB,QAAQ,WACR,KAAA,QAAQ,gBAAU,QAAA,OAAA,SAAA,KAAI,SAAS;AAGjC,4BAA0B,MAAK;AAC7B,WAAO,mBAAmB,MAAM,QAAQ,MAAM;KAC7C,CAAC,QAAQ,QAAQ,kBAAkB,CAAC;AAEvC,kCAAc,aAAa;AAE3B,SAAO;AACT;ACpKA,IAAM,QAAQ;AACd,IAAM,QAAQ,OAAO,WAAW;AAChC,IAAM,SAAS,SAAS,QAAQ,OAAO,WAAW,eAAgB,OAAe,IAAI;AAwBrF,IAAM,wBAAN,MAAM,uBAAqB;EAqCzB,YAAY,SAA2C;AAjC/C,SAAM,SAAkB;AAWxB,SAAA,gBAAgB,oBAAI,IAAG;AAUvB,SAAkB,qBAAG;AAKrB,SAAY,eAA0B;AAKvC,SAAU,aAAG;AAGlB,SAAK,UAAU;AACf,SAAK,gBAAgB,oBAAI,IAAG;AAC5B,SAAK,UAAU,KAAK,iBAAgB,CAAE;AACtC,SAAK,gBAAe;AAEpB,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;;EAGzC,UAAU,QAAqB;AACrC,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;AAGvD,SAAK,cAAc,QAAQ,QAAM,GAAE,CAAE;;EAG/B,mBAAgB;AACtB,QAAI,KAAK,QAAQ,QAAQ,sBAAsB,QAAW;AACxD,UAAI,SAAS,QAAQ;AAEnB,YAAI,OAAO;AAKT,kBAAQ,KACN,0HAA0H;;AAK9H,eAAO;;AAIT,aAAO,KAAK,aAAY;;AAG1B,QAAI,KAAK,QAAQ,QAAQ,qBAAqB,SAAS,OAAO;AAE5D,YAAM,IAAI,MACR,kOAAkO;;AAItO,QAAI,KAAK,QAAQ,QAAQ,mBAAmB;AAC1C,aAAO,KAAK,aAAY;;AAG1B,WAAO;;;;;EAMD,eAAY;AAClB,UAAM,iBAAyC;MAC7C,GAAG,KAAK,QAAQ;;MAEhB,gBAAgB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3E,QAAQ,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3D,UAAU,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC/D,WAAW,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACjE,SAAS,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC7D,mBAAmB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACjF,eAAe,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MACzE,UAAU,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC/D,gBAAgB,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3E,QAAQ,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;MAC3D,SAAS,IAAI,SAAQ;AAAA,YAAA,IAAA;AAAC,gBAAA,MAAA,KAAA,KAAK,QAAQ,SAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,GAAG,IAAI;MAAC;;AAE/D,UAAM,SAAS,IAAI,OAAO,cAAc;AAIxC,WAAO;;;;;EAMT,YAAS;AACP,WAAO,KAAK;;;;;EAMd,oBAAiB;AACf,WAAO;;;;;EAMT,UAAU,eAAyB;AACjC,SAAK,cAAc,IAAI,aAAa;AAEpC,WAAO,MAAK;AACV,WAAK,cAAc,OAAO,aAAa;IACzC;;EAGF,OAAO,eAAe,GAAqB,GAAmB;AAC5D,WAAQ,OAAO,KAAK,CAAC,EAAiC,MAAM,SAAM;AAChE,UAAI,CAAC,YAAY,kBAAkB,aAAa,YAAY,iBAAiB,WAAW,UAAU,qBAAqB,kBAAkB,UAAU,SAAS,EAAE,SAAS,GAAG,GAAG;AAE3K,eAAO;;AAIT,UAAI,QAAQ,gBAAgB,EAAE,cAAc,EAAE,YAAY;AACxD,YAAI,EAAE,WAAW,WAAW,EAAE,WAAW,QAAQ;AAC/C,iBAAO;;AAET,eAAO,EAAE,WAAW,MAAM,CAAC,WAAWC,WAAS;;AAC7C,cAAI,gBAAc,KAAA,EAAE,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAGA,MAAK,IAAG;AACvC,mBAAO;;AAET,iBAAO;QACT,CAAC;;AAEH,UAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AAErB,eAAO;;AAET,aAAO;IACT,CAAC;;;;;;;EAQH,SAAS,MAAoB;AAE3B,WAAO,MAAK;AACV,WAAK,qBAAqB;AAE1B,mBAAa,KAAK,2BAA2B;AAE7C,UAAI,KAAK,UAAU,CAAC,KAAK,OAAO,eAAe,KAAK,WAAW,GAAG;AAEhE,YAAI,CAAC,uBAAsB,eAAe,KAAK,QAAQ,SAAS,KAAK,OAAO,OAAO,GAAG;AAGpF,eAAK,OAAO,WAAW;YACrB,GAAG,KAAK,QAAQ;YAChB,UAAU,KAAK,OAAO;UACvB,CAAA;;aAEE;AAML,aAAK,sBAAsB,IAAI;;AAGjC,aAAO,MAAK;AACV,aAAK,qBAAqB;AAC1B,aAAK,gBAAe;MACtB;IACF;;;;;EAMM,sBAAsB,MAAoB;AAChD,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,UAAI,KAAK,iBAAiB,MAAM;AAE9B,aAAK,eAAe;AACpB;;AAEF,YAAM,eAAe,KAAK,aAAa,WAAW,KAAK,UAClD,KAAK,aAAa,MAAM,CAAC,KAAKA,WAAU,QAAQ,KAAKA,MAAK,CAAC;AAEhE,UAAI,cAAc;AAEhB;;;AAIJ,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,WAAK,OAAO,QAAO;;AAGrB,SAAK,UAAU,KAAK,aAAY,CAAE;AAGlC,SAAK,eAAe;;;;;;;EAQd,kBAAe;AACrB,UAAM,oBAAoB,KAAK;AAC/B,UAAM,gBAAgB,KAAK;AAG3B,SAAK,8BAA8B,WAAW,MAAK;AACjD,UAAI,KAAK,sBAAsB,KAAK,eAAe,mBAAmB;AAEpE,YAAI,eAAe;AAEjB,wBAAc,WAAW,KAAK,QAAQ,OAAO;;AAE/C;;AAEF,UAAI,iBAAiB,CAAC,cAAc,aAAa;AAC/C,sBAAc,QAAO;AACrB,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,UAAU,IAAI;;;OAKtB,CAAC;;AAEP;SAuBe,UACd,UAA4B,CAAA,GAC5B,OAAuB,CAAA,GAAE;AAEzB,QAAM,wBAAoB,qBAAO,OAAO;AAExC,oBAAkB,UAAU;AAE5B,QAAM,CAAC,eAAe,QAAI,uBAAS,MAAM,IAAI,sBAAsB,iBAAiB,CAAC;AAErF,QAAM,SAASC,YAAAA,qBACb,gBAAgB,WAChB,gBAAgB,WAChB,gBAAgB,iBAAiB;AAGnC,kCAAc,MAAM;AAIpB,8BAAU,gBAAgB,SAAS,IAAI,CAAC;AAIxC,iBAAe;IACb;IACA,UAAU,CAAC,EAAE,kBAAiB,MAAM;AAClC,UAAI,QAAQ,gCAAgC,OAAO;AAEjD,eAAO;;AAIT,UAAI,QAAQ,qBAAqB,sBAAsB,GAAG;AACxD,eAAO;;AAET,aAAO,oBAAoB;;EAE9B,CAAA;AAED,SAAO;AACT;AC3WO,IAAM,oBAAgB,4BAAkC;EAC7D,QAAQ;AACT,CAAA;AAEY,IAAA,iBAAiB,cAAc;AAK/B,IAAA,mBAAmB,UAAM,yBAAW,aAAa;SAc9C,eAAe,EAC7B,UAAU,WAAW,YAAY,uBAAuB,CAAA,GAAI,GAAG,cAAa,GACxD;AACpB,QAAM,SAAS,UAAU,aAAa;AAEtC,MAAI,CAAC,QAAQ;AACX,WAAO;;AAGT,SACE,aAAAV,QAAC;IAAA,cAAc;IAAQ,EAAC,OAAO,EAAE,OAAM,EAAE;IACtC;IACD,aAAAA,QAAC,cAAA,gBAAc,MACZ,CAAC,EAAE,QAAQ,cAAa,MACvB,aAAAA,QAAC,cAAA,eAAa,EAAC,QAAQ,eAAmB,GAAA,qBAAoB,CAAI,CACnE;IAEF;IACA;EAAS;AAGhB;AC1Ca,IAAAW,cAAa,CAAC,UAA0B;AACnD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAgC,IAAI;AAClE,QAAM,EAAE,QAAQ,cAAa,IAAK,iBAAgB;AAElD,8BAAU,MAAK;;AACb,QAAI,CAAC,SAAS;AACZ;;AAGF,UAAI,KAAA,MAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAe,kBAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,cAAa;AAC3D;;AAGF,UAAM,EACJ,YAAY,cAAc,QAAQ,eAAe,CAAA,GAAI,aAAa,aAAa,KAAI,IACjF;AAEJ,UAAM,aAAa,UAAU;AAE7B,QAAI,CAAC,YAAY;AACf,cAAQ,KAAK,kGAAkG;AAC/G;;AAGF,UAAM,SAAS,iBAAiB;MAC9B;MACA,QAAQ;MACR;MACA;MACA;MACA;IACD,CAAA;AAED,eAAW,eAAe,MAAM;AAChC,WAAO,MAAQ;AAAA,iBAAW,iBAAiB,SAAS;IAAC;KACpD,CAAC,MAAM,QAAQ,eAAe,OAAO,CAAC;AAEzC,SACE,aAAAX,QAAK,cAAA,OAAA,EAAA,KAAK,YAAY,WAAW,MAAM,WAAW,OAAO,EAAE,YAAY,SAAQ,EAAE,GAC9E,MAAM,QAAQ;AAGrB;ACzCa,IAAAY,gBAAe,CAAC,UAA4B;AACvD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAgC,IAAI;AAClE,QAAM,EAAE,QAAQ,cAAa,IAAK,iBAAgB;AAElD,8BAAU,MAAK;;AACb,QAAI,CAAC,SAAS;AACZ;;AAGF,UAAI,KAAA,MAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAe,kBAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,cAAa;AAC3D;;AAGF,UAAM,EACJ,YAAY,gBACZ,QACA,eAAe,CAAA,GACf,aAAa,KAAI,IACf;AAEJ,UAAM,aAAa,UAAU;AAE7B,QAAI,CAAC,YAAY;AACf,cAAQ,KAAK,oGAAoG;AACjH;;AAGF,UAAM,SAAS,mBAAmB;MAChC;MACA,QAAQ;MACR;MACA;MACA;IACD,CAAA;AAED,eAAW,eAAe,MAAM;AAChC,WAAO,MAAQ;AAAA,iBAAW,iBAAiB,SAAS;IAAC;EACvD,GAAG;IACD,MAAM;IACN;IACA;EACD,CAAA;AAED,SACE,aAAAZ,QAAK,cAAA,OAAA,EAAA,KAAK,YAAY,WAAW,MAAM,WAAW,OAAO,EAAE,YAAY,SAAQ,EAAE,GAC9E,MAAM,QAAQ;AAGrB;ACxDO,IAAM,2BAAuB,4BAAkD;EACpF,aAAa;AACd,CAAA;AAEY,IAAA,mBAAmB,UAAM,yBAAW,oBAAoB;ACFxD,IAAA,kBAAkD,WAAQ;AACrE,QAAM,MAAM,MAAM,MAAM;AACxB,QAAM,EAAE,mBAAkB,IAAK,iBAAgB;AAE/C;;IAEE,aAAAA,QAAC,cAAA,KACK,EAAA,GAAA,OACJ,KAAK,oBACkB,0BAAA,IACvB,OAAO;MACL,YAAY;MACZ,GAAG,MAAM;IACV,EAAA,CAAA;;AAGP;AChBO,IAAM,kBAAkD,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAO;AAC7F,QAAM,EAAE,YAAW,IAAK,iBAAgB;AACxC,QAAM,MAAM,MAAM,MAAM;AAExB;;IAEE,aAAAA,QAAA,cAAC,KAAG,EAAA,GACE,OACJ,KACuB,0BAAA,IACvB,aACA,OAAO;MACL,YAAY;MACZ,GAAG,MAAM;IACV,EAAA,CAAA;;AAGP,CAAC;ACPD,SAAS,iBAAiB,WAAc;AACtC,SAAO,CAAC,EACN,OAAO,cAAc,cAClB,UAAU,aACV,UAAU,UAAU;AAE3B;AAOA,SAAS,sBAAsB,WAAc;AAC3C,SAAO,CAAC,EACN,OAAO,cAAc,YAClB,UAAU,aACT,UAAU,SAAS,SAAQ,MAAO,+BACjC,UAAU,SAAS,gBAAgB;AAE5C;AAOA,SAAS,gBAAgB,WAAc;AACrC,SAAO,CAAC,EACN,OAAO,cAAc,YAClB,UAAU,aACT,UAAU,SAAS,SAAQ,MAAO,wBAAwB,UAAU,SAAS,gBAAgB;AAErG;AASA,SAAS,cAAc,WAAc;AAEnC,MAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAO;;AAIT,MAAI,sBAAsB,SAAS,GAAG;AACpC,WAAO;;AAIT,MAAI,gBAAgB,SAAS,GAAG;AAE9B,UAAM,mBAAmB,UAAU;AAEnC,QAAI,kBAAkB;AACpB,aAAO,iBAAiB,gBAAgB,KAAK,sBAAsB,gBAAgB;;;AAIvF,SAAO;AACT;AAMA,SAAS,gBAAa;AAIpB,MAAI;AAEF,QAAIa,sBAAc;AAChB,YAAM,eAAe,SAASA,qBAAa,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAE5D,aAAO,gBAAgB;;UAEnB;;AAGR,SAAO;AACT;IAgDa,sBAAa;;;;EAkBxB,YAAY,WAAgC,EAC1C,QACA,QAAQ,CAAA,GACR,KAAK,OACL,YAAY,GAAE,GACO;AAVvB,SAAG,MAAa;AAWd,SAAK,KAAK,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,EAAE,SAAQ;AACzD,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,UAAU,SAAS,cAAc,EAAE;AACxC,SAAK,QAAQ,UAAU,IAAI,gBAAgB;AAE3C,QAAI,WAAW;AACb,WAAK,QAAQ,UAAU,IAAI,GAAG,UAAU,MAAM,GAAG,CAAC;;AAMpD,QAAI,KAAK,OAAO,eAAe;AAC7B,sCAAU,MAAK;AACb,aAAK,OAAM;MACb,CAAC;WACI;AACL,qBAAe,MAAK;AAClB,aAAK,OAAM;MACb,CAAC;;;;;;EAOL,SAAM;;AACJ,UAAM,YAAY,KAAK;AACvB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AAGpB,UAAM,YAAY,cAAa;AAC/B,UAAM,yBAAyB,cAAc,SAAS;AAEtD,UAAM,eAAe,EAAE,GAAG,MAAK;AAG/B,QAAI,aAAa,OAAO,EAAE,aAAa,yBAAyB;AAC9D,aAAO,aAAa;;AAItB,QAAI,CAAC,aAAa,QAAQ,aAAa,yBAAyB;AAE9D,mBAAa,MAAM,CAAC,QAAU;AAC5B,aAAK,MAAM;MACb;;AAGF,SAAK,eAAe,aAAAb,QAAA,cAAC,WAAc,EAAA,GAAA,aAAY,CAAA;AAE/C,KAAA,KAAA,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,KAAK,IAAI,IAAI;;;;;EAMrD,YAAY,QAA6B,CAAA,GAAE;AACzC,SAAK,QAAQ;MACX,GAAG,KAAK;MACR,GAAG;;AAGL,SAAK,OAAM;;;;;EAMb,UAAO;;AACL,UAAM,SAAS,KAAK;AAEpB,KAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,sBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,eAAe,KAAK,EAAE;;;;;EAMlD,iBAAiB,YAAkC;AACjD,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAM;AACpC,WAAK,QAAQ,aAAa,KAAK,WAAW,GAAG,CAAC;IAChD,CAAC;;AAEJ;AC9MK,IAAO,gBAAP,cAKI,SAAwC;EAWhD,YAAY,WAAsB,OAA8B,SAA0B;AACxF,UAAM,WAAW,OAAO,OAAO;AAE/B,QAAI,CAAC,KAAK,KAAK,QAAQ;AACrB,UAAI,KAAK,QAAQ,sBAAsB;AACrC,aAAK,oBAAoB,SAAS,cAAc,KAAK,QAAQ,oBAAoB;aAC5E;AACL,aAAK,oBAAoB,SAAS,cAAc,KAAK,KAAK,WAAW,SAAS,KAAK;;AAGrF,WAAK,kBAAkB,QAAQ,uBAAuB;AACtD,WAAK,kBAAkB,QAAQ,kBAAkB;AAKjD,WAAK,kBAAkB,MAAM,aAAa;AAE1C,YAAM,gBAAgB,KAAK,IAAI,cAAc,0BAA0B;AAEvE,UAAI,CAAC,eAAe;AAClB;;AAGF,oBAAc,YAAY,KAAK,iBAAiB;;;;;;;EAQpD,QAAK;AACH,UAAM,QAAQ;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,kBAAkB,KAAK;MACvB,MAAM,KAAK;MACX,UAAU;MACV,WAAW,KAAK;MAChB,gBAAgB,KAAK;MACrB,QAAQ,MAAM,KAAK,OAAM;MACzB,kBAAkB,CAAC,aAAa,CAAA,MAAO,KAAK,iBAAiB,UAAU;MACvE,YAAY,MAAM,KAAK,WAAU;MACjC,SAAK,wBAAS;;AAGhB,QAAI,CAAE,KAAK,UAAkB,aAAa;AACxC,YAAM,sBAAsB,CAAC,WAA0B;AACrD,eAAO,OAAO,OAAO,CAAC,EAAE,YAAW,IAAK,OAAO,UAAU,CAAC;MAC5D;AAEA,WAAK,UAAU,cAAc,oBAAoB,KAAK,UAAU,IAAI;;AAGtE,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAC9C,UAAM,qBAAsE,aAAU;AACpF,UAAI,WAAW,KAAK,qBAAqB,QAAQ,eAAe,KAAK,mBAAmB;AAEtF,YAAI,QAAQ,aAAa,wBAAwB,GAAG;AAClD,kBAAQ,gBAAgB,wBAAwB;;AAElD,gBAAQ,YAAY,KAAK,iBAAiB;;IAE9C;AACA,UAAM,UAAU,EAAE,aAAa,mBAAkB;AACjD,UAAM,YAAY,KAAK;AAGvB,UAAM,4BAAqE,mBAAK,oBAAiB;AAC/F,aACE,aAAAA,QAAC,cAAA,qBAAqB,UAAQ,EAAC,OAAO,QAAO,OAC1C,4BAAc,WAAW,cAAc,CAAC;IAG/C,CAAC;AAED,0BAAsB,cAAc;AAEpC,QAAI,KAAK,KAAK,KAAK,WAAW,SAAS;AAEvC,QAAI,KAAK,QAAQ,IAAI;AACnB,WAAK,KAAK,QAAQ;;AAGpB,UAAM,EAAE,YAAY,GAAE,IAAK,KAAK;AAEhC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AAEjE,SAAK,WAAW,IAAI,cAAc,uBAAuB;MACvD,QAAQ,KAAK;MACb;MACA;MACA,WAAW,QAAQ,KAAK,KAAK,KAAK,IAAI,IAAI,SAAS,GAAG,KAAI;IAC3D,CAAA;AAED,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAC5D,SAAK,wBAAuB;;;;;;EAO9B,IAAI,MAAG;;AACL,QACE,KAAK,SAAS,QAAQ,qBACnB,GAAC,KAAA,KAAK,SAAS,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,wBAAwB,IAClF;AACA,YAAM,MAAM,8DAA8D;;AAG5E,WAAO,KAAK,SAAS;;;;;;EAOvB,IAAI,aAAU;AACZ,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;;AAGT,WAAO,KAAK;;;;;;EAOd,wBAAqB;AACnB,UAAM,EAAE,MAAM,GAAE,IAAK,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAM;AAEvB,QAAI,OAAO,QAAQ,UAAU;AAC3B;;AAGF,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;;AAGF,WAAK,WAAU;WACV;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;;AAGF,WAAK,aAAY;;;;;;;EAQrB,OACE,MACA,aACA,kBAAkC;AAElC,UAAM,oBAAoB,CAAC,UAA+B;AACxD,WAAK,SAAS,YAAY,KAAK;AAC/B,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,aAAK,wBAAuB;;IAEhC;AAEA,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;;AAGT,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;QACzB;QACA;QACA,SAAS;QACT,gBAAgB;QAChB;QACA;QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;MAC7E,CAAA;;AAGH,QACE,SAAS,KAAK,QACX,KAAK,gBAAgB,eACrB,KAAK,qBAAqB,kBAC7B;AACA,aAAO;;AAGT,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;AAEzD,WAAO;;;;;;EAOT,aAAU;AACR,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,SAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;;;;;;EAOhE,eAAY;AACV,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,SAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;;;;;EAMnE,UAAO;AACL,SAAK,SAAS,QAAO;AACrB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;AAC7D,SAAK,oBAAoB;;;;;;EAO3B,0BAAuB;AACrB,QAAI,KAAK,QAAQ,OAAO;AACtB,UAAI,WAAmC,CAAA;AAEvC,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,cAAM,sBAAsB,KAAK,OAAO,iBAAiB;AACzD,cAAM,iBAAiB,sBAAsB,KAAK,MAAM,mBAAmB;AAE3E,mBAAW,KAAK,QAAQ,MAAM,EAAE,MAAM,KAAK,MAAM,eAAc,CAAE;aAC5D;AACL,mBAAW,KAAK,QAAQ;;AAG1B,WAAK,SAAS,iBAAiB,QAAQ;;;AAG5C;AAKe,SAAA,sBACd,WACA,SAA+C;AAE/C,SAAO,WAAQ;AAIb,QAAI,CAAE,MAAM,OAAsC,kBAAkB;AAClE,aAAO,CAAA;;AAGT,WAAO,IAAI,cAAiB,WAAW,OAAO,OAAO;EACvD;AACF;", "names": ["view", "shouldShow", "view", "React", "require$$0", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "error", "shim", "shimModule", "require$$1", "useSyncExternalStore", "ReactDOM", "React", "require$$0", "shim", "require$$1", "useRef", "useEffect", "useDebugValue", "withSelectorModule", "useSyncExternalStoreWithSelector", "index", "useSyncExternalStore", "BubbleMenu", "FloatingMenu", "reactVersion"]}