import { ActionFunction, LoaderFunctionArgs, MetaFunction } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import { StepFormWizardIndexApi } from "~/custom/modules/stepFormWizard/routes/api/stepFormWizards/StepFormWizardsIndexApi.server";
import StepFormWizardIndexRoute from "~/custom/modules/stepFormWizard/routes/components/stepFormWizards/StepFormWizardsIndexRoute";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
export const loader = (args: LoaderFunctionArgs) => StepFormWizardIndexApi.loader(args);
export const action: ActionFunction = (args) => StepFormWizardIndexApi.action(args);

export default () => <StepFormWizardIndexRoute />;

export function ErrorBoundary() {
  return <ServerError />;
}
