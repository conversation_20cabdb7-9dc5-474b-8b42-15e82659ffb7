{"version": 3, "sources": ["../../remix-i18next/src/lib/format-language-string.ts", "../../remix-i18next/src/lib/parser.ts", "../../remix-i18next/src/lib/get-client-locales.ts", "../../remix-i18next/src/lib/language-detector.ts", "../../remix-i18next/src/server.ts"], "sourcesContent": ["import type { Language } from \"./parser.js\";\n\nexport function formatLanguageString(\n\tlanguage: Pick<Language, \"code\" | \"region\" | \"script\">,\n): string {\n\tlet parts = [language.code];\n\tif (language.script) parts.push(language.script);\n\tif (language.region) parts.push(language.region);\n\treturn parts.join(\"-\");\n}\n", "import { formatLanguageString } from \"./format-language-string.js\";\n\nlet REGEX =\n\t/[ ]*((([a-zA-Z]+(-[a-zA-Z0-9]+){0,2})|\\*)(;[ ]*q=[0-1](\\.[0-9]+)?[ ]*)?)*/g;\n\nexport interface Language {\n\tcode: string;\n\tscript?: string | null | undefined;\n\tregion?: string | undefined;\n\tquality: number;\n}\n\nexport interface PickOptions {\n\tloose?: boolean | undefined;\n}\n\nfunction isString(value: unknown): value is string {\n\treturn typeof value === \"string\";\n}\n\nexport function parse(acceptLanguage?: string): Language[] {\n\tlet strings = (acceptLanguage || \"\").match(REGEX);\n\tif (!strings) throw new Error(\"Invalid Accept-Language header\");\n\n\tlet languages: Language[] = [];\n\n\tfor (let m of strings) {\n\t\tif (!m) continue;\n\n\t\tm = m.trim();\n\n\t\tlet bits = m.split(\";\");\n\t\tlet ietf = bits[0]?.split(\"-\") ?? [];\n\t\tlet hasScript = ietf.length === 3;\n\n\t\tlanguages.push({\n\t\t\t// biome-ignore lint/style/noNonNullAssertion: We know this is not null\n\t\t\tcode: ietf[0]!,\n\t\t\tscript: hasScript ? ietf[1] : null,\n\t\t\tregion: hasScript ? ietf[2] : ietf[1],\n\t\t\tquality: bits[1]\n\t\t\t\t? // biome-ignore lint/style/noNonNullAssertion: We know this is not null\n\t\t\t\t\t(Number.parseFloat(bits[1]!.split(\"=\")[1]!) ?? 1.0)\n\t\t\t\t: 1.0,\n\t\t});\n\t}\n\n\treturn languages.sort((a, b) => b.quality - a.quality);\n}\n\nexport function pick<T extends string>(\n\tsupportedLanguages: readonly T[],\n\tacceptLanguage: string | Language[],\n\toptions: PickOptions = { loose: false },\n): T | null {\n\tif (!supportedLanguages || !supportedLanguages.length || !acceptLanguage) {\n\t\treturn null;\n\t}\n\n\tlet parsedAcceptLanguage = isString(acceptLanguage)\n\t\t? parse(acceptLanguage)\n\t\t: acceptLanguage;\n\n\tlet supported = supportedLanguages.map((support) => {\n\t\tlet bits = support.split(\"-\");\n\t\tlet hasScript = bits.length === 3;\n\n\t\treturn {\n\t\t\t// biome-ignore lint/style/noNonNullAssertion: We know this is not null\n\t\t\tcode: bits[0]!,\n\t\t\tscript: hasScript ? bits[1] : null,\n\t\t\tregion: (hasScript ? bits[2] : bits[1]) ?? undefined,\n\t\t};\n\t}) satisfies Array<Pick<Language, \"code\" | \"script\" | \"region\">>;\n\n\tfor (let lang of parsedAcceptLanguage) {\n\t\tif (!lang) continue;\n\t\tlet langCode = lang.code.toLowerCase();\n\t\tlet langRegion = lang.region ? lang.region.toLowerCase() : lang.region;\n\t\tlet langScript = lang.script ? lang.script.toLowerCase() : lang.script;\n\n\t\tfor (let supportedLanguage of supported) {\n\t\t\tlet supportedCode = supportedLanguage.code?.toLowerCase() ?? \"\";\n\t\t\tif (langCode !== supportedCode) continue;\n\n\t\t\tlet supportedScript = supportedLanguage.script\n\t\t\t\t? supportedLanguage.script.toLowerCase()\n\t\t\t\t: supportedLanguage.script;\n\t\t\tlet supportedRegion = supportedLanguage.region\n\t\t\t\t? supportedLanguage.region.toLowerCase()\n\t\t\t\t: supportedLanguage.region;\n\n\t\t\tif (\n\t\t\t\tlangCode === supportedCode &&\n\t\t\t\t(options?.loose || !langScript || langScript === supportedScript) &&\n\t\t\t\t(options?.loose || !langRegion || langRegion === supportedRegion)\n\t\t\t) {\n\t\t\t\treturn formatLanguageString(supportedLanguage) as T;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn null;\n}\n", "import { formatLanguageString } from \"./format-language-string.js\";\nimport { parse, pick } from \"./parser.js\";\n\nexport type Locales = string | string[] | undefined;\n\n/**\n * Get the client's locales from the Accept-Language header.\n * If the header is not defined returns null.\n * If the header is defined return an array of locales, sorted by the quality\n * value.\n *\n * @example\n * export let loader: LoaderFunction = async ({ request }) => {\n *   let locales = getClientLocales(request)\n *   let date = new Date().toLocaleDateString(locales, {\n *     \"day\": \"numeric\",\n *   });\n *   return json({ date })\n * }\n */\nexport function getClientLocales(headers: Headers): Locales;\nexport function getClientLocales(request: Request): Locales;\nexport function getClientLocales(requestOrHeaders: Request | Headers): Locales {\n\tlet headers = getHeaders(requestOrHeaders);\n\n\tlet acceptLanguage = headers.get(\"Accept-Language\");\n\n\t// if the header is not defined, return undefined\n\tif (!acceptLanguage) return undefined;\n\n\tlet parsedLocales = parse(acceptLanguage)\n\t\t.filter((lang) => lang.code !== \"*\")\n\t\t.map(formatLanguageString);\n\n\tlet validLocales: string[] = [];\n\n\tfor (let locale of parsedLocales) {\n\t\ttry {\n\t\t\t// This will throw on invalid locales\n\t\t\tnew Intl.Locale(locale);\n\n\t\t\t// If we get here, the locale is valid\n\t\t\tvalidLocales.push(locale);\n\t\t} catch {\n\t\t\t// We want to ignore errors here\n\t\t}\n\t}\n\n\tlet locale = pick(\n\t\tIntl.DateTimeFormat.supportedLocalesOf(validLocales),\n\t\tacceptLanguage,\n\t);\n\n\treturn locale ?? undefined;\n}\n\n/**\n * Receives a Request or Headers objects.\n * If it's a Request returns the request.headers\n * If it's a Headers returns the object directly.\n */\nfunction getHeaders(requestOrHeaders: Request | Headers): Headers {\n\tif (requestOrHeaders instanceof Request) return requestOrHeaders.headers;\n\treturn requestOrHeaders;\n}\n", "import type { <PERSON><PERSON>, SessionStorage } from \"react-router\";\nimport { getClientLocales } from \"./get-client-locales.js\";\nimport { pick } from \"./parser.js\";\n\nexport interface LanguageDetectorOption {\n\t/**\n\t * Define the list of supported languages, this is used to determine if one of\n\t * the languages requested by the user is supported by the application.\n\t * This should be be same as the supportedLngs in the i18next options.\n\t */\n\tsupportedLanguages: string[];\n\t/**\n\t * Define the fallback language that it's going to be used in the case user\n\t * expected language is not supported.\n\t * This should be be same as the fallbackLng in the i18next options.\n\t */\n\tfallbackLanguage: string;\n\t/**\n\t * If you want to use a cookie to store the user preferred language, you can\n\t * pass the Cookie object here.\n\t */\n\tcookie?: Cookie;\n\t/**\n\t * If you want to use a session to store the user preferred language, you can\n\t * pass the SessionStorage object here.\n\t * When this is not defined, getting the locale will ignore the session.\n\t */\n\tsessionStorage?: SessionStorage;\n\t/**\n\t * If defined a sessionStorage and want to change the default key used to\n\t * store the user preferred language, you can pass the key here.\n\t * @default \"lng\"\n\t */\n\tsessionKey?: string;\n\t/**\n\t * If you want to use search parameters for language detection and want to\n\t * change the default key used to for the parameter name,\n\t * you can pass the key here.\n\t * @default \"lng\"\n\t */\n\tsearchParamKey?: string;\n\t/**\n\t * The order the library will use to detect the user preferred language.\n\t * By default the order is\n\t * - searchParams\n\t * - cookie\n\t * - session\n\t * - header\n\t * If customized, a an extra `custom` option can be added to the order.\n\t * And finally the fallback language.\n\t */\n\torder?: Array<\"searchParams\" | \"cookie\" | \"session\" | \"header\" | \"custom\">;\n\t/**\n\t * A function that can be used to find the locale based on the request object\n\t * using any custom logic you want.\n\t * This can be useful to get the locale from the URL pathname, or to query it\n\t * from the database or fetch it from an API.\n\t * @param request The request object received by the server.\n\t */\n\tfindLocale?(request: Request): Promise<string | Array<string> | null>;\n}\n\n/**\n * The LanguageDetector contains the logic to detect the user preferred language\n * fully server-side by using a SessionStorage, Cookie, URLSearchParams, or\n * Headers.\n */\nexport class LanguageDetector {\n\tconstructor(private options: LanguageDetectorOption) {\n\t\tthis.isSessionOnly(options);\n\t\tthis.isCookieOnly(options);\n\t}\n\n\tprivate isSessionOnly(options: LanguageDetectorOption) {\n\t\tif (\n\t\t\toptions.order?.length === 1 &&\n\t\t\toptions.order[0] === \"session\" &&\n\t\t\t!options.sessionStorage\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t\"You need a sessionStorage if you want to only get the locale from the session\",\n\t\t\t);\n\t\t}\n\t}\n\n\tprivate isCookieOnly(options: LanguageDetectorOption) {\n\t\tif (\n\t\t\toptions.order?.length === 1 &&\n\t\t\toptions.order[0] === \"cookie\" &&\n\t\t\t!options.cookie\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t\"You need a cookie if you want to only get the locale from the cookie\",\n\t\t\t);\n\t\t}\n\t}\n\n\tpublic async detect(request: Request): Promise<string> {\n\t\tlet order = this.options.order ?? this.defaultOrder;\n\n\t\tfor (let method of order) {\n\t\t\tlet locale: string | null = null;\n\n\t\t\tif (method === \"searchParams\") {\n\t\t\t\tlocale = this.fromSearchParams(request);\n\t\t\t}\n\n\t\t\tif (method === \"cookie\") {\n\t\t\t\tlocale = await this.fromCookie(request);\n\t\t\t}\n\n\t\t\tif (method === \"session\") {\n\t\t\t\tlocale = await this.fromSessionStorage(request);\n\t\t\t}\n\n\t\t\tif (method === \"header\") {\n\t\t\t\tlocale = this.fromHeader(request);\n\t\t\t}\n\n\t\t\tif (method === \"custom\") {\n\t\t\t\tlocale = await this.fromCustom(request);\n\t\t\t}\n\n\t\t\tif (locale) return locale;\n\t\t}\n\n\t\treturn this.options.fallbackLanguage;\n\t}\n\n\tprivate get defaultOrder() {\n\t\tlet order: Array<\n\t\t\t\"searchParams\" | \"cookie\" | \"session\" | \"header\" | \"custom\"\n\t\t> = [\"searchParams\", \"cookie\", \"session\", \"header\"];\n\t\tif (this.options.findLocale) order.unshift(\"custom\");\n\t\treturn order;\n\t}\n\n\tprivate fromSearchParams(request: Request): string | null {\n\t\tlet url = new URL(request.url);\n\t\tif (!url.searchParams.has(this.options.searchParamKey ?? \"lng\")) {\n\t\t\treturn null;\n\t\t}\n\t\treturn this.fromSupported(\n\t\t\turl.searchParams.get(this.options.searchParamKey ?? \"lng\"),\n\t\t);\n\t}\n\n\tprivate async fromCookie(request: Request): Promise<string | null> {\n\t\tif (!this.options.cookie) return null;\n\n\t\tlet cookie = this.options.cookie;\n\t\tlet lng = await cookie.parse(request.headers.get(\"Cookie\"));\n\t\tif (typeof lng !== \"string\" || !lng) return null;\n\n\t\treturn this.fromSupported(lng);\n\t}\n\n\tprivate async fromSessionStorage(request: Request): Promise<string | null> {\n\t\tif (!this.options.sessionStorage) return null;\n\n\t\tlet session = await this.options.sessionStorage.getSession(\n\t\t\trequest.headers.get(\"Cookie\"),\n\t\t);\n\n\t\tlet lng = session.get(this.options.sessionKey ?? \"lng\");\n\n\t\tif (!lng) return null;\n\n\t\treturn this.fromSupported(lng);\n\t}\n\n\tprivate fromHeader(request: Request): string | null {\n\t\tlet locales = getClientLocales(request);\n\t\tif (!locales) return null;\n\t\tif (Array.isArray(locales)) return this.fromSupported(locales.join(\",\"));\n\t\treturn this.fromSupported(locales);\n\t}\n\n\tprivate async fromCustom(request: Request): Promise<string | null> {\n\t\tif (!this.options.findLocale) {\n\t\t\tthrow new ReferenceError(\n\t\t\t\t\"You tried to find a locale using `findLocale` but it iss not defined. Change your order to not include `custom` or provide a findLocale functions.\",\n\t\t\t);\n\t\t}\n\t\tlet locales = await this.options.findLocale(request);\n\t\tif (!locales) return null;\n\t\tif (Array.isArray(locales)) return this.fromSupported(locales.join(\",\"));\n\t\treturn this.fromSupported(locales);\n\t}\n\n\tprivate fromSupported(language: string | null) {\n\t\treturn (\n\t\t\tpick(\n\t\t\t\tthis.options.supportedLanguages,\n\t\t\t\tlanguage ?? this.options.fallbackLanguage,\n\t\t\t\t{ loose: false },\n\t\t\t) ||\n\t\t\tpick(\n\t\t\t\tthis.options.supportedLanguages,\n\t\t\t\tlanguage ?? this.options.fallbackLanguage,\n\t\t\t\t{ loose: true },\n\t\t\t)\n\t\t);\n\t}\n}\n", "import {\n\ttype BackendModule,\n\ttype DefaultNamespace,\n\ttype FlatNamespace,\n\ttype InitOptions,\n\ttype KeyPrefix,\n\ttype Module,\n\ttype Namespace,\n\ttype NewableModule,\n\ttype TFunction,\n\tcreateInstance,\n} from \"i18next\";\nimport type { EntryContext } from \"react-router\";\nimport {\n\tLanguageDetector,\n\ttype LanguageDetectorOption,\n} from \"./lib/language-detector.js\";\n\ntype FallbackNs<Ns> = Ns extends undefined\n\t? DefaultNamespace\n\t: Ns extends Namespace\n\t\t? Ns\n\t\t: DefaultNamespace;\n\nexport interface RemixI18NextOption {\n\t/**\n\t * The i18next options used to initialize the internal i18next instance.\n\t */\n\ti18next?: Omit<InitOptions, \"react\" | \"detection\"> | null;\n\t/**\n\t * @deprecated Use `plugins` instead.\n\t * The i18next backend module used to load the translations when creating a\n\t * new TFunction.\n\t */\n\tbackend?: NewableModule<BackendModule<unknown>> | BackendModule<unknown>;\n\t/**\n\t * The i18next plugins used to extend the internal i18next instance\n\t * when creating a new TFunction.\n\t */\n\tplugins?: NewableModule<Module>[] | Module[];\n\tdetection: LanguageDetectorOption;\n}\n\nexport class RemixI18Next {\n\tprivate detector: LanguageDetector;\n\n\tconstructor(private options: RemixI18NextOption) {\n\t\tthis.detector = new LanguageDetector(this.options.detection);\n\t}\n\n\t/**\n\t * Detect the current locale by following the order defined in the\n\t * `detection.order` option.\n\t * By default the order is\n\t * - searchParams\n\t * - cookie\n\t * - session\n\t * - header\n\t * And finally the fallback language.\n\t */\n\tpublic async getLocale(request: Request): Promise<string> {\n\t\treturn this.detector.detect(request);\n\t}\n\n\t/**\n\t * Get the namespaces required by the routes which are going to be rendered\n\t * when doing SSR.\n\t *\n\t * @param context The EntryContext object received by `handleRequest` in entry.server\n\t *\n\t * @example\n\t * await instance.init({\n\t *   ns: i18n.getRouteNamespaces(context),\n\t *   // ...more options\n\t * });\n\t */\n\tpublic getRouteNamespaces(context: EntryContext): string[] {\n\t\tlet namespaces = Object.values(context.routeModules).flatMap((route) => {\n\t\t\tif (typeof route?.handle !== \"object\") return [];\n\t\t\tif (!route.handle) return [];\n\t\t\tif (!(\"i18n\" in route.handle)) return [];\n\t\t\tif (typeof route.handle.i18n === \"string\") return [route.handle.i18n];\n\t\t\tif (\n\t\t\t\tArray.isArray(route.handle.i18n) &&\n\t\t\t\troute.handle.i18n.every((value) => typeof value === \"string\")\n\t\t\t) {\n\t\t\t\treturn route.handle.i18n as string[];\n\t\t\t}\n\t\t\treturn [];\n\t\t});\n\n\t\treturn [...new Set(namespaces)];\n\t}\n\n\t/**\n\t * Return a TFunction that can be used to translate strings server-side.\n\t * This function is fixed to a specific namespace.\n\t *\n\t * @param requestOrLocale The request object or the locale string already detected\n\t * @param namespaces The namespaces to use for the T function. (Default: `translation`).\n\t * @param options The i18next init options and the key prefix to prepend to translation keys.\n\t */\n\tasync getFixedT<\n\t\tN extends\n\t\t\t| FlatNamespace\n\t\t\t| readonly [FlatNamespace, ...FlatNamespace[]] = DefaultNamespace,\n\t\tKPrefix extends KeyPrefix<FallbackNs<N>> = undefined,\n\t>(\n\t\tlocale: string,\n\t\tnamespaces?: N,\n\t\toptions?: Omit<InitOptions, \"react\"> & { keyPrefix?: KPrefix },\n\t): Promise<TFunction<FallbackNs<N>, KPrefix>>;\n\tasync getFixedT<\n\t\tN extends\n\t\t\t| FlatNamespace\n\t\t\t| readonly [FlatNamespace, ...FlatNamespace[]] = DefaultNamespace,\n\t\tKPrefix extends KeyPrefix<FallbackNs<N>> = undefined,\n\t>(\n\t\trequest: Request,\n\t\tnamespaces?: N,\n\t\toptions?: Omit<InitOptions, \"react\"> & { keyPrefix?: KPrefix },\n\t): Promise<TFunction<FallbackNs<N>, KPrefix>>;\n\tasync getFixedT<\n\t\tN extends\n\t\t\t| FlatNamespace\n\t\t\t| readonly [FlatNamespace, ...FlatNamespace[]] = DefaultNamespace,\n\t\tKPrefix extends KeyPrefix<FallbackNs<N>> = undefined,\n\t>(\n\t\trequestOrLocale: Request | string,\n\t\tnamespaces?: N,\n\t\toptions: Omit<InitOptions, \"react\"> & { keyPrefix?: KPrefix } = {},\n\t): Promise<TFunction<FallbackNs<N>, KPrefix>> {\n\t\tlet [instance, locale] = await Promise.all([\n\t\t\tthis.createInstance({ ...this.options.i18next, ...options }),\n\t\t\ttypeof requestOrLocale === \"string\"\n\t\t\t\t? requestOrLocale\n\t\t\t\t: this.getLocale(requestOrLocale),\n\t\t]);\n\n\t\tawait instance.changeLanguage(locale);\n\n\t\tif (namespaces) await instance.loadNamespaces(namespaces);\n\t\telse if (instance.options.defaultNS) {\n\t\t\tawait instance.loadNamespaces(instance.options.defaultNS);\n\t\t} else await instance.loadNamespaces(\"translation\" as DefaultNamespace);\n\n\t\treturn instance.getFixedT<N, KPrefix, N>(\n\t\t\tlocale,\n\t\t\tnamespaces,\n\t\t\toptions?.keyPrefix,\n\t\t);\n\t}\n\n\tprivate async createInstance(options: Omit<InitOptions, \"react\"> = {}) {\n\t\tlet instance = createInstance();\n\t\tlet plugins = [\n\t\t\t...(this.options.backend ? [this.options.backend] : []),\n\t\t\t...(this.options.plugins || []),\n\t\t];\n\t\tfor (const plugin of plugins) instance.use(plugin);\n\t\tawait instance.init(options);\n\t\treturn instance;\n\t}\n}\n\nexport { LanguageDetector };\nexport type { LanguageDetectorOption };\n"], "mappings": ";;;;;;AAEM,SAAU,qBACf,UAAsD;AAEtD,MAAI,QAAQ,CAAC,SAAS,IAAI;AAC1B,MAAI,SAAS;AAAQ,UAAM,KAAK,SAAS,MAAM;AAC/C,MAAI,SAAS;AAAQ,UAAM,KAAK,SAAS,MAAM;AAC/C,SAAO,MAAM,KAAK,GAAG;AACtB;;;ACPA,IAAI,QACH;AAaD,SAAS,SAAS,OAAc;AAC/B,SAAO,OAAO,UAAU;AACzB;AAEM,SAAU,MAAM,gBAAuB;AAC5C,MAAI,WAAW,kBAAkB,IAAI,MAAM,KAAK;AAChD,MAAI,CAAC;AAAS,UAAM,IAAI,MAAM,gCAAgC;AAE9D,MAAI,YAAwB,CAAA;AAE5B,WAAS,KAAK,SAAS;AACtB,QAAI,CAAC;AAAG;AAER,QAAI,EAAE,KAAI;AAEV,QAAI,OAAO,EAAE,MAAM,GAAG;AACtB,QAAI,OAAO,KAAK,CAAC,GAAG,MAAM,GAAG,KAAK,CAAA;AAClC,QAAI,YAAY,KAAK,WAAW;AAEhC,cAAU,KAAK;;MAEd,MAAM,KAAK,CAAC;MACZ,QAAQ,YAAY,KAAK,CAAC,IAAI;MAC9B,QAAQ,YAAY,KAAK,CAAC,IAAI,KAAK,CAAC;MACpC,SAAS,KAAK,CAAC;;QAEZ,OAAO,WAAW,KAAK,CAAC,EAAG,MAAM,GAAG,EAAE,CAAC,CAAE,KAAK;UAC9C;KACH;EACF;AAEA,SAAO,UAAU,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD;AAEM,SAAU,KACf,oBACA,gBACA,UAAuB,EAAE,OAAO,MAAK,GAAE;AAEvC,MAAI,CAAC,sBAAsB,CAAC,mBAAmB,UAAU,CAAC,gBAAgB;AACzE,WAAO;EACR;AAEA,MAAI,uBAAuB,SAAS,cAAc,IAC/C,MAAM,cAAc,IACpB;AAEH,MAAI,YAAY,mBAAmB,IAAI,CAAC,YAAW;AAClD,QAAI,OAAO,QAAQ,MAAM,GAAG;AAC5B,QAAI,YAAY,KAAK,WAAW;AAEhC,WAAO;;MAEN,MAAM,KAAK,CAAC;MACZ,QAAQ,YAAY,KAAK,CAAC,IAAI;MAC9B,SAAS,YAAY,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM;;EAE7C,CAAC;AAED,WAAS,QAAQ,sBAAsB;AACtC,QAAI,CAAC;AAAM;AACX,QAAI,WAAW,KAAK,KAAK,YAAW;AACpC,QAAI,aAAa,KAAK,SAAS,KAAK,OAAO,YAAW,IAAK,KAAK;AAChE,QAAI,aAAa,KAAK,SAAS,KAAK,OAAO,YAAW,IAAK,KAAK;AAEhE,aAAS,qBAAqB,WAAW;AACxC,UAAI,gBAAgB,kBAAkB,MAAM,YAAW,KAAM;AAC7D,UAAI,aAAa;AAAe;AAEhC,UAAI,kBAAkB,kBAAkB,SACrC,kBAAkB,OAAO,YAAW,IACpC,kBAAkB;AACrB,UAAI,kBAAkB,kBAAkB,SACrC,kBAAkB,OAAO,YAAW,IACpC,kBAAkB;AAErB,UACC,aAAa,kBACZ,SAAS,SAAS,CAAC,cAAc,eAAe,qBAChD,SAAS,SAAS,CAAC,cAAc,eAAe,kBAChD;AACD,eAAO,qBAAqB,iBAAiB;MAC9C;IACD;EACD;AAEA,SAAO;AACR;;;ACjFM,SAAU,iBAAiB,kBAAmC;AACnE,MAAI,UAAU,WAAW,gBAAgB;AAEzC,MAAI,iBAAiB,QAAQ,IAAI,iBAAiB;AAGlD,MAAI,CAAC;AAAgB,WAAO;AAE5B,MAAI,gBAAgB,MAAM,cAAc,EACtC,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,EAClC,IAAI,oBAAoB;AAE1B,MAAI,eAAyB,CAAA;AAE7B,WAASA,WAAU,eAAe;AACjC,QAAI;AAEH,UAAI,KAAK,OAAOA,OAAM;AAGtB,mBAAa,KAAKA,OAAM;IACzB,QAAQ;IAER;EACD;AAEA,MAAI,SAAS,KACZ,KAAK,eAAe,mBAAmB,YAAY,GACnD,cAAc;AAGf,SAAO,UAAU;AAClB;AAOA,SAAS,WAAW,kBAAmC;AACtD,MAAI,4BAA4B;AAAS,WAAO,iBAAiB;AACjE,SAAO;AACR;;;ACGM,IAAO,mBAAP,MAAuB;EACR;EAApB,YAAoB,SAA+B;AAA/B,SAAA,UAAA;AACnB,SAAK,cAAc,OAAO;AAC1B,SAAK,aAAa,OAAO;EAC1B;EAEQ,cAAc,SAA+B;AACpD,QACC,QAAQ,OAAO,WAAW,KAC1B,QAAQ,MAAM,CAAC,MAAM,aACrB,CAAC,QAAQ,gBACR;AACD,YAAM,IAAI,MACT,+EAA+E;IAEjF;EACD;EAEQ,aAAa,SAA+B;AACnD,QACC,QAAQ,OAAO,WAAW,KAC1B,QAAQ,MAAM,CAAC,MAAM,YACrB,CAAC,QAAQ,QACR;AACD,YAAM,IAAI,MACT,sEAAsE;IAExE;EACD;EAEO,MAAM,OAAO,SAAgB;AACnC,QAAI,QAAQ,KAAK,QAAQ,SAAS,KAAK;AAEvC,aAAS,UAAU,OAAO;AACzB,UAAI,SAAwB;AAE5B,UAAI,WAAW,gBAAgB;AAC9B,iBAAS,KAAK,iBAAiB,OAAO;MACvC;AAEA,UAAI,WAAW,UAAU;AACxB,iBAAS,MAAM,KAAK,WAAW,OAAO;MACvC;AAEA,UAAI,WAAW,WAAW;AACzB,iBAAS,MAAM,KAAK,mBAAmB,OAAO;MAC/C;AAEA,UAAI,WAAW,UAAU;AACxB,iBAAS,KAAK,WAAW,OAAO;MACjC;AAEA,UAAI,WAAW,UAAU;AACxB,iBAAS,MAAM,KAAK,WAAW,OAAO;MACvC;AAEA,UAAI;AAAQ,eAAO;IACpB;AAEA,WAAO,KAAK,QAAQ;EACrB;EAEA,IAAY,eAAY;AACvB,QAAI,QAEA,CAAC,gBAAgB,UAAU,WAAW,QAAQ;AAClD,QAAI,KAAK,QAAQ;AAAY,YAAM,QAAQ,QAAQ;AACnD,WAAO;EACR;EAEQ,iBAAiB,SAAgB;AACxC,QAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,QAAI,CAAC,IAAI,aAAa,IAAI,KAAK,QAAQ,kBAAkB,KAAK,GAAG;AAChE,aAAO;IACR;AACA,WAAO,KAAK,cACX,IAAI,aAAa,IAAI,KAAK,QAAQ,kBAAkB,KAAK,CAAC;EAE5D;EAEQ,MAAM,WAAW,SAAgB;AACxC,QAAI,CAAC,KAAK,QAAQ;AAAQ,aAAO;AAEjC,QAAI,SAAS,KAAK,QAAQ;AAC1B,QAAI,MAAM,MAAM,OAAO,MAAM,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AAC1D,QAAI,OAAO,QAAQ,YAAY,CAAC;AAAK,aAAO;AAE5C,WAAO,KAAK,cAAc,GAAG;EAC9B;EAEQ,MAAM,mBAAmB,SAAgB;AAChD,QAAI,CAAC,KAAK,QAAQ;AAAgB,aAAO;AAEzC,QAAI,UAAU,MAAM,KAAK,QAAQ,eAAe,WAC/C,QAAQ,QAAQ,IAAI,QAAQ,CAAC;AAG9B,QAAI,MAAM,QAAQ,IAAI,KAAK,QAAQ,cAAc,KAAK;AAEtD,QAAI,CAAC;AAAK,aAAO;AAEjB,WAAO,KAAK,cAAc,GAAG;EAC9B;EAEQ,WAAW,SAAgB;AAClC,QAAI,UAAU,iBAAiB,OAAO;AACtC,QAAI,CAAC;AAAS,aAAO;AACrB,QAAI,MAAM,QAAQ,OAAO;AAAG,aAAO,KAAK,cAAc,QAAQ,KAAK,GAAG,CAAC;AACvE,WAAO,KAAK,cAAc,OAAO;EAClC;EAEQ,MAAM,WAAW,SAAgB;AACxC,QAAI,CAAC,KAAK,QAAQ,YAAY;AAC7B,YAAM,IAAI,eACT,oJAAoJ;IAEtJ;AACA,QAAI,UAAU,MAAM,KAAK,QAAQ,WAAW,OAAO;AACnD,QAAI,CAAC;AAAS,aAAO;AACrB,QAAI,MAAM,QAAQ,OAAO;AAAG,aAAO,KAAK,cAAc,QAAQ,KAAK,GAAG,CAAC;AACvE,WAAO,KAAK,cAAc,OAAO;EAClC;EAEQ,cAAc,UAAuB;AAC5C,WACC,KACC,KAAK,QAAQ,oBACb,YAAY,KAAK,QAAQ,kBACzB,EAAE,OAAO,MAAK,CAAE,KAEjB,KACC,KAAK,QAAQ,oBACb,YAAY,KAAK,QAAQ,kBACzB,EAAE,OAAO,KAAI,CAAE;EAGlB;;;;AChKK,IAAO,eAAP,MAAmB;EAGJ;EAFZ;EAER,YAAoB,SAA2B;AAA3B,SAAA,UAAA;AACnB,SAAK,WAAW,IAAI,iBAAiB,KAAK,QAAQ,SAAS;EAC5D;;;;;;;;;;;EAYO,MAAM,UAAU,SAAgB;AACtC,WAAO,KAAK,SAAS,OAAO,OAAO;EACpC;;;;;;;;;;;;;EAcO,mBAAmB,SAAqB;AAC9C,QAAI,aAAa,OAAO,OAAO,QAAQ,YAAY,EAAE,QAAQ,CAAC,UAAS;AACtE,UAAI,OAAO,OAAO,WAAW;AAAU,eAAO,CAAA;AAC9C,UAAI,CAAC,MAAM;AAAQ,eAAO,CAAA;AAC1B,UAAI,EAAE,UAAU,MAAM;AAAS,eAAO,CAAA;AACtC,UAAI,OAAO,MAAM,OAAO,SAAS;AAAU,eAAO,CAAC,MAAM,OAAO,IAAI;AACpE,UACC,MAAM,QAAQ,MAAM,OAAO,IAAI,KAC/B,MAAM,OAAO,KAAK,MAAM,CAAC,UAAU,OAAO,UAAU,QAAQ,GAC3D;AACD,eAAO,MAAM,OAAO;MACrB;AACA,aAAO,CAAA;IACR,CAAC;AAED,WAAO,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC;EAC/B;EA8BA,MAAM,UAML,iBACA,YACA,UAAgE,CAAA,GAAE;AAElE,QAAI,CAAC,UAAU,MAAM,IAAI,MAAM,QAAQ,IAAI;MAC1C,KAAK,eAAe,EAAE,GAAG,KAAK,QAAQ,SAAS,GAAG,QAAO,CAAE;MAC3D,OAAO,oBAAoB,WACxB,kBACA,KAAK,UAAU,eAAe;KACjC;AAED,UAAM,SAAS,eAAe,MAAM;AAEpC,QAAI;AAAY,YAAM,SAAS,eAAe,UAAU;aAC/C,SAAS,QAAQ,WAAW;AACpC,YAAM,SAAS,eAAe,SAAS,QAAQ,SAAS;IACzD;AAAO,YAAM,SAAS,eAAe,aAAiC;AAEtE,WAAO,SAAS,UACf,QACA,YACA,SAAS,SAAS;EAEpB;EAEQ,MAAM,eAAe,UAAsC,CAAA,GAAE;AACpE,QAAI,WAAW,eAAc;AAC7B,QAAI,UAAU;MACb,GAAI,KAAK,QAAQ,UAAU,CAAC,KAAK,QAAQ,OAAO,IAAI,CAAA;MACpD,GAAI,KAAK,QAAQ,WAAW,CAAA;;AAE7B,eAAW,UAAU;AAAS,eAAS,IAAI,MAAM;AACjD,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO;EACR;;", "names": ["locale"]}