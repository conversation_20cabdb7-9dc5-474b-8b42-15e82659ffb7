import {
  IS_REACT_LEGACY,
  UNDEFINED,
  getTimestamp,
  mergeObjects,
  serialize,
  useIsomorphicLayoutEffect,
  useSWR,
  useSWRConfig,
  withMiddleware
} from "./chunk-T23HCMUU.js";
import "./chunk-7NB6W5H5.js";
import {
  require_react
} from "./chunk-R26XTA6N.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/swr/dist/mutation/index.mjs
var import_react = __toESM(require_react(), 1);
var startTransition = IS_REACT_LEGACY ? (cb) => {
  cb();
} : import_react.default.startTransition;
var useStateWithDeps = (initialState) => {
  const [, rerender] = (0, import_react.useState)({});
  const unmountedRef = (0, import_react.useRef)(false);
  const stateRef = (0, import_react.useRef)(initialState);
  const stateDependenciesRef = (0, import_react.useRef)({
    data: false,
    error: false,
    isValidating: false
  });
  const setState = (0, import_react.useCallback)((payload) => {
    let shouldRerender = false;
    const currentState = stateRef.current;
    for (const key in payload) {
      if (Object.prototype.hasOwnProperty.call(payload, key)) {
        const k = key;
        if (currentState[k] !== payload[k]) {
          currentState[k] = payload[k];
          if (stateDependenciesRef.current[k]) {
            shouldRerender = true;
          }
        }
      }
    }
    if (shouldRerender && !unmountedRef.current) {
      rerender({});
    }
  }, []);
  useIsomorphicLayoutEffect(() => {
    unmountedRef.current = false;
    return () => {
      unmountedRef.current = true;
    };
  });
  return [
    stateRef,
    stateDependenciesRef.current,
    setState
  ];
};
var mutation = () => (key, fetcher, config = {}) => {
  const { mutate } = useSWRConfig();
  const keyRef = (0, import_react.useRef)(key);
  const fetcherRef = (0, import_react.useRef)(fetcher);
  const configRef = (0, import_react.useRef)(config);
  const ditchMutationsUntilRef = (0, import_react.useRef)(0);
  const [stateRef, stateDependencies, setState] = useStateWithDeps({
    data: UNDEFINED,
    error: UNDEFINED,
    isMutating: false
  });
  const currentState = stateRef.current;
  const trigger = (0, import_react.useCallback)(
    async (arg, opts) => {
      const [serializedKey, resolvedKey] = serialize(keyRef.current);
      if (!fetcherRef.current) {
        throw new Error("Can’t trigger the mutation: missing fetcher.");
      }
      if (!serializedKey) {
        throw new Error("Can’t trigger the mutation: missing key.");
      }
      const options = mergeObjects(mergeObjects({
        populateCache: false,
        throwOnError: true
      }, configRef.current), opts);
      const mutationStartedAt = getTimestamp();
      ditchMutationsUntilRef.current = mutationStartedAt;
      setState({
        isMutating: true
      });
      try {
        const data = await mutate(
          serializedKey,
          fetcherRef.current(resolvedKey, {
            arg
          }),
          // We must throw the error here so we can catch and update the states.
          mergeObjects(options, {
            throwOnError: true
          })
        );
        if (ditchMutationsUntilRef.current <= mutationStartedAt) {
          startTransition(() => setState({
            data,
            isMutating: false,
            error: void 0
          }));
          options.onSuccess == null ? void 0 : options.onSuccess.call(options, data, serializedKey, options);
        }
        return data;
      } catch (error) {
        if (ditchMutationsUntilRef.current <= mutationStartedAt) {
          startTransition(() => setState({
            error,
            isMutating: false
          }));
          options.onError == null ? void 0 : options.onError.call(options, error, serializedKey, options);
          if (options.throwOnError) {
            throw error;
          }
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const reset = (0, import_react.useCallback)(() => {
    ditchMutationsUntilRef.current = getTimestamp();
    setState({
      data: UNDEFINED,
      error: UNDEFINED,
      isMutating: false
    });
  }, []);
  useIsomorphicLayoutEffect(() => {
    keyRef.current = key;
    fetcherRef.current = fetcher;
    configRef.current = config;
  });
  return {
    trigger,
    reset,
    get data() {
      stateDependencies.data = true;
      return currentState.data;
    },
    get error() {
      stateDependencies.error = true;
      return currentState.error;
    },
    get isMutating() {
      stateDependencies.isMutating = true;
      return currentState.isMutating;
    }
  };
};
var useSWRMutation = withMiddleware(useSWR, mutation);
export {
  useSWRMutation as default
};
//# sourceMappingURL=swr_mutation.js.map
