import { StepFormWizardSession } from "@prisma/client";
import { useTranslation } from "react-i18next";
import { Colors } from "~/application/enums/shared/Colors";
import SimpleBadge from "~/components/ui/badges/SimpleBadge";

export default function StepFormWizardSessionBadge({ item }: { item: StepFormWizardSession }) {
  const { t } = useTranslation();
  return (
    <>
      {item.status === "active" && <SimpleBadge title={t("stepFormWizard.object.sessions.active")} color={Colors.YELLOW} className="!bg-primary-light !text-[#202229] !font-medium h-[22px] py-[6px] text-[12px]" />}
      {item.status === "started" && <SimpleBadge title={t("stepFormWizard.object.sessions.started")} color={Colors.INDIGO} className="!bg-[#f2e9db] !text-[#202229] !font-medium h-[22px] py-[6px] text-[12px]" />}
      {item.status === "dismissed" && <SimpleBadge title={t("stepFormWizard.object.sessions.dismissed")} color={Colors.ORANGE} className="!bg-[#f8dddd] !text-[#202229] !font-medium h-[22px] py-[6px] text-[12px]" />}
      {item.status === "completed" && <SimpleBadge title={t("stepFormWizard.object.sessions.completed")} color={Colors.GREEN} className="!bg-[#d5f8e5] !text-[#202229] !font-medium h-[22px] py-[6px] text-[12px]" />}
    </>
  );
}
