import { db } from "~/utils/db.server";

export async function updateStepFormWizardSessionStep(id: string, data: { seenAt?: Date; completedAt?: Date }) {
  return await db.stepFormWizardSessionStep.update({
    where: { id },
    data,
  });
}

export async function deleteStepFormWizardSessionSteps(ids: string[]) {
  return await db.stepFormWizardSessionStep.deleteMany({
    where: { id: { in: ids } },
  });
}
