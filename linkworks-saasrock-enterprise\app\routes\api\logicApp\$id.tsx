import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import type { ActionFunction } from "react-router";
import { updateStepFormWizardSession } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessions.db.server";
import { db } from "~/utils/db.server";

export const action: ActionFunction = async ({ request, params }) => {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const data = await request.json();
    const sessionId = String(params.id || "");

    const fieldsToUpdate = ["status", "logicAppRunId", "callBackURL", "currentStep", "error"];

    const updates = Object.fromEntries(
      fieldsToUpdate
        .filter((key) => data?.[key] !== undefined && data?.[key] !== null)
        .map((key) => [key, key === "currentStep" ? String(data[key]) : data[key]])
    );

    updates.currentStepIndex = String(data.currentStep);
    delete updates.currentStep;

    const updatedSession = await updateStepFormWizardSession(sessionId?.trim(), updates);

    const stepToBeUpdated = await db.stepFormWizardSessionStep.findFirst({
      where: {
        stepFormWizardSessionId: sessionId?.trim(),
        step: {
          is: {
            order: parseInt(data.currentStep) + 1,
          },
        },
      },
    });

    let updatedStepData: any = {
      status: updates?.status,
      error: updates?.error,
    };

    if(!updates?.error?.length) updatedStepData = { ...updatedStepData, completedAt: new Date() };
    const updatedStep = await db.stepFormWizardSessionStep.update({
      where: {
        id: stepToBeUpdated?.id,
      },
      data: {
        ...updatedStepData
      },
    });

    return Response.json({ message: "Data received successfully", data: updatedSession }, { status: 200 });
  } catch (error) {
    if (error instanceof PrismaClientKnownRequestError && error.code === "P2025") {
      console.error(`No stepFormWizard session found with ID: ${params?.id}`);
      return Response.json({ error: "Step form wizard session not found." });
    }
    return Response.json({ error: "Invalid request data" }, { status: 400 });
  }
};
