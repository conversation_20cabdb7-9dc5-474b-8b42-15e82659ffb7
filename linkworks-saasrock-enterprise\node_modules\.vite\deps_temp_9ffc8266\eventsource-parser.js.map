{"version": 3, "sources": ["../../eventsource-parser/src/parse.ts"], "sourcesContent": ["/**\n * EventSource/Server-Sent Events parser\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n *\n * Based on code from the {@link https://github.com/EventSource/eventsource | EventSource module},\n * which is licensed under the MIT license. And copyrighted the EventSource GitHub organisation.\n */\nimport type {EventSourceParseCallback, EventSourceParser} from './types.js'\n\n/**\n * Creates a new EventSource parser.\n *\n * @param onParse - Callback to invoke when a new event is parsed, or a new reconnection interval\n *                  has been sent from the server\n *\n * @returns A new EventSource parser, with `parse` and `reset` methods.\n * @public\n */\nexport function createParser(onParse: EventSourceParseCallback): EventSourceParser {\n  // Processing state\n  let isFirstChunk: boolean\n  let buffer: string\n  let startingPosition: number\n  let startingFieldLength: number\n\n  // Event state\n  let eventId: string | undefined\n  let eventName: string | undefined\n  let data: string\n\n  reset()\n  return {feed, reset}\n\n  function reset(): void {\n    isFirstChunk = true\n    buffer = ''\n    startingPosition = 0\n    startingFieldLength = -1\n\n    eventId = undefined\n    eventName = undefined\n    data = ''\n  }\n\n  function feed(chunk: string): void {\n    buffer = buffer ? buffer + chunk : chunk\n\n    // Strip any UTF8 byte order mark (BOM) at the start of the stream.\n    // Note that we do not strip any non - UTF8 BOM, as eventsource streams are\n    // always decoded as UTF8 as per the specification.\n    if (isFirstChunk && hasBom(buffer)) {\n      buffer = buffer.slice(BOM.length)\n    }\n\n    isFirstChunk = false\n\n    // Set up chunk-specific processing state\n    const length = buffer.length\n    let position = 0\n    let discardTrailingNewline = false\n\n    // Read the current buffer byte by byte\n    while (position < length) {\n      // EventSource allows for carriage return + line feed, which means we\n      // need to ignore a linefeed character if the previous character was a\n      // carriage return\n      // @todo refactor to reduce nesting, consider checking previous byte?\n      // @todo but consider multiple chunks etc\n      if (discardTrailingNewline) {\n        if (buffer[position] === '\\n') {\n          ++position\n        }\n        discardTrailingNewline = false\n      }\n\n      let lineLength = -1\n      let fieldLength = startingFieldLength\n      let character: string\n\n      for (let index = startingPosition; lineLength < 0 && index < length; ++index) {\n        character = buffer[index]\n        if (character === ':' && fieldLength < 0) {\n          fieldLength = index - position\n        } else if (character === '\\r') {\n          discardTrailingNewline = true\n          lineLength = index - position\n        } else if (character === '\\n') {\n          lineLength = index - position\n        }\n      }\n\n      if (lineLength < 0) {\n        startingPosition = length - position\n        startingFieldLength = fieldLength\n        break\n      } else {\n        startingPosition = 0\n        startingFieldLength = -1\n      }\n\n      parseEventStreamLine(buffer, position, fieldLength, lineLength)\n\n      position += lineLength + 1\n    }\n\n    if (position === length) {\n      // If we consumed the entire buffer to read the event, reset the buffer\n      buffer = ''\n    } else if (position > 0) {\n      // If there are bytes left to process, set the buffer to the unprocessed\n      // portion of the buffer only\n      buffer = buffer.slice(position)\n    }\n  }\n\n  function parseEventStreamLine(\n    lineBuffer: string,\n    index: number,\n    fieldLength: number,\n    lineLength: number,\n  ) {\n    if (lineLength === 0) {\n      // We reached the last line of this event\n      if (data.length > 0) {\n        onParse({\n          type: 'event',\n          id: eventId,\n          event: eventName || undefined,\n          data: data.slice(0, -1), // remove trailing newline\n        })\n\n        data = ''\n        eventId = undefined\n      }\n      eventName = undefined\n      return\n    }\n\n    const noValue = fieldLength < 0\n    const field = lineBuffer.slice(index, index + (noValue ? lineLength : fieldLength))\n    let step = 0\n\n    if (noValue) {\n      step = lineLength\n    } else if (lineBuffer[index + fieldLength + 1] === ' ') {\n      step = fieldLength + 2\n    } else {\n      step = fieldLength + 1\n    }\n\n    const position = index + step\n    const valueLength = lineLength - step\n    const value = lineBuffer.slice(position, position + valueLength).toString()\n\n    if (field === 'data') {\n      data += value ? `${value}\\n` : '\\n'\n    } else if (field === 'event') {\n      eventName = value\n    } else if (field === 'id' && !value.includes('\\u0000')) {\n      eventId = value\n    } else if (field === 'retry') {\n      const retry = parseInt(value, 10)\n      if (!Number.isNaN(retry)) {\n        onParse({type: 'reconnect-interval', value: retry})\n      }\n    }\n  }\n}\n\nconst BOM = [239, 187, 191]\n\nfunction hasBom(buffer: string) {\n  return BOM.every((charCode: number, index: number) => buffer.charCodeAt(index) === charCode)\n}\n"], "mappings": ";;;AAkBO,SAASA,aAAaC,SAAsD;AAE7E,MAAAC;AACA,MAAAC;AACA,MAAAC;AACA,MAAAC;AAGA,MAAAC;AACA,MAAAC;AACA,MAAAC;AAEEC,QAAA;AACC,SAAA;IAACC;IAAMD;;AAEd,WAASA,QAAc;AACNP,mBAAA;AACNC,aAAA;AACUC,uBAAA;AACGC,0BAAA;AAEZC,cAAA;AACEC,gBAAA;AACLC,WAAA;EACT;AAEA,WAASE,KAAKC,OAAqB;AACxBR,aAAAA,SAASA,SAASQ,QAAQA;AAK/B,QAAAT,gBAAgBU,OAAOT,MAAM,GAAG;AACzBA,eAAAA,OAAOU,MAAMC,IAAIC,MAAM;IAClC;AAEeb,mBAAA;AAGf,UAAMa,SAASZ,OAAOY;AACtB,QAAIC,WAAW;AACf,QAAIC,yBAAyB;AAG7B,WAAOD,WAAWD,QAAQ;AAMxB,UAAIE,wBAAwB;AACtB,YAAAd,OAAOa,QAAQ,MAAM,MAAM;AAC3B,YAAAA;QACJ;AACyBC,iCAAA;MAC3B;AAEA,UAAIC,aAAa;AACjB,UAAIC,cAAcd;AACd,UAAAe;AAEJ,eAASC,QAAQjB,kBAAkBc,aAAa,KAAKG,QAAQN,QAAQ,EAAEM,OAAO;AAC5ED,oBAAYjB,OAAOkB,KAAK;AACpB,YAAAD,cAAc,OAAOD,cAAc,GAAG;AACxCA,wBAAcE,QAAQL;QAAA,WACbI,cAAc,MAAM;AACJH,mCAAA;AACzBC,uBAAaG,QAAQL;QAAA,WACZI,cAAc,MAAM;AAC7BF,uBAAaG,QAAQL;QACvB;MACF;AAEA,UAAIE,aAAa,GAAG;AAClBd,2BAAmBW,SAASC;AACNX,8BAAAc;AACtB;MAAA,OACK;AACcf,2BAAA;AACGC,8BAAA;MACxB;AAEqBiB,2BAAAnB,QAAQa,UAAUG,aAAaD,UAAU;AAE9DF,kBAAYE,aAAa;IAC3B;AAEA,QAAIF,aAAaD,QAAQ;AAEdZ,eAAA;IAAA,WACAa,WAAW,GAAG;AAGdb,eAAAA,OAAOU,MAAMG,QAAQ;IAChC;EACF;AAEA,WAASM,qBACPC,YACAF,OACAF,aACAD,YACA;AACA,QAAIA,eAAe,GAAG;AAEhB,UAAAV,KAAKO,SAAS,GAAG;AACXd,gBAAA;UACNuB,MAAM;UACNC,IAAInB;UACJoB,OAAOnB,aAAa;UACpBC,MAAMA,KAAKK,MAAM,GAAG,EAAE;;QAAA,CACvB;AAEML,eAAA;AACGF,kBAAA;MACZ;AACYC,kBAAA;AACZ;IACF;AAEA,UAAMoB,UAAUR,cAAc;AAC9B,UAAMS,QAAQL,WAAWV,MAAMQ,OAAOA,SAASM,UAAUT,aAAaC,YAAY;AAClF,QAAIU,OAAO;AAEX,QAAIF,SAAS;AACJE,aAAAX;IAAA,WACEK,WAAWF,QAAQF,cAAc,CAAC,MAAM,KAAK;AACtDU,aAAOV,cAAc;IAAA,OAChB;AACLU,aAAOV,cAAc;IACvB;AAEA,UAAMH,WAAWK,QAAQQ;AACzB,UAAMC,cAAcZ,aAAaW;AACjC,UAAME,QAAQR,WAAWV,MAAMG,UAAUA,WAAWc,WAAW,EAAEE,SAAS;AAE1E,QAAIJ,UAAU,QAAQ;AACZpB,cAAAuB,QAAQ,GAAGE,OAAAF,OAAK,IAAO,IAAA;IAAA,WACtBH,UAAU,SAAS;AAChBrB,kBAAAwB;IAAA,WACHH,UAAU,QAAQ,CAACG,MAAMG,SAAS,IAAQ,GAAG;AAC5C5B,gBAAAyB;IAAA,WACDH,UAAU,SAAS;AACtB,YAAAO,QAAQC,SAASL,OAAO,EAAE;AAChC,UAAI,CAACM,OAAOC,MAAMH,KAAK,GAAG;AACxBlC,gBAAQ;UAACuB,MAAM;UAAsBO,OAAOI;QAAM,CAAA;MACpD;IACF;EACF;AACF;AAEA,IAAMrB,MAAM,CAAC,KAAK,KAAK,GAAG;AAE1B,SAASF,OAAOT,QAAgB;AACvB,SAAAW,IAAIyB,MAAM,CAACC,UAAkBnB,UAAkBlB,OAAOsC,WAAWpB,KAAK,MAAMmB,QAAQ;AAC7F;", "names": ["create<PERSON><PERSON><PERSON>", "onParse", "isFirstChunk", "buffer", "startingPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventId", "eventName", "data", "reset", "feed", "chunk", "hasBom", "slice", "BOM", "length", "position", "discardTrailingNewline", "lineLength", "<PERSON><PERSON><PERSON><PERSON>", "character", "index", "parseEventStreamLine", "lineBuffer", "type", "id", "event", "noValue", "field", "step", "valueLength", "value", "toString", "concat", "includes", "retry", "parseInt", "Number", "isNaN", "every", "charCode", "charCodeAt"]}