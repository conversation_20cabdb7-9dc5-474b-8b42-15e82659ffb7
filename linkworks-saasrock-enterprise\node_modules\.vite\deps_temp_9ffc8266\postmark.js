import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/postmark/dist/client/errors/Errors.js
var require_Errors = __commonJS({
  "node_modules/postmark/dist/client/errors/Errors.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.InvalidEmailRequestError = exports.InactiveRecipientsError = exports.ApiInputError = exports.UnknownError = exports.RateLimitExceededError = exports.ServiceUnavailablerError = exports.InternalServerError = exports.InvalidAPIKeyError = exports.HttpError = exports.PostmarkError = void 0;
    var PostmarkError = (
      /** @class */
      function(_super) {
        __extends(PostmarkError2, _super);
        function PostmarkError2(message, code, statusCode) {
          if (code === void 0) {
            code = 0;
          }
          if (statusCode === void 0) {
            statusCode = 0;
          }
          var _this = _super.call(this, message) || this;
          _this.statusCode = statusCode;
          _this.code = code;
          Object.setPrototypeOf(_this, PostmarkError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        PostmarkError2.prototype.setUpStackTrace = function() {
          this.name = this.constructor.name;
          Error.captureStackTrace(this, this.constructor);
        };
        return PostmarkError2;
      }(Error)
    );
    exports.PostmarkError = PostmarkError;
    var HttpError = (
      /** @class */
      function(_super) {
        __extends(HttpError2, _super);
        function HttpError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, HttpError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return HttpError2;
      }(PostmarkError)
    );
    exports.HttpError = HttpError;
    var InvalidAPIKeyError = (
      /** @class */
      function(_super) {
        __extends(InvalidAPIKeyError2, _super);
        function InvalidAPIKeyError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, InvalidAPIKeyError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return InvalidAPIKeyError2;
      }(HttpError)
    );
    exports.InvalidAPIKeyError = InvalidAPIKeyError;
    var InternalServerError = (
      /** @class */
      function(_super) {
        __extends(InternalServerError2, _super);
        function InternalServerError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, InternalServerError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return InternalServerError2;
      }(HttpError)
    );
    exports.InternalServerError = InternalServerError;
    var ServiceUnavailablerError = (
      /** @class */
      function(_super) {
        __extends(ServiceUnavailablerError2, _super);
        function ServiceUnavailablerError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, ServiceUnavailablerError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return ServiceUnavailablerError2;
      }(HttpError)
    );
    exports.ServiceUnavailablerError = ServiceUnavailablerError;
    var RateLimitExceededError = (
      /** @class */
      function(_super) {
        __extends(RateLimitExceededError2, _super);
        function RateLimitExceededError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, RateLimitExceededError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return RateLimitExceededError2;
      }(HttpError)
    );
    exports.RateLimitExceededError = RateLimitExceededError;
    var UnknownError = (
      /** @class */
      function(_super) {
        __extends(UnknownError2, _super);
        function UnknownError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, UnknownError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return UnknownError2;
      }(HttpError)
    );
    exports.UnknownError = UnknownError;
    var ApiInputError = (
      /** @class */
      function(_super) {
        __extends(ApiInputError2, _super);
        function ApiInputError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, ApiInputError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        ApiInputError2.buildSpecificError = function(message, code, statusCode) {
          switch (code) {
            case this.ERROR_CODES.inactiveRecipient:
              return new InactiveRecipientsError(message, code, statusCode);
            case this.ERROR_CODES.invalidEmailRequest:
              return new InvalidEmailRequestError(message, code, statusCode);
            default:
              return new ApiInputError2(message, code, statusCode);
          }
        };
        ApiInputError2.ERROR_CODES = {
          inactiveRecipient: 406,
          invalidEmailRequest: 300
        };
        return ApiInputError2;
      }(HttpError)
    );
    exports.ApiInputError = ApiInputError;
    var InactiveRecipientsError = (
      /** @class */
      function(_super) {
        __extends(InactiveRecipientsError2, _super);
        function InactiveRecipientsError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, InactiveRecipientsError2.prototype);
          _this.setUpStackTrace();
          _this.recipients = InactiveRecipientsError2.parseInactiveRecipients(message);
          return _this;
        }
        InactiveRecipientsError2.parseInactiveRecipients = function(message) {
          var result = [];
          this.inactiveRecipientsPatterns.some(function(pattern) {
            var regexResult = message.match(pattern);
            if (regexResult !== null) {
              result = regexResult[1].split(",").map(function(element) {
                return element.trim();
              });
              return result;
            } else {
              result = [];
            }
          });
          return result;
        };
        InactiveRecipientsError2.inactiveRecipientsPatterns = [
          /Found inactive addresses: (.+?)\.? Inactive/,
          /these inactive addresses: (.+?)\.?$/
        ];
        return InactiveRecipientsError2;
      }(ApiInputError)
    );
    exports.InactiveRecipientsError = InactiveRecipientsError;
    var InvalidEmailRequestError = (
      /** @class */
      function(_super) {
        __extends(InvalidEmailRequestError2, _super);
        function InvalidEmailRequestError2(message, code, statusCode) {
          var _this = _super.call(this, message, code, statusCode) || this;
          Object.setPrototypeOf(_this, InvalidEmailRequestError2.prototype);
          _this.setUpStackTrace();
          return _this;
        }
        return InvalidEmailRequestError2;
      }(ApiInputError)
    );
    exports.InvalidEmailRequestError = InvalidEmailRequestError;
  }
});

// node_modules/postmark/dist/client/errors/ErrorHandler.js
var require_ErrorHandler = __commonJS({
  "node_modules/postmark/dist/client/errors/ErrorHandler.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ErrorHandler = void 0;
    var Errors = require_Errors();
    var ErrorHandler = (
      /** @class */
      function() {
        function ErrorHandler2() {
        }
        ErrorHandler2.prototype.buildError = function(errorMessage, code, statusCode) {
          if (code === void 0) {
            code = 0;
          }
          if (statusCode === void 0) {
            statusCode = 0;
          }
          if (statusCode === 0 && code === 0) {
            return new Errors.PostmarkError(errorMessage);
          } else {
            return this.buildErrorByHttpStatusCode(errorMessage, code, statusCode);
          }
        };
        ErrorHandler2.prototype.buildErrorByHttpStatusCode = function(errorMessage, errorCode, errorStatusCode) {
          switch (errorStatusCode) {
            case 401:
              return new Errors.InvalidAPIKeyError(errorMessage, errorCode, errorStatusCode);
            case 404:
              return new Errors.PostmarkError(errorMessage, errorCode, errorStatusCode);
            case 422:
              return Errors.ApiInputError.buildSpecificError(errorMessage, errorCode, errorStatusCode);
            case 429:
              return new Errors.RateLimitExceededError(errorMessage, errorCode, errorStatusCode);
            case 500:
              return new Errors.InternalServerError(errorMessage, errorCode, errorStatusCode);
            case 503:
              return new Errors.ServiceUnavailablerError(errorMessage, errorCode, errorStatusCode);
            default:
              return new Errors.UnknownError(errorMessage, errorCode, errorStatusCode);
          }
        };
        return ErrorHandler2;
      }()
    );
    exports.ErrorHandler = ErrorHandler;
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/bind.js
var require_bind = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/bind.js"(exports, module) {
    "use strict";
    module.exports = function bind(fn, thisArg) {
      return function wrap() {
        var args = new Array(arguments.length);
        for (var i = 0; i < args.length; i++) {
          args[i] = arguments[i];
        }
        return fn.apply(thisArg, args);
      };
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/utils.js
var require_utils = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/utils.js"(exports, module) {
    "use strict";
    var bind = require_bind();
    var toString = Object.prototype.toString;
    function isArray(val) {
      return Array.isArray(val);
    }
    function isUndefined(val) {
      return typeof val === "undefined";
    }
    function isBuffer(val) {
      return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && typeof val.constructor.isBuffer === "function" && val.constructor.isBuffer(val);
    }
    function isArrayBuffer(val) {
      return toString.call(val) === "[object ArrayBuffer]";
    }
    function isFormData(val) {
      return toString.call(val) === "[object FormData]";
    }
    function isArrayBufferView(val) {
      var result;
      if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
        result = ArrayBuffer.isView(val);
      } else {
        result = val && val.buffer && isArrayBuffer(val.buffer);
      }
      return result;
    }
    function isString(val) {
      return typeof val === "string";
    }
    function isNumber(val) {
      return typeof val === "number";
    }
    function isObject(val) {
      return val !== null && typeof val === "object";
    }
    function isPlainObject(val) {
      if (toString.call(val) !== "[object Object]") {
        return false;
      }
      var prototype = Object.getPrototypeOf(val);
      return prototype === null || prototype === Object.prototype;
    }
    function isDate(val) {
      return toString.call(val) === "[object Date]";
    }
    function isFile(val) {
      return toString.call(val) === "[object File]";
    }
    function isBlob(val) {
      return toString.call(val) === "[object Blob]";
    }
    function isFunction(val) {
      return toString.call(val) === "[object Function]";
    }
    function isStream(val) {
      return isObject(val) && isFunction(val.pipe);
    }
    function isURLSearchParams(val) {
      return toString.call(val) === "[object URLSearchParams]";
    }
    function trim(str) {
      return str.trim ? str.trim() : str.replace(/^\s+|\s+$/g, "");
    }
    function isStandardBrowserEnv() {
      if (typeof navigator !== "undefined" && (navigator.product === "ReactNative" || navigator.product === "NativeScript" || navigator.product === "NS")) {
        return false;
      }
      return typeof window !== "undefined" && typeof document !== "undefined";
    }
    function forEach(obj, fn) {
      if (obj === null || typeof obj === "undefined") {
        return;
      }
      if (typeof obj !== "object") {
        obj = [obj];
      }
      if (isArray(obj)) {
        for (var i = 0, l = obj.length; i < l; i++) {
          fn.call(null, obj[i], i, obj);
        }
      } else {
        for (var key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            fn.call(null, obj[key], key, obj);
          }
        }
      }
    }
    function merge() {
      var result = {};
      function assignValue(val, key) {
        if (isPlainObject(result[key]) && isPlainObject(val)) {
          result[key] = merge(result[key], val);
        } else if (isPlainObject(val)) {
          result[key] = merge({}, val);
        } else if (isArray(val)) {
          result[key] = val.slice();
        } else {
          result[key] = val;
        }
      }
      for (var i = 0, l = arguments.length; i < l; i++) {
        forEach(arguments[i], assignValue);
      }
      return result;
    }
    function extend(a, b, thisArg) {
      forEach(b, function assignValue(val, key) {
        if (thisArg && typeof val === "function") {
          a[key] = bind(val, thisArg);
        } else {
          a[key] = val;
        }
      });
      return a;
    }
    function stripBOM(content) {
      if (content.charCodeAt(0) === 65279) {
        content = content.slice(1);
      }
      return content;
    }
    module.exports = {
      isArray,
      isArrayBuffer,
      isBuffer,
      isFormData,
      isArrayBufferView,
      isString,
      isNumber,
      isObject,
      isPlainObject,
      isUndefined,
      isDate,
      isFile,
      isBlob,
      isFunction,
      isStream,
      isURLSearchParams,
      isStandardBrowserEnv,
      forEach,
      merge,
      extend,
      trim,
      stripBOM
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/buildURL.js
var require_buildURL = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/buildURL.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    function encode(val) {
      return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
    }
    module.exports = function buildURL(url, params, paramsSerializer) {
      if (!params) {
        return url;
      }
      var serializedParams;
      if (paramsSerializer) {
        serializedParams = paramsSerializer(params);
      } else if (utils.isURLSearchParams(params)) {
        serializedParams = params.toString();
      } else {
        var parts = [];
        utils.forEach(params, function serialize(val, key) {
          if (val === null || typeof val === "undefined") {
            return;
          }
          if (utils.isArray(val)) {
            key = key + "[]";
          } else {
            val = [val];
          }
          utils.forEach(val, function parseValue(v) {
            if (utils.isDate(v)) {
              v = v.toISOString();
            } else if (utils.isObject(v)) {
              v = JSON.stringify(v);
            }
            parts.push(encode(key) + "=" + encode(v));
          });
        });
        serializedParams = parts.join("&");
      }
      if (serializedParams) {
        var hashmarkIndex = url.indexOf("#");
        if (hashmarkIndex !== -1) {
          url = url.slice(0, hashmarkIndex);
        }
        url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
      }
      return url;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/InterceptorManager.js
var require_InterceptorManager = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/InterceptorManager.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    function InterceptorManager() {
      this.handlers = [];
    }
    InterceptorManager.prototype.use = function use(fulfilled, rejected, options) {
      this.handlers.push({
        fulfilled,
        rejected,
        synchronous: options ? options.synchronous : false,
        runWhen: options ? options.runWhen : null
      });
      return this.handlers.length - 1;
    };
    InterceptorManager.prototype.eject = function eject(id) {
      if (this.handlers[id]) {
        this.handlers[id] = null;
      }
    };
    InterceptorManager.prototype.forEach = function forEach(fn) {
      utils.forEach(this.handlers, function forEachHandler(h) {
        if (h !== null) {
          fn(h);
        }
      });
    };
    module.exports = InterceptorManager;
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/normalizeHeaderName.js
var require_normalizeHeaderName = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/normalizeHeaderName.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    module.exports = function normalizeHeaderName(headers, normalizedName) {
      utils.forEach(headers, function processHeader(value, name) {
        if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {
          headers[normalizedName] = value;
          delete headers[name];
        }
      });
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/enhanceError.js
var require_enhanceError = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/enhanceError.js"(exports, module) {
    "use strict";
    module.exports = function enhanceError(error, config, code, request, response) {
      error.config = config;
      if (code) {
        error.code = code;
      }
      error.request = request;
      error.response = response;
      error.isAxiosError = true;
      error.toJSON = function toJSON() {
        return {
          // Standard
          message: this.message,
          name: this.name,
          // Microsoft
          description: this.description,
          number: this.number,
          // Mozilla
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          // Axios
          config: this.config,
          code: this.code,
          status: this.response && this.response.status ? this.response.status : null
        };
      };
      return error;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/createError.js
var require_createError = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/createError.js"(exports, module) {
    "use strict";
    var enhanceError = require_enhanceError();
    module.exports = function createError(message, config, code, request, response) {
      var error = new Error(message);
      return enhanceError(error, config, code, request, response);
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/settle.js
var require_settle = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/settle.js"(exports, module) {
    "use strict";
    var createError = require_createError();
    module.exports = function settle(resolve, reject, response) {
      var validateStatus = response.config.validateStatus;
      if (!response.status || !validateStatus || validateStatus(response.status)) {
        resolve(response);
      } else {
        reject(createError(
          "Request failed with status code " + response.status,
          response.config,
          null,
          response.request,
          response
        ));
      }
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/cookies.js
var require_cookies = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/cookies.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    module.exports = utils.isStandardBrowserEnv() ? (
      // Standard browser envs support document.cookie
      /* @__PURE__ */ function standardBrowserEnv() {
        return {
          write: function write(name, value, expires, path, domain, secure) {
            var cookie = [];
            cookie.push(name + "=" + encodeURIComponent(value));
            if (utils.isNumber(expires)) {
              cookie.push("expires=" + new Date(expires).toGMTString());
            }
            if (utils.isString(path)) {
              cookie.push("path=" + path);
            }
            if (utils.isString(domain)) {
              cookie.push("domain=" + domain);
            }
            if (secure === true) {
              cookie.push("secure");
            }
            document.cookie = cookie.join("; ");
          },
          read: function read(name) {
            var match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
            return match ? decodeURIComponent(match[3]) : null;
          },
          remove: function remove(name) {
            this.write(name, "", Date.now() - 864e5);
          }
        };
      }()
    ) : (
      // Non standard browser env (web workers, react-native) lack needed support.
      /* @__PURE__ */ function nonStandardBrowserEnv() {
        return {
          write: function write() {
          },
          read: function read() {
            return null;
          },
          remove: function remove() {
          }
        };
      }()
    );
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/isAbsoluteURL.js
var require_isAbsoluteURL = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/isAbsoluteURL.js"(exports, module) {
    "use strict";
    module.exports = function isAbsoluteURL(url) {
      return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/combineURLs.js
var require_combineURLs = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/combineURLs.js"(exports, module) {
    "use strict";
    module.exports = function combineURLs(baseURL, relativeURL) {
      return relativeURL ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/buildFullPath.js
var require_buildFullPath = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/buildFullPath.js"(exports, module) {
    "use strict";
    var isAbsoluteURL = require_isAbsoluteURL();
    var combineURLs = require_combineURLs();
    module.exports = function buildFullPath(baseURL, requestedURL) {
      if (baseURL && !isAbsoluteURL(requestedURL)) {
        return combineURLs(baseURL, requestedURL);
      }
      return requestedURL;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/parseHeaders.js
var require_parseHeaders = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/parseHeaders.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var ignoreDuplicateOf = [
      "age",
      "authorization",
      "content-length",
      "content-type",
      "etag",
      "expires",
      "from",
      "host",
      "if-modified-since",
      "if-unmodified-since",
      "last-modified",
      "location",
      "max-forwards",
      "proxy-authorization",
      "referer",
      "retry-after",
      "user-agent"
    ];
    module.exports = function parseHeaders(headers) {
      var parsed = {};
      var key;
      var val;
      var i;
      if (!headers) {
        return parsed;
      }
      utils.forEach(headers.split("\n"), function parser(line) {
        i = line.indexOf(":");
        key = utils.trim(line.substr(0, i)).toLowerCase();
        val = utils.trim(line.substr(i + 1));
        if (key) {
          if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {
            return;
          }
          if (key === "set-cookie") {
            parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);
          } else {
            parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
          }
        }
      });
      return parsed;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/isURLSameOrigin.js
var require_isURLSameOrigin = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/isURLSameOrigin.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    module.exports = utils.isStandardBrowserEnv() ? (
      // Standard browser envs have full support of the APIs needed to test
      // whether the request URL is of the same origin as current location.
      function standardBrowserEnv() {
        var msie = /(msie|trident)/i.test(navigator.userAgent);
        var urlParsingNode = document.createElement("a");
        var originURL;
        function resolveURL(url) {
          var href = url;
          if (msie) {
            urlParsingNode.setAttribute("href", href);
            href = urlParsingNode.href;
          }
          urlParsingNode.setAttribute("href", href);
          return {
            href: urlParsingNode.href,
            protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, "") : "",
            host: urlParsingNode.host,
            search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, "") : "",
            hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, "") : "",
            hostname: urlParsingNode.hostname,
            port: urlParsingNode.port,
            pathname: urlParsingNode.pathname.charAt(0) === "/" ? urlParsingNode.pathname : "/" + urlParsingNode.pathname
          };
        }
        originURL = resolveURL(window.location.href);
        return function isURLSameOrigin(requestURL) {
          var parsed = utils.isString(requestURL) ? resolveURL(requestURL) : requestURL;
          return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
        };
      }()
    ) : (
      // Non standard browser envs (web workers, react-native) lack needed support.
      /* @__PURE__ */ function nonStandardBrowserEnv() {
        return function isURLSameOrigin() {
          return true;
        };
      }()
    );
  }
});

// node_modules/postmark/node_modules/axios/lib/cancel/Cancel.js
var require_Cancel = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/cancel/Cancel.js"(exports, module) {
    "use strict";
    function Cancel(message) {
      this.message = message;
    }
    Cancel.prototype.toString = function toString() {
      return "Cancel" + (this.message ? ": " + this.message : "");
    };
    Cancel.prototype.__CANCEL__ = true;
    module.exports = Cancel;
  }
});

// node_modules/postmark/node_modules/axios/lib/adapters/xhr.js
var require_xhr = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/adapters/xhr.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var settle = require_settle();
    var cookies = require_cookies();
    var buildURL = require_buildURL();
    var buildFullPath = require_buildFullPath();
    var parseHeaders = require_parseHeaders();
    var isURLSameOrigin = require_isURLSameOrigin();
    var createError = require_createError();
    var defaults = require_defaults();
    var Cancel = require_Cancel();
    module.exports = function xhrAdapter(config) {
      return new Promise(function dispatchXhrRequest(resolve, reject) {
        var requestData = config.data;
        var requestHeaders = config.headers;
        var responseType = config.responseType;
        var onCanceled;
        function done() {
          if (config.cancelToken) {
            config.cancelToken.unsubscribe(onCanceled);
          }
          if (config.signal) {
            config.signal.removeEventListener("abort", onCanceled);
          }
        }
        if (utils.isFormData(requestData)) {
          delete requestHeaders["Content-Type"];
        }
        var request = new XMLHttpRequest();
        if (config.auth) {
          var username = config.auth.username || "";
          var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : "";
          requestHeaders.Authorization = "Basic " + btoa(username + ":" + password);
        }
        var fullPath = buildFullPath(config.baseURL, config.url);
        request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);
        request.timeout = config.timeout;
        function onloadend() {
          if (!request) {
            return;
          }
          var responseHeaders = "getAllResponseHeaders" in request ? parseHeaders(request.getAllResponseHeaders()) : null;
          var responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
          var response = {
            data: responseData,
            status: request.status,
            statusText: request.statusText,
            headers: responseHeaders,
            config,
            request
          };
          settle(function _resolve(value) {
            resolve(value);
            done();
          }, function _reject(err) {
            reject(err);
            done();
          }, response);
          request = null;
        }
        if ("onloadend" in request) {
          request.onloadend = onloadend;
        } else {
          request.onreadystatechange = function handleLoad() {
            if (!request || request.readyState !== 4) {
              return;
            }
            if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
              return;
            }
            setTimeout(onloadend);
          };
        }
        request.onabort = function handleAbort() {
          if (!request) {
            return;
          }
          reject(createError("Request aborted", config, "ECONNABORTED", request));
          request = null;
        };
        request.onerror = function handleError() {
          reject(createError("Network Error", config, null, request));
          request = null;
        };
        request.ontimeout = function handleTimeout() {
          var timeoutErrorMessage = config.timeout ? "timeout of " + config.timeout + "ms exceeded" : "timeout exceeded";
          var transitional = config.transitional || defaults.transitional;
          if (config.timeoutErrorMessage) {
            timeoutErrorMessage = config.timeoutErrorMessage;
          }
          reject(createError(
            timeoutErrorMessage,
            config,
            transitional.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED",
            request
          ));
          request = null;
        };
        if (utils.isStandardBrowserEnv()) {
          var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ? cookies.read(config.xsrfCookieName) : void 0;
          if (xsrfValue) {
            requestHeaders[config.xsrfHeaderName] = xsrfValue;
          }
        }
        if ("setRequestHeader" in request) {
          utils.forEach(requestHeaders, function setRequestHeader(val, key) {
            if (typeof requestData === "undefined" && key.toLowerCase() === "content-type") {
              delete requestHeaders[key];
            } else {
              request.setRequestHeader(key, val);
            }
          });
        }
        if (!utils.isUndefined(config.withCredentials)) {
          request.withCredentials = !!config.withCredentials;
        }
        if (responseType && responseType !== "json") {
          request.responseType = config.responseType;
        }
        if (typeof config.onDownloadProgress === "function") {
          request.addEventListener("progress", config.onDownloadProgress);
        }
        if (typeof config.onUploadProgress === "function" && request.upload) {
          request.upload.addEventListener("progress", config.onUploadProgress);
        }
        if (config.cancelToken || config.signal) {
          onCanceled = function(cancel) {
            if (!request) {
              return;
            }
            reject(!cancel || cancel && cancel.type ? new Cancel("canceled") : cancel);
            request.abort();
            request = null;
          };
          config.cancelToken && config.cancelToken.subscribe(onCanceled);
          if (config.signal) {
            config.signal.aborted ? onCanceled() : config.signal.addEventListener("abort", onCanceled);
          }
        }
        if (!requestData) {
          requestData = null;
        }
        request.send(requestData);
      });
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/defaults.js
var require_defaults = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/defaults.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var normalizeHeaderName = require_normalizeHeaderName();
    var enhanceError = require_enhanceError();
    var DEFAULT_CONTENT_TYPE = {
      "Content-Type": "application/x-www-form-urlencoded"
    };
    function setContentTypeIfUnset(headers, value) {
      if (!utils.isUndefined(headers) && utils.isUndefined(headers["Content-Type"])) {
        headers["Content-Type"] = value;
      }
    }
    function getDefaultAdapter() {
      var adapter;
      if (typeof XMLHttpRequest !== "undefined") {
        adapter = require_xhr();
      } else if (typeof process !== "undefined" && Object.prototype.toString.call(process) === "[object process]") {
        adapter = require_xhr();
      }
      return adapter;
    }
    function stringifySafely(rawValue, parser, encoder) {
      if (utils.isString(rawValue)) {
        try {
          (parser || JSON.parse)(rawValue);
          return utils.trim(rawValue);
        } catch (e) {
          if (e.name !== "SyntaxError") {
            throw e;
          }
        }
      }
      return (encoder || JSON.stringify)(rawValue);
    }
    var defaults = {
      transitional: {
        silentJSONParsing: true,
        forcedJSONParsing: true,
        clarifyTimeoutError: false
      },
      adapter: getDefaultAdapter(),
      transformRequest: [function transformRequest(data, headers) {
        normalizeHeaderName(headers, "Accept");
        normalizeHeaderName(headers, "Content-Type");
        if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {
          return data;
        }
        if (utils.isArrayBufferView(data)) {
          return data.buffer;
        }
        if (utils.isURLSearchParams(data)) {
          setContentTypeIfUnset(headers, "application/x-www-form-urlencoded;charset=utf-8");
          return data.toString();
        }
        if (utils.isObject(data) || headers && headers["Content-Type"] === "application/json") {
          setContentTypeIfUnset(headers, "application/json");
          return stringifySafely(data);
        }
        return data;
      }],
      transformResponse: [function transformResponse(data) {
        var transitional = this.transitional || defaults.transitional;
        var silentJSONParsing = transitional && transitional.silentJSONParsing;
        var forcedJSONParsing = transitional && transitional.forcedJSONParsing;
        var strictJSONParsing = !silentJSONParsing && this.responseType === "json";
        if (strictJSONParsing || forcedJSONParsing && utils.isString(data) && data.length) {
          try {
            return JSON.parse(data);
          } catch (e) {
            if (strictJSONParsing) {
              if (e.name === "SyntaxError") {
                throw enhanceError(e, this, "E_JSON_PARSE");
              }
              throw e;
            }
          }
        }
        return data;
      }],
      /**
       * A timeout in milliseconds to abort a request. If set to 0 (default) a
       * timeout is not created.
       */
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: function validateStatus(status) {
        return status >= 200 && status < 300;
      },
      headers: {
        common: {
          "Accept": "application/json, text/plain, */*"
        }
      }
    };
    utils.forEach(["delete", "get", "head"], function forEachMethodNoData(method) {
      defaults.headers[method] = {};
    });
    utils.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
      defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);
    });
    module.exports = defaults;
  }
});

// node_modules/postmark/node_modules/axios/lib/core/transformData.js
var require_transformData = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/transformData.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var defaults = require_defaults();
    module.exports = function transformData(data, headers, fns) {
      var context = this || defaults;
      utils.forEach(fns, function transform(fn) {
        data = fn.call(context, data, headers);
      });
      return data;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/cancel/isCancel.js
var require_isCancel = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/cancel/isCancel.js"(exports, module) {
    "use strict";
    module.exports = function isCancel(value) {
      return !!(value && value.__CANCEL__);
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/dispatchRequest.js
var require_dispatchRequest = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/dispatchRequest.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var transformData = require_transformData();
    var isCancel = require_isCancel();
    var defaults = require_defaults();
    var Cancel = require_Cancel();
    function throwIfCancellationRequested(config) {
      if (config.cancelToken) {
        config.cancelToken.throwIfRequested();
      }
      if (config.signal && config.signal.aborted) {
        throw new Cancel("canceled");
      }
    }
    module.exports = function dispatchRequest(config) {
      throwIfCancellationRequested(config);
      config.headers = config.headers || {};
      config.data = transformData.call(
        config,
        config.data,
        config.headers,
        config.transformRequest
      );
      config.headers = utils.merge(
        config.headers.common || {},
        config.headers[config.method] || {},
        config.headers
      );
      utils.forEach(
        ["delete", "get", "head", "post", "put", "patch", "common"],
        function cleanHeaderConfig(method) {
          delete config.headers[method];
        }
      );
      var adapter = config.adapter || defaults.adapter;
      return adapter(config).then(function onAdapterResolution(response) {
        throwIfCancellationRequested(config);
        response.data = transformData.call(
          config,
          response.data,
          response.headers,
          config.transformResponse
        );
        return response;
      }, function onAdapterRejection(reason) {
        if (!isCancel(reason)) {
          throwIfCancellationRequested(config);
          if (reason && reason.response) {
            reason.response.data = transformData.call(
              config,
              reason.response.data,
              reason.response.headers,
              config.transformResponse
            );
          }
        }
        return Promise.reject(reason);
      });
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/mergeConfig.js
var require_mergeConfig = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/mergeConfig.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    module.exports = function mergeConfig(config1, config2) {
      config2 = config2 || {};
      var config = {};
      function getMergedValue(target, source) {
        if (utils.isPlainObject(target) && utils.isPlainObject(source)) {
          return utils.merge(target, source);
        } else if (utils.isPlainObject(source)) {
          return utils.merge({}, source);
        } else if (utils.isArray(source)) {
          return source.slice();
        }
        return source;
      }
      function mergeDeepProperties(prop) {
        if (!utils.isUndefined(config2[prop])) {
          return getMergedValue(config1[prop], config2[prop]);
        } else if (!utils.isUndefined(config1[prop])) {
          return getMergedValue(void 0, config1[prop]);
        }
      }
      function valueFromConfig2(prop) {
        if (!utils.isUndefined(config2[prop])) {
          return getMergedValue(void 0, config2[prop]);
        }
      }
      function defaultToConfig2(prop) {
        if (!utils.isUndefined(config2[prop])) {
          return getMergedValue(void 0, config2[prop]);
        } else if (!utils.isUndefined(config1[prop])) {
          return getMergedValue(void 0, config1[prop]);
        }
      }
      function mergeDirectKeys(prop) {
        if (prop in config2) {
          return getMergedValue(config1[prop], config2[prop]);
        } else if (prop in config1) {
          return getMergedValue(void 0, config1[prop]);
        }
      }
      var mergeMap = {
        "url": valueFromConfig2,
        "method": valueFromConfig2,
        "data": valueFromConfig2,
        "baseURL": defaultToConfig2,
        "transformRequest": defaultToConfig2,
        "transformResponse": defaultToConfig2,
        "paramsSerializer": defaultToConfig2,
        "timeout": defaultToConfig2,
        "timeoutMessage": defaultToConfig2,
        "withCredentials": defaultToConfig2,
        "adapter": defaultToConfig2,
        "responseType": defaultToConfig2,
        "xsrfCookieName": defaultToConfig2,
        "xsrfHeaderName": defaultToConfig2,
        "onUploadProgress": defaultToConfig2,
        "onDownloadProgress": defaultToConfig2,
        "decompress": defaultToConfig2,
        "maxContentLength": defaultToConfig2,
        "maxBodyLength": defaultToConfig2,
        "transport": defaultToConfig2,
        "httpAgent": defaultToConfig2,
        "httpsAgent": defaultToConfig2,
        "cancelToken": defaultToConfig2,
        "socketPath": defaultToConfig2,
        "responseEncoding": defaultToConfig2,
        "validateStatus": mergeDirectKeys
      };
      utils.forEach(Object.keys(config1).concat(Object.keys(config2)), function computeConfigValue(prop) {
        var merge = mergeMap[prop] || mergeDeepProperties;
        var configValue = merge(prop);
        utils.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);
      });
      return config;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/env/data.js
var require_data = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/env/data.js"(exports, module) {
    module.exports = {
      "version": "0.25.0"
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/validator.js
var require_validator = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/validator.js"(exports, module) {
    "use strict";
    var VERSION = require_data().version;
    var validators = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach(function(type, i) {
      validators[type] = function validator(thing) {
        return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
      };
    });
    var deprecatedWarnings = {};
    validators.transitional = function transitional(validator, version, message) {
      function formatMessage(opt, desc) {
        return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
      }
      return function(value, opt, opts) {
        if (validator === false) {
          throw new Error(formatMessage(opt, " has been removed" + (version ? " in " + version : "")));
        }
        if (version && !deprecatedWarnings[opt]) {
          deprecatedWarnings[opt] = true;
          console.warn(
            formatMessage(
              opt,
              " has been deprecated since v" + version + " and will be removed in the near future"
            )
          );
        }
        return validator ? validator(value, opt, opts) : true;
      };
    };
    function assertOptions(options, schema, allowUnknown) {
      if (typeof options !== "object") {
        throw new TypeError("options must be an object");
      }
      var keys = Object.keys(options);
      var i = keys.length;
      while (i-- > 0) {
        var opt = keys[i];
        var validator = schema[opt];
        if (validator) {
          var value = options[opt];
          var result = value === void 0 || validator(value, opt, options);
          if (result !== true) {
            throw new TypeError("option " + opt + " must be " + result);
          }
          continue;
        }
        if (allowUnknown !== true) {
          throw Error("Unknown option " + opt);
        }
      }
    }
    module.exports = {
      assertOptions,
      validators
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/core/Axios.js
var require_Axios = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/core/Axios.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var buildURL = require_buildURL();
    var InterceptorManager = require_InterceptorManager();
    var dispatchRequest = require_dispatchRequest();
    var mergeConfig = require_mergeConfig();
    var validator = require_validator();
    var validators = validator.validators;
    function Axios(instanceConfig) {
      this.defaults = instanceConfig;
      this.interceptors = {
        request: new InterceptorManager(),
        response: new InterceptorManager()
      };
    }
    Axios.prototype.request = function request(configOrUrl, config) {
      if (typeof configOrUrl === "string") {
        config = config || {};
        config.url = configOrUrl;
      } else {
        config = configOrUrl || {};
      }
      if (!config.url) {
        throw new Error("Provided config url is not valid");
      }
      config = mergeConfig(this.defaults, config);
      if (config.method) {
        config.method = config.method.toLowerCase();
      } else if (this.defaults.method) {
        config.method = this.defaults.method.toLowerCase();
      } else {
        config.method = "get";
      }
      var transitional = config.transitional;
      if (transitional !== void 0) {
        validator.assertOptions(transitional, {
          silentJSONParsing: validators.transitional(validators.boolean),
          forcedJSONParsing: validators.transitional(validators.boolean),
          clarifyTimeoutError: validators.transitional(validators.boolean)
        }, false);
      }
      var requestInterceptorChain = [];
      var synchronousRequestInterceptors = true;
      this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
        if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
          return;
        }
        synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
        requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
      });
      var responseInterceptorChain = [];
      this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
        responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
      });
      var promise;
      if (!synchronousRequestInterceptors) {
        var chain = [dispatchRequest, void 0];
        Array.prototype.unshift.apply(chain, requestInterceptorChain);
        chain = chain.concat(responseInterceptorChain);
        promise = Promise.resolve(config);
        while (chain.length) {
          promise = promise.then(chain.shift(), chain.shift());
        }
        return promise;
      }
      var newConfig = config;
      while (requestInterceptorChain.length) {
        var onFulfilled = requestInterceptorChain.shift();
        var onRejected = requestInterceptorChain.shift();
        try {
          newConfig = onFulfilled(newConfig);
        } catch (error) {
          onRejected(error);
          break;
        }
      }
      try {
        promise = dispatchRequest(newConfig);
      } catch (error) {
        return Promise.reject(error);
      }
      while (responseInterceptorChain.length) {
        promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());
      }
      return promise;
    };
    Axios.prototype.getUri = function getUri(config) {
      if (!config.url) {
        throw new Error("Provided config url is not valid");
      }
      config = mergeConfig(this.defaults, config);
      return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\?/, "");
    };
    utils.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
      Axios.prototype[method] = function(url, config) {
        return this.request(mergeConfig(config || {}, {
          method,
          url,
          data: (config || {}).data
        }));
      };
    });
    utils.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
      Axios.prototype[method] = function(url, data, config) {
        return this.request(mergeConfig(config || {}, {
          method,
          url,
          data
        }));
      };
    });
    module.exports = Axios;
  }
});

// node_modules/postmark/node_modules/axios/lib/cancel/CancelToken.js
var require_CancelToken = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/cancel/CancelToken.js"(exports, module) {
    "use strict";
    var Cancel = require_Cancel();
    function CancelToken(executor) {
      if (typeof executor !== "function") {
        throw new TypeError("executor must be a function.");
      }
      var resolvePromise;
      this.promise = new Promise(function promiseExecutor(resolve) {
        resolvePromise = resolve;
      });
      var token = this;
      this.promise.then(function(cancel) {
        if (!token._listeners) return;
        var i;
        var l = token._listeners.length;
        for (i = 0; i < l; i++) {
          token._listeners[i](cancel);
        }
        token._listeners = null;
      });
      this.promise.then = function(onfulfilled) {
        var _resolve;
        var promise = new Promise(function(resolve) {
          token.subscribe(resolve);
          _resolve = resolve;
        }).then(onfulfilled);
        promise.cancel = function reject() {
          token.unsubscribe(_resolve);
        };
        return promise;
      };
      executor(function cancel(message) {
        if (token.reason) {
          return;
        }
        token.reason = new Cancel(message);
        resolvePromise(token.reason);
      });
    }
    CancelToken.prototype.throwIfRequested = function throwIfRequested() {
      if (this.reason) {
        throw this.reason;
      }
    };
    CancelToken.prototype.subscribe = function subscribe(listener) {
      if (this.reason) {
        listener(this.reason);
        return;
      }
      if (this._listeners) {
        this._listeners.push(listener);
      } else {
        this._listeners = [listener];
      }
    };
    CancelToken.prototype.unsubscribe = function unsubscribe(listener) {
      if (!this._listeners) {
        return;
      }
      var index = this._listeners.indexOf(listener);
      if (index !== -1) {
        this._listeners.splice(index, 1);
      }
    };
    CancelToken.source = function source() {
      var cancel;
      var token = new CancelToken(function executor(c) {
        cancel = c;
      });
      return {
        token,
        cancel
      };
    };
    module.exports = CancelToken;
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/spread.js
var require_spread = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/spread.js"(exports, module) {
    "use strict";
    module.exports = function spread(callback) {
      return function wrap(arr) {
        return callback.apply(null, arr);
      };
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/helpers/isAxiosError.js
var require_isAxiosError = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/helpers/isAxiosError.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    module.exports = function isAxiosError(payload) {
      return utils.isObject(payload) && payload.isAxiosError === true;
    };
  }
});

// node_modules/postmark/node_modules/axios/lib/axios.js
var require_axios = __commonJS({
  "node_modules/postmark/node_modules/axios/lib/axios.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var bind = require_bind();
    var Axios = require_Axios();
    var mergeConfig = require_mergeConfig();
    var defaults = require_defaults();
    function createInstance(defaultConfig) {
      var context = new Axios(defaultConfig);
      var instance = bind(Axios.prototype.request, context);
      utils.extend(instance, Axios.prototype, context);
      utils.extend(instance, context);
      instance.create = function create(instanceConfig) {
        return createInstance(mergeConfig(defaultConfig, instanceConfig));
      };
      return instance;
    }
    var axios = createInstance(defaults);
    axios.Axios = Axios;
    axios.Cancel = require_Cancel();
    axios.CancelToken = require_CancelToken();
    axios.isCancel = require_isCancel();
    axios.VERSION = require_data().version;
    axios.all = function all(promises) {
      return Promise.all(promises);
    };
    axios.spread = require_spread();
    axios.isAxiosError = require_isAxiosError();
    module.exports = axios;
    module.exports.default = axios;
  }
});

// node_modules/postmark/node_modules/axios/index.js
var require_axios2 = __commonJS({
  "node_modules/postmark/node_modules/axios/index.js"(exports, module) {
    module.exports = require_axios();
  }
});

// node_modules/postmark/dist/client/models/client/ClientOptions.js
var require_ClientOptions = __commonJS({
  "node_modules/postmark/dist/client/models/client/ClientOptions.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ClientOptions = void 0;
    var ClientOptions;
    (function(ClientOptions2) {
      var Configuration = (
        /** @class */
        /* @__PURE__ */ function() {
          function Configuration2(useHttps, requestHost, timeout) {
            this.useHttps = useHttps;
            this.requestHost = requestHost;
            this.timeout = timeout;
          }
          return Configuration2;
        }()
      );
      ClientOptions2.Configuration = Configuration;
      var HttpMethod;
      (function(HttpMethod2) {
        HttpMethod2["GET"] = "GET";
        HttpMethod2["POST"] = "POST";
        HttpMethod2["DELETE"] = "DELETE";
        HttpMethod2["PUT"] = "PUT";
        HttpMethod2["OPTIONS"] = "OPTIONS";
        HttpMethod2["HEAD"] = "HEAD";
        HttpMethod2["PATCH"] = "PATCH";
      })(HttpMethod = ClientOptions2.HttpMethod || (ClientOptions2.HttpMethod = {}));
      var AuthHeaderNames;
      (function(AuthHeaderNames2) {
        AuthHeaderNames2["SERVER_TOKEN"] = "X-Postmark-Server-Token";
        AuthHeaderNames2["ACCOUNT_TOKEN"] = "X-Postmark-Account-Token";
      })(AuthHeaderNames = ClientOptions2.AuthHeaderNames || (ClientOptions2.AuthHeaderNames = {}));
    })(ClientOptions = exports.ClientOptions || (exports.ClientOptions = {}));
  }
});

// node_modules/postmark/dist/client/models/client/SupportingTypes.js
var require_SupportingTypes = __commonJS({
  "node_modules/postmark/dist/client/models/client/SupportingTypes.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/client/HttpClient.js
var require_HttpClient = __commonJS({
  "node_modules/postmark/dist/client/models/client/HttpClient.js"(exports) {
    "use strict";
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.HttpClient = void 0;
    var HttpClient = (
      /** @class */
      function() {
        function HttpClient2(configOptions) {
          this.clientOptions = __assign(__assign({}, HttpClient2.DefaultOptions), configOptions);
          this.initHttpClient(this.clientOptions);
        }
        HttpClient2.prototype.getBaseHttpRequestURL = function() {
          var scheme = this.clientOptions.useHttps ? "https" : "http";
          return "".concat(scheme, "://").concat(this.clientOptions.requestHost);
        };
        HttpClient2.DefaultOptions = {
          useHttps: true,
          requestHost: "api.postmarkapp.com",
          timeout: 180
        };
        return HttpClient2;
      }()
    );
    exports.HttpClient = HttpClient;
  }
});

// node_modules/postmark/dist/client/models/client/Callback.js
var require_Callback = __commonJS({
  "node_modules/postmark/dist/client/models/client/Callback.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/client/DefaultResponse.js
var require_DefaultResponse = __commonJS({
  "node_modules/postmark/dist/client/models/client/DefaultResponse.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/client/FilteringParameters.js
var require_FilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/client/FilteringParameters.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.FilteringParameters = void 0;
    var FilteringParameters = (
      /** @class */
      /* @__PURE__ */ function() {
        function FilteringParameters2(count, offset) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          this.count = count;
          this.offset = offset;
        }
        return FilteringParameters2;
      }()
    );
    exports.FilteringParameters = FilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/bounces/Bounce.js
var require_Bounce = __commonJS({
  "node_modules/postmark/dist/client/models/bounces/Bounce.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/bounces/BounceFilteringParameters.js
var require_BounceFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/bounces/BounceFilteringParameters.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BounceFilteringParameters = exports.BounceType = void 0;
    var FilteringParameters_1 = require_FilteringParameters();
    var BounceType;
    (function(BounceType2) {
      BounceType2["HardBounce"] = "HardBounce";
      BounceType2["Transient"] = "Transient";
      BounceType2["Unsubscribe"] = "Unsubscribe";
      BounceType2["Subscribe"] = "Subscribe";
      BounceType2["AutoResponder"] = "AutoResponder";
      BounceType2["AddressChange"] = "AddressChange";
      BounceType2["DnsError"] = "DnsError";
      BounceType2["SpamNotification"] = "SpamNotification";
      BounceType2["OpenRelayTest"] = "OpenRelayTest";
      BounceType2["Unknown"] = "Unknown";
      BounceType2["SoftBounce"] = "SoftBounce";
      BounceType2["VirusNotification"] = "VirusNotification";
      BounceType2["ChallengeVerification"] = "ChallengeVerification";
      BounceType2["BadEmailAddress"] = "BadEmailAddress";
      BounceType2["SpamComplaint"] = "SpamComplaint";
      BounceType2["ManuallyDeactivated"] = "ManuallyDeactivated";
      BounceType2["Unconfirmed"] = "Unconfirmed";
      BounceType2["Blocked"] = "Blocked";
      BounceType2["SMTPApiError"] = "SMTPApiError";
      BounceType2["InboundError"] = "InboundError";
      BounceType2["DMARCPolicy"] = "DMARCPolicy";
      BounceType2["TemplateRenderingFailed"] = "TemplateRenderingFailed";
    })(BounceType = exports.BounceType || (exports.BounceType = {}));
    var BounceFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(BounceFilteringParameters2, _super);
        function BounceFilteringParameters2(count, offset, type, inactive, emailFilter, tag, messageID, fromDate, toDate, messageStream) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.type = type;
          _this.inactive = inactive;
          _this.emailFilter = emailFilter;
          _this.tag = tag;
          _this.messageID = messageID;
          _this.fromDate = fromDate;
          _this.toDate = toDate;
          _this.messageStream = messageStream;
          return _this;
        }
        return BounceFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.BounceFilteringParameters = BounceFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/message/Message.js
var require_Message = __commonJS({
  "node_modules/postmark/dist/client/models/message/Message.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Message = void 0;
    var Message = (
      /** @class */
      /* @__PURE__ */ function() {
        function Message2(From, Subject, HtmlBody, TextBody, To, Cc, Bcc, ReplyTo, Tag, TrackOpens, TrackLinks, Headers, Attachments, Metadata) {
          this.From = From;
          this.To = To;
          this.Cc = Cc;
          this.Bcc = Bcc;
          this.Subject = Subject;
          this.ReplyTo = ReplyTo;
          this.HtmlBody = HtmlBody;
          this.TextBody = TextBody;
          this.Tag = Tag;
          this.TrackOpens = TrackOpens;
          this.TrackLinks = TrackLinks;
          this.Headers = Headers;
          this.Attachments = Attachments;
          this.Metadata = Metadata;
        }
        return Message2;
      }()
    );
    exports.Message = Message;
  }
});

// node_modules/postmark/dist/client/models/message/SupportingTypes.js
var require_SupportingTypes2 = __commonJS({
  "node_modules/postmark/dist/client/models/message/SupportingTypes.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Attachment = exports.Header = exports.ServerDeliveryTypes = exports.LinkClickLocation = exports.LinkTrackingOptions = void 0;
    var LinkTrackingOptions;
    (function(LinkTrackingOptions2) {
      LinkTrackingOptions2["TextOnly"] = "TextOnly";
      LinkTrackingOptions2["HtmlOnly"] = "HtmlOnly";
      LinkTrackingOptions2["HtmlAndText"] = "HtmlAndText";
      LinkTrackingOptions2["None"] = "None";
    })(LinkTrackingOptions = exports.LinkTrackingOptions || (exports.LinkTrackingOptions = {}));
    var LinkClickLocation;
    (function(LinkClickLocation2) {
      LinkClickLocation2["HTML"] = "HTML";
      LinkClickLocation2["Text"] = "Text";
    })(LinkClickLocation = exports.LinkClickLocation || (exports.LinkClickLocation = {}));
    var ServerDeliveryTypes;
    (function(ServerDeliveryTypes2) {
      ServerDeliveryTypes2["Live"] = "Live";
      ServerDeliveryTypes2["Sandbox"] = "Sandbox";
    })(ServerDeliveryTypes = exports.ServerDeliveryTypes || (exports.ServerDeliveryTypes = {}));
    var Header = (
      /** @class */
      /* @__PURE__ */ function() {
        function Header2(Name, Value) {
          this.Name = Name;
          this.Value = Value;
        }
        return Header2;
      }()
    );
    exports.Header = Header;
    var Attachment = (
      /** @class */
      /* @__PURE__ */ function() {
        function Attachment2(Name, Content, ContentType, ContentID, ContentLength) {
          if (ContentID === void 0) {
            ContentID = null;
          }
          this.Name = Name;
          this.Content = Content;
          this.ContentType = ContentType;
          this.ContentID = ContentID;
          this.ContentLength = ContentLength;
        }
        return Attachment2;
      }()
    );
    exports.Attachment = Attachment;
  }
});

// node_modules/postmark/dist/client/models/messages/OutboundMessage.js
var require_OutboundMessage = __commonJS({
  "node_modules/postmark/dist/client/models/messages/OutboundMessage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/messages/OutboundMessageOpen.js
var require_OutboundMessageOpen = __commonJS({
  "node_modules/postmark/dist/client/models/messages/OutboundMessageOpen.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/messages/OutboundMessageClick.js
var require_OutboundMessageClick = __commonJS({
  "node_modules/postmark/dist/client/models/messages/OutboundMessageClick.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/messages/InboundMessage.js
var require_InboundMessage = __commonJS({
  "node_modules/postmark/dist/client/models/messages/InboundMessage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/messages/MessageFilteringParameters.js
var require_MessageFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/messages/MessageFilteringParameters.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.OutboundMessageClicksFilteringParameters = exports.OutboundMessageOpensFilteringParameters = exports.OutboundMessageTrackingFilteringParameters = exports.InboundMessagesFilteringParameters = exports.OutboundMessagesFilteringParameters = exports.InboundMessageStatus = exports.OutboundMessageStatus = void 0;
    var FilteringParameters_1 = require_FilteringParameters();
    var OutboundMessageStatus;
    (function(OutboundMessageStatus2) {
      OutboundMessageStatus2["Queued"] = "queued";
      OutboundMessageStatus2["Sent"] = "sent";
      OutboundMessageStatus2["Processed"] = "processed";
    })(OutboundMessageStatus = exports.OutboundMessageStatus || (exports.OutboundMessageStatus = {}));
    var InboundMessageStatus;
    (function(InboundMessageStatus2) {
      InboundMessageStatus2["Queued"] = "queued";
      InboundMessageStatus2["Sent"] = "sent";
      InboundMessageStatus2["Processed"] = "processed";
      InboundMessageStatus2["Blocked"] = "blocked";
      InboundMessageStatus2["Failed"] = "failed";
      InboundMessageStatus2["Scheduled"] = "scheduled";
    })(InboundMessageStatus = exports.InboundMessageStatus || (exports.InboundMessageStatus = {}));
    var OutboundMessagesFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(OutboundMessagesFilteringParameters2, _super);
        function OutboundMessagesFilteringParameters2(count, offset, recipient, fromEmail, tag, status, fromDate, toDate, subject, messageStream) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.recipient = recipient;
          _this.fromEmail = fromEmail;
          _this.tag = tag;
          _this.status = status;
          _this.fromDate = fromDate;
          _this.toDate = toDate;
          _this.subject = subject;
          _this.messageStream = messageStream;
          return _this;
        }
        return OutboundMessagesFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.OutboundMessagesFilteringParameters = OutboundMessagesFilteringParameters;
    var InboundMessagesFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(InboundMessagesFilteringParameters2, _super);
        function InboundMessagesFilteringParameters2(count, offset, mailboxHash, recipient, fromEmail, tag, status, fromDate, toDate, subject) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.status = status;
          _this.mailboxHash = mailboxHash;
          _this.recipient = recipient;
          _this.fromEmail = fromEmail;
          _this.tag = tag;
          _this.fromDate = fromDate;
          _this.toDate = toDate;
          _this.subject = subject;
          return _this;
        }
        return InboundMessagesFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.InboundMessagesFilteringParameters = InboundMessagesFilteringParameters;
    var OutboundMessageTrackingFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(OutboundMessageTrackingFilteringParameters2, _super);
        function OutboundMessageTrackingFilteringParameters2(count, offset, recipient, tag, client_name, client_company, client_family, os_name, os_family, os_company, platform, country, region, city, messageStream) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.recipient = recipient;
          _this.tag = tag;
          _this.client_name = client_name;
          _this.client_company = client_company;
          _this.client_family = client_family;
          _this.os_name = os_name;
          _this.os_family = os_family;
          _this.os_company = os_company;
          _this.platform = platform;
          _this.country = country;
          _this.region = region;
          _this.city = city;
          _this.messageStream = messageStream;
          return _this;
        }
        return OutboundMessageTrackingFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.OutboundMessageTrackingFilteringParameters = OutboundMessageTrackingFilteringParameters;
    var OutboundMessageOpensFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(OutboundMessageOpensFilteringParameters2, _super);
        function OutboundMessageOpensFilteringParameters2() {
          return _super !== null && _super.apply(this, arguments) || this;
        }
        return OutboundMessageOpensFilteringParameters2;
      }(OutboundMessageTrackingFilteringParameters)
    );
    exports.OutboundMessageOpensFilteringParameters = OutboundMessageOpensFilteringParameters;
    var OutboundMessageClicksFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(OutboundMessageClicksFilteringParameters2, _super);
        function OutboundMessageClicksFilteringParameters2() {
          return _super !== null && _super.apply(this, arguments) || this;
        }
        return OutboundMessageClicksFilteringParameters2;
      }(OutboundMessageTrackingFilteringParameters)
    );
    exports.OutboundMessageClicksFilteringParameters = OutboundMessageClicksFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/templates/Template.js
var require_Template = __commonJS({
  "node_modules/postmark/dist/client/models/templates/Template.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.TemplateFilteringParameters = exports.TemplatedMessage = exports.TemplatesPushRequest = exports.TemplateTypes = exports.TemplateValidationOptions = exports.CreateTemplateRequest = exports.UpdateTemplateRequest = void 0;
    var FilteringParameters_1 = require_FilteringParameters();
    var UpdateTemplateRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateTemplateRequest2(Name, Subject, HtmlBody, TextBody, Alias, TemplateType, LayoutTemplate) {
          this.Name = Name;
          this.Subject = Subject;
          this.HtmlBody = HtmlBody;
          this.TextBody = TextBody;
          this.Alias = Alias;
          this.LayoutTemplate = LayoutTemplate;
          this.TemplateType = TemplateType;
        }
        return UpdateTemplateRequest2;
      }()
    );
    exports.UpdateTemplateRequest = UpdateTemplateRequest;
    var CreateTemplateRequest = (
      /** @class */
      function(_super) {
        __extends(CreateTemplateRequest2, _super);
        function CreateTemplateRequest2(Name, Subject, HtmlBody, TextBody, Alias, TemplateType, LayoutTemplate) {
          return _super.call(this, Name, Subject, HtmlBody, TextBody, Alias, TemplateType, LayoutTemplate) || this;
        }
        return CreateTemplateRequest2;
      }(UpdateTemplateRequest)
    );
    exports.CreateTemplateRequest = CreateTemplateRequest;
    var TemplateValidationOptions = (
      /** @class */
      /* @__PURE__ */ function() {
        function TemplateValidationOptions2(Subject, HtmlBody, TextBody, TestRenderModel, TemplateType, LayoutTemplate, InlineCssForHtmlTestRender) {
          this.Subject = Subject;
          this.HtmlBody = HtmlBody;
          this.TextBody = TextBody;
          this.TestRenderModel = TestRenderModel;
          this.TemplateType = TemplateType;
          this.LayoutTemplate = LayoutTemplate;
          this.InlineCssForHtmlTestRender = InlineCssForHtmlTestRender;
        }
        return TemplateValidationOptions2;
      }()
    );
    exports.TemplateValidationOptions = TemplateValidationOptions;
    var TemplateTypes;
    (function(TemplateTypes2) {
      TemplateTypes2["Standard"] = "Standard";
      TemplateTypes2["Layout"] = "Layout";
    })(TemplateTypes = exports.TemplateTypes || (exports.TemplateTypes = {}));
    var TemplatesPushRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function TemplatesPushRequest2(SourceServerID, DestinationServerID, PerformChanges) {
          this.SourceServerID = SourceServerID;
          this.DestinationServerID = DestinationServerID;
          this.PerformChanges = PerformChanges;
        }
        return TemplatesPushRequest2;
      }()
    );
    exports.TemplatesPushRequest = TemplatesPushRequest;
    var TemplatedMessage = (
      /** @class */
      /* @__PURE__ */ function() {
        function TemplatedMessage2(from, templateIdOrAlias, templateModel, to, cc, bcc, replyTo, tag, trackOpens, trackLinks, headers, attachments) {
          this.From = from;
          this.TemplateModel = templateModel;
          if (typeof templateIdOrAlias === "number") {
            this.TemplateId = templateIdOrAlias;
          } else {
            this.TemplateAlias = templateIdOrAlias;
          }
          this.To = to;
          this.Cc = cc;
          this.Bcc = bcc;
          this.ReplyTo = replyTo;
          this.Tag = tag;
          this.TrackOpens = trackOpens;
          this.TrackLinks = trackLinks;
          this.Headers = headers;
          this.Attachments = attachments;
        }
        return TemplatedMessage2;
      }()
    );
    exports.TemplatedMessage = TemplatedMessage;
    var TemplateFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(TemplateFilteringParameters2, _super);
        function TemplateFilteringParameters2(count, offset, templateType, layoutTemplate) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.templateType = templateType;
          _this.layoutTemplate = layoutTemplate;
          return _this;
        }
        return TemplateFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.TemplateFilteringParameters = TemplateFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/server/Server.js
var require_Server = __commonJS({
  "node_modules/postmark/dist/client/models/server/Server.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CreateServerRequest = exports.UpdateServerRequest = void 0;
    var UpdateServerRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateServerRequest2(Name, Color, SmtpApiActivated, RawEmailEnabled, InboundHookUrl, BounceHookUrl, OpenHookUrl, DeliveryHookUrl, ClickHookUrl, PostFirstOpenOnly, InboundSpamThreshold, TrackOpens, TrackLinks, IncludeBounceContentInHook, EnableSmtpApiErrorHooks, InboundDomain) {
          this.Name = Name;
          this.Color = Color;
          this.SmtpApiActivated = SmtpApiActivated;
          this.RawEmailEnabled = RawEmailEnabled;
          this.InboundHookUrl = InboundHookUrl;
          this.BounceHookUrl = BounceHookUrl;
          this.OpenHookUrl = OpenHookUrl;
          this.DeliveryHookUrl = DeliveryHookUrl;
          this.ClickHookUrl = ClickHookUrl;
          this.PostFirstOpenOnly = PostFirstOpenOnly;
          this.InboundSpamThreshold = InboundSpamThreshold;
          this.InboundDomain = InboundDomain;
          this.TrackOpens = TrackOpens;
          this.TrackLinks = TrackLinks;
          this.IncludeBounceContentInHook = IncludeBounceContentInHook;
          this.EnableSmtpApiErrorHooks = EnableSmtpApiErrorHooks;
        }
        return UpdateServerRequest2;
      }()
    );
    exports.UpdateServerRequest = UpdateServerRequest;
    var CreateServerRequest = (
      /** @class */
      function(_super) {
        __extends(CreateServerRequest2, _super);
        function CreateServerRequest2(Name, Color, SmtpApiActivated, RawEmailEnabled, InboundHookUrl, BounceHookUrl, OpenHookUrl, DeliveryHookUrl, ClickHookUrl, PostFirstOpenOnly, InboundSpamThreshold, TrackOpens, TrackLinks, IncludeBounceContentInHook, EnableSmtpApiErrorHooks, InboundDomain, DeliveryType) {
          var _this = _super.call(this, Name, Color, SmtpApiActivated, RawEmailEnabled, InboundHookUrl, BounceHookUrl, OpenHookUrl, DeliveryHookUrl, ClickHookUrl, PostFirstOpenOnly, InboundSpamThreshold, TrackOpens, TrackLinks, IncludeBounceContentInHook, EnableSmtpApiErrorHooks, InboundDomain) || this;
          _this.DeliveryType = DeliveryType;
          return _this;
        }
        return CreateServerRequest2;
      }(UpdateServerRequest)
    );
    exports.CreateServerRequest = CreateServerRequest;
  }
});

// node_modules/postmark/dist/client/models/server/Servers.js
var require_Servers = __commonJS({
  "node_modules/postmark/dist/client/models/server/Servers.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/server/ServerFilteringParameters.js
var require_ServerFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/server/ServerFilteringParameters.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ServerFilteringParameters = void 0;
    var FilteringParameters_1 = require_FilteringParameters();
    var ServerFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(ServerFilteringParameters2, _super);
        function ServerFilteringParameters2(count, offset, name) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.name = name;
          return _this;
        }
        return ServerFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.ServerFilteringParameters = ServerFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/domains/Domain.js
var require_Domain = __commonJS({
  "node_modules/postmark/dist/client/models/domains/Domain.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.UpdateDomainRequest = exports.CreateDomainRequest = void 0;
    var CreateDomainRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function CreateDomainRequest2(Name, ReturnPathDomain) {
          this.Name = Name;
          this.ReturnPathDomain = ReturnPathDomain;
        }
        return CreateDomainRequest2;
      }()
    );
    exports.CreateDomainRequest = CreateDomainRequest;
    var UpdateDomainRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateDomainRequest2(ReturnPathDomain) {
          this.ReturnPathDomain = ReturnPathDomain;
        }
        return UpdateDomainRequest2;
      }()
    );
    exports.UpdateDomainRequest = UpdateDomainRequest;
  }
});

// node_modules/postmark/dist/client/models/senders/Signature.js
var require_Signature = __commonJS({
  "node_modules/postmark/dist/client/models/senders/Signature.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CreateSignatureRequest = exports.UpdateSignatureRequest = void 0;
    var UpdateSignatureRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateSignatureRequest2(Name, ReplyToEmail, ReturnPathDomain, ConfirmationPersonalNote) {
          this.Name = Name;
          this.ReplyToEmail = ReplyToEmail;
          this.ReturnPathDomain = ReturnPathDomain;
          this.ConfirmationPersonalNote = ConfirmationPersonalNote;
        }
        return UpdateSignatureRequest2;
      }()
    );
    exports.UpdateSignatureRequest = UpdateSignatureRequest;
    var CreateSignatureRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function CreateSignatureRequest2(Name, FromEmail, ReplyToEmail, ReturnPathDomain, ConfirmationPersonalNote) {
          this.Name = Name;
          this.ReplyToEmail = ReplyToEmail;
          this.ReturnPathDomain = ReturnPathDomain;
          this.FromEmail = FromEmail;
          this.ConfirmationPersonalNote = ConfirmationPersonalNote;
        }
        return CreateSignatureRequest2;
      }()
    );
    exports.CreateSignatureRequest = CreateSignatureRequest;
  }
});

// node_modules/postmark/dist/client/models/suppressions/Suppression.js
var require_Suppression = __commonJS({
  "node_modules/postmark/dist/client/models/suppressions/Suppression.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/stats/Stats.js
var require_Stats = __commonJS({
  "node_modules/postmark/dist/client/models/stats/Stats.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/stats/StatsFilteringParameters.js
var require_StatsFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/stats/StatsFilteringParameters.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.StatisticsFilteringParameters = void 0;
    var StatisticsFilteringParameters = (
      /** @class */
      /* @__PURE__ */ function() {
        function StatisticsFilteringParameters2(tag, fromDate, toDate, messageStream) {
          this.tag = tag;
          this.fromDate = fromDate;
          this.toDate = toDate;
          this.messageStream = messageStream;
        }
        return StatisticsFilteringParameters2;
      }()
    );
    exports.StatisticsFilteringParameters = StatisticsFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/triggers/InboundRule.js
var require_InboundRule = __commonJS({
  "node_modules/postmark/dist/client/models/triggers/InboundRule.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CreateInboundRuleRequest = void 0;
    var CreateInboundRuleRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function CreateInboundRuleRequest2(Rule) {
          this.Rule = Rule;
        }
        return CreateInboundRuleRequest2;
      }()
    );
    exports.CreateInboundRuleRequest = CreateInboundRuleRequest;
  }
});

// node_modules/postmark/dist/client/models/webhooks/Webhooks.js
var require_Webhooks = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/Webhooks.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/Webhook.js
var require_Webhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/Webhook.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CreateWebhookRequest = exports.UpdateWebhookRequest = void 0;
    var UpdateWebhookRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateWebhookRequest2(url, triggers, httpAuth, httpHeaders) {
          this.Url = url;
          this.HttpAuth = httpAuth;
          this.HttpHeaders = httpHeaders;
          this.Triggers = triggers;
        }
        return UpdateWebhookRequest2;
      }()
    );
    exports.UpdateWebhookRequest = UpdateWebhookRequest;
    var CreateWebhookRequest = (
      /** @class */
      function(_super) {
        __extends(CreateWebhookRequest2, _super);
        function CreateWebhookRequest2(url, triggers, httpAuth, httpHeaders, messageStream) {
          var _this = _super.call(this, url, triggers, httpAuth, httpHeaders) || this;
          _this.MessageStream = messageStream;
          return _this;
        }
        return CreateWebhookRequest2;
      }(UpdateWebhookRequest)
    );
    exports.CreateWebhookRequest = CreateWebhookRequest;
  }
});

// node_modules/postmark/dist/client/models/webhooks/WebhookFilteringParameters.js
var require_WebhookFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/WebhookFilteringParameters.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.WebhookFilteringParameters = void 0;
    var WebhookFilteringParameters = (
      /** @class */
      /* @__PURE__ */ function() {
        function WebhookFilteringParameters2(messageStream) {
          this.messageStream = messageStream;
        }
        return WebhookFilteringParameters2;
      }()
    );
    exports.WebhookFilteringParameters = WebhookFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/BounceWebhook.js
var require_BounceWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/BounceWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/DeliveryWebhook.js
var require_DeliveryWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/DeliveryWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/ClickWebhook.js
var require_ClickWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/ClickWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/OpenWebhook.js
var require_OpenWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/OpenWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/SubscriptionChangeWebhook.js
var require_SubscriptionChangeWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/SubscriptionChangeWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/webhooks/payload/InboundWebhook.js
var require_InboundWebhook = __commonJS({
  "node_modules/postmark/dist/client/models/webhooks/payload/InboundWebhook.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/postmark/dist/client/models/suppressions/SuppressionFilteringParameters.js
var require_SuppressionFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/suppressions/SuppressionFilteringParameters.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SuppressionFilteringParameters = exports.SuppressionOrigin = exports.SuppressionReason = void 0;
    var FilteringParameters_1 = require_FilteringParameters();
    var SuppressionReason;
    (function(SuppressionReason2) {
      SuppressionReason2["HardBounce"] = "HardBounce";
      SuppressionReason2["SpamComplaint"] = "SpamComplaint";
      SuppressionReason2["ManualSuppression"] = "ManualSuppression";
    })(SuppressionReason = exports.SuppressionReason || (exports.SuppressionReason = {}));
    var SuppressionOrigin;
    (function(SuppressionOrigin2) {
      SuppressionOrigin2["Recipient"] = "Recipient";
      SuppressionOrigin2["Customer"] = "Customer";
      SuppressionOrigin2["Admin"] = "Admin";
    })(SuppressionOrigin = exports.SuppressionOrigin || (exports.SuppressionOrigin = {}));
    var SuppressionFilteringParameters = (
      /** @class */
      function(_super) {
        __extends(SuppressionFilteringParameters2, _super);
        function SuppressionFilteringParameters2(count, offset, suppressionReason, origin, emailAddress, fromDate, toDate) {
          if (count === void 0) {
            count = 100;
          }
          if (offset === void 0) {
            offset = 0;
          }
          var _this = _super.call(this, count, offset) || this;
          _this.suppressionReason = suppressionReason;
          _this.origin = origin;
          _this.emailAddress = emailAddress;
          _this.fromDate = fromDate;
          _this.toDate = toDate;
          return _this;
        }
        return SuppressionFilteringParameters2;
      }(FilteringParameters_1.FilteringParameters)
    );
    exports.SuppressionFilteringParameters = SuppressionFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/streams/MessageStream.js
var require_MessageStream = __commonJS({
  "node_modules/postmark/dist/client/models/streams/MessageStream.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CreateMessageStreamRequest = exports.UpdateMessageStreamRequest = exports.UnsubscribeHandlingTypes = void 0;
    var UnsubscribeHandlingTypes;
    (function(UnsubscribeHandlingTypes2) {
      UnsubscribeHandlingTypes2["None"] = "None";
      UnsubscribeHandlingTypes2["Postmark"] = "Postmark";
      UnsubscribeHandlingTypes2["Custom"] = "Custom";
    })(UnsubscribeHandlingTypes = exports.UnsubscribeHandlingTypes || (exports.UnsubscribeHandlingTypes = {}));
    var UpdateMessageStreamRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function UpdateMessageStreamRequest2(name, description, subscriptionManagementConfiguration) {
          this.Name = name;
          this.Description = description;
          this.SubscriptionManagementConfiguration = subscriptionManagementConfiguration;
        }
        return UpdateMessageStreamRequest2;
      }()
    );
    exports.UpdateMessageStreamRequest = UpdateMessageStreamRequest;
    var CreateMessageStreamRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function CreateMessageStreamRequest2(id, name, messageStreamType, description, subscriptionManagementConfiguration) {
          this.Name = name;
          this.Description = description;
          this.ID = id;
          this.MessageStreamType = messageStreamType;
          this.SubscriptionManagementConfiguration = subscriptionManagementConfiguration;
        }
        return CreateMessageStreamRequest2;
      }()
    );
    exports.CreateMessageStreamRequest = CreateMessageStreamRequest;
  }
});

// node_modules/postmark/dist/client/models/streams/MessageStreamsFilteringParameters.js
var require_MessageStreamsFilteringParameters = __commonJS({
  "node_modules/postmark/dist/client/models/streams/MessageStreamsFilteringParameters.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MessageStreamsFilteringParameters = void 0;
    var MessageStreamsFilteringParameters = (
      /** @class */
      /* @__PURE__ */ function() {
        function MessageStreamsFilteringParameters2(messageStreamType, includeArchivedStreams) {
          this.messageStreamType = messageStreamType;
          this.includeArchivedStreams = includeArchivedStreams;
        }
        return MessageStreamsFilteringParameters2;
      }()
    );
    exports.MessageStreamsFilteringParameters = MessageStreamsFilteringParameters;
  }
});

// node_modules/postmark/dist/client/models/data_removal/DataRemovals.js
var require_DataRemovals = __commonJS({
  "node_modules/postmark/dist/client/models/data_removal/DataRemovals.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.DataRemovalRequest = exports.DataRemovalStatusTypes = void 0;
    var DataRemovalStatusTypes;
    (function(DataRemovalStatusTypes2) {
      DataRemovalStatusTypes2["Pending"] = "Pending";
      DataRemovalStatusTypes2["Done"] = "Done";
    })(DataRemovalStatusTypes = exports.DataRemovalStatusTypes || (exports.DataRemovalStatusTypes = {}));
    var DataRemovalRequest = (
      /** @class */
      /* @__PURE__ */ function() {
        function DataRemovalRequest2(requestedBy, requestedFor, notifyWhenCompleted) {
          this.RequestedBy = requestedBy;
          this.RequestedFor = requestedFor;
          this.NotifyWhenCompleted = notifyWhenCompleted;
        }
        return DataRemovalRequest2;
      }()
    );
    exports.DataRemovalRequest = DataRemovalRequest;
  }
});

// node_modules/postmark/dist/client/models/index.js
var require_models = __commonJS({
  "node_modules/postmark/dist/client/models/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      Object.defineProperty(o, k2, { enumerable: true, get: function() {
        return m[k];
      } });
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_ClientOptions(), exports);
    __exportStar(require_SupportingTypes(), exports);
    __exportStar(require_HttpClient(), exports);
    __exportStar(require_Callback(), exports);
    __exportStar(require_DefaultResponse(), exports);
    __exportStar(require_FilteringParameters(), exports);
    __exportStar(require_Bounce(), exports);
    __exportStar(require_BounceFilteringParameters(), exports);
    __exportStar(require_Message(), exports);
    __exportStar(require_SupportingTypes2(), exports);
    __exportStar(require_OutboundMessage(), exports);
    __exportStar(require_OutboundMessageOpen(), exports);
    __exportStar(require_OutboundMessageClick(), exports);
    __exportStar(require_InboundMessage(), exports);
    __exportStar(require_MessageFilteringParameters(), exports);
    __exportStar(require_Template(), exports);
    __exportStar(require_Server(), exports);
    __exportStar(require_Servers(), exports);
    __exportStar(require_ServerFilteringParameters(), exports);
    __exportStar(require_Domain(), exports);
    __exportStar(require_Signature(), exports);
    __exportStar(require_Suppression(), exports);
    __exportStar(require_Stats(), exports);
    __exportStar(require_StatsFilteringParameters(), exports);
    __exportStar(require_InboundRule(), exports);
    __exportStar(require_Webhooks(), exports);
    __exportStar(require_Webhook(), exports);
    __exportStar(require_WebhookFilteringParameters(), exports);
    __exportStar(require_BounceWebhook(), exports);
    __exportStar(require_DeliveryWebhook(), exports);
    __exportStar(require_ClickWebhook(), exports);
    __exportStar(require_OpenWebhook(), exports);
    __exportStar(require_SubscriptionChangeWebhook(), exports);
    __exportStar(require_InboundWebhook(), exports);
    __exportStar(require_Suppression(), exports);
    __exportStar(require_SuppressionFilteringParameters(), exports);
    __exportStar(require_MessageStream(), exports);
    __exportStar(require_MessageStreamsFilteringParameters(), exports);
    __exportStar(require_DataRemovals(), exports);
  }
});

// node_modules/postmark/dist/client/errors/index.js
var require_errors = __commonJS({
  "node_modules/postmark/dist/client/errors/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      Object.defineProperty(o, k2, { enumerable: true, get: function() {
        return m[k];
      } });
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_ErrorHandler(), exports);
    __exportStar(require_Errors(), exports);
  }
});

// node_modules/postmark/dist/client/HttpClient.js
var require_HttpClient2 = __commonJS({
  "node_modules/postmark/dist/client/HttpClient.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.AxiosHttpClient = void 0;
    var axios_1 = require_axios2();
    var models_1 = require_models();
    var index_1 = require_errors();
    var AxiosHttpClient = (
      /** @class */
      function(_super) {
        __extends(AxiosHttpClient2, _super);
        function AxiosHttpClient2(configOptions) {
          var _this = _super.call(this, configOptions) || this;
          _this.errorHandler = new index_1.ErrorHandler();
          return _this;
        }
        AxiosHttpClient2.prototype.initHttpClient = function(configOptions) {
          this.clientOptions = __assign(__assign({}, models_1.HttpClient.DefaultOptions), configOptions);
          var httpClient = axios_1.default.create({
            baseURL: this.getBaseHttpRequestURL(),
            timeout: this.getRequestTimeoutInMilliseconds(),
            responseType: "json",
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
            validateStatus: function(status) {
              return status >= 200 && status < 300;
            }
          });
          httpClient.interceptors.response.use(function(response) {
            return response.data;
          });
          this.client = httpClient;
        };
        AxiosHttpClient2.prototype.httpRequest = function(method, path, queryParameters, body, requestHeaders) {
          var _this = this;
          return this.client.request({
            method,
            url: path,
            data: body,
            headers: requestHeaders,
            params: queryParameters
          }).catch(function(errorThrown) {
            return Promise.reject(_this.transformError(errorThrown));
          });
        };
        AxiosHttpClient2.prototype.transformError = function(errorThrown) {
          var response = errorThrown.response;
          if (response !== void 0) {
            var status_1 = this.adjustValue(0, response.status);
            var errorCode = this.adjustValue(0, response.data.ErrorCode);
            var message = this.adjustValue(errorThrown.message, response.data.Message);
            return this.errorHandler.buildError(message, errorCode, status_1);
          } else if (errorThrown.message !== void 0) {
            return this.errorHandler.buildError(errorThrown.message);
          } else {
            return this.errorHandler.buildError(JSON.stringify(errorThrown, Object.getOwnPropertyNames(errorThrown)));
          }
        };
        AxiosHttpClient2.prototype.getRequestTimeoutInMilliseconds = function() {
          return (this.clientOptions.timeout || 60) * 1e3;
        };
        AxiosHttpClient2.prototype.adjustValue = function(defaultValue, data) {
          return data === void 0 ? defaultValue : data;
        };
        return AxiosHttpClient2;
      }(models_1.HttpClient)
    );
    exports.AxiosHttpClient = AxiosHttpClient;
  }
});

// node_modules/postmark/package.json
var require_package = __commonJS({
  "node_modules/postmark/package.json"(exports, module) {
    module.exports = {
      name: "postmark",
      description: "Official Node.js client library for the Postmark HTTP API - https://www.postmarkapp.com",
      license: "MIT",
      tags: [
        "email",
        "utility",
        "postmark",
        "sending",
        "transactional"
      ],
      version: "3.11.0",
      author: "Igor Balos",
      contributors: [
        "Igor Balos",
        "Andrew Theken",
        "Aaron Blum",
        "Aleksey Aleksandrov",
        "Alex Shepard",
        "Antony Jones",
        "Ben Burwell",
        "Ben Williamson",
        "Chris Williams",
        "Jakub Borys",
        "Mark Nguyen",
        "Matt",
        "Matthew Blackshaw",
        "Matthew Conlen",
        "Ryan Kirkman",
        "Scott Anderson",
        "Sebastien Chopin",
        "Theophane RUPIN",
        "codesplicer",
        "francescoRubini"
      ],
      main: "./dist/index.js",
      types: "./dist/index.d.ts",
      directories: {
        lib: "./dist/index.js"
      },
      scripts: {
        compile: "rm -r -f ./dist && node_modules/.bin/tsc",
        test: "node_modules/mocha/bin/mocha --timeout 30000 --retries 1 -r ts-node/register test/**/*test.ts",
        unittest: "node_modules/mocha/bin/mocha --timeout 30000 --retries 1 -r ts-node/register test/unit/**/*test.ts",
        watchtests: "node_modules/.bin/mocha --timeout 30000 --retries 1 -r ts-node/register -R list -w --recursive -G test/**/*test.ts",
        lint: "tslint -c tslint.json 'src/**/*.ts'",
        lintfix: "tslint -c tslint.json 'src/**/*.ts' --fix",
        "compile-docs": "echo 'Generating docs...' && mkdir -p ./docs && rm -r ./docs && node_modules/.bin/typedoc --options typedoc.json && git add -A ./docs && echo 'Generated docs!'"
      },
      homepage: "http://ActiveCampaign.github.io/postmark.js",
      repository: {
        type: "git",
        url: "https://github.com/ActiveCampaign/postmark.js.git"
      },
      bugs: {
        url: "https://github.com/ActiveCampaign/postmark.js/issues"
      },
      precommit: [
        "compile",
        "lint",
        "test",
        "compile-docs"
      ],
      devDependencies: {
        "@types/chai": "4.3.1",
        "@types/mocha": "^5.2.5",
        "@types/dotenv": "^4.0.3",
        "@types/node": "^4.0.29",
        "@types/sinon": "^7.5.0",
        chai: "4.3.1",
        mocha: "5.2.0",
        dotenv: "^4.0.0",
        sinon: "^7.5.0",
        "pre-commit": "1.2.2",
        "ts-node": "^7.0.1",
        tslint: "^6.1.3",
        typedoc: "^0.22.11",
        typescript: "4.5.5"
      },
      dependencies: {
        axios: "^0.25.0"
      }
    };
  }
});

// node_modules/postmark/dist/client/BaseClient.js
var require_BaseClient = __commonJS({
  "node_modules/postmark/dist/client/BaseClient.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var ErrorHandler_1 = require_ErrorHandler();
    var HttpClient_1 = require_HttpClient2();
    var packageJson = require_package();
    var CLIENT_VERSION = packageJson.version;
    var BaseClient = (
      /** @class */
      function() {
        function BaseClient2(token, authHeader, configOptions) {
          this.errorHandler = new ErrorHandler_1.ErrorHandler();
          this.verifyToken(token);
          this.token = token.trim();
          this.authHeader = authHeader;
          this.clientVersion = CLIENT_VERSION;
          this.httpClient = new HttpClient_1.AxiosHttpClient(configOptions);
        }
        BaseClient2.prototype.setClientOptions = function(configOptions) {
          this.httpClient.initHttpClient(configOptions);
        };
        BaseClient2.prototype.getClientOptions = function() {
          return this.httpClient.clientOptions;
        };
        BaseClient2.prototype.processRequestWithBody = function(method, path, body, callback) {
          return this.processRequest(method, path, {}, body, callback);
        };
        BaseClient2.prototype.processRequestWithoutBody = function(method, path, queryParameters, callback) {
          if (queryParameters === void 0) {
            queryParameters = {};
          }
          return this.processRequest(method, path, queryParameters, null, callback);
        };
        BaseClient2.prototype.processRequest = function(method, path, queryParameters, body, callback) {
          var httpRequest = this.processHttpRequest(method, path, queryParameters, body);
          this.processCallbackRequest(httpRequest, callback);
          return httpRequest;
        };
        BaseClient2.prototype.processHttpRequest = function(method, path, queryParameters, body) {
          return this.httpClient.httpRequest(method, path, queryParameters, body, this.getComposedHttpRequestHeaders()).then(function(response) {
            return response;
          }).catch(function(error) {
            return Promise.reject(error);
          });
        };
        BaseClient2.prototype.processCallbackRequest = function(httpRequest, callback) {
          if (callback) {
            httpRequest.then(function(response) {
              return callback(null, response);
            }).catch(function(error) {
              return callback(error, null);
            });
          }
        };
        BaseClient2.prototype.getComposedHttpRequestHeaders = function() {
          var _a;
          return _a = {}, _a[this.authHeader] = this.token, _a["Accept"] = "application/json", _a["Content-Type"] = "application/json", _a["User-Agent"] = "Postmark.JS - ".concat(this.clientVersion), _a;
        };
        BaseClient2.prototype.verifyToken = function(token) {
          if (!token || token.trim() === "") {
            throw this.errorHandler.buildError("A valid API token must be provided.");
          }
        };
        BaseClient2.prototype.setDefaultPaginationValues = function(filter) {
          filter.count = filter.count || 100;
          filter.offset = filter.offset || 0;
        };
        return BaseClient2;
      }()
    );
    exports.default = BaseClient;
  }
});

// node_modules/postmark/dist/client/AccountClient.js
var require_AccountClient = __commonJS({
  "node_modules/postmark/dist/client/AccountClient.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    var BaseClient_1 = require_BaseClient();
    var models_1 = require_models();
    var models_2 = require_models();
    var AccountClient = (
      /** @class */
      function(_super) {
        __extends(AccountClient2, _super);
        function AccountClient2(accountToken, configOptions) {
          return _super.call(this, accountToken, models_1.ClientOptions.AuthHeaderNames.ACCOUNT_TOKEN, configOptions) || this;
        }
        AccountClient2.prototype.getServers = function(filter, callback) {
          if (filter === void 0) {
            filter = new models_2.ServerFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/servers", filter, callback);
        };
        AccountClient2.prototype.getServer = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/servers/".concat(id), {}, callback);
        };
        AccountClient2.prototype.createServer = function(options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.POST, "/servers", options, callback);
        };
        AccountClient2.prototype.editServer = function(id, options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.PUT, "/servers/".concat(id), options, callback);
        };
        AccountClient2.prototype.deleteServer = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.DELETE, "/servers/".concat(id), {}, callback);
        };
        AccountClient2.prototype.getDomains = function(filter, callback) {
          if (filter === void 0) {
            filter = new models_1.FilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/domains", filter, callback);
        };
        AccountClient2.prototype.getDomain = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/domains/".concat(id), {}, callback);
        };
        AccountClient2.prototype.createDomain = function(options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.POST, "/domains/", options, callback);
        };
        AccountClient2.prototype.editDomain = function(id, options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.PUT, "/domains/".concat(id), options, callback);
        };
        AccountClient2.prototype.deleteDomain = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.DELETE, "/domains/".concat(id), {}, callback);
        };
        AccountClient2.prototype.verifyDomainDKIM = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.PUT, "/domains/".concat(id, "/verifyDKIM"), {}, callback);
        };
        AccountClient2.prototype.verifyDomainReturnPath = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.PUT, "/domains/".concat(id, "/verifyReturnPath"), {}, callback);
        };
        AccountClient2.prototype.verifyDomainSPF = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.POST, "/domains/".concat(id, "/verifySPF"), {}, callback);
        };
        AccountClient2.prototype.rotateDomainDKIM = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.POST, "/domains/".concat(id, "/rotateDKIM"), {}, callback);
        };
        AccountClient2.prototype.getSenderSignature = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/senders/".concat(id), {}, callback);
        };
        AccountClient2.prototype.getSenderSignatures = function(filter, callback) {
          if (filter === void 0) {
            filter = new models_1.FilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/senders", filter, callback);
        };
        AccountClient2.prototype.createSenderSignature = function(options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.POST, "/senders/", options, callback);
        };
        AccountClient2.prototype.editSenderSignature = function(id, options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.PUT, "/senders/".concat(id), options, callback);
        };
        AccountClient2.prototype.deleteSenderSignature = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.DELETE, "/senders/".concat(id), {}, callback);
        };
        AccountClient2.prototype.resendSenderSignatureConfirmation = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.POST, "/senders/".concat(id, "/resend"), {}, callback);
        };
        AccountClient2.prototype.verifySenderSignatureSPF = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.POST, "/senders/".concat(id, "/verifySpf"), {}, callback);
        };
        AccountClient2.prototype.requestNewDKIMForSenderSignature = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.POST, "/senders/".concat(id, "/requestNewDkim"), {}, callback);
        };
        AccountClient2.prototype.pushTemplates = function(options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.PUT, "/templates/push", options, callback);
        };
        AccountClient2.prototype.requestDataRemoval = function(options, callback) {
          return this.processRequestWithBody(models_1.ClientOptions.HttpMethod.POST, "/data-removals", options, callback);
        };
        AccountClient2.prototype.getDataRemovalStatus = function(id, callback) {
          return this.processRequestWithoutBody(models_1.ClientOptions.HttpMethod.GET, "/data-removals/".concat(id), {}, callback);
        };
        return AccountClient2;
      }(BaseClient_1.default)
    );
    exports.default = AccountClient;
  }
});

// node_modules/postmark/dist/client/ServerClient.js
var require_ServerClient = __commonJS({
  "node_modules/postmark/dist/client/ServerClient.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    var BaseClient_1 = require_BaseClient();
    var index_1 = require_models();
    var index_2 = require_models();
    var ServerClient = (
      /** @class */
      function(_super) {
        __extends(ServerClient2, _super);
        function ServerClient2(serverToken, configOptions) {
          return _super.call(this, serverToken, index_1.ClientOptions.AuthHeaderNames.SERVER_TOKEN, configOptions) || this;
        }
        ServerClient2.prototype.sendEmail = function(email, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/email", email, callback);
        };
        ServerClient2.prototype.sendEmailBatch = function(emails, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/email/batch", emails, callback);
        };
        ServerClient2.prototype.sendEmailWithTemplate = function(template, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/email/withTemplate", template, callback);
        };
        ServerClient2.prototype.sendEmailBatchWithTemplates = function(templates, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/email/batchWithTemplates", { Messages: templates }, callback);
        };
        ServerClient2.prototype.getDeliveryStatistics = function(callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/deliveryStats", {}, callback);
        };
        ServerClient2.prototype.getBounces = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.BounceFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/bounces", filter, callback);
        };
        ServerClient2.prototype.getBounce = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/bounces/".concat(id), {}, callback);
        };
        ServerClient2.prototype.getBounceDump = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/bounces/".concat(id, "/dump"), {}, callback);
        };
        ServerClient2.prototype.activateBounce = function(id, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.PUT, "/bounces/".concat(id, "/activate"), {}, callback);
        };
        ServerClient2.prototype.getTemplates = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.TemplateFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/templates", filter, callback);
        };
        ServerClient2.prototype.getTemplate = function(idOrAlias, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/templates/".concat(idOrAlias), {}, callback);
        };
        ServerClient2.prototype.deleteTemplate = function(idOrAlias, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.DELETE, "/templates/".concat(idOrAlias), {}, callback);
        };
        ServerClient2.prototype.createTemplate = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/templates/", options, callback);
        };
        ServerClient2.prototype.editTemplate = function(idOrAlias, options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.PUT, "/templates/".concat(idOrAlias), options, callback);
        };
        ServerClient2.prototype.validateTemplate = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/templates/validate", options, callback);
        };
        ServerClient2.prototype.getServer = function(callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/server", {}, callback);
        };
        ServerClient2.prototype.editServer = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.PUT, "/server", options, callback);
        };
        ServerClient2.prototype.getOutboundMessages = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.OutboundMessagesFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound", filter, callback);
        };
        ServerClient2.prototype.getOutboundMessageDetails = function(messageId, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/".concat(messageId), {}, callback);
        };
        ServerClient2.prototype.getOutboundMessageDump = function(messageId, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/".concat(messageId, "/dump"), {}, callback);
        };
        ServerClient2.prototype.getInboundMessages = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.InboundMessagesFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/inbound", filter, callback);
        };
        ServerClient2.prototype.getInboundMessageDetails = function(messageId, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/inbound/".concat(messageId, "/details"), {}, callback);
        };
        ServerClient2.prototype.bypassBlockedInboundMessage = function(messageId, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.PUT, "/messages/inbound/".concat(messageId, "/bypass"), {}, callback);
        };
        ServerClient2.prototype.retryInboundHookForMessage = function(messageId, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.PUT, "/messages/inbound/".concat(messageId, "/retry"), {}, callback);
        };
        ServerClient2.prototype.getMessageOpens = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.OutboundMessageOpensFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/opens", filter, callback);
        };
        ServerClient2.prototype.getMessageOpensForSingleMessage = function(messageId, filter, callback) {
          if (filter === void 0) {
            filter = new index_2.OutboundMessageOpensFilteringParameters(50, 0);
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/opens/".concat(messageId), filter, callback);
        };
        ServerClient2.prototype.getMessageClicks = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.OutboundMessageClicksFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/clicks", filter, callback);
        };
        ServerClient2.prototype.getMessageClicksForSingleMessage = function(messageId, filter, callback) {
          if (filter === void 0) {
            filter = new index_2.OutboundMessageClicksFilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/messages/outbound/clicks/".concat(messageId), filter, callback);
        };
        ServerClient2.prototype.getOutboundOverview = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound", filter, callback);
        };
        ServerClient2.prototype.getSentCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/sends", filter, callback);
        };
        ServerClient2.prototype.getBounceCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/bounces", filter, callback);
        };
        ServerClient2.prototype.getSpamComplaintsCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/spam", filter, callback);
        };
        ServerClient2.prototype.getTrackedEmailCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/tracked", filter, callback);
        };
        ServerClient2.prototype.getEmailOpenCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/opens", filter, callback);
        };
        ServerClient2.prototype.getEmailOpenPlatformUsage = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/opens/platforms", filter, callback);
        };
        ServerClient2.prototype.getEmailOpenClientUsage = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/opens/emailClients", filter, callback);
        };
        ServerClient2.prototype.getEmailOpenReadTimes = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/opens/readTimes", filter, callback);
        };
        ServerClient2.prototype.getClickCounts = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/clicks", filter, callback);
        };
        ServerClient2.prototype.getClickBrowserUsage = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/clicks/browserFamilies", filter, callback);
        };
        ServerClient2.prototype.getClickPlatformUsage = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/clicks/platforms", filter, callback);
        };
        ServerClient2.prototype.getClickLocation = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_2.StatisticsFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/stats/outbound/clicks/location", filter, callback);
        };
        ServerClient2.prototype.createInboundRuleTrigger = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/triggers/inboundRules", options, callback);
        };
        ServerClient2.prototype.deleteInboundRuleTrigger = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.DELETE, "/triggers/inboundRules/".concat(id), {}, callback);
        };
        ServerClient2.prototype.getInboundRuleTriggers = function(filter, callback) {
          if (filter === void 0) {
            filter = new index_1.FilteringParameters();
          }
          this.setDefaultPaginationValues(filter);
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/triggers/inboundRules", filter, callback);
        };
        ServerClient2.prototype.getWebhooks = function(filter, callback) {
          if (filter === void 0) {
            filter = {};
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/webhooks", filter, callback);
        };
        ServerClient2.prototype.getWebhook = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/webhooks/".concat(id), {}, callback);
        };
        ServerClient2.prototype.createWebhook = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/webhooks", options, callback);
        };
        ServerClient2.prototype.editWebhook = function(id, options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.PUT, "/webhooks/".concat(id), options, callback);
        };
        ServerClient2.prototype.deleteWebhook = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.DELETE, "/webhooks/".concat(id), {}, callback);
        };
        ServerClient2.prototype.getMessageStreams = function(filter, callback) {
          if (filter === void 0) {
            filter = {};
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/message-streams", filter, callback);
        };
        ServerClient2.prototype.getMessageStream = function(id, callback) {
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/message-streams/".concat(id), {}, callback);
        };
        ServerClient2.prototype.editMessageStream = function(id, options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.PATCH, "/message-streams/".concat(id), options, callback);
        };
        ServerClient2.prototype.createMessageStream = function(options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/message-streams", options, callback);
        };
        ServerClient2.prototype.archiveMessageStream = function(id, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/message-streams/".concat(id, "/archive"), {}, callback);
        };
        ServerClient2.prototype.unarchiveMessageStream = function(id, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/message-streams/".concat(id, "/unarchive"), {}, callback);
        };
        ServerClient2.prototype.getSuppressions = function(messageStream, filter, callback) {
          if (filter === void 0) {
            filter = new index_2.SuppressionFilteringParameters();
          }
          return this.processRequestWithoutBody(index_1.ClientOptions.HttpMethod.GET, "/message-streams/".concat(messageStream, "/suppressions/dump"), filter, callback);
        };
        ServerClient2.prototype.createSuppressions = function(messageStream, options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/message-streams/".concat(messageStream, "/suppressions"), options, callback);
        };
        ServerClient2.prototype.deleteSuppressions = function(messageStream, options, callback) {
          return this.processRequestWithBody(index_1.ClientOptions.HttpMethod.POST, "/message-streams/".concat(messageStream, "/suppressions/delete"), options, callback);
        };
        return ServerClient2;
      }(BaseClient_1.default)
    );
    exports.default = ServerClient;
  }
});

// node_modules/postmark/dist/index.js
var require_dist = __commonJS({
  "node_modules/postmark/dist/index.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Header = exports.Attachment = exports.TemplatedMessage = exports.Message = exports.Errors = exports.Models = exports.AdminClient = exports.AccountClient = exports.Client = exports.ServerClient = void 0;
    var AccountClient_1 = require_AccountClient();
    exports.AccountClient = AccountClient_1.default;
    exports.AdminClient = AccountClient_1.default;
    var ServerClient_1 = require_ServerClient();
    exports.ServerClient = ServerClient_1.default;
    exports.Client = ServerClient_1.default;
    var Models = require_models();
    exports.Models = Models;
    var Errors = require_Errors();
    exports.Errors = Errors;
    var models_1 = require_models();
    Object.defineProperty(exports, "Message", { enumerable: true, get: function() {
      return models_1.Message;
    } });
    var models_2 = require_models();
    Object.defineProperty(exports, "TemplatedMessage", { enumerable: true, get: function() {
      return models_2.TemplatedMessage;
    } });
    var models_3 = require_models();
    Object.defineProperty(exports, "Attachment", { enumerable: true, get: function() {
      return models_3.Attachment;
    } });
    var models_4 = require_models();
    Object.defineProperty(exports, "Header", { enumerable: true, get: function() {
      return models_4.Header;
    } });
  }
});
export default require_dist();
//# sourceMappingURL=postmark.js.map
