const redirects: { [key: string]: string } = {
  "/docs/features/admin-portal": "/docs/articles/admin-portal",
  "/docs/features/app-portal": "/docs/articles/app-portal",
  "/docs/features/prompt-flow-builder": "/docs/articles/prompt-flow-builder",
  "/docs/features/unit-tests": "/docs/articles/unit-tests",
  "/docs/features/help-desk": "/docs/articles/help-desk",
  "/docs/features/feature-flags": "/docs/articles/feature-flags",
  "/docs/features/onboarding": "/docs/articles/onboarding",
  "/docs/features/step-form-wizard": "/docs/articles/step-form-wizard",
  "/docs/features/email-marketing": "/docs/articles/email-marketing",
  "/docs/features/analytics": "/docs/articles/analytics",
  "/docs/features/roles-and-permissions": "/docs/articles/roles-and-permissions",
  "/docs/features/authentication": "/docs/articles/authentication",
  "/docs/features/marketing-pages": "/docs/articles/marketing-pages",
  "/docs/features/notifications": "/docs/articles/notifications",
  "/docs/features/audit-trails": "/docs/articles/audit-trails",
  "/docs/features/api": "/docs/articles/api",
  "/docs/features/subscriptions": "/docs/articles/subscriptions",
  "/docs/features/entity-builder": "/docs/articles/entity-builder",
  "/docs/features/events-and-webhooks": "/docs/articles/events-and-webhooks",
  "/docs/features/metrics": "/docs/articles/metrics",
  "/docs/features/blogging": "/docs/articles/blogging",
  "/docs/features/knowledge-base": "/docs/articles/knowledge-base",
  "/docs/features/webhooks": "/docs/articles/webhooks",
  "/docs/features/page-blocks": "/docs/articles/page-blocks",
  "/docs/roadmap": "/roadmap",
  "/docs/license": "/docs/articles/license",
  "/docs/page-blocks": "/docs/articles/page-blocks",
  "/docs/page-blocks/editor": "/docs/articles/page-blocks",
  "/docs/learning-center/guides/database/supabase": "/docs/articles/supabase",
  "/docs/learning-center/guides/entities/use-the-custom-entity-api": "/docs/articles/use-the-custom-entity-api",
  "/docs/learning-center/guides/hosting/vercel": "/docs/articles/vercel",
  "/docs/core-models/custom-entity/entity": "/docs/articles/model-entity",
  "/docs/core-models/custom-entity/field": "/docs/articles/model-field",
  "/docs/learning-center/guides/entities/extend-existing-models": "/docs/articles/extend-existing-models",
  "/docs/learning-center/guides/entities/create-a-custom-entity": "/docs/articles/create-a-custom-entity",
  "/docs/components": "/docs/articles/components",
  "/docs/stack": "/docs/articles/stack",
};

export default {
  redirects,
};
