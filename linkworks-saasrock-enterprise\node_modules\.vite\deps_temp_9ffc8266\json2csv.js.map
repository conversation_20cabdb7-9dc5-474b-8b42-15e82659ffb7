{"version": 3, "sources": ["browser-external:os", "../../json2csv/dist/json2csv.esm.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"os\" has been externalized for browser compatibility. Cannot access \"os.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "import stream from 'stream';\nimport os from 'os';\n\nfunction _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n\n  return target;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _toArray(arr) {\n  return _arrayWithHoles(arr) || _iterableToArray(arr) || _nonIterableRest();\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArray(iter) {\n  if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance\");\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\n/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/** `Object#toString` result references. */\nvar funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    symbolTag = '[object Symbol]';\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/,\n    reLeadingDot = /^\\./,\n    rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to match backslashes in property paths. */\nvar reEscapeChar = /\\\\(\\\\)?/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Symbol$1 = root.Symbol,\n    splice = arrayProto.splice;\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map'),\n    nativeCreate = getNative(Object, 'create');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol$1 ? Symbol$1.prototype : undefined,\n    symbolToString = symbolProto ? symbolProto.toString : undefined;\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  return this.has(key) && delete this.__data__[key];\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  return getMapData(this, key)['delete'](key);\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  getMapData(this, key).set(key, value);\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = isKey(path, object) ? [path] : castPath(path);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.toString` which doesn't convert nullish\n * values to empty strings.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n */\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return symbolToString ? symbolToString.call(value) : '';\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value) {\n  return isArray(value) ? value : stringToPath(value);\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' ||\n      value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||\n    (object != null && value in Object(object));\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\nvar stringToPath = memoize(function(string) {\n  string = toString(string);\n\n  var result = [];\n  if (reLeadingDot.test(string)) {\n    result.push('');\n  }\n  string.replace(rePropName, function(match, number, quote, string) {\n    result.push(quote ? string.replace(reEscapeChar, '$1') : (number || match));\n  });\n  return result;\n});\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to process.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * Creates a function that memoizes the result of `func`. If `resolver` is\n * provided, it determines the cache key for storing the result based on the\n * arguments provided to the memoized function. By default, the first argument\n * provided to the memoized function is used as the map cache key. The `func`\n * is invoked with the `this` binding of the memoized function.\n *\n * **Note:** The cache is exposed as the `cache` property on the memoized\n * function. Its creation may be customized by replacing the `_.memoize.Cache`\n * constructor with one whose instances implement the\n * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)\n * method interface of `delete`, `get`, `has`, and `set`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to have its output memoized.\n * @param {Function} [resolver] The function to resolve the cache key.\n * @returns {Function} Returns the new memoized function.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n * var other = { 'c': 3, 'd': 4 };\n *\n * var values = _.memoize(_.values);\n * values(object);\n * // => [1, 2]\n *\n * values(other);\n * // => [3, 4]\n *\n * object.a = 2;\n * values(object);\n * // => [1, 2]\n *\n * // Modify the result cache.\n * values.cache.set(object, ['a', 'b']);\n * values(object);\n * // => ['a', 'b']\n *\n * // Replace `_.memoize.Cache`.\n * _.memoize.Cache = WeakMap;\n */\nfunction memoize(func, resolver) {\n  if (typeof func != 'function' || (resolver && typeof resolver != 'function')) {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var memoized = function() {\n    var args = arguments,\n        key = resolver ? resolver.apply(this, args) : args[0],\n        cache = memoized.cache;\n\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    var result = func.apply(this, args);\n    memoized.cache = cache.set(key, result);\n    return result;\n  };\n  memoized.cache = new (memoize.Cache || MapCache);\n  return memoized;\n}\n\n// Assign cache to `_.memoize`.\nmemoize.Cache = MapCache;\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a string. An empty string is returned for `null`\n * and `undefined` values. The sign of `-0` is preserved.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n * @example\n *\n * _.toString(null);\n * // => ''\n *\n * _.toString(-0);\n * // => '-0'\n *\n * _.toString([1, 2, 3]);\n * // => '1,2,3'\n */\nfunction toString(value) {\n  return value == null ? '' : baseToString(value);\n}\n\n/**\n * Gets the value at `path` of `object`. If the resolved value is\n * `undefined`, the `defaultValue` is returned in its place.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.get(object, 'a[0].b.c');\n * // => 3\n *\n * _.get(object, ['a', '0', 'b', 'c']);\n * // => 3\n *\n * _.get(object, 'a.b.c', 'default');\n * // => 'default'\n */\nfunction get(object, path, defaultValue) {\n  var result = object == null ? undefined : baseGet(object, path);\n  return result === undefined ? defaultValue : result;\n}\n\nvar lodash_get = get;\n\nfunction getProp(obj, path, defaultValue) {\n  return obj[path] === undefined ? defaultValue : obj[path];\n}\n\nfunction setProp(obj, path, value) {\n  var pathArray = Array.isArray(path) ? path : path.split('.');\n\n  var _pathArray = _toArray(pathArray),\n      key = _pathArray[0],\n      restPath = _pathArray.slice(1);\n\n  return _objectSpread({}, obj, _defineProperty({}, key, pathArray.length > 1 ? setProp(obj[key] || {}, restPath, value) : value));\n}\n\nfunction unsetProp(obj, path) {\n  var pathArray = Array.isArray(path) ? path : path.split('.');\n\n  var _pathArray2 = _toArray(pathArray),\n      key = _pathArray2[0],\n      restPath = _pathArray2.slice(1);\n\n  if (_typeof(obj[key]) !== 'object') {\n    // This will never be hit in the current code because unwind does the check before calling unsetProp\n\n    /* istanbul ignore next */\n    return obj;\n  }\n\n  if (pathArray.length === 1) {\n    return Object.keys(obj).filter(function (prop) {\n      return prop !== key;\n    }).reduce(function (acc, prop) {\n      return Object.assign(acc, _defineProperty({}, prop, obj[prop]));\n    }, {});\n  }\n\n  return Object.keys(obj).reduce(function (acc, prop) {\n    return _objectSpread({}, acc, _defineProperty({}, prop, prop !== key ? obj[prop] : unsetProp(obj[key], restPath)));\n  }, {});\n}\n\nfunction flattenReducer(acc, arr) {\n  try {\n    // This is faster but susceptible to `RangeError: Maximum call stack size exceeded`\n    acc.push.apply(acc, _toConsumableArray(arr));\n    return acc;\n  } catch (err) {\n    // Fallback to a slower but safer option\n    return acc.concat(arr);\n  }\n}\n\nfunction fastJoin(arr, separator) {\n  var isFirst = true;\n  return arr.reduce(function (acc, elem) {\n    if (elem === null || elem === undefined) {\n      elem = '';\n    }\n\n    if (isFirst) {\n      isFirst = false;\n      return \"\".concat(elem);\n    }\n\n    return \"\".concat(acc).concat(separator).concat(elem);\n  }, '');\n}\n\nvar utils = {\n  getProp: getProp,\n  setProp: setProp,\n  unsetProp: unsetProp,\n  fastJoin: fastJoin,\n  flattenReducer: flattenReducer\n};\n\nvar getProp$1 = utils.getProp,\n    fastJoin$1 = utils.fastJoin,\n    flattenReducer$1 = utils.flattenReducer;\n\nvar JSON2CSVBase =\n/*#__PURE__*/\nfunction () {\n  function JSON2CSVBase(opts) {\n    _classCallCheck(this, JSON2CSVBase);\n\n    this.opts = this.preprocessOpts(opts);\n  }\n  /**\n   * Check passing opts and set defaults.\n   *\n   * @param {Json2CsvOptions} opts Options object containing fields,\n   * delimiter, default value, quote mark, header, etc.\n   */\n\n\n  _createClass(JSON2CSVBase, [{\n    key: \"preprocessOpts\",\n    value: function preprocessOpts(opts) {\n      var processedOpts = Object.assign({}, opts);\n      processedOpts.transforms = !Array.isArray(processedOpts.transforms) ? processedOpts.transforms ? [processedOpts.transforms] : [] : processedOpts.transforms;\n      processedOpts.delimiter = processedOpts.delimiter || ',';\n      processedOpts.eol = processedOpts.eol || os.EOL;\n      processedOpts.quote = typeof processedOpts.quote === 'string' ? processedOpts.quote : '\"';\n      processedOpts.escapedQuote = typeof processedOpts.escapedQuote === 'string' ? processedOpts.escapedQuote : \"\".concat(processedOpts.quote).concat(processedOpts.quote);\n      processedOpts.header = processedOpts.header !== false;\n      processedOpts.includeEmptyRows = processedOpts.includeEmptyRows || false;\n      processedOpts.withBOM = processedOpts.withBOM || false;\n      return processedOpts;\n    }\n    /**\n     * Check and normalize the fields configuration.\n     *\n     * @param {(string|object)[]} fields Fields configuration provided by the user\n     * or inferred from the data\n     * @returns {object[]} preprocessed FieldsInfo array\n     */\n\n  }, {\n    key: \"preprocessFieldsInfo\",\n    value: function preprocessFieldsInfo(fields) {\n      var _this = this;\n\n      return fields.map(function (fieldInfo) {\n        if (typeof fieldInfo === 'string') {\n          return {\n            label: fieldInfo,\n            value: fieldInfo.includes('.') || fieldInfo.includes('[') ? function (row) {\n              return lodash_get(row, fieldInfo, _this.opts.defaultValue);\n            } : function (row) {\n              return getProp$1(row, fieldInfo, _this.opts.defaultValue);\n            }\n          };\n        }\n\n        if (_typeof(fieldInfo) === 'object') {\n          var defaultValue = 'default' in fieldInfo ? fieldInfo.default : _this.opts.defaultValue;\n\n          if (typeof fieldInfo.value === 'string') {\n            return {\n              label: fieldInfo.label || fieldInfo.value,\n              value: fieldInfo.value.includes('.') || fieldInfo.value.includes('[') ? function (row) {\n                return lodash_get(row, fieldInfo.value, defaultValue);\n              } : function (row) {\n                return getProp$1(row, fieldInfo.value, defaultValue);\n              }\n            };\n          }\n\n          if (typeof fieldInfo.value === 'function') {\n            var label = fieldInfo.label || fieldInfo.value.name || '';\n            var field = {\n              label: label,\n              default: defaultValue\n            };\n            return {\n              label: label,\n              value: function value(row) {\n                var value = fieldInfo.value(row, field);\n                return value === null || value === undefined ? defaultValue : value;\n              }\n            };\n          }\n        }\n\n        throw new Error('Invalid field info option. ' + JSON.stringify(fieldInfo));\n      });\n    }\n    /**\n     * Create the title row with all the provided fields as column headings\n     *\n     * @returns {String} titles as a string\n     */\n\n  }, {\n    key: \"getHeader\",\n    value: function getHeader() {\n      var _this2 = this;\n\n      return fastJoin$1(this.opts.fields.map(function (fieldInfo) {\n        return _this2.processValue(fieldInfo.label);\n      }), this.opts.delimiter);\n    }\n    /**\n     * Preprocess each object according to the given transforms (unwind, flatten, etc.).\n     * @param {Object} row JSON object to be converted in a CSV row\n     */\n\n  }, {\n    key: \"preprocessRow\",\n    value: function preprocessRow(row) {\n      return this.opts.transforms.reduce(function (rows, transform) {\n        return rows.map(function (row) {\n          return transform(row);\n        }).reduce(flattenReducer$1, []);\n      }, [row]);\n    }\n    /**\n     * Create the content of a specific CSV row\n     *\n     * @param {Object} row JSON object to be converted in a CSV row\n     * @returns {String} CSV string (row)\n     */\n\n  }, {\n    key: \"processRow\",\n    value: function processRow(row) {\n      var _this3 = this;\n\n      if (!row) {\n        return undefined;\n      }\n\n      var processedRow = this.opts.fields.map(function (fieldInfo) {\n        return _this3.processCell(row, fieldInfo);\n      });\n\n      if (!this.opts.includeEmptyRows && processedRow.every(function (field) {\n        return field === undefined;\n      })) {\n        return undefined;\n      }\n\n      return fastJoin$1(processedRow, this.opts.delimiter);\n    }\n    /**\n     * Create the content of a specfic CSV row cell\n     *\n     * @param {Object} row JSON object representing the  CSV row that the cell belongs to\n     * @param {FieldInfo} fieldInfo Details of the field to process to be a CSV cell\n     * @returns {String} CSV string (cell)\n     */\n\n  }, {\n    key: \"processCell\",\n    value: function processCell(row, fieldInfo) {\n      return this.processValue(fieldInfo.value(row));\n    }\n    /**\n     * Create the content of a specfic CSV row cell\n     *\n     * @param {Any} value Value to be included in a CSV cell\n     * @returns {String} Value stringified and processed\n     */\n\n  }, {\n    key: \"processValue\",\n    value: function processValue(value) {\n      if (value === null || value === undefined) {\n        return undefined;\n      }\n\n      var valueType = _typeof(value);\n\n      if (valueType !== 'boolean' && valueType !== 'number' && valueType !== 'string') {\n        value = JSON.stringify(value);\n\n        if (value === undefined) {\n          return undefined;\n        }\n\n        if (value[0] === '\"') {\n          value = value.replace(/^\"(.+)\"$/, '$1');\n        }\n      }\n\n      if (typeof value === 'string') {\n        if (this.opts.excelStrings) {\n          if (value.includes(this.opts.quote)) {\n            value = value.replace(new RegExp(this.opts.quote, 'g'), \"\".concat(this.opts.escapedQuote).concat(this.opts.escapedQuote));\n          }\n\n          value = \"\\\"=\\\"\\\"\".concat(value, \"\\\"\\\"\\\"\");\n        } else {\n          if (value.includes(this.opts.quote)) {\n            value = value.replace(new RegExp(this.opts.quote, 'g'), this.opts.escapedQuote);\n          }\n\n          value = \"\".concat(this.opts.quote).concat(value).concat(this.opts.quote);\n        }\n      }\n\n      return value;\n    }\n  }]);\n\n  return JSON2CSVBase;\n}();\n\nvar JSON2CSVBase_1 = JSON2CSVBase;\n\nvar fastJoin$2 = utils.fastJoin,\n    flattenReducer$2 = utils.flattenReducer;\n\nvar JSON2CSVParser =\n/*#__PURE__*/\nfunction (_JSON2CSVBase) {\n  _inherits(JSON2CSVParser, _JSON2CSVBase);\n\n  function JSON2CSVParser(opts) {\n    var _this;\n\n    _classCallCheck(this, JSON2CSVParser);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(JSON2CSVParser).call(this, opts));\n\n    if (_this.opts.fields) {\n      _this.opts.fields = _this.preprocessFieldsInfo(_this.opts.fields);\n    }\n\n    return _this;\n  }\n  /**\n   * Main function that converts json to csv.\n   *\n   * @param {Array|Object} data Array of JSON objects to be converted to CSV\n   * @returns {String} The CSV formated data as a string\n   */\n\n\n  _createClass(JSON2CSVParser, [{\n    key: \"parse\",\n    value: function parse(data) {\n      var processedData = this.preprocessData(data);\n\n      if (!this.opts.fields) {\n        this.opts.fields = processedData.reduce(function (fields, item) {\n          Object.keys(item).forEach(function (field) {\n            if (!fields.includes(field)) {\n              fields.push(field);\n            }\n          });\n          return fields;\n        }, []);\n        this.opts.fields = this.preprocessFieldsInfo(this.opts.fields);\n      }\n\n      var header = this.opts.header ? this.getHeader() : '';\n      var rows = this.processData(processedData);\n      var csv = (this.opts.withBOM ? \"\\uFEFF\" : '') + header + (header && rows ? this.opts.eol : '') + rows;\n      return csv;\n    }\n    /**\n     * Preprocess the data according to the give opts (unwind, flatten, etc.)\n      and calculate the fields and field names if they are not provided.\n     *\n     * @param {Array|Object} data Array or object to be converted to CSV\n     */\n\n  }, {\n    key: \"preprocessData\",\n    value: function preprocessData(data) {\n      var _this2 = this;\n\n      var processedData = Array.isArray(data) ? data : [data];\n\n      if (!this.opts.fields && (processedData.length === 0 || _typeof(processedData[0]) !== 'object')) {\n        throw new Error('Data should not be empty or the \"fields\" option should be included');\n      }\n\n      if (this.opts.transforms.length === 0) return processedData;\n      return processedData.map(function (row) {\n        return _this2.preprocessRow(row);\n      }).reduce(flattenReducer$2, []);\n    }\n    /**\n     * Create the content row by row below the header\n     *\n     * @param {Array} data Array of JSON objects to be converted to CSV\n     * @returns {String} CSV string (body)\n     */\n\n  }, {\n    key: \"processData\",\n    value: function processData(data) {\n      var _this3 = this;\n\n      return fastJoin$2(data.map(function (row) {\n        return _this3.processRow(row);\n      }).filter(function (row) {\n        return row;\n      }), // Filter empty rows\n      this.opts.eol);\n    }\n  }]);\n\n  return JSON2CSVParser;\n}(JSON2CSVBase_1);\n\nvar JSON2CSVParser_1 = JSON2CSVParser;\n\n/*global Buffer*/\n// Named constants with unique integer values\nvar C = {};\n// Tokens\nvar LEFT_BRACE    = C.LEFT_BRACE    = 0x1;\nvar RIGHT_BRACE   = C.RIGHT_BRACE   = 0x2;\nvar LEFT_BRACKET  = C.LEFT_BRACKET  = 0x3;\nvar RIGHT_BRACKET = C.RIGHT_BRACKET = 0x4;\nvar COLON         = C.COLON         = 0x5;\nvar COMMA         = C.COMMA         = 0x6;\nvar TRUE          = C.TRUE          = 0x7;\nvar FALSE         = C.FALSE         = 0x8;\nvar NULL          = C.NULL          = 0x9;\nvar STRING        = C.STRING        = 0xa;\nvar NUMBER        = C.NUMBER        = 0xb;\n// Tokenizer States\nvar START   = C.START   = 0x11;\nvar STOP    = C.STOP    = 0x12;\nvar TRUE1   = C.TRUE1   = 0x21;\nvar TRUE2   = C.TRUE2   = 0x22;\nvar TRUE3   = C.TRUE3   = 0x23;\nvar FALSE1  = C.FALSE1  = 0x31;\nvar FALSE2  = C.FALSE2  = 0x32;\nvar FALSE3  = C.FALSE3  = 0x33;\nvar FALSE4  = C.FALSE4  = 0x34;\nvar NULL1   = C.NULL1   = 0x41;\nvar NULL2   = C.NULL2   = 0x42;\nvar NULL3   = C.NULL3   = 0x43;\nvar NUMBER1 = C.NUMBER1 = 0x51;\nvar NUMBER3 = C.NUMBER3 = 0x53;\nvar STRING1 = C.STRING1 = 0x61;\nvar STRING2 = C.STRING2 = 0x62;\nvar STRING3 = C.STRING3 = 0x63;\nvar STRING4 = C.STRING4 = 0x64;\nvar STRING5 = C.STRING5 = 0x65;\nvar STRING6 = C.STRING6 = 0x66;\n// Parser States\nvar VALUE   = C.VALUE   = 0x71;\nvar KEY     = C.KEY     = 0x72;\n// Parser Modes\nvar OBJECT  = C.OBJECT  = 0x81;\nvar ARRAY   = C.ARRAY   = 0x82;\n// Character constants\nvar BACK_SLASH =      \"\\\\\".charCodeAt(0);\nvar FORWARD_SLASH =   \"\\/\".charCodeAt(0);\nvar BACKSPACE =       \"\\b\".charCodeAt(0);\nvar FORM_FEED =       \"\\f\".charCodeAt(0);\nvar NEWLINE =         \"\\n\".charCodeAt(0);\nvar CARRIAGE_RETURN = \"\\r\".charCodeAt(0);\nvar TAB =             \"\\t\".charCodeAt(0);\n\nvar STRING_BUFFER_SIZE = 64 * 1024;\n\nfunction Parser() {\n  this.tState = START;\n  this.value = undefined;\n\n  this.string = undefined; // string data\n  this.stringBuffer = Buffer.alloc ? Buffer.alloc(STRING_BUFFER_SIZE) : new Buffer(STRING_BUFFER_SIZE);\n  this.stringBufferOffset = 0;\n  this.unicode = undefined; // unicode escapes\n  this.highSurrogate = undefined;\n\n  this.key = undefined;\n  this.mode = undefined;\n  this.stack = [];\n  this.state = VALUE;\n  this.bytes_remaining = 0; // number of bytes remaining in multi byte utf8 char to read after split boundary\n  this.bytes_in_sequence = 0; // bytes in multi byte utf8 char to read\n  this.temp_buffs = { \"2\": new Buffer(2), \"3\": new Buffer(3), \"4\": new Buffer(4) }; // for rebuilding chars split before boundary is reached\n\n  // Stream offset\n  this.offset = -1;\n}\n\n// Slow code to string converter (only used when throwing syntax errors)\nParser.toknam = function (code) {\n  var keys = Object.keys(C);\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    if (C[key] === code) { return key; }\n  }\n  return code && (\"0x\" + code.toString(16));\n};\n\nvar proto = Parser.prototype;\nproto.onError = function (err) { throw err; };\nproto.charError = function (buffer, i) {\n  this.tState = STOP;\n  this.onError(new Error(\"Unexpected \" + JSON.stringify(String.fromCharCode(buffer[i])) + \" at position \" + i + \" in state \" + Parser.toknam(this.tState)));\n};\nproto.appendStringChar = function (char) {\n  if (this.stringBufferOffset >= STRING_BUFFER_SIZE) {\n    this.string += this.stringBuffer.toString('utf8');\n    this.stringBufferOffset = 0;\n  }\n\n  this.stringBuffer[this.stringBufferOffset++] = char;\n};\nproto.appendStringBuf = function (buf, start, end) {\n  var size = buf.length;\n  if (typeof start === 'number') {\n    if (typeof end === 'number') {\n      if (end < 0) {\n        // adding a negative end decreeses the size\n        size = buf.length - start + end;\n      } else {\n        size = end - start;\n      }\n    } else {\n      size = buf.length - start;\n    }\n  }\n\n  if (size < 0) {\n    size = 0;\n  }\n\n  if (this.stringBufferOffset + size > STRING_BUFFER_SIZE) {\n    this.string += this.stringBuffer.toString('utf8', 0, this.stringBufferOffset);\n    this.stringBufferOffset = 0;\n  }\n\n  buf.copy(this.stringBuffer, this.stringBufferOffset, start, end);\n  this.stringBufferOffset += size;\n};\nproto.write = function (buffer) {\n  if (typeof buffer === \"string\") buffer = new Buffer(buffer);\n  var n;\n  for (var i = 0, l = buffer.length; i < l; i++) {\n    if (this.tState === START){\n      n = buffer[i];\n      this.offset++;\n      if(n === 0x7b){ this.onToken(LEFT_BRACE, \"{\"); // {\n      }else if(n === 0x7d){ this.onToken(RIGHT_BRACE, \"}\"); // }\n      }else if(n === 0x5b){ this.onToken(LEFT_BRACKET, \"[\"); // [\n      }else if(n === 0x5d){ this.onToken(RIGHT_BRACKET, \"]\"); // ]\n      }else if(n === 0x3a){ this.onToken(COLON, \":\");  // :\n      }else if(n === 0x2c){ this.onToken(COMMA, \",\"); // ,\n      }else if(n === 0x74){ this.tState = TRUE1;  // t\n      }else if(n === 0x66){ this.tState = FALSE1;  // f\n      }else if(n === 0x6e){ this.tState = NULL1; // n\n      }else if(n === 0x22){ // \"\n        this.string = \"\";\n        this.stringBufferOffset = 0;\n        this.tState = STRING1;\n      }else if(n === 0x2d){ this.string = \"-\"; this.tState = NUMBER1; // -\n      }else{\n        if (n >= 0x30 && n < 0x40) { // 1-9\n          this.string = String.fromCharCode(n); this.tState = NUMBER3;\n        } else if (n === 0x20 || n === 0x09 || n === 0x0a || n === 0x0d) ; else {\n          return this.charError(buffer, i);\n        }\n      }\n    }else if (this.tState === STRING1){ // After open quote\n      n = buffer[i]; // get current byte from buffer\n      // check for carry over of a multi byte char split between data chunks\n      // & fill temp buffer it with start of this data chunk up to the boundary limit set in the last iteration\n      if (this.bytes_remaining > 0) {\n        for (var j = 0; j < this.bytes_remaining; j++) {\n          this.temp_buffs[this.bytes_in_sequence][this.bytes_in_sequence - this.bytes_remaining + j] = buffer[j];\n        }\n\n        this.appendStringBuf(this.temp_buffs[this.bytes_in_sequence]);\n        this.bytes_in_sequence = this.bytes_remaining = 0;\n        i = i + j - 1;\n      } else if (this.bytes_remaining === 0 && n >= 128) { // else if no remainder bytes carried over, parse multi byte (>=128) chars one at a time\n        if (n <= 193 || n > 244) {\n          return this.onError(new Error(\"Invalid UTF-8 character at position \" + i + \" in state \" + Parser.toknam(this.tState)));\n        }\n        if ((n >= 194) && (n <= 223)) this.bytes_in_sequence = 2;\n        if ((n >= 224) && (n <= 239)) this.bytes_in_sequence = 3;\n        if ((n >= 240) && (n <= 244)) this.bytes_in_sequence = 4;\n        if ((this.bytes_in_sequence + i) > buffer.length) { // if bytes needed to complete char fall outside buffer length, we have a boundary split\n          for (var k = 0; k <= (buffer.length - 1 - i); k++) {\n            this.temp_buffs[this.bytes_in_sequence][k] = buffer[i + k]; // fill temp buffer of correct size with bytes available in this chunk\n          }\n          this.bytes_remaining = (i + this.bytes_in_sequence) - buffer.length;\n          i = buffer.length - 1;\n        } else {\n          this.appendStringBuf(buffer, i, i + this.bytes_in_sequence);\n          i = i + this.bytes_in_sequence - 1;\n        }\n      } else if (n === 0x22) {\n        this.tState = START;\n        this.string += this.stringBuffer.toString('utf8', 0, this.stringBufferOffset);\n        this.stringBufferOffset = 0;\n        this.onToken(STRING, this.string);\n        this.offset += Buffer.byteLength(this.string, 'utf8') + 1;\n        this.string = undefined;\n      }\n      else if (n === 0x5c) {\n        this.tState = STRING2;\n      }\n      else if (n >= 0x20) { this.appendStringChar(n); }\n      else {\n          return this.charError(buffer, i);\n      }\n    }else if (this.tState === STRING2){ // After backslash\n      n = buffer[i];\n      if(n === 0x22){ this.appendStringChar(n); this.tState = STRING1;\n      }else if(n === 0x5c){ this.appendStringChar(BACK_SLASH); this.tState = STRING1;\n      }else if(n === 0x2f){ this.appendStringChar(FORWARD_SLASH); this.tState = STRING1;\n      }else if(n === 0x62){ this.appendStringChar(BACKSPACE); this.tState = STRING1;\n      }else if(n === 0x66){ this.appendStringChar(FORM_FEED); this.tState = STRING1;\n      }else if(n === 0x6e){ this.appendStringChar(NEWLINE); this.tState = STRING1;\n      }else if(n === 0x72){ this.appendStringChar(CARRIAGE_RETURN); this.tState = STRING1;\n      }else if(n === 0x74){ this.appendStringChar(TAB); this.tState = STRING1;\n      }else if(n === 0x75){ this.unicode = \"\"; this.tState = STRING3;\n      }else{\n        return this.charError(buffer, i);\n      }\n    }else if (this.tState === STRING3 || this.tState === STRING4 || this.tState === STRING5 || this.tState === STRING6){ // unicode hex codes\n      n = buffer[i];\n      // 0-9 A-F a-f\n      if ((n >= 0x30 && n < 0x40) || (n > 0x40 && n <= 0x46) || (n > 0x60 && n <= 0x66)) {\n        this.unicode += String.fromCharCode(n);\n        if (this.tState++ === STRING6) {\n          var intVal = parseInt(this.unicode, 16);\n          this.unicode = undefined;\n          if (this.highSurrogate !== undefined && intVal >= 0xDC00 && intVal < (0xDFFF + 1)) { //<56320,57343> - lowSurrogate\n            this.appendStringBuf(new Buffer(String.fromCharCode(this.highSurrogate, intVal)));\n            this.highSurrogate = undefined;\n          } else if (this.highSurrogate === undefined && intVal >= 0xD800 && intVal < (0xDBFF + 1)) { //<55296,56319> - highSurrogate\n            this.highSurrogate = intVal;\n          } else {\n            if (this.highSurrogate !== undefined) {\n              this.appendStringBuf(new Buffer(String.fromCharCode(this.highSurrogate)));\n              this.highSurrogate = undefined;\n            }\n            this.appendStringBuf(new Buffer(String.fromCharCode(intVal)));\n          }\n          this.tState = STRING1;\n        }\n      } else {\n        return this.charError(buffer, i);\n      }\n    } else if (this.tState === NUMBER1 || this.tState === NUMBER3) {\n        n = buffer[i];\n\n        switch (n) {\n          case 0x30: // 0\n          case 0x31: // 1\n          case 0x32: // 2\n          case 0x33: // 3\n          case 0x34: // 4\n          case 0x35: // 5\n          case 0x36: // 6\n          case 0x37: // 7\n          case 0x38: // 8\n          case 0x39: // 9\n          case 0x2e: // .\n          case 0x65: // e\n          case 0x45: // E\n          case 0x2b: // +\n          case 0x2d: // -\n            this.string += String.fromCharCode(n);\n            this.tState = NUMBER3;\n            break;\n          default:\n            this.tState = START;\n            var result = Number(this.string);\n\n            if (isNaN(result)){\n              return this.charError(buffer, i);\n            }\n\n            if ((this.string.match(/[0-9]+/) == this.string) && (result.toString() != this.string)) {\n              // Long string of digits which is an ID string and not valid and/or safe JavaScript integer Number\n              this.onToken(STRING, this.string);\n            } else {\n              this.onToken(NUMBER, result);\n            }\n\n            this.offset += this.string.length - 1;\n            this.string = undefined;\n            i--;\n            break;\n        }\n    }else if (this.tState === TRUE1){ // r\n      if (buffer[i] === 0x72) { this.tState = TRUE2; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === TRUE2){ // u\n      if (buffer[i] === 0x75) { this.tState = TRUE3; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === TRUE3){ // e\n      if (buffer[i] === 0x65) { this.tState = START; this.onToken(TRUE, true); this.offset+= 3; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === FALSE1){ // a\n      if (buffer[i] === 0x61) { this.tState = FALSE2; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === FALSE2){ // l\n      if (buffer[i] === 0x6c) { this.tState = FALSE3; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === FALSE3){ // s\n      if (buffer[i] === 0x73) { this.tState = FALSE4; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === FALSE4){ // e\n      if (buffer[i] === 0x65) { this.tState = START; this.onToken(FALSE, false); this.offset+= 4; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === NULL1){ // u\n      if (buffer[i] === 0x75) { this.tState = NULL2; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === NULL2){ // l\n      if (buffer[i] === 0x6c) { this.tState = NULL3; }\n      else { return this.charError(buffer, i); }\n    }else if (this.tState === NULL3){ // l\n      if (buffer[i] === 0x6c) { this.tState = START; this.onToken(NULL, null); this.offset += 3; }\n      else { return this.charError(buffer, i); }\n    }\n  }\n};\nproto.onToken = function (token, value) {\n  // Override this to get events\n};\n\nproto.parseError = function (token, value) {\n  this.tState = STOP;\n  this.onError(new Error(\"Unexpected \" + Parser.toknam(token) + (value ? (\"(\" + JSON.stringify(value) + \")\") : \"\") + \" in state \" + Parser.toknam(this.state)));\n};\nproto.push = function () {\n  this.stack.push({value: this.value, key: this.key, mode: this.mode});\n};\nproto.pop = function () {\n  var value = this.value;\n  var parent = this.stack.pop();\n  this.value = parent.value;\n  this.key = parent.key;\n  this.mode = parent.mode;\n  this.emit(value);\n  if (!this.mode) { this.state = VALUE; }\n};\nproto.emit = function (value) {\n  if (this.mode) { this.state = COMMA; }\n  this.onValue(value);\n};\nproto.onValue = function (value) {\n  // Override me\n};\nproto.onToken = function (token, value) {\n  if(this.state === VALUE){\n    if(token === STRING || token === NUMBER || token === TRUE || token === FALSE || token === NULL){\n      if (this.value) {\n        this.value[this.key] = value;\n      }\n      this.emit(value);\n    }else if(token === LEFT_BRACE){\n      this.push();\n      if (this.value) {\n        this.value = this.value[this.key] = {};\n      } else {\n        this.value = {};\n      }\n      this.key = undefined;\n      this.state = KEY;\n      this.mode = OBJECT;\n    }else if(token === LEFT_BRACKET){\n      this.push();\n      if (this.value) {\n        this.value = this.value[this.key] = [];\n      } else {\n        this.value = [];\n      }\n      this.key = 0;\n      this.mode = ARRAY;\n      this.state = VALUE;\n    }else if(token === RIGHT_BRACE){\n      if (this.mode === OBJECT) {\n        this.pop();\n      } else {\n        return this.parseError(token, value);\n      }\n    }else if(token === RIGHT_BRACKET){\n      if (this.mode === ARRAY) {\n        this.pop();\n      } else {\n        return this.parseError(token, value);\n      }\n    }else{\n      return this.parseError(token, value);\n    }\n  }else if(this.state === KEY){\n    if (token === STRING) {\n      this.key = value;\n      this.state = COLON;\n    } else if (token === RIGHT_BRACE) {\n      this.pop();\n    } else {\n      return this.parseError(token, value);\n    }\n  }else if(this.state === COLON){\n    if (token === COLON) { this.state = VALUE; }\n    else { return this.parseError(token, value); }\n  }else if(this.state === COMMA){\n    if (token === COMMA) {\n      if (this.mode === ARRAY) { this.key++; this.state = VALUE; }\n      else if (this.mode === OBJECT) { this.state = KEY; }\n\n    } else if (token === RIGHT_BRACKET && this.mode === ARRAY || token === RIGHT_BRACE && this.mode === OBJECT) {\n      this.pop();\n    } else {\n      return this.parseError(token, value);\n    }\n  }else{\n    return this.parseError(token, value);\n  }\n};\n\nParser.C = C;\n\nvar jsonparse = Parser;\n\nvar Transform = stream.Transform;\n\nvar JSON2CSVTransform =\n/*#__PURE__*/\nfunction (_Transform) {\n  _inherits(JSON2CSVTransform, _Transform);\n\n  function JSON2CSVTransform(opts, transformOpts) {\n    var _this;\n\n    _classCallCheck(this, JSON2CSVTransform);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(JSON2CSVTransform).call(this, transformOpts)); // Inherit methods from JSON2CSVBase since extends doesn't\n    // allow multiple inheritance and manually preprocess opts\n\n    Object.getOwnPropertyNames(JSON2CSVBase_1.prototype).forEach(function (key) {\n      return _this[key] = JSON2CSVBase_1.prototype[key];\n    });\n    _this.opts = _this.preprocessOpts(opts);\n    _this._data = '';\n    _this._hasWritten = false;\n\n    if (_this._readableState.objectMode) {\n      _this.initObjectModeParse();\n    } else if (_this.opts.ndjson) {\n      _this.initNDJSONParse();\n    } else {\n      _this.initJSONParser();\n    }\n\n    if (_this.opts.withBOM) {\n      _this.push(\"\\uFEFF\");\n    }\n\n    if (_this.opts.fields) {\n      _this.opts.fields = _this.preprocessFieldsInfo(_this.opts.fields);\n\n      _this.pushHeader();\n    }\n\n    return _this;\n  }\n  /**\n   * Init the transform with a parser to process data in object mode.\n   * It receives JSON objects one by one and send them to `pushLine for processing.\n   */\n\n\n  _createClass(JSON2CSVTransform, [{\n    key: \"initObjectModeParse\",\n    value: function initObjectModeParse() {\n      var transform = this;\n      this.parser = {\n        write: function write(line) {\n          transform.pushLine(line);\n        },\n        getPendingData: function getPendingData() {\n          return undefined;\n        }\n      };\n    }\n    /**\n     * Init the transform with a parser to process NDJSON data.\n     * It maintains a buffer of received data, parses each line\n     * as JSON and send it to `pushLine for processing.\n     */\n\n  }, {\n    key: \"initNDJSONParse\",\n    value: function initNDJSONParse() {\n      var transform = this;\n      this.parser = {\n        _data: '',\n        write: function write(chunk) {\n          this._data += chunk.toString();\n\n          var lines = this._data.split('\\n').map(function (line) {\n            return line.trim();\n          }).filter(function (line) {\n            return line !== '';\n          });\n\n          var pendingData = false;\n          lines.forEach(function (line, i) {\n            try {\n              transform.pushLine(JSON.parse(line));\n            } catch (e) {\n              if (i === lines.length - 1) {\n                pendingData = true;\n              } else {\n                e.message = \"Invalid JSON (\".concat(line, \")\");\n                transform.emit('error', e);\n              }\n            }\n          });\n          this._data = pendingData ? this._data.slice(this._data.lastIndexOf('\\n')) : '';\n        },\n        getPendingData: function getPendingData() {\n          return this._data;\n        }\n      };\n    }\n    /**\n     * Init the transform with a parser to process JSON data.\n     * It maintains a buffer of received data, parses each as JSON \n     * item if the data is an array or the data itself otherwise\n     * and send it to `pushLine` for processing.\n     */\n\n  }, {\n    key: \"initJSONParser\",\n    value: function initJSONParser() {\n      var transform = this;\n      this.parser = new jsonparse();\n\n      this.parser.onValue = function (value) {\n        if (this.stack.length !== this.depthToEmit) return;\n        transform.pushLine(value);\n      };\n\n      this.parser._onToken = this.parser.onToken;\n\n      this.parser.onToken = function (token, value) {\n        transform.parser._onToken(token, value);\n\n        if (this.stack.length === 0 && !transform.opts.fields && this.mode !== jsonparse.C.ARRAY && this.mode !== jsonparse.C.OBJECT) {\n          this.onError(new Error('Data should not be empty or the \"fields\" option should be included'));\n        }\n\n        if (this.stack.length === 1) {\n          if (this.depthToEmit === undefined) {\n            // If Array emit its content, else emit itself\n            this.depthToEmit = this.mode === jsonparse.C.ARRAY ? 1 : 0;\n          }\n\n          if (this.depthToEmit !== 0 && this.stack.length === 1) {\n            // No need to store the whole root array in memory\n            this.value = undefined;\n          }\n        }\n      };\n\n      this.parser.getPendingData = function () {\n        return this.value;\n      };\n\n      this.parser.onError = function (err) {\n        if (err.message.includes('Unexpected')) {\n          err.message = \"Invalid JSON (\".concat(err.message, \")\");\n        }\n\n        transform.emit('error', err);\n      };\n    }\n    /**\n     * Main function that send data to the parse to be processed.\n     *\n     * @param {Buffer} chunk Incoming data\n     * @param {String} encoding Encoding of the incoming data. Defaults to 'utf8'\n     * @param {Function} done Called when the proceesing of the supplied chunk is done\n     */\n\n  }, {\n    key: \"_transform\",\n    value: function _transform(chunk, encoding, done) {\n      this.parser.write(chunk);\n      done();\n    }\n  }, {\n    key: \"_flush\",\n    value: function _flush(done) {\n      if (this.parser.getPendingData()) {\n        done(new Error('Invalid data received from stdin', this.parser.getPendingData()));\n      }\n\n      done();\n    }\n    /**\n     * Generate the csv header and pushes it downstream.\n     */\n\n  }, {\n    key: \"pushHeader\",\n    value: function pushHeader() {\n      if (this.opts.header) {\n        var header = this.getHeader();\n        this.emit('header', header);\n        this.push(header);\n        this._hasWritten = true;\n      }\n    }\n    /**\n     * Transforms an incoming json data to csv and pushes it downstream.\n     *\n     * @param {Object} data JSON object to be converted in a CSV row\n     */\n\n  }, {\n    key: \"pushLine\",\n    value: function pushLine(data) {\n      var _this2 = this;\n\n      var processedData = this.preprocessRow(data);\n\n      if (!this._hasWritten) {\n        this.opts.fields = this.opts.fields || this.preprocessFieldsInfo(Object.keys(processedData[0]));\n        this.pushHeader();\n      }\n\n      processedData.forEach(function (row) {\n        var line = _this2.processRow(row, _this2.opts);\n\n        if (line === undefined) return;\n\n        _this2.emit('line', line);\n\n        _this2.push(_this2._hasWritten ? _this2.opts.eol + line : line);\n\n        _this2._hasWritten = true;\n      });\n    }\n  }]);\n\n  return JSON2CSVTransform;\n}(Transform);\n\nvar JSON2CSVTransform_1 = JSON2CSVTransform;\n\nvar Transform$1 = stream.Transform;\nvar fastJoin$3 = utils.fastJoin;\n\nvar JSON2CSVAsyncParser =\n/*#__PURE__*/\nfunction () {\n  function JSON2CSVAsyncParser(opts, transformOpts) {\n    _classCallCheck(this, JSON2CSVAsyncParser);\n\n    this.input = new Transform$1(transformOpts);\n\n    this.input._read = function () {};\n\n    this.transform = new JSON2CSVTransform_1(opts, transformOpts);\n    this.processor = this.input.pipe(this.transform);\n  }\n\n  _createClass(JSON2CSVAsyncParser, [{\n    key: \"fromInput\",\n    value: function fromInput(input) {\n      if (this._input) {\n        throw new Error('Async parser already has an input.');\n      }\n\n      this._input = input;\n      this.input = this._input.pipe(this.processor);\n      return this;\n    }\n  }, {\n    key: \"throughTransform\",\n    value: function throughTransform(transform) {\n      if (this._output) {\n        throw new Error('Can\\'t add transforms once an output has been added.');\n      }\n\n      this.processor = this.processor.pipe(transform);\n      return this;\n    }\n  }, {\n    key: \"toOutput\",\n    value: function toOutput(output) {\n      if (this._output) {\n        throw new Error('Async parser already has an output.');\n      }\n\n      this._output = output;\n      this.processor = this.processor.pipe(output);\n      return this;\n    }\n  }, {\n    key: \"promise\",\n    value: function promise() {\n      var _this = this;\n\n      var returnCSV = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      return new Promise(function (resolve, reject) {\n        if (!returnCSV) {\n          _this.processor.on('finish', function () {\n            return resolve();\n          }).on('error', function (err) {\n            return reject(err);\n          });\n\n          return;\n        }\n\n        var csvBuffer = [];\n\n        _this.processor.on('data', function (chunk) {\n          return csvBuffer.push(chunk.toString());\n        }).on('finish', function () {\n          return resolve(fastJoin$3(csvBuffer, ''));\n        }).on('error', function (err) {\n          return reject(err);\n        });\n      });\n    }\n  }]);\n\n  return JSON2CSVAsyncParser;\n}();\n\nvar JSON2CSVAsyncParser_1 = JSON2CSVAsyncParser;\n\n/**\n * Performs the flattening of a data row recursively\n *\n * @param {String} separator Separator to be used as the flattened field name\n * @returns {Object => Object} Flattened object\n */\nfunction flatten() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$objects = _ref.objects,\n      objects = _ref$objects === void 0 ? true : _ref$objects,\n      _ref$arrays = _ref.arrays,\n      arrays = _ref$arrays === void 0 ? false : _ref$arrays,\n      _ref$separator = _ref.separator,\n      separator = _ref$separator === void 0 ? '.' : _ref$separator;\n\n  function step(obj, flatDataRow, currentPath) {\n    Object.keys(obj).forEach(function (key) {\n      var newPath = currentPath ? \"\".concat(currentPath).concat(separator).concat(key) : key;\n      var value = obj[key];\n\n      if (objects && _typeof(value) === 'object' && value !== null && !Array.isArray(value) && Object.prototype.toString.call(value.toJSON) !== '[object Function]' && Object.keys(value).length) {\n        step(value, flatDataRow, newPath);\n        return;\n      }\n\n      if (arrays && Array.isArray(value)) {\n        step(value, flatDataRow, newPath);\n        return;\n      }\n\n      flatDataRow[newPath] = value;\n    });\n    return flatDataRow;\n  }\n\n  return function (dataRow) {\n    return step(dataRow, {});\n  };\n}\n\nvar flatten_1 = flatten;\n\nvar setProp$1 = utils.setProp,\n    unsetProp$1 = utils.unsetProp,\n    flattenReducer$3 = utils.flattenReducer;\n\nfunction getUnwindablePaths(obj, currentPath) {\n  return Object.keys(obj).reduce(function (unwindablePaths, key) {\n    var newPath = currentPath ? \"\".concat(currentPath, \".\").concat(key) : key;\n    var value = obj[key];\n\n    if (_typeof(value) === 'object' && value !== null && !Array.isArray(value) && Object.prototype.toString.call(value.toJSON) !== '[object Function]' && Object.keys(value).length) {\n      unwindablePaths = unwindablePaths.concat(getUnwindablePaths(value, newPath));\n    } else if (Array.isArray(value)) {\n      unwindablePaths.push(newPath);\n      unwindablePaths = unwindablePaths.concat(value.map(function (arrObj) {\n        return getUnwindablePaths(arrObj, newPath);\n      }).reduce(flattenReducer$3, []).filter(function (item, index, arr) {\n        return arr.indexOf(item) !== index;\n      }));\n    }\n\n    return unwindablePaths;\n  }, []);\n}\n/**\n * Performs the unwind recursively in specified sequence\n *\n * @param {String[]} unwindPaths The paths as strings to be used to deconstruct the array\n * @returns {Object => Array} Array of objects containing all rows after unwind of chosen paths\n*/\n\n\nfunction unwind() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$paths = _ref.paths,\n      paths = _ref$paths === void 0 ? undefined : _ref$paths,\n      _ref$blankOut = _ref.blankOut,\n      blankOut = _ref$blankOut === void 0 ? false : _ref$blankOut;\n\n  function unwindReducer(rows, unwindPath) {\n    return rows.map(function (row) {\n      var unwindArray = lodash_get(row, unwindPath);\n\n      if (!Array.isArray(unwindArray)) {\n        return row;\n      }\n\n      if (!unwindArray.length) {\n        return unsetProp$1(row, unwindPath);\n      }\n\n      return unwindArray.map(function (unwindRow, index) {\n        var clonedRow = blankOut && index > 0 ? {} : row;\n        return setProp$1(clonedRow, unwindPath, unwindRow);\n      });\n    }).reduce(flattenReducer$3, []);\n  }\n\n  paths = Array.isArray(paths) ? paths : paths ? [paths] : undefined;\n  return function (dataRow) {\n    return (paths || getUnwindablePaths(dataRow)).reduce(unwindReducer, [dataRow]);\n  };\n}\n\nvar unwind_1 = unwind;\n\nvar Readable = stream.Readable;\nvar Parser$1 = JSON2CSVParser_1;\nvar AsyncParser = JSON2CSVAsyncParser_1;\nvar Transform$2 = JSON2CSVTransform_1; // Convenience method to keep the API similar to version 3.X\n\nvar parse = function parse(data, opts) {\n  return new JSON2CSVParser_1(opts).parse(data);\n};\n\nvar parseAsync = function parseAsync(data, opts, transformOpts) {\n  try {\n    if (!(data instanceof Readable)) {\n      transformOpts = Object.assign({}, transformOpts, {\n        objectMode: true\n      });\n    }\n\n    var asyncParser = new JSON2CSVAsyncParser_1(opts, transformOpts);\n    var promise = asyncParser.promise();\n\n    if (Array.isArray(data)) {\n      data.forEach(function (item) {\n        return asyncParser.input.push(item);\n      });\n      asyncParser.input.push(null);\n    } else if (data instanceof Readable) {\n      asyncParser.fromInput(data);\n    } else {\n      asyncParser.input.push(data);\n      asyncParser.input.push(null);\n    }\n\n    return promise;\n  } catch (err) {\n    return Promise.reject(err);\n  }\n};\n\nvar transforms = {\n  flatten: flatten_1,\n  unwind: unwind_1\n};\nvar json2csv = {\n  Parser: Parser$1,\n  AsyncParser: AsyncParser,\n  Transform: Transform$2,\n  parse: parse,\n  parseAsync: parseAsync,\n  transforms: transforms\n};\n\nexport default json2csv;\nexport { AsyncParser, Parser$1 as Parser, Transform$2 as Transform, parse, parseAsync, transforms };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,kFAAkF,GAAG,mIAAmI;AAAA,QACvO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF,oBAAmB;AACnB,gBAAe;AAEf,SAAS,QAAQ,KAAK;AACpB,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUA,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ;AAC7B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,UAAU,OAAO,KAAK,MAAM;AAEhC,QAAI,OAAO,OAAO,0BAA0B,YAAY;AACtD,gBAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAU,KAAK;AAClF,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC,CAAC;AAAA,IACJ;AAEA,YAAQ,QAAQ,SAAU,KAAK;AAC7B,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,WAAY,iBAAgB,UAAU,UAAU;AACtD;AAEA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASC,iBAAgBC,IAAG;AAC5F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AACxE,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AAEA,SAAS,uBAAuBG,OAAM;AACpC,MAAIA,UAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAOA;AACT;AAEA,SAAS,2BAA2BA,OAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuBA,KAAI;AACpC;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,gBAAgB,GAAG,KAAK,iBAAiB,GAAG,KAAK,iBAAiB;AAC3E;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,mBAAmB;AAChF;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAS,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,QAAQ,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAElF,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,YAAY,OAAO,IAAI,KAAK,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,qBAAsB,QAAO,MAAM,KAAK,IAAI;AAC9H;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,iDAAiD;AACvE;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,sDAAsD;AAC5E;AAEA,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAY9L,IAAI,kBAAkB;AAGtB,IAAI,iBAAiB;AAGrB,IAAI,WAAW,IAAI;AAGnB,IAAI,UAAU;AAAd,IACI,SAAS;AADb,IAEI,YAAY;AAGhB,IAAI,eAAe;AAAnB,IACI,gBAAgB;AADpB,IAEI,eAAe;AAFnB,IAGI,aAAa;AAMjB,IAAI,eAAe;AAGnB,IAAI,eAAe;AAGnB,IAAI,eAAe;AAGnB,IAAI,aAAa,OAAO,kBAAkB,YAAY,kBAAkB,eAAe,WAAW,UAAU;AAG5G,IAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,IAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAU7D,SAAS,SAAS,QAAQ,KAAK;AAC7B,SAAO,UAAU,OAAO,SAAY,OAAO,GAAG;AAChD;AASA,SAAS,aAAa,OAAO;AAG3B,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,OAAO,MAAM,YAAY,YAAY;AACxD,QAAI;AACF,eAAS,CAAC,EAAE,QAAQ;AAAA,IACtB,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACA,SAAO;AACT;AAGA,IAAI,aAAa,MAAM;AAAvB,IACI,YAAY,SAAS;AADzB,IAEI,cAAc,OAAO;AAGzB,IAAI,aAAa,KAAK,oBAAoB;AAG1C,IAAI,aAAc,WAAW;AAC3B,MAAI,MAAM,SAAS,KAAK,cAAc,WAAW,QAAQ,WAAW,KAAK,YAAY,EAAE;AACvF,SAAO,MAAO,mBAAmB,MAAO;AAC1C,EAAE;AAGF,IAAI,eAAe,UAAU;AAG7B,IAAI,iBAAiB,YAAY;AAOjC,IAAI,iBAAiB,YAAY;AAGjC,IAAI,aAAa;AAAA,EAAO,MACtB,aAAa,KAAK,cAAc,EAAE,QAAQ,cAAc,MAAM,EAC7D,QAAQ,0DAA0D,OAAO,IAAI;AAChF;AAGA,IAAI,WAAW,KAAK;AAApB,IACI,SAAS,WAAW;AAGxB,IAAI,MAAM,UAAU,MAAM,KAAK;AAA/B,IACI,eAAe,UAAU,QAAQ,QAAQ;AAG7C,IAAI,cAAc,WAAW,SAAS,YAAY;AAAlD,IACI,iBAAiB,cAAc,YAAY,WAAW;AAS1D,SAAS,KAAK,SAAS;AACrB,MAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,OAAK,MAAM;AACX,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,QAAQ,KAAK;AACzB,SAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAC7B;AACF;AASA,SAAS,YAAY;AACnB,OAAK,WAAW,eAAe,aAAa,IAAI,IAAI,CAAC;AACvD;AAYA,SAAS,WAAW,KAAK;AACvB,SAAO,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK,SAAS,GAAG;AAClD;AAWA,SAAS,QAAQ,KAAK;AACpB,MAAI,OAAO,KAAK;AAChB,MAAI,cAAc;AAChB,QAAI,SAAS,KAAK,GAAG;AACrB,WAAO,WAAW,iBAAiB,SAAY;AAAA,EACjD;AACA,SAAO,eAAe,KAAK,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI;AACtD;AAWA,SAAS,QAAQ,KAAK;AACpB,MAAI,OAAO,KAAK;AAChB,SAAO,eAAe,KAAK,GAAG,MAAM,SAAY,eAAe,KAAK,MAAM,GAAG;AAC/E;AAYA,SAAS,QAAQ,KAAK,OAAO;AAC3B,MAAI,OAAO,KAAK;AAChB,OAAK,GAAG,IAAK,gBAAgB,UAAU,SAAa,iBAAiB;AACrE,SAAO;AACT;AAGA,KAAK,UAAU,QAAQ;AACvB,KAAK,UAAU,QAAQ,IAAI;AAC3B,KAAK,UAAU,MAAM;AACrB,KAAK,UAAU,MAAM;AACrB,KAAK,UAAU,MAAM;AASrB,SAAS,UAAU,SAAS;AAC1B,MAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,OAAK,MAAM;AACX,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,QAAQ,KAAK;AACzB,SAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAC7B;AACF;AASA,SAAS,iBAAiB;AACxB,OAAK,WAAW,CAAC;AACnB;AAWA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,MAAI,YAAY,KAAK,SAAS;AAC9B,MAAI,SAAS,WAAW;AACtB,SAAK,IAAI;AAAA,EACX,OAAO;AACL,WAAO,KAAK,MAAM,OAAO,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AAWA,SAAS,aAAa,KAAK;AACzB,MAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,SAAO,QAAQ,IAAI,SAAY,KAAK,KAAK,EAAE,CAAC;AAC9C;AAWA,SAAS,aAAa,KAAK;AACzB,SAAO,aAAa,KAAK,UAAU,GAAG,IAAI;AAC5C;AAYA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,MAAI,QAAQ,GAAG;AACb,SAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,EACxB,OAAO;AACL,SAAK,KAAK,EAAE,CAAC,IAAI;AAAA,EACnB;AACA,SAAO;AACT;AAGA,UAAU,UAAU,QAAQ;AAC5B,UAAU,UAAU,QAAQ,IAAI;AAChC,UAAU,UAAU,MAAM;AAC1B,UAAU,UAAU,MAAM;AAC1B,UAAU,UAAU,MAAM;AAS1B,SAAS,SAAS,SAAS;AACzB,MAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,OAAK,MAAM;AACX,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,QAAQ,KAAK;AACzB,SAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAC7B;AACF;AASA,SAAS,gBAAgB;AACvB,OAAK,WAAW;AAAA,IACd,QAAQ,IAAI;AAAA,IACZ,OAAO,KAAK,OAAO;AAAA,IACnB,UAAU,IAAI;AAAA,EAChB;AACF;AAWA,SAAS,eAAe,KAAK;AAC3B,SAAO,WAAW,MAAM,GAAG,EAAE,QAAQ,EAAE,GAAG;AAC5C;AAWA,SAAS,YAAY,KAAK;AACxB,SAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AACtC;AAWA,SAAS,YAAY,KAAK;AACxB,SAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AACtC;AAYA,SAAS,YAAY,KAAK,OAAO;AAC/B,aAAW,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK;AACpC,SAAO;AACT;AAGA,SAAS,UAAU,QAAQ;AAC3B,SAAS,UAAU,QAAQ,IAAI;AAC/B,SAAS,UAAU,MAAM;AACzB,SAAS,UAAU,MAAM;AACzB,SAAS,UAAU,MAAM;AAUzB,SAAS,aAAa,OAAO,KAAK;AAChC,MAAI,SAAS,MAAM;AACnB,SAAO,UAAU;AACf,QAAI,GAAG,MAAM,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,QAAQ,QAAQ,MAAM;AAC7B,SAAO,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;AAEnD,MAAI,QAAQ,GACR,SAAS,KAAK;AAElB,SAAO,UAAU,QAAQ,QAAQ,QAAQ;AACvC,aAAS,OAAO,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,EACtC;AACA,SAAQ,SAAS,SAAS,SAAU,SAAS;AAC/C;AAUA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACvC,WAAO;AAAA,EACT;AACA,MAAI,UAAW,WAAW,KAAK,KAAK,aAAa,KAAK,IAAK,aAAa;AACxE,SAAO,QAAQ,KAAK,SAAS,KAAK,CAAC;AACrC;AAUA,SAAS,aAAa,OAAO;AAE3B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,SAAS,KAAK,GAAG;AACnB,WAAO,iBAAiB,eAAe,KAAK,KAAK,IAAI;AAAA,EACvD;AACA,MAAI,SAAU,QAAQ;AACtB,SAAQ,UAAU,OAAQ,IAAI,SAAU,CAAC,WAAY,OAAO;AAC9D;AASA,SAAS,SAAS,OAAO;AACvB,SAAO,QAAQ,KAAK,IAAI,QAAQ,aAAa,KAAK;AACpD;AAUA,SAAS,WAAW,KAAK,KAAK;AAC5B,MAAI,OAAO,IAAI;AACf,SAAO,UAAU,GAAG,IAChB,KAAK,OAAO,OAAO,WAAW,WAAW,MAAM,IAC/C,KAAK;AACX;AAUA,SAAS,UAAU,QAAQ,KAAK;AAC9B,MAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,SAAO,aAAa,KAAK,IAAI,QAAQ;AACvC;AAUA,SAAS,MAAM,OAAO,QAAQ;AAC5B,MAAI,QAAQ,KAAK,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAO;AAClB,MAAI,QAAQ,YAAY,QAAQ,YAAY,QAAQ,aAChD,SAAS,QAAQ,SAAS,KAAK,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAO,cAAc,KAAK,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,KACzD,UAAU,QAAQ,SAAS,OAAO,MAAM;AAC7C;AASA,SAAS,UAAU,OAAO;AACxB,MAAI,OAAO,OAAO;AAClB,SAAQ,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YACvE,UAAU,cACV,UAAU;AACjB;AASA,SAAS,SAAS,MAAM;AACtB,SAAO,CAAC,CAAC,cAAe,cAAc;AACxC;AASA,IAAI,eAAe,QAAQ,SAAS,QAAQ;AAC1C,WAAS,SAAS,MAAM;AAExB,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,KAAK,MAAM,GAAG;AAC7B,WAAO,KAAK,EAAE;AAAA,EAChB;AACA,SAAO,QAAQ,YAAY,SAAS,OAAO,QAAQ,OAAOC,SAAQ;AAChE,WAAO,KAAK,QAAQA,QAAO,QAAQ,cAAc,IAAI,IAAK,UAAU,KAAM;AAAA,EAC5E,CAAC;AACD,SAAO;AACT,CAAC;AASD,SAAS,MAAM,OAAO;AACpB,MAAI,OAAO,SAAS,YAAY,SAAS,KAAK,GAAG;AAC/C,WAAO;AAAA,EACT;AACA,MAAI,SAAU,QAAQ;AACtB,SAAQ,UAAU,OAAQ,IAAI,SAAU,CAAC,WAAY,OAAO;AAC9D;AASA,SAAS,SAAS,MAAM;AACtB,MAAI,QAAQ,MAAM;AAChB,QAAI;AACF,aAAO,aAAa,KAAK,IAAI;AAAA,IAC/B,SAAS,GAAG;AAAA,IAAC;AACb,QAAI;AACF,aAAQ,OAAO;AAAA,IACjB,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACA,SAAO;AACT;AA8CA,SAAS,QAAQ,MAAM,UAAU;AAC/B,MAAI,OAAO,QAAQ,cAAe,YAAY,OAAO,YAAY,YAAa;AAC5E,UAAM,IAAI,UAAU,eAAe;AAAA,EACrC;AACA,MAAI,WAAW,WAAW;AACxB,QAAI,OAAO,WACP,MAAM,WAAW,SAAS,MAAM,MAAM,IAAI,IAAI,KAAK,CAAC,GACpD,QAAQ,SAAS;AAErB,QAAI,MAAM,IAAI,GAAG,GAAG;AAClB,aAAO,MAAM,IAAI,GAAG;AAAA,IACtB;AACA,QAAI,SAAS,KAAK,MAAM,MAAM,IAAI;AAClC,aAAS,QAAQ,MAAM,IAAI,KAAK,MAAM;AACtC,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,KAAK,QAAQ,SAAS;AACvC,SAAO;AACT;AAGA,QAAQ,QAAQ;AAkChB,SAAS,GAAG,OAAO,OAAO;AACxB,SAAO,UAAU,SAAU,UAAU,SAAS,UAAU;AAC1D;AAyBA,IAAI,UAAU,MAAM;AAmBpB,SAAS,WAAW,OAAO;AAGzB,MAAI,MAAM,SAAS,KAAK,IAAI,eAAe,KAAK,KAAK,IAAI;AACzD,SAAO,OAAO,WAAW,OAAO;AAClC;AA2BA,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO,OAAO;AAClB,SAAO,CAAC,CAAC,UAAU,QAAQ,YAAY,QAAQ;AACjD;AA0BA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AACpC;AAmBA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,eAAe,KAAK,KAAK,KAAK;AAC1D;AAuBA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,OAAO,KAAK,aAAa,KAAK;AAChD;AA2BA,SAAS,IAAI,QAAQ,MAAM,cAAc;AACvC,MAAI,SAAS,UAAU,OAAO,SAAY,QAAQ,QAAQ,IAAI;AAC9D,SAAO,WAAW,SAAY,eAAe;AAC/C;AAEA,IAAI,aAAa;AAEjB,SAAS,QAAQ,KAAK,MAAM,cAAc;AACxC,SAAO,IAAI,IAAI,MAAM,SAAY,eAAe,IAAI,IAAI;AAC1D;AAEA,SAAS,QAAQ,KAAK,MAAM,OAAO;AACjC,MAAI,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG;AAE3D,MAAI,aAAa,SAAS,SAAS,GAC/B,MAAM,WAAW,CAAC,GAClB,WAAW,WAAW,MAAM,CAAC;AAEjC,SAAO,cAAc,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,KAAK,UAAU,SAAS,IAAI,QAAQ,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,KAAK,IAAI,KAAK,CAAC;AACjI;AAEA,SAAS,UAAU,KAAK,MAAM;AAC5B,MAAI,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG;AAE3D,MAAI,cAAc,SAAS,SAAS,GAChC,MAAM,YAAY,CAAC,GACnB,WAAW,YAAY,MAAM,CAAC;AAElC,MAAI,QAAQ,IAAI,GAAG,CAAC,MAAM,UAAU;AAIlC,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,MAAM;AAC7C,aAAO,SAAS;AAAA,IAClB,CAAC,EAAE,OAAO,SAAU,KAAK,MAAM;AAC7B,aAAO,OAAO,OAAO,KAAK,gBAAgB,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC;AAAA,IAChE,GAAG,CAAC,CAAC;AAAA,EACP;AAEA,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,KAAK,MAAM;AAClD,WAAO,cAAc,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,MAAM,SAAS,MAAM,IAAI,IAAI,IAAI,UAAU,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC;AAAA,EACnH,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,eAAe,KAAK,KAAK;AAChC,MAAI;AAEF,QAAI,KAAK,MAAM,KAAK,mBAAmB,GAAG,CAAC;AAC3C,WAAO;AAAA,EACT,SAAS,KAAK;AAEZ,WAAO,IAAI,OAAO,GAAG;AAAA,EACvB;AACF;AAEA,SAAS,SAAS,KAAK,WAAW;AAChC,MAAI,UAAU;AACd,SAAO,IAAI,OAAO,SAAU,KAAK,MAAM;AACrC,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,aAAO;AAAA,IACT;AAEA,QAAI,SAAS;AACX,gBAAU;AACV,aAAO,GAAG,OAAO,IAAI;AAAA,IACvB;AAEA,WAAO,GAAG,OAAO,GAAG,EAAE,OAAO,SAAS,EAAE,OAAO,IAAI;AAAA,EACrD,GAAG,EAAE;AACP;AAEA,IAAI,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,YAAY,MAAM;AAAtB,IACI,aAAa,MAAM;AADvB,IAEI,mBAAmB,MAAM;AAE7B,IAAI,eAEJ,WAAY;AACV,WAASC,cAAa,MAAM;AAC1B,oBAAgB,MAAMA,aAAY;AAElC,SAAK,OAAO,KAAK,eAAe,IAAI;AAAA,EACtC;AASA,eAAaA,eAAc,CAAC;AAAA,IAC1B,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,MAAM;AACnC,UAAI,gBAAgB,OAAO,OAAO,CAAC,GAAG,IAAI;AAC1C,oBAAc,aAAa,CAAC,MAAM,QAAQ,cAAc,UAAU,IAAI,cAAc,aAAa,CAAC,cAAc,UAAU,IAAI,CAAC,IAAI,cAAc;AACjJ,oBAAc,YAAY,cAAc,aAAa;AACrD,oBAAc,MAAM,cAAc,OAAO,UAAAC,QAAG;AAC5C,oBAAc,QAAQ,OAAO,cAAc,UAAU,WAAW,cAAc,QAAQ;AACtF,oBAAc,eAAe,OAAO,cAAc,iBAAiB,WAAW,cAAc,eAAe,GAAG,OAAO,cAAc,KAAK,EAAE,OAAO,cAAc,KAAK;AACpK,oBAAc,SAAS,cAAc,WAAW;AAChD,oBAAc,mBAAmB,cAAc,oBAAoB;AACnE,oBAAc,UAAU,cAAc,WAAW;AACjD,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB,QAAQ;AAC3C,UAAI,QAAQ;AAEZ,aAAO,OAAO,IAAI,SAAU,WAAW;AACrC,YAAI,OAAO,cAAc,UAAU;AACjC,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO,UAAU,SAAS,GAAG,KAAK,UAAU,SAAS,GAAG,IAAI,SAAU,KAAK;AACzE,qBAAO,WAAW,KAAK,WAAW,MAAM,KAAK,YAAY;AAAA,YAC3D,IAAI,SAAU,KAAK;AACjB,qBAAO,UAAU,KAAK,WAAW,MAAM,KAAK,YAAY;AAAA,YAC1D;AAAA,UACF;AAAA,QACF;AAEA,YAAI,QAAQ,SAAS,MAAM,UAAU;AACnC,cAAI,eAAe,aAAa,YAAY,UAAU,UAAU,MAAM,KAAK;AAE3E,cAAI,OAAO,UAAU,UAAU,UAAU;AACvC,mBAAO;AAAA,cACL,OAAO,UAAU,SAAS,UAAU;AAAA,cACpC,OAAO,UAAU,MAAM,SAAS,GAAG,KAAK,UAAU,MAAM,SAAS,GAAG,IAAI,SAAU,KAAK;AACrF,uBAAO,WAAW,KAAK,UAAU,OAAO,YAAY;AAAA,cACtD,IAAI,SAAU,KAAK;AACjB,uBAAO,UAAU,KAAK,UAAU,OAAO,YAAY;AAAA,cACrD;AAAA,YACF;AAAA,UACF;AAEA,cAAI,OAAO,UAAU,UAAU,YAAY;AACzC,gBAAI,QAAQ,UAAU,SAAS,UAAU,MAAM,QAAQ;AACvD,gBAAI,QAAQ;AAAA,cACV;AAAA,cACA,SAAS;AAAA,YACX;AACA,mBAAO;AAAA,cACL;AAAA,cACA,OAAO,SAAS,MAAM,KAAK;AACzB,oBAAIC,SAAQ,UAAU,MAAM,KAAK,KAAK;AACtC,uBAAOA,WAAU,QAAQA,WAAU,SAAY,eAAeA;AAAA,cAChE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,cAAM,IAAI,MAAM,gCAAgC,KAAK,UAAU,SAAS,CAAC;AAAA,MAC3E,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,UAAI,SAAS;AAEb,aAAO,WAAW,KAAK,KAAK,OAAO,IAAI,SAAU,WAAW;AAC1D,eAAO,OAAO,aAAa,UAAU,KAAK;AAAA,MAC5C,CAAC,GAAG,KAAK,KAAK,SAAS;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,KAAK;AACjC,aAAO,KAAK,KAAK,WAAW,OAAO,SAAU,MAAM,WAAW;AAC5D,eAAO,KAAK,IAAI,SAAUC,MAAK;AAC7B,iBAAO,UAAUA,IAAG;AAAA,QACtB,CAAC,EAAE,OAAO,kBAAkB,CAAC,CAAC;AAAA,MAChC,GAAG,CAAC,GAAG,CAAC;AAAA,IACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,KAAK;AAC9B,UAAI,SAAS;AAEb,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,KAAK,KAAK,OAAO,IAAI,SAAU,WAAW;AAC3D,eAAO,OAAO,YAAY,KAAK,SAAS;AAAA,MAC1C,CAAC;AAED,UAAI,CAAC,KAAK,KAAK,oBAAoB,aAAa,MAAM,SAAU,OAAO;AACrE,eAAO,UAAU;AAAA,MACnB,CAAC,GAAG;AACF,eAAO;AAAA,MACT;AAEA,aAAO,WAAW,cAAc,KAAK,KAAK,SAAS;AAAA,IACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,KAAK,WAAW;AAC1C,aAAO,KAAK,aAAa,UAAU,MAAM,GAAG,CAAC;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,OAAO;AAClC,UAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,QAAQ,KAAK;AAE7B,UAAI,cAAc,aAAa,cAAc,YAAY,cAAc,UAAU;AAC/E,gBAAQ,KAAK,UAAU,KAAK;AAE5B,YAAI,UAAU,QAAW;AACvB,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,CAAC,MAAM,KAAK;AACpB,kBAAQ,MAAM,QAAQ,YAAY,IAAI;AAAA,QACxC;AAAA,MACF;AAEA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,KAAK,KAAK,cAAc;AAC1B,cAAI,MAAM,SAAS,KAAK,KAAK,KAAK,GAAG;AACnC,oBAAQ,MAAM,QAAQ,IAAI,OAAO,KAAK,KAAK,OAAO,GAAG,GAAG,GAAG,OAAO,KAAK,KAAK,YAAY,EAAE,OAAO,KAAK,KAAK,YAAY,CAAC;AAAA,UAC1H;AAEA,kBAAQ,OAAU,OAAO,OAAO,KAAQ;AAAA,QAC1C,OAAO;AACL,cAAI,MAAM,SAAS,KAAK,KAAK,KAAK,GAAG;AACnC,oBAAQ,MAAM,QAAQ,IAAI,OAAO,KAAK,KAAK,OAAO,GAAG,GAAG,KAAK,KAAK,YAAY;AAAA,UAChF;AAEA,kBAAQ,GAAG,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK,EAAE,OAAO,KAAK,KAAK,KAAK;AAAA,QACzE;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AAEF,SAAOH;AACT,EAAE;AAEF,IAAI,iBAAiB;AAErB,IAAI,aAAa,MAAM;AAAvB,IACI,mBAAmB,MAAM;AAE7B,IAAI,iBAEJ,SAAU,eAAe;AACvB,YAAUI,iBAAgB,aAAa;AAEvC,WAASA,gBAAe,MAAM;AAC5B,QAAI;AAEJ,oBAAgB,MAAMA,eAAc;AAEpC,YAAQ,2BAA2B,MAAM,gBAAgBA,eAAc,EAAE,KAAK,MAAM,IAAI,CAAC;AAEzF,QAAI,MAAM,KAAK,QAAQ;AACrB,YAAM,KAAK,SAAS,MAAM,qBAAqB,MAAM,KAAK,MAAM;AAAA,IAClE;AAEA,WAAO;AAAA,EACT;AASA,eAAaA,iBAAgB,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAASC,OAAM,MAAM;AAC1B,UAAI,gBAAgB,KAAK,eAAe,IAAI;AAE5C,UAAI,CAAC,KAAK,KAAK,QAAQ;AACrB,aAAK,KAAK,SAAS,cAAc,OAAO,SAAU,QAAQ,MAAM;AAC9D,iBAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,OAAO;AACzC,gBAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,qBAAO,KAAK,KAAK;AAAA,YACnB;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AACL,aAAK,KAAK,SAAS,KAAK,qBAAqB,KAAK,KAAK,MAAM;AAAA,MAC/D;AAEA,UAAI,SAAS,KAAK,KAAK,SAAS,KAAK,UAAU,IAAI;AACnD,UAAI,OAAO,KAAK,YAAY,aAAa;AACzC,UAAI,OAAO,KAAK,KAAK,UAAU,WAAW,MAAM,UAAU,UAAU,OAAO,KAAK,KAAK,MAAM,MAAM;AACjG,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,MAAM;AACnC,UAAI,SAAS;AAEb,UAAI,gBAAgB,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAEtD,UAAI,CAAC,KAAK,KAAK,WAAW,cAAc,WAAW,KAAK,QAAQ,cAAc,CAAC,CAAC,MAAM,WAAW;AAC/F,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,UAAI,KAAK,KAAK,WAAW,WAAW,EAAG,QAAO;AAC9C,aAAO,cAAc,IAAI,SAAU,KAAK;AACtC,eAAO,OAAO,cAAc,GAAG;AAAA,MACjC,CAAC,EAAE,OAAO,kBAAkB,CAAC,CAAC;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,MAAM;AAChC,UAAI,SAAS;AAEb,aAAO;AAAA,QAAW,KAAK,IAAI,SAAU,KAAK;AACxC,iBAAO,OAAO,WAAW,GAAG;AAAA,QAC9B,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,iBAAO;AAAA,QACT,CAAC;AAAA;AAAA,QACD,KAAK,KAAK;AAAA,MAAG;AAAA,IACf;AAAA,EACF,CAAC,CAAC;AAEF,SAAOD;AACT,EAAE,cAAc;AAEhB,IAAI,mBAAmB;AAIvB,IAAI,IAAI,CAAC;AAET,IAAI,aAAgB,EAAE,aAAgB;AACtC,IAAI,cAAgB,EAAE,cAAgB;AACtC,IAAI,eAAgB,EAAE,eAAgB;AACtC,IAAI,gBAAgB,EAAE,gBAAgB;AACtC,IAAI,QAAgB,EAAE,QAAgB;AACtC,IAAI,QAAgB,EAAE,QAAgB;AACtC,IAAI,OAAgB,EAAE,OAAgB;AACtC,IAAI,QAAgB,EAAE,QAAgB;AACtC,IAAI,OAAgB,EAAE,OAAgB;AACtC,IAAI,SAAgB,EAAE,SAAgB;AACtC,IAAI,SAAgB,EAAE,SAAgB;AAEtC,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,OAAU,EAAE,OAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,SAAU,EAAE,SAAU;AAC1B,IAAI,SAAU,EAAE,SAAU;AAC1B,IAAI,SAAU,EAAE,SAAU;AAC1B,IAAI,SAAU,EAAE,SAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAC1B,IAAI,UAAU,EAAE,UAAU;AAE1B,IAAI,QAAU,EAAE,QAAU;AAC1B,IAAI,MAAU,EAAE,MAAU;AAE1B,IAAI,SAAU,EAAE,SAAU;AAC1B,IAAI,QAAU,EAAE,QAAU;AAE1B,IAAI,aAAkB,KAAK,WAAW,CAAC;AACvC,IAAI,gBAAkB,IAAK,WAAW,CAAC;AACvC,IAAI,YAAkB,KAAK,WAAW,CAAC;AACvC,IAAI,YAAkB,KAAK,WAAW,CAAC;AACvC,IAAI,UAAkB,KAAK,WAAW,CAAC;AACvC,IAAI,kBAAkB,KAAK,WAAW,CAAC;AACvC,IAAI,MAAkB,IAAK,WAAW,CAAC;AAEvC,IAAI,qBAAqB,KAAK;AAE9B,SAAS,SAAS;AAChB,OAAK,SAAS;AACd,OAAK,QAAQ;AAEb,OAAK,SAAS;AACd,OAAK,eAAe,OAAO,QAAQ,OAAO,MAAM,kBAAkB,IAAI,IAAI,OAAO,kBAAkB;AACnG,OAAK,qBAAqB;AAC1B,OAAK,UAAU;AACf,OAAK,gBAAgB;AAErB,OAAK,MAAM;AACX,OAAK,OAAO;AACZ,OAAK,QAAQ,CAAC;AACd,OAAK,QAAQ;AACb,OAAK,kBAAkB;AACvB,OAAK,oBAAoB;AACzB,OAAK,aAAa,EAAE,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;AAG/E,OAAK,SAAS;AAChB;AAGA,OAAO,SAAS,SAAU,MAAM;AAC9B,MAAI,OAAO,OAAO,KAAK,CAAC;AACxB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,EAAE,GAAG,MAAM,MAAM;AAAE,aAAO;AAAA,IAAK;AAAA,EACrC;AACA,SAAO,QAAS,OAAO,KAAK,SAAS,EAAE;AACzC;AAEA,IAAI,QAAQ,OAAO;AACnB,MAAM,UAAU,SAAU,KAAK;AAAE,QAAM;AAAK;AAC5C,MAAM,YAAY,SAAU,QAAQ,GAAG;AACrC,OAAK,SAAS;AACd,OAAK,QAAQ,IAAI,MAAM,gBAAgB,KAAK,UAAU,OAAO,aAAa,OAAO,CAAC,CAAC,CAAC,IAAI,kBAAkB,IAAI,eAAe,OAAO,OAAO,KAAK,MAAM,CAAC,CAAC;AAC1J;AACA,MAAM,mBAAmB,SAAU,MAAM;AACvC,MAAI,KAAK,sBAAsB,oBAAoB;AACjD,SAAK,UAAU,KAAK,aAAa,SAAS,MAAM;AAChD,SAAK,qBAAqB;AAAA,EAC5B;AAEA,OAAK,aAAa,KAAK,oBAAoB,IAAI;AACjD;AACA,MAAM,kBAAkB,SAAU,KAAK,OAAO,KAAK;AACjD,MAAI,OAAO,IAAI;AACf,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,MAAM,GAAG;AAEX,eAAO,IAAI,SAAS,QAAQ;AAAA,MAC9B,OAAO;AACL,eAAO,MAAM;AAAA,MACf;AAAA,IACF,OAAO;AACL,aAAO,IAAI,SAAS;AAAA,IACtB;AAAA,EACF;AAEA,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,qBAAqB,OAAO,oBAAoB;AACvD,SAAK,UAAU,KAAK,aAAa,SAAS,QAAQ,GAAG,KAAK,kBAAkB;AAC5E,SAAK,qBAAqB;AAAA,EAC5B;AAEA,MAAI,KAAK,KAAK,cAAc,KAAK,oBAAoB,OAAO,GAAG;AAC/D,OAAK,sBAAsB;AAC7B;AACA,MAAM,QAAQ,SAAU,QAAQ;AAC9B,MAAI,OAAO,WAAW,SAAU,UAAS,IAAI,OAAO,MAAM;AAC1D,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,QAAI,KAAK,WAAW,OAAM;AACxB,UAAI,OAAO,CAAC;AACZ,WAAK;AACL,UAAG,MAAM,KAAK;AAAE,aAAK,QAAQ,YAAY,GAAG;AAAA,MAC5C,WAAS,MAAM,KAAK;AAAE,aAAK,QAAQ,aAAa,GAAG;AAAA,MACnD,WAAS,MAAM,IAAK;AAAE,aAAK,QAAQ,cAAc,GAAG;AAAA,MACpD,WAAS,MAAM,IAAK;AAAE,aAAK,QAAQ,eAAe,GAAG;AAAA,MACrD,WAAS,MAAM,IAAK;AAAE,aAAK,QAAQ,OAAO,GAAG;AAAA,MAC7C,WAAS,MAAM,IAAK;AAAE,aAAK,QAAQ,OAAO,GAAG;AAAA,MAC7C,WAAS,MAAM,KAAK;AAAE,aAAK,SAAS;AAAA,MACpC,WAAS,MAAM,KAAK;AAAE,aAAK,SAAS;AAAA,MACpC,WAAS,MAAM,KAAK;AAAE,aAAK,SAAS;AAAA,MACpC,WAAS,MAAM,IAAK;AAClB,aAAK,SAAS;AACd,aAAK,qBAAqB;AAC1B,aAAK,SAAS;AAAA,MAChB,WAAS,MAAM,IAAK;AAAE,aAAK,SAAS;AAAK,aAAK,SAAS;AAAA,MACvD,OAAK;AACH,YAAI,KAAK,MAAQ,IAAI,IAAM;AACzB,eAAK,SAAS,OAAO,aAAa,CAAC;AAAG,eAAK,SAAS;AAAA,QACtD,WAAW,MAAM,MAAQ,MAAM,KAAQ,MAAM,MAAQ,MAAM,GAAM;AAAA,aAAO;AACtE,iBAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IACF,WAAU,KAAK,WAAW,SAAQ;AAChC,UAAI,OAAO,CAAC;AAGZ,UAAI,KAAK,kBAAkB,GAAG;AAC5B,iBAAS,IAAI,GAAG,IAAI,KAAK,iBAAiB,KAAK;AAC7C,eAAK,WAAW,KAAK,iBAAiB,EAAE,KAAK,oBAAoB,KAAK,kBAAkB,CAAC,IAAI,OAAO,CAAC;AAAA,QACvG;AAEA,aAAK,gBAAgB,KAAK,WAAW,KAAK,iBAAiB,CAAC;AAC5D,aAAK,oBAAoB,KAAK,kBAAkB;AAChD,YAAI,IAAI,IAAI;AAAA,MACd,WAAW,KAAK,oBAAoB,KAAK,KAAK,KAAK;AACjD,YAAI,KAAK,OAAO,IAAI,KAAK;AACvB,iBAAO,KAAK,QAAQ,IAAI,MAAM,yCAAyC,IAAI,eAAe,OAAO,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,QACvH;AACA,YAAK,KAAK,OAAS,KAAK,IAAM,MAAK,oBAAoB;AACvD,YAAK,KAAK,OAAS,KAAK,IAAM,MAAK,oBAAoB;AACvD,YAAK,KAAK,OAAS,KAAK,IAAM,MAAK,oBAAoB;AACvD,YAAK,KAAK,oBAAoB,IAAK,OAAO,QAAQ;AAChD,mBAAS,IAAI,GAAG,KAAM,OAAO,SAAS,IAAI,GAAI,KAAK;AACjD,iBAAK,WAAW,KAAK,iBAAiB,EAAE,CAAC,IAAI,OAAO,IAAI,CAAC;AAAA,UAC3D;AACA,eAAK,kBAAmB,IAAI,KAAK,oBAAqB,OAAO;AAC7D,cAAI,OAAO,SAAS;AAAA,QACtB,OAAO;AACL,eAAK,gBAAgB,QAAQ,GAAG,IAAI,KAAK,iBAAiB;AAC1D,cAAI,IAAI,KAAK,oBAAoB;AAAA,QACnC;AAAA,MACF,WAAW,MAAM,IAAM;AACrB,aAAK,SAAS;AACd,aAAK,UAAU,KAAK,aAAa,SAAS,QAAQ,GAAG,KAAK,kBAAkB;AAC5E,aAAK,qBAAqB;AAC1B,aAAK,QAAQ,QAAQ,KAAK,MAAM;AAChC,aAAK,UAAU,OAAO,WAAW,KAAK,QAAQ,MAAM,IAAI;AACxD,aAAK,SAAS;AAAA,MAChB,WACS,MAAM,IAAM;AACnB,aAAK,SAAS;AAAA,MAChB,WACS,KAAK,IAAM;AAAE,aAAK,iBAAiB,CAAC;AAAA,MAAG,OAC3C;AACD,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MACnC;AAAA,IACF,WAAU,KAAK,WAAW,SAAQ;AAChC,UAAI,OAAO,CAAC;AACZ,UAAG,MAAM,IAAK;AAAE,aAAK,iBAAiB,CAAC;AAAG,aAAK,SAAS;AAAA,MACxD,WAAS,MAAM,IAAK;AAAE,aAAK,iBAAiB,UAAU;AAAG,aAAK,SAAS;AAAA,MACvE,WAAS,MAAM,IAAK;AAAE,aAAK,iBAAiB,aAAa;AAAG,aAAK,SAAS;AAAA,MAC1E,WAAS,MAAM,IAAK;AAAE,aAAK,iBAAiB,SAAS;AAAG,aAAK,SAAS;AAAA,MACtE,WAAS,MAAM,KAAK;AAAE,aAAK,iBAAiB,SAAS;AAAG,aAAK,SAAS;AAAA,MACtE,WAAS,MAAM,KAAK;AAAE,aAAK,iBAAiB,OAAO;AAAG,aAAK,SAAS;AAAA,MACpE,WAAS,MAAM,KAAK;AAAE,aAAK,iBAAiB,eAAe;AAAG,aAAK,SAAS;AAAA,MAC5E,WAAS,MAAM,KAAK;AAAE,aAAK,iBAAiB,GAAG;AAAG,aAAK,SAAS;AAAA,MAChE,WAAS,MAAM,KAAK;AAAE,aAAK,UAAU;AAAI,aAAK,SAAS;AAAA,MACvD,OAAK;AACH,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MACjC;AAAA,IACF,WAAU,KAAK,WAAW,WAAW,KAAK,WAAW,WAAW,KAAK,WAAW,WAAW,KAAK,WAAW,SAAQ;AACjH,UAAI,OAAO,CAAC;AAEZ,UAAK,KAAK,MAAQ,IAAI,MAAU,IAAI,MAAQ,KAAK,MAAU,IAAI,MAAQ,KAAK,KAAO;AACjF,aAAK,WAAW,OAAO,aAAa,CAAC;AACrC,YAAI,KAAK,aAAa,SAAS;AAC7B,cAAI,SAAS,SAAS,KAAK,SAAS,EAAE;AACtC,eAAK,UAAU;AACf,cAAI,KAAK,kBAAkB,UAAa,UAAU,SAAU,SAAU,QAAS,GAAI;AACjF,iBAAK,gBAAgB,IAAI,OAAO,OAAO,aAAa,KAAK,eAAe,MAAM,CAAC,CAAC;AAChF,iBAAK,gBAAgB;AAAA,UACvB,WAAW,KAAK,kBAAkB,UAAa,UAAU,SAAU,SAAU,QAAS,GAAI;AACxF,iBAAK,gBAAgB;AAAA,UACvB,OAAO;AACL,gBAAI,KAAK,kBAAkB,QAAW;AACpC,mBAAK,gBAAgB,IAAI,OAAO,OAAO,aAAa,KAAK,aAAa,CAAC,CAAC;AACxE,mBAAK,gBAAgB;AAAA,YACvB;AACA,iBAAK,gBAAgB,IAAI,OAAO,OAAO,aAAa,MAAM,CAAC,CAAC;AAAA,UAC9D;AACA,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,OAAO;AACL,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MACjC;AAAA,IACF,WAAW,KAAK,WAAW,WAAW,KAAK,WAAW,SAAS;AAC3D,UAAI,OAAO,CAAC;AAEZ,cAAQ,GAAG;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,UAAU,OAAO,aAAa,CAAC;AACpC,eAAK,SAAS;AACd;AAAA,QACF;AACE,eAAK,SAAS;AACd,cAAI,SAAS,OAAO,KAAK,MAAM;AAE/B,cAAI,MAAM,MAAM,GAAE;AAChB,mBAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,UACjC;AAEA,cAAK,KAAK,OAAO,MAAM,QAAQ,KAAK,KAAK,UAAY,OAAO,SAAS,KAAK,KAAK,QAAS;AAEtF,iBAAK,QAAQ,QAAQ,KAAK,MAAM;AAAA,UAClC,OAAO;AACL,iBAAK,QAAQ,QAAQ,MAAM;AAAA,UAC7B;AAEA,eAAK,UAAU,KAAK,OAAO,SAAS;AACpC,eAAK,SAAS;AACd;AACA;AAAA,MACJ;AAAA,IACJ,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAO,OAC1C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAO,OAC1C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAO,aAAK,QAAQ,MAAM,IAAI;AAAG,aAAK,UAAS;AAAA,MAAG,OACrF;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,QAAO;AAC/B,UAAI,OAAO,CAAC,MAAM,IAAM;AAAE,aAAK,SAAS;AAAA,MAAQ,OAC3C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,QAAO;AAC/B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAQ,OAC3C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,QAAO;AAC/B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAQ,OAC3C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,QAAO;AAC/B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAO,aAAK,QAAQ,OAAO,KAAK;AAAG,aAAK,UAAS;AAAA,MAAG,OACvF;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAO,OAC1C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAA,MAAO,OAC1C;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C,WAAU,KAAK,WAAW,OAAM;AAC9B,UAAI,OAAO,CAAC,MAAM,KAAM;AAAE,aAAK,SAAS;AAAO,aAAK,QAAQ,MAAM,IAAI;AAAG,aAAK,UAAU;AAAA,MAAG,OACtF;AAAE,eAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,MAAG;AAAA,IAC3C;AAAA,EACF;AACF;AACA,MAAM,UAAU,SAAU,OAAO,OAAO;AAExC;AAEA,MAAM,aAAa,SAAU,OAAO,OAAO;AACzC,OAAK,SAAS;AACd,OAAK,QAAQ,IAAI,MAAM,gBAAgB,OAAO,OAAO,KAAK,KAAK,QAAS,MAAM,KAAK,UAAU,KAAK,IAAI,MAAO,MAAM,eAAe,OAAO,OAAO,KAAK,KAAK,CAAC,CAAC;AAC9J;AACA,MAAM,OAAO,WAAY;AACvB,OAAK,MAAM,KAAK,EAAC,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,KAAI,CAAC;AACrE;AACA,MAAM,MAAM,WAAY;AACtB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK,MAAM,IAAI;AAC5B,OAAK,QAAQ,OAAO;AACpB,OAAK,MAAM,OAAO;AAClB,OAAK,OAAO,OAAO;AACnB,OAAK,KAAK,KAAK;AACf,MAAI,CAAC,KAAK,MAAM;AAAE,SAAK,QAAQ;AAAA,EAAO;AACxC;AACA,MAAM,OAAO,SAAU,OAAO;AAC5B,MAAI,KAAK,MAAM;AAAE,SAAK,QAAQ;AAAA,EAAO;AACrC,OAAK,QAAQ,KAAK;AACpB;AACA,MAAM,UAAU,SAAU,OAAO;AAEjC;AACA,MAAM,UAAU,SAAU,OAAO,OAAO;AACtC,MAAG,KAAK,UAAU,OAAM;AACtB,QAAG,UAAU,UAAU,UAAU,UAAU,UAAU,QAAQ,UAAU,SAAS,UAAU,MAAK;AAC7F,UAAI,KAAK,OAAO;AACd,aAAK,MAAM,KAAK,GAAG,IAAI;AAAA,MACzB;AACA,WAAK,KAAK,KAAK;AAAA,IACjB,WAAS,UAAU,YAAW;AAC5B,WAAK,KAAK;AACV,UAAI,KAAK,OAAO;AACd,aAAK,QAAQ,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC;AAAA,MACvC,OAAO;AACL,aAAK,QAAQ,CAAC;AAAA,MAChB;AACA,WAAK,MAAM;AACX,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IACd,WAAS,UAAU,cAAa;AAC9B,WAAK,KAAK;AACV,UAAI,KAAK,OAAO;AACd,aAAK,QAAQ,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC;AAAA,MACvC,OAAO;AACL,aAAK,QAAQ,CAAC;AAAA,MAChB;AACA,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACf,WAAS,UAAU,aAAY;AAC7B,UAAI,KAAK,SAAS,QAAQ;AACxB,aAAK,IAAI;AAAA,MACX,OAAO;AACL,eAAO,KAAK,WAAW,OAAO,KAAK;AAAA,MACrC;AAAA,IACF,WAAS,UAAU,eAAc;AAC/B,UAAI,KAAK,SAAS,OAAO;AACvB,aAAK,IAAI;AAAA,MACX,OAAO;AACL,eAAO,KAAK,WAAW,OAAO,KAAK;AAAA,MACrC;AAAA,IACF,OAAK;AACH,aAAO,KAAK,WAAW,OAAO,KAAK;AAAA,IACrC;AAAA,EACF,WAAS,KAAK,UAAU,KAAI;AAC1B,QAAI,UAAU,QAAQ;AACpB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf,WAAW,UAAU,aAAa;AAChC,WAAK,IAAI;AAAA,IACX,OAAO;AACL,aAAO,KAAK,WAAW,OAAO,KAAK;AAAA,IACrC;AAAA,EACF,WAAS,KAAK,UAAU,OAAM;AAC5B,QAAI,UAAU,OAAO;AAAE,WAAK,QAAQ;AAAA,IAAO,OACtC;AAAE,aAAO,KAAK,WAAW,OAAO,KAAK;AAAA,IAAG;AAAA,EAC/C,WAAS,KAAK,UAAU,OAAM;AAC5B,QAAI,UAAU,OAAO;AACnB,UAAI,KAAK,SAAS,OAAO;AAAE,aAAK;AAAO,aAAK,QAAQ;AAAA,MAAO,WAClD,KAAK,SAAS,QAAQ;AAAE,aAAK,QAAQ;AAAA,MAAK;AAAA,IAErD,WAAW,UAAU,iBAAiB,KAAK,SAAS,SAAS,UAAU,eAAe,KAAK,SAAS,QAAQ;AAC1G,WAAK,IAAI;AAAA,IACX,OAAO;AACL,aAAO,KAAK,WAAW,OAAO,KAAK;AAAA,IACrC;AAAA,EACF,OAAK;AACH,WAAO,KAAK,WAAW,OAAO,KAAK;AAAA,EACrC;AACF;AAEA,OAAO,IAAI;AAEX,IAAI,YAAY;AAEhB,IAAI,YAAY,cAAAE,QAAO;AAEvB,IAAI,oBAEJ,SAAU,YAAY;AACpB,YAAUC,oBAAmB,UAAU;AAEvC,WAASA,mBAAkB,MAAM,eAAe;AAC9C,QAAI;AAEJ,oBAAgB,MAAMA,kBAAiB;AAEvC,YAAQ,2BAA2B,MAAM,gBAAgBA,kBAAiB,EAAE,KAAK,MAAM,aAAa,CAAC;AAGrG,WAAO,oBAAoB,eAAe,SAAS,EAAE,QAAQ,SAAU,KAAK;AAC1E,aAAO,MAAM,GAAG,IAAI,eAAe,UAAU,GAAG;AAAA,IAClD,CAAC;AACD,UAAM,OAAO,MAAM,eAAe,IAAI;AACtC,UAAM,QAAQ;AACd,UAAM,cAAc;AAEpB,QAAI,MAAM,eAAe,YAAY;AACnC,YAAM,oBAAoB;AAAA,IAC5B,WAAW,MAAM,KAAK,QAAQ;AAC5B,YAAM,gBAAgB;AAAA,IACxB,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,MAAM,KAAK,SAAS;AACtB,YAAM,KAAK,QAAQ;AAAA,IACrB;AAEA,QAAI,MAAM,KAAK,QAAQ;AACrB,YAAM,KAAK,SAAS,MAAM,qBAAqB,MAAM,KAAK,MAAM;AAEhE,YAAM,WAAW;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAOA,eAAaA,oBAAmB,CAAC;AAAA,IAC/B,KAAK;AAAA,IACL,OAAO,SAAS,sBAAsB;AACpC,UAAI,YAAY;AAChB,WAAK,SAAS;AAAA,QACZ,OAAO,SAAS,MAAM,MAAM;AAC1B,oBAAU,SAAS,IAAI;AAAA,QACzB;AAAA,QACA,gBAAgB,SAAS,iBAAiB;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,UAAI,YAAY;AAChB,WAAK,SAAS;AAAA,QACZ,OAAO;AAAA,QACP,OAAO,SAAS,MAAM,OAAO;AAC3B,eAAK,SAAS,MAAM,SAAS;AAE7B,cAAI,QAAQ,KAAK,MAAM,MAAM,IAAI,EAAE,IAAI,SAAU,MAAM;AACrD,mBAAO,KAAK,KAAK;AAAA,UACnB,CAAC,EAAE,OAAO,SAAU,MAAM;AACxB,mBAAO,SAAS;AAAA,UAClB,CAAC;AAED,cAAI,cAAc;AAClB,gBAAM,QAAQ,SAAU,MAAM,GAAG;AAC/B,gBAAI;AACF,wBAAU,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,YACrC,SAAS,GAAG;AACV,kBAAI,MAAM,MAAM,SAAS,GAAG;AAC1B,8BAAc;AAAA,cAChB,OAAO;AACL,kBAAE,UAAU,iBAAiB,OAAO,MAAM,GAAG;AAC7C,0BAAU,KAAK,SAAS,CAAC;AAAA,cAC3B;AAAA,YACF;AAAA,UACF,CAAC;AACD,eAAK,QAAQ,cAAc,KAAK,MAAM,MAAM,KAAK,MAAM,YAAY,IAAI,CAAC,IAAI;AAAA,QAC9E;AAAA,QACA,gBAAgB,SAAS,iBAAiB;AACxC,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,YAAY;AAChB,WAAK,SAAS,IAAI,UAAU;AAE5B,WAAK,OAAO,UAAU,SAAU,OAAO;AACrC,YAAI,KAAK,MAAM,WAAW,KAAK,YAAa;AAC5C,kBAAU,SAAS,KAAK;AAAA,MAC1B;AAEA,WAAK,OAAO,WAAW,KAAK,OAAO;AAEnC,WAAK,OAAO,UAAU,SAAU,OAAO,OAAO;AAC5C,kBAAU,OAAO,SAAS,OAAO,KAAK;AAEtC,YAAI,KAAK,MAAM,WAAW,KAAK,CAAC,UAAU,KAAK,UAAU,KAAK,SAAS,UAAU,EAAE,SAAS,KAAK,SAAS,UAAU,EAAE,QAAQ;AAC5H,eAAK,QAAQ,IAAI,MAAM,oEAAoE,CAAC;AAAA,QAC9F;AAEA,YAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,cAAI,KAAK,gBAAgB,QAAW;AAElC,iBAAK,cAAc,KAAK,SAAS,UAAU,EAAE,QAAQ,IAAI;AAAA,UAC3D;AAEA,cAAI,KAAK,gBAAgB,KAAK,KAAK,MAAM,WAAW,GAAG;AAErD,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,WAAK,OAAO,iBAAiB,WAAY;AACvC,eAAO,KAAK;AAAA,MACd;AAEA,WAAK,OAAO,UAAU,SAAU,KAAK;AACnC,YAAI,IAAI,QAAQ,SAAS,YAAY,GAAG;AACtC,cAAI,UAAU,iBAAiB,OAAO,IAAI,SAAS,GAAG;AAAA,QACxD;AAEA,kBAAU,KAAK,SAAS,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,OAAO,UAAU,MAAM;AAChD,WAAK,OAAO,MAAM,KAAK;AACvB,WAAK;AAAA,IACP;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,MAAM;AAC3B,UAAI,KAAK,OAAO,eAAe,GAAG;AAChC,aAAK,IAAI,MAAM,oCAAoC,KAAK,OAAO,eAAe,CAAC,CAAC;AAAA,MAClF;AAEA,WAAK;AAAA,IACP;AAAA;AAAA;AAAA;AAAA,EAKF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,UAAI,KAAK,KAAK,QAAQ;AACpB,YAAI,SAAS,KAAK,UAAU;AAC5B,aAAK,KAAK,UAAU,MAAM;AAC1B,aAAK,KAAK,MAAM;AAChB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,MAAM;AAC7B,UAAI,SAAS;AAEb,UAAI,gBAAgB,KAAK,cAAc,IAAI;AAE3C,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,KAAK,SAAS,KAAK,KAAK,UAAU,KAAK,qBAAqB,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC;AAC9F,aAAK,WAAW;AAAA,MAClB;AAEA,oBAAc,QAAQ,SAAU,KAAK;AACnC,YAAI,OAAO,OAAO,WAAW,KAAK,OAAO,IAAI;AAE7C,YAAI,SAAS,OAAW;AAExB,eAAO,KAAK,QAAQ,IAAI;AAExB,eAAO,KAAK,OAAO,cAAc,OAAO,KAAK,MAAM,OAAO,IAAI;AAE9D,eAAO,cAAc;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE,SAAS;AAEX,IAAI,sBAAsB;AAE1B,IAAI,cAAc,cAAAD,QAAO;AACzB,IAAI,aAAa,MAAM;AAEvB,IAAI,sBAEJ,WAAY;AACV,WAASE,qBAAoB,MAAM,eAAe;AAChD,oBAAgB,MAAMA,oBAAmB;AAEzC,SAAK,QAAQ,IAAI,YAAY,aAAa;AAE1C,SAAK,MAAM,QAAQ,WAAY;AAAA,IAAC;AAEhC,SAAK,YAAY,IAAI,oBAAoB,MAAM,aAAa;AAC5D,SAAK,YAAY,KAAK,MAAM,KAAK,KAAK,SAAS;AAAA,EACjD;AAEA,eAAaA,sBAAqB,CAAC;AAAA,IACjC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO;AAC/B,UAAI,KAAK,QAAQ;AACf,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,WAAK,SAAS;AACd,WAAK,QAAQ,KAAK,OAAO,KAAK,KAAK,SAAS;AAC5C,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,WAAW;AAC1C,UAAI,KAAK,SAAS;AAChB,cAAM,IAAI,MAAM,qDAAsD;AAAA,MACxE;AAEA,WAAK,YAAY,KAAK,UAAU,KAAK,SAAS;AAC9C,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,QAAQ;AAC/B,UAAI,KAAK,SAAS;AAChB,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AAEA,WAAK,UAAU;AACf,WAAK,YAAY,KAAK,UAAU,KAAK,MAAM;AAC3C,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,QAAQ;AAEZ,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACpF,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,CAAC,WAAW;AACd,gBAAM,UAAU,GAAG,UAAU,WAAY;AACvC,mBAAO,QAAQ;AAAA,UACjB,CAAC,EAAE,GAAG,SAAS,SAAU,KAAK;AAC5B,mBAAO,OAAO,GAAG;AAAA,UACnB,CAAC;AAED;AAAA,QACF;AAEA,YAAI,YAAY,CAAC;AAEjB,cAAM,UAAU,GAAG,QAAQ,SAAU,OAAO;AAC1C,iBAAO,UAAU,KAAK,MAAM,SAAS,CAAC;AAAA,QACxC,CAAC,EAAE,GAAG,UAAU,WAAY;AAC1B,iBAAO,QAAQ,WAAW,WAAW,EAAE,CAAC;AAAA,QAC1C,CAAC,EAAE,GAAG,SAAS,SAAU,KAAK;AAC5B,iBAAO,OAAO,GAAG;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,IAAI,wBAAwB;AAQ5B,SAAS,UAAU;AACjB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,OAAO,cAC3C,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,QAAQ,aAC1C,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,MAAM;AAElD,WAAS,KAAK,KAAK,aAAa,aAAa;AAC3C,WAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,KAAK;AACtC,UAAI,UAAU,cAAc,GAAG,OAAO,WAAW,EAAE,OAAO,SAAS,EAAE,OAAO,GAAG,IAAI;AACnF,UAAI,QAAQ,IAAI,GAAG;AAEnB,UAAI,WAAW,QAAQ,KAAK,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,MAAM,uBAAuB,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC1L,aAAK,OAAO,aAAa,OAAO;AAChC;AAAA,MACF;AAEA,UAAI,UAAU,MAAM,QAAQ,KAAK,GAAG;AAClC,aAAK,OAAO,aAAa,OAAO;AAChC;AAAA,MACF;AAEA,kBAAY,OAAO,IAAI;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AAEA,SAAO,SAAU,SAAS;AACxB,WAAO,KAAK,SAAS,CAAC,CAAC;AAAA,EACzB;AACF;AAEA,IAAI,YAAY;AAEhB,IAAI,YAAY,MAAM;AAAtB,IACI,cAAc,MAAM;AADxB,IAEI,mBAAmB,MAAM;AAE7B,SAAS,mBAAmB,KAAK,aAAa;AAC5C,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,iBAAiB,KAAK;AAC7D,QAAI,UAAU,cAAc,GAAG,OAAO,aAAa,GAAG,EAAE,OAAO,GAAG,IAAI;AACtE,QAAI,QAAQ,IAAI,GAAG;AAEnB,QAAI,QAAQ,KAAK,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,MAAM,uBAAuB,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC/K,wBAAkB,gBAAgB,OAAO,mBAAmB,OAAO,OAAO,CAAC;AAAA,IAC7E,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,sBAAgB,KAAK,OAAO;AAC5B,wBAAkB,gBAAgB,OAAO,MAAM,IAAI,SAAU,QAAQ;AACnE,eAAO,mBAAmB,QAAQ,OAAO;AAAA,MAC3C,CAAC,EAAE,OAAO,kBAAkB,CAAC,CAAC,EAAE,OAAO,SAAU,MAAM,OAAO,KAAK;AACjE,eAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,MAC/B,CAAC,CAAC;AAAA,IACJ;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AASA,SAAS,SAAS;AAChB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,SAAY,YAC5C,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,QAAQ;AAElD,WAAS,cAAc,MAAM,YAAY;AACvC,WAAO,KAAK,IAAI,SAAU,KAAK;AAC7B,UAAI,cAAc,WAAW,KAAK,UAAU;AAE5C,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC/B,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,YAAY,QAAQ;AACvB,eAAO,YAAY,KAAK,UAAU;AAAA,MACpC;AAEA,aAAO,YAAY,IAAI,SAAU,WAAW,OAAO;AACjD,YAAI,YAAY,YAAY,QAAQ,IAAI,CAAC,IAAI;AAC7C,eAAO,UAAU,WAAW,YAAY,SAAS;AAAA,MACnD,CAAC;AAAA,IACH,CAAC,EAAE,OAAO,kBAAkB,CAAC,CAAC;AAAA,EAChC;AAEA,UAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI;AACzD,SAAO,SAAU,SAAS;AACxB,YAAQ,SAAS,mBAAmB,OAAO,GAAG,OAAO,eAAe,CAAC,OAAO,CAAC;AAAA,EAC/E;AACF;AAEA,IAAI,WAAW;AAEf,IAAI,WAAW,cAAAF,QAAO;AACtB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,cAAc;AAElB,IAAI,QAAQ,SAASD,OAAM,MAAM,MAAM;AACrC,SAAO,IAAI,iBAAiB,IAAI,EAAE,MAAM,IAAI;AAC9C;AAEA,IAAI,aAAa,SAASI,YAAW,MAAM,MAAM,eAAe;AAC9D,MAAI;AACF,QAAI,EAAE,gBAAgB,WAAW;AAC/B,sBAAgB,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,QAC/C,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,QAAI,cAAc,IAAI,sBAAsB,MAAM,aAAa;AAC/D,QAAI,UAAU,YAAY,QAAQ;AAElC,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAK,QAAQ,SAAU,MAAM;AAC3B,eAAO,YAAY,MAAM,KAAK,IAAI;AAAA,MACpC,CAAC;AACD,kBAAY,MAAM,KAAK,IAAI;AAAA,IAC7B,WAAW,gBAAgB,UAAU;AACnC,kBAAY,UAAU,IAAI;AAAA,IAC5B,OAAO;AACL,kBAAY,MAAM,KAAK,IAAI;AAC3B,kBAAY,MAAM,KAAK,IAAI;AAAA,IAC7B;AAEA,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,WAAO,QAAQ,OAAO,GAAG;AAAA,EAC3B;AACF;AAEA,IAAI,aAAa;AAAA,EACf,SAAS;AAAA,EACT,QAAQ;AACV;AACA,IAAI,WAAW;AAAA,EACb,QAAQ;AAAA,EACR;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,uBAAQ;", "names": ["obj", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "self", "string", "JSON2CSVBase", "os", "value", "row", "JSON2CSVParser", "parse", "stream", "JSON2CSVTransform", "JSON2CSVAsyncParser", "parseAsync"]}