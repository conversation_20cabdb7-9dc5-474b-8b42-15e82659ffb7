import {
  axios_default
} from "./chunk-EUIEE7YH.js";
import {
  require_events
} from "./chunk-HZ374QO7.js";
import {
  v4_default
} from "./chunk-RPXC7Q6H.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/is-retry-allowed/index.js
var require_is_retry_allowed = __commonJS({
  "node_modules/is-retry-allowed/index.js"(exports, module) {
    "use strict";
    var denyList = /* @__PURE__ */ new Set([
      "ENOTFOUND",
      "ENETUNREACH",
      // SSL errors from https://github.com/nodejs/node/blob/fc8e3e2cdc521978351de257030db0076d79e0ab/src/crypto/crypto_common.cc#L301-L328
      "UNABLE_TO_GET_ISSUER_CERT",
      "UNABLE_TO_GET_CRL",
      "UNABLE_TO_DECRYPT_CERT_SIGNATURE",
      "UNABLE_TO_DECRYPT_CRL_SIGNATURE",
      "UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY",
      "CERT_SIGNATURE_FAILURE",
      "CRL_SIGNATURE_FAILURE",
      "CERT_NOT_YET_VALID",
      "CERT_HAS_EXPIRED",
      "CRL_NOT_YET_VALID",
      "CRL_HAS_EXPIRED",
      "ERROR_IN_CERT_NOT_BEFORE_FIELD",
      "ERROR_IN_CERT_NOT_AFTER_FIELD",
      "ERROR_IN_CRL_LAST_UPDATE_FIELD",
      "ERROR_IN_CRL_NEXT_UPDATE_FIELD",
      "OUT_OF_MEM",
      "DEPTH_ZERO_SELF_SIGNED_CERT",
      "SELF_SIGNED_CERT_IN_CHAIN",
      "UNABLE_TO_GET_ISSUER_CERT_LOCALLY",
      "UNABLE_TO_VERIFY_LEAF_SIGNATURE",
      "CERT_CHAIN_TOO_LONG",
      "CERT_REVOKED",
      "INVALID_CA",
      "PATH_LENGTH_EXCEEDED",
      "INVALID_PURPOSE",
      "CERT_UNTRUSTED",
      "CERT_REJECTED",
      "HOSTNAME_MISMATCH"
    ]);
    module.exports = (error) => !denyList.has(error && error.code);
  }
});

// node_modules/@novu/shared/dist/esm/config/contextPath.js
var NovuComponentEnum;
(function(NovuComponentEnum2) {
  NovuComponentEnum2[NovuComponentEnum2["WEB"] = 0] = "WEB";
  NovuComponentEnum2[NovuComponentEnum2["API"] = 1] = "API";
  NovuComponentEnum2[NovuComponentEnum2["WIDGET"] = 2] = "WIDGET";
  NovuComponentEnum2[NovuComponentEnum2["WS"] = 3] = "WS";
})(NovuComponentEnum || (NovuComponentEnum = {}));

// node_modules/@novu/shared/dist/esm/config/job-queue.js
var JobTopicNameEnum;
(function(JobTopicNameEnum2) {
  JobTopicNameEnum2["EXECUTION_LOG"] = "execution-logs";
  JobTopicNameEnum2["ACTIVE_JOBS_METRIC"] = "metric-active-jobs";
  JobTopicNameEnum2["INBOUND_PARSE_MAIL"] = "inbound-parse-mail";
  JobTopicNameEnum2["STANDARD"] = "standard";
  JobTopicNameEnum2["WEB_SOCKETS"] = "ws_socket_queue";
  JobTopicNameEnum2["WORKFLOW"] = "trigger-handler";
  JobTopicNameEnum2["PROCESS_SUBSCRIBER"] = "process-subscriber";
})(JobTopicNameEnum || (JobTopicNameEnum = {}));
var ObservabilityBackgroundTransactionEnum;
(function(ObservabilityBackgroundTransactionEnum2) {
  ObservabilityBackgroundTransactionEnum2["JOB_PROCESSING_QUEUE"] = "job-processing-queue";
  ObservabilityBackgroundTransactionEnum2["SUBSCRIBER_PROCESSING_QUEUE"] = "subscriber-processing-queue";
  ObservabilityBackgroundTransactionEnum2["TRIGGER_HANDLER_QUEUE"] = "trigger-handler-queue";
  ObservabilityBackgroundTransactionEnum2["EXECUTION_LOG_QUEUE"] = "execution-log-queue";
  ObservabilityBackgroundTransactionEnum2["WS_SOCKET_QUEUE"] = "ws_socket_queue";
  ObservabilityBackgroundTransactionEnum2["WS_SOCKET_SOCKET_CONNECTION"] = "ws_socket_handle_connection";
  ObservabilityBackgroundTransactionEnum2["WS_SOCKET_HANDLE_DISCONNECT"] = "ws_socket_handle_disconnect";
  ObservabilityBackgroundTransactionEnum2["CRON_JOB_QUEUE"] = "cron-job-queue";
})(ObservabilityBackgroundTransactionEnum || (ObservabilityBackgroundTransactionEnum = {}));
var JobCronNameEnum;
(function(JobCronNameEnum2) {
  JobCronNameEnum2["SEND_CRON_METRICS"] = "send-cron-metrics";
  JobCronNameEnum2["CREATE_BILLING_USAGE_RECORDS"] = "create-billing-usage-records";
})(JobCronNameEnum || (JobCronNameEnum = {}));

// node_modules/@novu/shared/dist/esm/consts/providers/provider.enum.js
var CredentialsKeyEnum;
(function(CredentialsKeyEnum2) {
  CredentialsKeyEnum2["ApiKey"] = "apiKey";
  CredentialsKeyEnum2["User"] = "user";
  CredentialsKeyEnum2["SecretKey"] = "secretKey";
  CredentialsKeyEnum2["Domain"] = "domain";
  CredentialsKeyEnum2["Password"] = "password";
  CredentialsKeyEnum2["Host"] = "host";
  CredentialsKeyEnum2["Port"] = "port";
  CredentialsKeyEnum2["Secure"] = "secure";
  CredentialsKeyEnum2["Region"] = "region";
  CredentialsKeyEnum2["AccountSid"] = "accountSid";
  CredentialsKeyEnum2["MessageProfileId"] = "messageProfileId";
  CredentialsKeyEnum2["Token"] = "token";
  CredentialsKeyEnum2["From"] = "from";
  CredentialsKeyEnum2["SenderName"] = "senderName";
  CredentialsKeyEnum2["ContentType"] = "contentType";
  CredentialsKeyEnum2["ApplicationId"] = "applicationId";
  CredentialsKeyEnum2["ClientId"] = "clientId";
  CredentialsKeyEnum2["ProjectName"] = "projectName";
  CredentialsKeyEnum2["ServiceAccount"] = "serviceAccount";
  CredentialsKeyEnum2["BaseUrl"] = "baseUrl";
  CredentialsKeyEnum2["WebhookUrl"] = "webhookUrl";
  CredentialsKeyEnum2["RequireTls"] = "requireTls";
  CredentialsKeyEnum2["IgnoreTls"] = "ignoreTls";
  CredentialsKeyEnum2["TlsOptions"] = "tlsOptions";
  CredentialsKeyEnum2["RedirectUrl"] = "redirectUrl";
  CredentialsKeyEnum2["Hmac"] = "hmac";
  CredentialsKeyEnum2["IpPoolName"] = "ipPoolName";
  CredentialsKeyEnum2["ApiKeyRequestHeader"] = "apiKeyRequestHeader";
  CredentialsKeyEnum2["SecretKeyRequestHeader"] = "secretKeyRequestHeader";
  CredentialsKeyEnum2["IdPath"] = "idPath";
  CredentialsKeyEnum2["DatePath"] = "datePath";
  CredentialsKeyEnum2["AuthenticateByToken"] = "authenticateByToken";
  CredentialsKeyEnum2["AuthenticationTokenKey"] = "authenticationTokenKey";
  CredentialsKeyEnum2["AccessKey"] = "accessKey";
  CredentialsKeyEnum2["InstanceId"] = "instanceId";
  CredentialsKeyEnum2["ApiToken"] = "apiToken";
  CredentialsKeyEnum2["ApiURL"] = "apiURL";
  CredentialsKeyEnum2["AppID"] = "appID";
  CredentialsKeyEnum2["alertUid"] = "alertUid";
  CredentialsKeyEnum2["title"] = "title";
  CredentialsKeyEnum2["imageUrl"] = "imageUrl";
  CredentialsKeyEnum2["state"] = "state";
  CredentialsKeyEnum2["externalLink"] = "externalLink";
  CredentialsKeyEnum2["channelId"] = "channelId";
  CredentialsKeyEnum2["phoneNumberIdentification"] = "phoneNumberIdentification";
})(CredentialsKeyEnum || (CredentialsKeyEnum = {}));
var EmailProviderIdEnum;
(function(EmailProviderIdEnum2) {
  EmailProviderIdEnum2["EmailJS"] = "emailjs";
  EmailProviderIdEnum2["Mailgun"] = "mailgun";
  EmailProviderIdEnum2["Mailjet"] = "mailjet";
  EmailProviderIdEnum2["Mandrill"] = "mandrill";
  EmailProviderIdEnum2["CustomSMTP"] = "nodemailer";
  EmailProviderIdEnum2["Postmark"] = "postmark";
  EmailProviderIdEnum2["SendGrid"] = "sendgrid";
  EmailProviderIdEnum2["Sendinblue"] = "sendinblue";
  EmailProviderIdEnum2["SES"] = "ses";
  EmailProviderIdEnum2["NetCore"] = "netcore";
  EmailProviderIdEnum2["Infobip"] = "infobip-email";
  EmailProviderIdEnum2["Resend"] = "resend";
  EmailProviderIdEnum2["Plunk"] = "plunk";
  EmailProviderIdEnum2["MailerSend"] = "mailersend";
  EmailProviderIdEnum2["Mailtrap"] = "mailtrap";
  EmailProviderIdEnum2["Clickatell"] = "clickatell";
  EmailProviderIdEnum2["Outlook365"] = "outlook365";
  EmailProviderIdEnum2["Novu"] = "novu-email";
  EmailProviderIdEnum2["SparkPost"] = "sparkpost";
  EmailProviderIdEnum2["EmailWebhook"] = "email-webhook";
  EmailProviderIdEnum2["Braze"] = "braze";
})(EmailProviderIdEnum || (EmailProviderIdEnum = {}));
var SmsProviderIdEnum;
(function(SmsProviderIdEnum2) {
  SmsProviderIdEnum2["Nexmo"] = "nexmo";
  SmsProviderIdEnum2["Plivo"] = "plivo";
  SmsProviderIdEnum2["Sms77"] = "sms77";
  SmsProviderIdEnum2["SmsCentral"] = "sms-central";
  SmsProviderIdEnum2["SNS"] = "sns";
  SmsProviderIdEnum2["Telnyx"] = "telnyx";
  SmsProviderIdEnum2["Twilio"] = "twilio";
  SmsProviderIdEnum2["Gupshup"] = "gupshup";
  SmsProviderIdEnum2["Firetext"] = "firetext";
  SmsProviderIdEnum2["Infobip"] = "infobip-sms";
  SmsProviderIdEnum2["BurstSms"] = "burst-sms";
  SmsProviderIdEnum2["BulkSms"] = "bulk-sms";
  SmsProviderIdEnum2["ISendSms"] = "isend-sms";
  SmsProviderIdEnum2["Clickatell"] = "clickatell";
  SmsProviderIdEnum2["FortySixElks"] = "forty-six-elks";
  SmsProviderIdEnum2["Kannel"] = "kannel";
  SmsProviderIdEnum2["Maqsam"] = "maqsam";
  SmsProviderIdEnum2["Termii"] = "termii";
  SmsProviderIdEnum2["AfricasTalking"] = "africas-talking";
  SmsProviderIdEnum2["Novu"] = "novu-sms";
  SmsProviderIdEnum2["Sendchamp"] = "sendchamp";
  SmsProviderIdEnum2["GenericSms"] = "generic-sms";
  SmsProviderIdEnum2["Clicksend"] = "clicksend";
  SmsProviderIdEnum2["Bandwidth"] = "bandwidth";
  SmsProviderIdEnum2["MessageBird"] = "messagebird";
  SmsProviderIdEnum2["Simpletexting"] = "simpletexting";
  SmsProviderIdEnum2["AzureSms"] = "azure-sms";
  SmsProviderIdEnum2["RingCentral"] = "ring-central";
  SmsProviderIdEnum2["BrevoSms"] = "brevo-sms";
  SmsProviderIdEnum2["EazySms"] = "eazy-sms";
})(SmsProviderIdEnum || (SmsProviderIdEnum = {}));
var ChatProviderIdEnum;
(function(ChatProviderIdEnum2) {
  ChatProviderIdEnum2["Slack"] = "slack";
  ChatProviderIdEnum2["Discord"] = "discord";
  ChatProviderIdEnum2["MsTeams"] = "msteams";
  ChatProviderIdEnum2["Mattermost"] = "mattermost";
  ChatProviderIdEnum2["Ryver"] = "ryver";
  ChatProviderIdEnum2["Zulip"] = "zulip";
  ChatProviderIdEnum2["GrafanaOnCall"] = "grafana-on-call";
  ChatProviderIdEnum2["GetStream"] = "getstream";
  ChatProviderIdEnum2["RocketChat"] = "rocket-chat";
  ChatProviderIdEnum2["WhatsAppBusiness"] = "whatsapp-business";
})(ChatProviderIdEnum || (ChatProviderIdEnum = {}));
var PushProviderIdEnum;
(function(PushProviderIdEnum2) {
  PushProviderIdEnum2["FCM"] = "fcm";
  PushProviderIdEnum2["APNS"] = "apns";
  PushProviderIdEnum2["EXPO"] = "expo";
  PushProviderIdEnum2["OneSignal"] = "one-signal";
  PushProviderIdEnum2["Pushpad"] = "pushpad";
  PushProviderIdEnum2["PushWebhook"] = "push-webhook";
  PushProviderIdEnum2["PusherBeams"] = "pusher-beams";
})(PushProviderIdEnum || (PushProviderIdEnum = {}));
var InAppProviderIdEnum;
(function(InAppProviderIdEnum2) {
  InAppProviderIdEnum2["Novu"] = "novu";
})(InAppProviderIdEnum || (InAppProviderIdEnum = {}));

// node_modules/@novu/shared/dist/esm/consts/providers/credentials/secure-credentials.js
var secureCredentials = [
  CredentialsKeyEnum.ApiKey,
  CredentialsKeyEnum.ApiToken,
  CredentialsKeyEnum.SecretKey,
  CredentialsKeyEnum.Token,
  CredentialsKeyEnum.Password,
  CredentialsKeyEnum.ServiceAccount
];

// node_modules/@novu/shared/dist/esm/consts/providers/credentials/provider-credentials.js
var mailConfigBase = [
  {
    key: CredentialsKeyEnum.From,
    displayName: "From email address",
    description: "Use the same email address you used to authenticate your delivery provider",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SenderName,
    displayName: "Sender name",
    type: "string",
    required: true
  }
];
var smsConfigBase = [
  {
    key: CredentialsKeyEnum.From,
    displayName: "From",
    type: "string",
    required: true
  }
];
var pushConfigBase = [];
var mailJsConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var mailgunConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.BaseUrl,
    displayName: "Base URL",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.User,
    displayName: "User name",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Domain,
    displayName: "Domain",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var mailjetConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "API Secret",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var nexmoConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "API secret",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var mandrillConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var nodemailerConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "User",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.Host,
    displayName: "Host",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Port,
    displayName: "Port",
    type: "number",
    required: true
  },
  {
    key: CredentialsKeyEnum.Secure,
    displayName: "Secure",
    type: "boolean",
    required: false
  },
  {
    key: CredentialsKeyEnum.RequireTls,
    displayName: "Require TLS",
    type: "switch",
    required: false
  },
  {
    key: CredentialsKeyEnum.IgnoreTls,
    displayName: "Ignore TLS",
    type: "switch",
    required: false
  },
  {
    key: CredentialsKeyEnum.TlsOptions,
    displayName: "TLS options",
    type: "object",
    required: false
  },
  {
    key: CredentialsKeyEnum.Domain,
    displayName: "DKIM: Domain name",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "DKIM: Private key",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.AccountSid,
    displayName: "DKIM: Key selector",
    type: "string",
    required: false
  },
  ...mailConfigBase
];
var postmarkConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var sendgridConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.IpPoolName,
    displayName: "IP Pool Name",
    type: "string",
    required: false
  },
  ...mailConfigBase
];
var resendConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var mailtrapConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var plunkConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var sparkpostConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Region,
    displayName: "Region",
    description: "Use EU if your account is registered to SparkPost EU",
    type: "dropdown",
    required: false,
    value: null,
    dropdown: [
      { name: "Default", value: null },
      { name: "EU", value: "eu" }
    ]
  },
  ...mailConfigBase
];
var netCoreConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var sendinblueConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var sesConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Access key ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret access key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Region,
    displayName: "Region",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var mailerSendConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var plivoConfig = [
  {
    key: CredentialsKeyEnum.AccountSid,
    displayName: "Account SID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Token,
    displayName: "Auth token",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var sms77Config = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var termiiConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var burstSmsConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "API Secret",
    type: "string",
    required: true
  }
];
var bulkSmsConfig = [
  {
    key: CredentialsKeyEnum.ApiToken,
    displayName: "API Token",
    type: "string",
    required: true
  }
];
var iSendSmsConfig = [
  {
    key: CredentialsKeyEnum.ApiToken,
    displayName: "API Token",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.From,
    displayName: "Default Sender ID",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.ContentType,
    displayName: "Content Type",
    type: "dropdown",
    required: false,
    value: null,
    dropdown: [
      { name: "Default", value: null },
      { name: "Unicode", value: "unicode" },
      { name: "Plain", value: "plain" }
    ]
  }
];
var clickatellConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  }
];
var snsConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Access key ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret access key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Region,
    displayName: "AWS region",
    type: "string",
    required: true
  }
];
var telnyxConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.MessageProfileId,
    displayName: "Message profile ID",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var twilioConfig = [
  {
    key: CredentialsKeyEnum.AccountSid,
    displayName: "Account SID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Token,
    displayName: "Auth token",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var messagebirdConfig = [
  {
    key: CredentialsKeyEnum.AccessKey,
    displayName: "Access key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var slackConfig = [
  {
    key: CredentialsKeyEnum.ApplicationId,
    displayName: "Application Id",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.ClientId,
    displayName: "Client ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Client Secret",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.RedirectUrl,
    displayName: "Redirect URL",
    description: "Redirect after Slack OAuth flow finished (default behaviour will close the tab)",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.Hmac,
    displayName: "HMAC",
    type: "switch",
    required: false
  }
];
var grafanaOnCallConfig = [
  {
    key: CredentialsKeyEnum.alertUid,
    displayName: "Alert UID",
    type: "string",
    description: "a unique alert ID for grouping, maps to alert_uid of grafana webhook body content",
    required: false
  },
  {
    key: CredentialsKeyEnum.title,
    displayName: "Title.",
    type: "string",
    description: "title for the alert",
    required: false
  },
  {
    key: CredentialsKeyEnum.imageUrl,
    displayName: "Image URL",
    type: "string",
    description: "a URL for an image attached to alert, maps to image_url of grafana webhook body content",
    required: false
  },
  {
    key: CredentialsKeyEnum.state,
    displayName: "Alert State",
    type: "string",
    description: 'either "ok" or "alerting". Helpful for auto-resolving',
    required: false
  },
  {
    key: CredentialsKeyEnum.externalLink,
    displayName: "External Link",
    type: "string",
    description: 'link back to your monitoring system, maps to "link_to_upstream_details" of grafana webhook body content',
    required: false
  }
];
var getstreamConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  }
];
var fcmConfig = [
  {
    key: CredentialsKeyEnum.ServiceAccount,
    displayName: "Service Account (entire JSON file)",
    type: "text",
    required: true
  },
  ...pushConfigBase
];
var expoConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Access Token",
    type: "text",
    required: true
  },
  ...pushConfigBase
];
var pushWebhookConfig = [
  {
    key: CredentialsKeyEnum.WebhookUrl,
    displayName: "Webhook URL",
    type: "string",
    description: "the webhook URL to call to trigger push notifications",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret Hmac Key",
    type: "string",
    description: "the secret used to sign webhooks calls",
    required: true
  },
  ...pushConfigBase
];
var oneSignalConfig = [
  {
    key: CredentialsKeyEnum.ApplicationId,
    displayName: "Application ID",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "text",
    required: true
  },
  ...pushConfigBase
];
var pushpadConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Auth Token",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApplicationId,
    displayName: "Project ID",
    type: "text",
    required: true
  },
  ...pushConfigBase
];
var apnsConfig = [
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Private Key",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Key ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.ProjectName,
    displayName: "Team ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApplicationId,
    displayName: "Bundle ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Secure,
    displayName: "Production",
    type: "switch",
    required: false
  },
  ...pushConfigBase
];
var gupshupConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "User id",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: true
  }
];
var firetextConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var outlook365Config = [
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var infobipSMSConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.BaseUrl,
    displayName: "Base URL",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var infobipEmailConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.BaseUrl,
    displayName: "Base URL",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var brazeEmailConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiURL,
    displayName: "Base URL",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.AppID,
    displayName: "Base URL",
    type: "string",
    required: true
  },
  ...mailConfigBase
];
var fortySixElksConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var kannelConfig = [
  {
    key: CredentialsKeyEnum.Host,
    displayName: "Host",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Port,
    displayName: "Port",
    type: "number",
    required: true
  },
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    type: "string",
    required: false
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: false
  },
  ...smsConfigBase
];
var maqsamConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "Access Key ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Access Secret",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var smsCentralConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.BaseUrl,
    displayName: "Base URL",
    type: "string",
    required: false
  },
  ...smsConfigBase
];
var emailWebhookConfig = [
  {
    key: CredentialsKeyEnum.WebhookUrl,
    displayName: "Webhook URL",
    type: "string",
    description: "the webhook URL to call instead of sending the email",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret Hmac Key",
    type: "string",
    description: "the secret used to sign webhooks calls",
    required: true
  },
  ...mailConfigBase
];
var africasTalkingConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var novuInAppConfig = [
  {
    key: CredentialsKeyEnum.Hmac,
    displayName: "Security HMAC encryption",
    type: "switch",
    required: false,
    tooltip: {
      text: "When active it verifies if a request is performed by a specific user",
      when: false
    }
  }
];
var sendchampConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var clickSendConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    description: "Your Clicksend API username",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "text",
    required: true
  },
  ...smsConfigBase
];
var simpleTextingConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "text",
    required: true
  },
  ...smsConfigBase
];
var bandwidthConfig = [
  {
    key: CredentialsKeyEnum.User,
    displayName: "Username",
    description: "Your Bandwidth account username",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.Password,
    displayName: "Password",
    type: "password",
    required: true
  },
  {
    key: CredentialsKeyEnum.AccountSid,
    displayName: "Account ID",
    type: "text",
    required: true
  },
  ...smsConfigBase
];
var genericSmsConfig = [
  {
    key: CredentialsKeyEnum.BaseUrl,
    displayName: "Base URL",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiKeyRequestHeader,
    displayName: "API Key Request Header",
    type: "string",
    description: "The name of the header attribute to use for the API key ex. (X-API-KEY, apiKey, ...)",
    required: true
  },
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    description: "The value of the header attribute to use for the API key.",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKeyRequestHeader,
    displayName: "Secret Key Request Header",
    type: "string",
    description: "The name of the header attribute to use for the secret key ex. (X-SECRET-KEY, secretKey, ...)",
    required: false
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret Key",
    type: "string",
    description: "The value of the header attribute to use for the secret key",
    required: false
  },
  {
    key: CredentialsKeyEnum.IdPath,
    displayName: "Id Path",
    type: "string",
    value: "data.id",
    description: "The path to the id field in the response data ex. (id, message.id, ...)",
    required: true
  },
  {
    key: CredentialsKeyEnum.DatePath,
    displayName: "Date Path",
    type: "string",
    value: "data.date",
    description: "The path to the date field in the response data ex. (date, message.date, ...)",
    required: false
  },
  {
    key: CredentialsKeyEnum.AuthenticateByToken,
    displayName: "Authenticate by token",
    type: "switch",
    description: "If enabled, the API key and secret key will be sent as a token in the Authorization header",
    required: false
  },
  {
    key: CredentialsKeyEnum.Domain,
    displayName: "Auth URL",
    type: "string",
    description: "The URL to use for authentication in case the Authenticate by token option is enabled",
    required: false,
    tooltip: {
      text: "The URL to use for authentication in case the Authenticate by token option is enabled",
      when: true
    }
  },
  {
    key: CredentialsKeyEnum.AuthenticationTokenKey,
    displayName: "Authentication Token Key",
    type: "string",
    description: "The name of the header attribute to use for the authentication token ex. (X-AUTH-TOKEN, auth-token, ...)",
    required: false
  },
  ...smsConfigBase
];
var pusherBeamsConfig = [
  {
    key: CredentialsKeyEnum.InstanceId,
    displayName: "Instance ID",
    description: "The unique identifier for your Beams instance",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Secret Key",
    description: "The secret key your server will use to access your Beams instance",
    type: "string",
    required: true
  },
  ...pushConfigBase
];
var azureSmsConfig = [
  {
    key: CredentialsKeyEnum.AccessKey,
    displayName: "Connection string",
    description: "Your Azure account connection string",
    type: "text",
    required: true
  },
  ...smsConfigBase
];
var rocketChatConfig = [
  {
    key: CredentialsKeyEnum.Token,
    displayName: "Personal Access Token (x-auth-token)",
    description: "Personal Access Token of your user",
    type: "text",
    required: true
  },
  {
    key: CredentialsKeyEnum.User,
    displayName: "User id (x-user-id)",
    description: "Your User id",
    type: "text",
    required: true
  }
];
var ringCentralConfig = [
  {
    key: CredentialsKeyEnum.ClientId,
    displayName: "Client ID",
    description: "Your RingCentral app client ID",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.SecretKey,
    displayName: "Client secret",
    description: "Your RingCentral app client secret",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.Secure,
    displayName: "Is sandbox",
    type: "switch",
    required: false
  },
  {
    key: CredentialsKeyEnum.Token,
    displayName: "JWT token",
    description: "Your RingCentral user JWT token",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var brevoSmsConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  ...smsConfigBase
];
var eazySmsConfig = [
  {
    key: CredentialsKeyEnum.ApiKey,
    displayName: "API Key",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.channelId,
    displayName: "SMS Channel Id",
    type: "string",
    required: true,
    description: "Your SMS Channel Id"
  }
];
var whatsAppBusinessConfig = [
  {
    key: CredentialsKeyEnum.ApiToken,
    displayName: "Access API token",
    description: "Your WhatsApp Business access API token",
    type: "string",
    required: true
  },
  {
    key: CredentialsKeyEnum.phoneNumberIdentification,
    displayName: "Phone Number Identification",
    description: "Your WhatsApp Business phone number identification",
    type: "string",
    required: true
  }
];

// node_modules/@novu/shared/dist/esm/types/builder/builder.types.js
var FieldOperatorEnum;
(function(FieldOperatorEnum2) {
  FieldOperatorEnum2["ALL_IN"] = "ALL_IN";
  FieldOperatorEnum2["ANY_IN"] = "ANY_IN";
  FieldOperatorEnum2["BETWEEN"] = "BETWEEN";
  FieldOperatorEnum2["EQUAL"] = "EQUAL";
  FieldOperatorEnum2["IN"] = "IN";
  FieldOperatorEnum2["IS_DEFINED"] = "IS_DEFINED";
  FieldOperatorEnum2["LARGER"] = "LARGER";
  FieldOperatorEnum2["LARGER_EQUAL"] = "LARGER_EQUAL";
  FieldOperatorEnum2["LIKE"] = "LIKE";
  FieldOperatorEnum2["NOT_BETWEEN"] = "NOT_BETWEEN";
  FieldOperatorEnum2["NOT_EQUAL"] = "NOT_EQUAL";
  FieldOperatorEnum2["NOT_IN"] = "NOT_IN";
  FieldOperatorEnum2["NOT_LIKE"] = "NOT_LIKE";
  FieldOperatorEnum2["SMALLER"] = "SMALLER";
  FieldOperatorEnum2["SMALLER_EQUAL"] = "SMALLER_EQUAL";
})(FieldOperatorEnum || (FieldOperatorEnum = {}));
var FieldLogicalOperatorEnum;
(function(FieldLogicalOperatorEnum2) {
  FieldLogicalOperatorEnum2["AND"] = "AND";
  FieldLogicalOperatorEnum2["OR"] = "OR";
})(FieldLogicalOperatorEnum || (FieldLogicalOperatorEnum = {}));

// node_modules/@novu/shared/dist/esm/types/builder/filter.types.js
var TimeOperatorEnum;
(function(TimeOperatorEnum2) {
  TimeOperatorEnum2["DAYS"] = "days";
  TimeOperatorEnum2["HOURS"] = "hours";
  TimeOperatorEnum2["MINUTES"] = "minutes";
})(TimeOperatorEnum || (TimeOperatorEnum = {}));
var FilterPartTypeEnum;
(function(FilterPartTypeEnum2) {
  FilterPartTypeEnum2["PAYLOAD"] = "payload";
  FilterPartTypeEnum2["SUBSCRIBER"] = "subscriber";
  FilterPartTypeEnum2["WEBHOOK"] = "webhook";
  FilterPartTypeEnum2["IS_ONLINE"] = "isOnline";
  FilterPartTypeEnum2["IS_ONLINE_IN_LAST"] = "isOnlineInLast";
  FilterPartTypeEnum2["PREVIOUS_STEP"] = "previousStep";
  FilterPartTypeEnum2["TENANT"] = "tenant";
})(FilterPartTypeEnum || (FilterPartTypeEnum = {}));
var PreviousStepTypeEnum;
(function(PreviousStepTypeEnum2) {
  PreviousStepTypeEnum2["READ"] = "read";
  PreviousStepTypeEnum2["UNREAD"] = "unread";
  PreviousStepTypeEnum2["SEEN"] = "seen";
  PreviousStepTypeEnum2["UNSEEN"] = "unseen";
})(PreviousStepTypeEnum || (PreviousStepTypeEnum = {}));

// node_modules/@novu/shared/dist/esm/types/channel/index.js
var ChannelTypeEnum;
(function(ChannelTypeEnum2) {
  ChannelTypeEnum2["IN_APP"] = "in_app";
  ChannelTypeEnum2["EMAIL"] = "email";
  ChannelTypeEnum2["SMS"] = "sms";
  ChannelTypeEnum2["CHAT"] = "chat";
  ChannelTypeEnum2["PUSH"] = "push";
})(ChannelTypeEnum || (ChannelTypeEnum = {}));
var ActionTypeEnum;
(function(ActionTypeEnum2) {
  ActionTypeEnum2["TRIGGER"] = "trigger";
  ActionTypeEnum2["DIGEST"] = "digest";
  ActionTypeEnum2["DELAY"] = "delay";
  ActionTypeEnum2["CUSTOM"] = "custom";
})(ActionTypeEnum || (ActionTypeEnum = {}));
var StepTypeEnum;
(function(StepTypeEnum2) {
  StepTypeEnum2["IN_APP"] = "in_app";
  StepTypeEnum2["EMAIL"] = "email";
  StepTypeEnum2["SMS"] = "sms";
  StepTypeEnum2["CHAT"] = "chat";
  StepTypeEnum2["PUSH"] = "push";
  StepTypeEnum2["DIGEST"] = "digest";
  StepTypeEnum2["TRIGGER"] = "trigger";
  StepTypeEnum2["DELAY"] = "delay";
  StepTypeEnum2["CUSTOM"] = "custom";
})(StepTypeEnum || (StepTypeEnum = {}));
var STEP_TYPE_TO_CHANNEL_TYPE = /* @__PURE__ */ new Map([
  [StepTypeEnum.IN_APP, ChannelTypeEnum.IN_APP],
  [StepTypeEnum.EMAIL, ChannelTypeEnum.EMAIL],
  [StepTypeEnum.SMS, ChannelTypeEnum.SMS],
  [StepTypeEnum.CHAT, ChannelTypeEnum.CHAT],
  [StepTypeEnum.PUSH, ChannelTypeEnum.PUSH]
]);
var ChannelCTATypeEnum;
(function(ChannelCTATypeEnum2) {
  ChannelCTATypeEnum2["REDIRECT"] = "redirect";
})(ChannelCTATypeEnum || (ChannelCTATypeEnum = {}));
var TemplateVariableTypeEnum;
(function(TemplateVariableTypeEnum2) {
  TemplateVariableTypeEnum2["STRING"] = "String";
  TemplateVariableTypeEnum2["ARRAY"] = "Array";
  TemplateVariableTypeEnum2["BOOLEAN"] = "Boolean";
})(TemplateVariableTypeEnum || (TemplateVariableTypeEnum = {}));
var ActorTypeEnum;
(function(ActorTypeEnum2) {
  ActorTypeEnum2["NONE"] = "none";
  ActorTypeEnum2["USER"] = "user";
  ActorTypeEnum2["SYSTEM_ICON"] = "system_icon";
  ActorTypeEnum2["SYSTEM_CUSTOM"] = "system_custom";
})(ActorTypeEnum || (ActorTypeEnum = {}));
var SystemAvatarIconEnum;
(function(SystemAvatarIconEnum2) {
  SystemAvatarIconEnum2["WARNING"] = "warning";
  SystemAvatarIconEnum2["INFO"] = "info";
  SystemAvatarIconEnum2["ERROR"] = "error";
  SystemAvatarIconEnum2["SUCCESS"] = "success";
  SystemAvatarIconEnum2["UP"] = "up";
  SystemAvatarIconEnum2["QUESTION"] = "question";
})(SystemAvatarIconEnum || (SystemAvatarIconEnum = {}));
var CHANNELS_WITH_PRIMARY = [ChannelTypeEnum.EMAIL, ChannelTypeEnum.SMS];
var DELAYED_STEPS = [StepTypeEnum.DELAY, StepTypeEnum.DIGEST];

// node_modules/@novu/shared/dist/esm/types/events/index.js
var TriggerEventStatusEnum;
(function(TriggerEventStatusEnum2) {
  TriggerEventStatusEnum2["ERROR"] = "error";
  TriggerEventStatusEnum2["NOT_ACTIVE"] = "trigger_not_active";
  TriggerEventStatusEnum2["NO_WORKFLOW_ACTIVE_STEPS"] = "no_workflow_active_steps_defined";
  TriggerEventStatusEnum2["NO_WORKFLOW_STEPS"] = "no_workflow_steps_defined";
  TriggerEventStatusEnum2["PROCESSED"] = "processed";
  TriggerEventStatusEnum2["SUBSCRIBER_MISSING"] = "subscriber_id_missing";
  TriggerEventStatusEnum2["TENANT_MISSING"] = "no_tenant_found";
})(TriggerEventStatusEnum || (TriggerEventStatusEnum = {}));
var TriggerRecipientsTypeEnum;
(function(TriggerRecipientsTypeEnum2) {
  TriggerRecipientsTypeEnum2["SUBSCRIBER"] = "Subscriber";
  TriggerRecipientsTypeEnum2["TOPIC"] = "Topic";
})(TriggerRecipientsTypeEnum || (TriggerRecipientsTypeEnum = {}));
var AddressingTypeEnum;
(function(AddressingTypeEnum2) {
  AddressingTypeEnum2["BROADCAST"] = "broadcast";
  AddressingTypeEnum2["MULTICAST"] = "multicast";
})(AddressingTypeEnum || (AddressingTypeEnum = {}));
var TriggerRequestCategoryEnum;
(function(TriggerRequestCategoryEnum2) {
  TriggerRequestCategoryEnum2["SINGLE"] = "single";
  TriggerRequestCategoryEnum2["BULK"] = "bulk";
})(TriggerRequestCategoryEnum || (TriggerRequestCategoryEnum = {}));

// node_modules/@novu/shared/dist/esm/types/feature-flags/feature-flags.js
var FeatureFlagsKeysEnum;
(function(FeatureFlagsKeysEnum2) {
  FeatureFlagsKeysEnum2["IS_TEMPLATE_STORE_ENABLED"] = "IS_TEMPLATE_STORE_ENABLED";
  FeatureFlagsKeysEnum2["IS_USE_MERGED_DIGEST_ID_ENABLED"] = "IS_USE_MERGED_DIGEST_ID_ENABLED";
  FeatureFlagsKeysEnum2["IS_API_RATE_LIMITING_ENABLED"] = "IS_API_RATE_LIMITING_ENABLED";
  FeatureFlagsKeysEnum2["IS_API_IDEMPOTENCY_ENABLED"] = "IS_API_IDEMPOTENCY_ENABLED";
  FeatureFlagsKeysEnum2["IS_API_EXECUTION_LOG_QUEUE_ENABLED"] = "IS_API_EXECUTION_LOG_QUEUE_ENABLED";
  FeatureFlagsKeysEnum2["IS_BILLING_ENABLED"] = "IS_BILLING_ENABLED";
  FeatureFlagsKeysEnum2["IS_ECHO_ENABLED"] = "IS_ECHO_ENABLED";
  FeatureFlagsKeysEnum2["IS_IMPROVED_ONBOARDING_ENABLED"] = "IS_IMPROVED_ONBOARDING_ENABLED";
  FeatureFlagsKeysEnum2["IS_NEW_MESSAGES_API_RESPONSE_ENABLED"] = "IS_NEW_MESSAGES_API_RESPONSE_ENABLED";
  FeatureFlagsKeysEnum2["IS_INFORMATION_ARCHITECTURE_ENABLED"] = "IS_INFORMATION_ARCHITECTURE_ENABLED";
  FeatureFlagsKeysEnum2["IS_BILLING_REVERSE_TRIAL_ENABLED"] = "IS_BILLING_REVERSE_TRIAL_ENABLED";
  FeatureFlagsKeysEnum2["IS_HUBSPOT_ONBOARDING_ENABLED"] = "IS_HUBSPOT_ONBOARDING_ENABLED";
  FeatureFlagsKeysEnum2["IS_QUOTA_LIMITING_ENABLED"] = "IS_QUOTA_LIMITING_ENABLED";
  FeatureFlagsKeysEnum2["IS_EVENT_QUOTA_LIMITING_ENABLED"] = "IS_EVENT_QUOTA_LIMITING_ENABLED";
  FeatureFlagsKeysEnum2["IS_TEAM_MEMBER_INVITE_NUDGE_ENABLED"] = "IS_TEAM_MEMBER_INVITE_NUDGE_ENABLED";
})(FeatureFlagsKeysEnum || (FeatureFlagsKeysEnum = {}));

// node_modules/@novu/shared/dist/esm/types/feature-flags/system-critical-flags.js
var SystemCriticalFlagsEnum;
(function(SystemCriticalFlagsEnum2) {
  SystemCriticalFlagsEnum2["IS_IN_MEMORY_CLUSTER_MODE_ENABLED"] = "IS_IN_MEMORY_CLUSTER_MODE_ENABLED";
})(SystemCriticalFlagsEnum || (SystemCriticalFlagsEnum = {}));

// node_modules/@novu/shared/dist/esm/types/message-template/index.js
var EmailBlockTypeEnum;
(function(EmailBlockTypeEnum2) {
  EmailBlockTypeEnum2["BUTTON"] = "button";
  EmailBlockTypeEnum2["TEXT"] = "text";
})(EmailBlockTypeEnum || (EmailBlockTypeEnum = {}));
var TextAlignEnum;
(function(TextAlignEnum2) {
  TextAlignEnum2["CENTER"] = "center";
  TextAlignEnum2["LEFT"] = "left";
  TextAlignEnum2["RIGHT"] = "right";
})(TextAlignEnum || (TextAlignEnum = {}));

// node_modules/@novu/shared/dist/esm/types/organization/index.js
var ApiServiceLevelEnum;
(function(ApiServiceLevelEnum2) {
  ApiServiceLevelEnum2["FREE"] = "free";
  ApiServiceLevelEnum2["BUSINESS"] = "business";
  ApiServiceLevelEnum2["ENTERPRISE"] = "enterprise";
  ApiServiceLevelEnum2["UNLIMITED"] = "unlimited";
})(ApiServiceLevelEnum || (ApiServiceLevelEnum = {}));
var ProductUseCasesEnum;
(function(ProductUseCasesEnum2) {
  ProductUseCasesEnum2["IN_APP"] = "in_app";
  ProductUseCasesEnum2["MULTI_CHANNEL"] = "multi_channel";
  ProductUseCasesEnum2["DELAY"] = "delay";
  ProductUseCasesEnum2["TRANSLATION"] = "translation";
  ProductUseCasesEnum2["DIGEST"] = "digest";
})(ProductUseCasesEnum || (ProductUseCasesEnum = {}));
var JobTitleEnum;
(function(JobTitleEnum2) {
  JobTitleEnum2["ENGINEER"] = "engineer";
  JobTitleEnum2["ENGINEERING_MANAGER"] = "engineering_manager";
  JobTitleEnum2["ARCHITECT"] = "architect";
  JobTitleEnum2["PRODUCT_MANAGER"] = "product_manager";
  JobTitleEnum2["DESIGNER"] = "designer";
  JobTitleEnum2["FOUNDER"] = "cxo_founder";
  JobTitleEnum2["MARKETING_MANAGER"] = "marketing_manager";
  JobTitleEnum2["OTHER"] = "other";
})(JobTitleEnum || (JobTitleEnum = {}));
var jobTitleToLabelMapper = {
  [JobTitleEnum.ENGINEER]: "Engineer",
  [JobTitleEnum.ARCHITECT]: "Architect",
  [JobTitleEnum.PRODUCT_MANAGER]: "Product Manager",
  [JobTitleEnum.DESIGNER]: "Designer",
  [JobTitleEnum.ENGINEERING_MANAGER]: "Engineering Manager",
  [JobTitleEnum.FOUNDER]: "CXO Founder",
  [JobTitleEnum.MARKETING_MANAGER]: "Marketing Manager",
  [JobTitleEnum.OTHER]: "Other"
};

// node_modules/@novu/shared/dist/esm/types/pagination/index.js
var OrderDirectionEnum;
(function(OrderDirectionEnum2) {
  OrderDirectionEnum2[OrderDirectionEnum2["ASC"] = 1] = "ASC";
  OrderDirectionEnum2[OrderDirectionEnum2["DESC"] = -1] = "DESC";
})(OrderDirectionEnum || (OrderDirectionEnum = {}));

// node_modules/@novu/shared/dist/esm/types/subscriber/index.js
var SubscriberSourceEnum;
(function(SubscriberSourceEnum2) {
  SubscriberSourceEnum2["BROADCAST"] = "broadcast";
  SubscriberSourceEnum2["SINGLE"] = "single";
  SubscriberSourceEnum2["TOPIC"] = "topic";
})(SubscriberSourceEnum || (SubscriberSourceEnum = {}));
var PreferenceOverrideSourceEnum;
(function(PreferenceOverrideSourceEnum2) {
  PreferenceOverrideSourceEnum2["SUBSCRIBER"] = "subscriber";
  PreferenceOverrideSourceEnum2["TEMPLATE"] = "template";
  PreferenceOverrideSourceEnum2["WORKFLOW_OVERRIDE"] = "workflowOverride";
})(PreferenceOverrideSourceEnum || (PreferenceOverrideSourceEnum = {}));

// node_modules/@novu/shared/dist/esm/types/analytics/index.js
var SignUpOriginEnum;
(function(SignUpOriginEnum2) {
  SignUpOriginEnum2["WEB"] = "web";
  SignUpOriginEnum2["CLI"] = "cli";
  SignUpOriginEnum2["VERCEL"] = "vercel";
})(SignUpOriginEnum || (SignUpOriginEnum = {}));

// node_modules/@novu/shared/dist/esm/types/messages/index.js
var MarkMessagesAsEnum;
(function(MarkMessagesAsEnum2) {
  MarkMessagesAsEnum2["READ"] = "read";
  MarkMessagesAsEnum2["SEEN"] = "seen";
  MarkMessagesAsEnum2["UNREAD"] = "unread";
  MarkMessagesAsEnum2["UNSEEN"] = "unseen";
})(MarkMessagesAsEnum || (MarkMessagesAsEnum = {}));

// node_modules/@novu/shared/dist/esm/types/notification-templates/index.js
var WorkflowTypeEnum;
(function(WorkflowTypeEnum2) {
  WorkflowTypeEnum2["REGULAR"] = "REGULAR";
  WorkflowTypeEnum2["ECHO"] = "ECHO";
})(WorkflowTypeEnum || (WorkflowTypeEnum = {}));

// node_modules/@novu/shared/dist/esm/types/web-sockets/index.js
var WebSocketEventEnum;
(function(WebSocketEventEnum2) {
  WebSocketEventEnum2["RECEIVED"] = "notification_received";
  WebSocketEventEnum2["UNREAD"] = "unread_count_changed";
  WebSocketEventEnum2["UNSEEN"] = "unseen_count_changed";
})(WebSocketEventEnum || (WebSocketEventEnum = {}));

// node_modules/@novu/shared/dist/esm/types/rate-limiting/algorithm.types.js
var ApiRateLimitAlgorithmEnum;
(function(ApiRateLimitAlgorithmEnum2) {
  ApiRateLimitAlgorithmEnum2["BURST_ALLOWANCE"] = "burst_allowance";
  ApiRateLimitAlgorithmEnum2["WINDOW_DURATION"] = "window_duration";
})(ApiRateLimitAlgorithmEnum || (ApiRateLimitAlgorithmEnum = {}));
ApiRateLimitAlgorithmEnum.BURST_ALLOWANCE, ApiRateLimitAlgorithmEnum.WINDOW_DURATION;

// node_modules/@novu/shared/dist/esm/types/rate-limiting/config.types.js
var ApiRateLimitConfigEnum;
(function(ApiRateLimitConfigEnum2) {
  ApiRateLimitConfigEnum2["ALGORITHM"] = "algorithm";
  ApiRateLimitConfigEnum2["COST"] = "cost";
  ApiRateLimitConfigEnum2["MAXIMUM"] = "maximum";
})(ApiRateLimitConfigEnum || (ApiRateLimitConfigEnum = {}));

// node_modules/@novu/shared/dist/esm/types/rate-limiting/cost.types.js
var ApiRateLimitCostEnum;
(function(ApiRateLimitCostEnum2) {
  ApiRateLimitCostEnum2["SINGLE"] = "single";
  ApiRateLimitCostEnum2["BULK"] = "bulk";
})(ApiRateLimitCostEnum || (ApiRateLimitCostEnum = {}));

// node_modules/@novu/shared/dist/esm/types/rate-limiting/service.types.js
var ApiRateLimitCategoryEnum;
(function(ApiRateLimitCategoryEnum2) {
  ApiRateLimitCategoryEnum2["TRIGGER"] = "trigger";
  ApiRateLimitCategoryEnum2["CONFIGURATION"] = "configuration";
  ApiRateLimitCategoryEnum2["GLOBAL"] = "global";
})(ApiRateLimitCategoryEnum || (ApiRateLimitCategoryEnum = {}));

// node_modules/@novu/shared/dist/esm/types/auth/auth.types.js
var ApiAuthSchemeEnum;
(function(ApiAuthSchemeEnum2) {
  ApiAuthSchemeEnum2["BEARER"] = "Bearer";
  ApiAuthSchemeEnum2["API_KEY"] = "ApiKey";
})(ApiAuthSchemeEnum || (ApiAuthSchemeEnum = {}));
var PassportStrategyEnum;
(function(PassportStrategyEnum2) {
  PassportStrategyEnum2["JWT"] = "jwt";
  PassportStrategyEnum2["HEADER_API_KEY"] = "headerapikey";
})(PassportStrategyEnum || (PassportStrategyEnum = {}));

// node_modules/@novu/shared/dist/esm/types/timezones/timezones.types.js
var TimezoneEnum;
(function(TimezoneEnum2) {
  TimezoneEnum2["AFRICA_ABIDJAN"] = "Africa/Abidjan";
  TimezoneEnum2["AFRICA_ACCRA"] = "Africa/Accra";
  TimezoneEnum2["AFRICA_ADDIS_ABABA"] = "Africa/Addis_Ababa";
  TimezoneEnum2["AFRICA_ALGIERS"] = "Africa/Algiers";
  TimezoneEnum2["AFRICA_ASMARA"] = "Africa/Asmara";
  TimezoneEnum2["AFRICA_ASMERA"] = "Africa/Asmera";
  TimezoneEnum2["AFRICA_BAMAKO"] = "Africa/Bamako";
  TimezoneEnum2["AFRICA_BANGUI"] = "Africa/Bangui";
  TimezoneEnum2["AFRICA_BANJUL"] = "Africa/Banjul";
  TimezoneEnum2["AFRICA_BISSAU"] = "Africa/Bissau";
  TimezoneEnum2["AFRICA_BLANTYRE"] = "Africa/Blantyre";
  TimezoneEnum2["AFRICA_BRAZZAVILLE"] = "Africa/Brazzaville";
  TimezoneEnum2["AFRICA_BUJUMBURA"] = "Africa/Bujumbura";
  TimezoneEnum2["AFRICA_CAIRO"] = "Africa/Cairo";
  TimezoneEnum2["AFRICA_CASABLANCA"] = "Africa/Casablanca";
  TimezoneEnum2["AFRICA_CEUTA"] = "Africa/Ceuta";
  TimezoneEnum2["AFRICA_CONAKRY"] = "Africa/Conakry";
  TimezoneEnum2["AFRICA_DAKAR"] = "Africa/Dakar";
  TimezoneEnum2["AFRICA_DAR_ES_SALAAM"] = "Africa/Dar_es_Salaam";
  TimezoneEnum2["AFRICA_DJIBOUTI"] = "Africa/Djibouti";
  TimezoneEnum2["AFRICA_DOUALA"] = "Africa/Douala";
  TimezoneEnum2["AFRICA_EL_AAIUN"] = "Africa/El_Aaiun";
  TimezoneEnum2["AFRICA_FREETOWN"] = "Africa/Freetown";
  TimezoneEnum2["AFRICA_GABORONE"] = "Africa/Gaborone";
  TimezoneEnum2["AFRICA_HARARE"] = "Africa/Harare";
  TimezoneEnum2["AFRICA_JOHANNESBURG"] = "Africa/Johannesburg";
  TimezoneEnum2["AFRICA_JUBA"] = "Africa/Juba";
  TimezoneEnum2["AFRICA_KAMPALA"] = "Africa/Kampala";
  TimezoneEnum2["AFRICA_KHARTOUM"] = "Africa/Khartoum";
  TimezoneEnum2["AFRICA_KIGALI"] = "Africa/Kigali";
  TimezoneEnum2["AFRICA_KINSHASA"] = "Africa/Kinshasa";
  TimezoneEnum2["AFRICA_LAGOS"] = "Africa/Lagos";
  TimezoneEnum2["AFRICA_LIBREVILLE"] = "Africa/Libreville";
  TimezoneEnum2["AFRICA_LOME"] = "Africa/Lome";
  TimezoneEnum2["AFRICA_LUANDA"] = "Africa/Luanda";
  TimezoneEnum2["AFRICA_LUBUMBASHI"] = "Africa/Lubumbashi";
  TimezoneEnum2["AFRICA_LUSAKA"] = "Africa/Lusaka";
  TimezoneEnum2["AFRICA_MALABO"] = "Africa/Malabo";
  TimezoneEnum2["AFRICA_MAPUTO"] = "Africa/Maputo";
  TimezoneEnum2["AFRICA_MASERU"] = "Africa/Maseru";
  TimezoneEnum2["AFRICA_MBABANE"] = "Africa/Mbabane";
  TimezoneEnum2["AFRICA_MOGADISHU"] = "Africa/Mogadishu";
  TimezoneEnum2["AFRICA_MONROVIA"] = "Africa/Monrovia";
  TimezoneEnum2["AFRICA_NAIROBI"] = "Africa/Nairobi";
  TimezoneEnum2["AFRICA_NDJAMENA"] = "Africa/Ndjamena";
  TimezoneEnum2["AFRICA_NIAMEY"] = "Africa/Niamey";
  TimezoneEnum2["AFRICA_NOUAKCHOTT"] = "Africa/Nouakchott";
  TimezoneEnum2["AFRICA_OUAGADOUGOU"] = "Africa/Ouagadougou";
  TimezoneEnum2["AFRICA_PORTO_NOVO"] = "Africa/Porto-Novo";
  TimezoneEnum2["AFRICA_SAO_TOME"] = "Africa/Sao_Tome";
  TimezoneEnum2["AFRICA_TIMBUKTU"] = "Africa/Timbuktu";
  TimezoneEnum2["AFRICA_TRIPOLI"] = "Africa/Tripoli";
  TimezoneEnum2["AFRICA_TUNIS"] = "Africa/Tunis";
  TimezoneEnum2["AFRICA_WINDHOEK"] = "Africa/Windhoek";
  TimezoneEnum2["AMERICA_ADAK"] = "America/Adak";
  TimezoneEnum2["AMERICA_ANCHORAGE"] = "America/Anchorage";
  TimezoneEnum2["AMERICA_ANGUILLA"] = "America/Anguilla";
  TimezoneEnum2["AMERICA_ANTIGUA"] = "America/Antigua";
  TimezoneEnum2["AMERICA_ARAGUAINA"] = "America/Araguaina";
  TimezoneEnum2["AMERICA_ARGENTINA_BUENOS_AIRES"] = "America/Argentina/Buenos_Aires";
  TimezoneEnum2["AMERICA_ARGENTINA_CATAMARCA"] = "America/Argentina/Catamarca";
  TimezoneEnum2["AMERICA_ARGENTINA_COMOD_RIVADAVIA"] = "America/Argentina/ComodRivadavia";
  TimezoneEnum2["AMERICA_ARGENTINA_CORDOBA"] = "America/Argentina/Cordoba";
  TimezoneEnum2["AMERICA_ARGENTINA_JUJUY"] = "America/Argentina/Jujuy";
  TimezoneEnum2["AMERICA_ARGENTINA_LA_RIOJA"] = "America/Argentina/La_Rioja";
  TimezoneEnum2["AMERICA_ARGENTINA_MENDOZA"] = "America/Argentina/Mendoza";
  TimezoneEnum2["AMERICA_ARGENTINA_RIO_GALLEGOS"] = "America/Argentina/Rio_Gallegos";
  TimezoneEnum2["AMERICA_ARGENTINA_SALTA"] = "America/Argentina/Salta";
  TimezoneEnum2["AMERICA_ARGENTINA_SAN_JUAN"] = "America/Argentina/San_Juan";
  TimezoneEnum2["AMERICA_ARGENTINA_SAN_LUIS"] = "America/Argentina/San_Luis";
  TimezoneEnum2["AMERICA_ARGENTINA_TUCUMAN"] = "America/Argentina/Tucuman";
  TimezoneEnum2["AMERICA_ARGENTINA_USHUAIA"] = "America/Argentina/Ushuaia";
  TimezoneEnum2["AMERICA_ARUBA"] = "America/Aruba";
  TimezoneEnum2["AMERICA_ASUNCION"] = "America/Asuncion";
  TimezoneEnum2["AMERICA_ATIKOKAN"] = "America/Atikokan";
  TimezoneEnum2["AMERICA_ATKA"] = "America/Atka";
  TimezoneEnum2["AMERICA_BAHIA"] = "America/Bahia";
  TimezoneEnum2["AMERICA_BAHIA_BANDERAS"] = "America/Bahia_Banderas";
  TimezoneEnum2["AMERICA_BARBADOS"] = "America/Barbados";
  TimezoneEnum2["AMERICA_BELEM"] = "America/Belem";
  TimezoneEnum2["AMERICA_BELIZE"] = "America/Belize";
  TimezoneEnum2["AMERICA_BLANC_SABLON"] = "America/Blanc-Sablon";
  TimezoneEnum2["AMERICA_BOA_VISTA"] = "America/Boa_Vista";
  TimezoneEnum2["AMERICA_BOGOTA"] = "America/Bogota";
  TimezoneEnum2["AMERICA_BOISE"] = "America/Boise";
  TimezoneEnum2["AMERICA_BUENOS_AIRES"] = "America/Buenos_Aires";
  TimezoneEnum2["AMERICA_CAMBRIDGE_BAY"] = "America/Cambridge_Bay";
  TimezoneEnum2["AMERICA_CAMPO_GRANDE"] = "America/Campo_Grande";
  TimezoneEnum2["AMERICA_CANCUN"] = "America/Cancun";
  TimezoneEnum2["AMERICA_CARACAS"] = "America/Caracas";
  TimezoneEnum2["AMERICA_CATAMARCA"] = "America/Catamarca";
  TimezoneEnum2["AMERICA_CAYENNE"] = "America/Cayenne";
  TimezoneEnum2["AMERICA_CAYMAN"] = "America/Cayman";
  TimezoneEnum2["AMERICA_CHICAGO"] = "America/Chicago";
  TimezoneEnum2["AMERICA_CHIHUAHUA"] = "America/Chihuahua";
  TimezoneEnum2["AMERICA_CORAL_HARBOUR"] = "America/Coral_Harbour";
  TimezoneEnum2["AMERICA_CORDOBA"] = "America/Cordoba";
  TimezoneEnum2["AMERICA_COSTA_RICA"] = "America/Costa_Rica";
  TimezoneEnum2["AMERICA_CRESTON"] = "America/Creston";
  TimezoneEnum2["AMERICA_CUIABA"] = "America/Cuiaba";
  TimezoneEnum2["AMERICA_CURACAO"] = "America/Curacao";
  TimezoneEnum2["AMERICA_DANMARKSHAVN"] = "America/Danmarkshavn";
  TimezoneEnum2["AMERICA_DAWSON"] = "America/Dawson";
  TimezoneEnum2["AMERICA_DAWSON_CREEK"] = "America/Dawson_Creek";
  TimezoneEnum2["AMERICA_DENVER"] = "America/Denver";
  TimezoneEnum2["AMERICA_DETROIT"] = "America/Detroit";
  TimezoneEnum2["AMERICA_DOMINICA"] = "America/Dominica";
  TimezoneEnum2["AMERICA_EDMONTON"] = "America/Edmonton";
  TimezoneEnum2["AMERICA_EIRUNEPE"] = "America/Eirunepe";
  TimezoneEnum2["AMERICA_EL_SALVADOR"] = "America/El_Salvador";
  TimezoneEnum2["AMERICA_ENSENADA"] = "America/Ensenada";
  TimezoneEnum2["AMERICA_FORT_NELSON"] = "America/Fort_Nelson";
  TimezoneEnum2["AMERICA_FORT_WAYNE"] = "America/Fort_Wayne";
  TimezoneEnum2["AMERICA_FORTALEZA"] = "America/Fortaleza";
  TimezoneEnum2["AMERICA_GLACE_BAY"] = "America/Glace_Bay";
  TimezoneEnum2["AMERICA_GODTHAB"] = "America/Godthab";
  TimezoneEnum2["AMERICA_GOOSE_BAY"] = "America/Goose_Bay";
  TimezoneEnum2["AMERICA_GRAND_TURK"] = "America/Grand_Turk";
  TimezoneEnum2["AMERICA_GRENADA"] = "America/Grenada";
  TimezoneEnum2["AMERICA_GUADELOUPE"] = "America/Guadeloupe";
  TimezoneEnum2["AMERICA_GUATEMALA"] = "America/Guatemala";
  TimezoneEnum2["AMERICA_GUAYAQUIL"] = "America/Guayaquil";
  TimezoneEnum2["AMERICA_GUYANA"] = "America/Guyana";
  TimezoneEnum2["AMERICA_HALIFAX"] = "America/Halifax";
  TimezoneEnum2["AMERICA_HAVANA"] = "America/Havana";
  TimezoneEnum2["AMERICA_HERMOSILLO"] = "America/Hermosillo";
  TimezoneEnum2["AMERICA_INDIANA_INDIANAPOLIS"] = "America/Indiana/Indianapolis";
  TimezoneEnum2["AMERICA_INDIANA_KNOX"] = "America/Indiana/Knox";
  TimezoneEnum2["AMERICA_INDIANA_MARENGO"] = "America/Indiana/Marengo";
  TimezoneEnum2["AMERICA_INDIANA_PETERSBURG"] = "America/Indiana/Petersburg";
  TimezoneEnum2["AMERICA_INDIANA_TELL_CITY"] = "America/Indiana/Tell_City";
  TimezoneEnum2["AMERICA_INDIANA_VEVAY"] = "America/Indiana/Vevay";
  TimezoneEnum2["AMERICA_INDIANA_VINCENNES"] = "America/Indiana/Vincennes";
  TimezoneEnum2["AMERICA_INDIANA_WINAMAC"] = "America/Indiana/Winamac";
  TimezoneEnum2["AMERICA_INDIANAPOLIS"] = "America/Indianapolis";
  TimezoneEnum2["AMERICA_INUVIK"] = "America/Inuvik";
  TimezoneEnum2["AMERICA_IQALUIT"] = "America/Iqaluit";
  TimezoneEnum2["AMERICA_JAMAICA"] = "America/Jamaica";
  TimezoneEnum2["AMERICA_JUJUY"] = "America/Jujuy";
  TimezoneEnum2["AMERICA_JUNEAU"] = "America/Juneau";
  TimezoneEnum2["AMERICA_KENTUCKY_LOUISVILLE"] = "America/Kentucky/Louisville";
  TimezoneEnum2["AMERICA_KENTUCKY_MONTICELLO"] = "America/Kentucky/Monticello";
  TimezoneEnum2["AMERICA_KNOX_IN"] = "America/Knox_IN";
  TimezoneEnum2["AMERICA_KRALENDIJK"] = "America/Kralendijk";
  TimezoneEnum2["AMERICA_LA_PAZ"] = "America/La_Paz";
  TimezoneEnum2["AMERICA_LIMA"] = "America/Lima";
  TimezoneEnum2["AMERICA_LOS_ANGELES"] = "America/Los_Angeles";
  TimezoneEnum2["AMERICA_LOUISVILLE"] = "America/Louisville";
  TimezoneEnum2["AMERICA_LOWER_PRINCES"] = "America/Lower_Princes";
  TimezoneEnum2["AMERICA_MACEIO"] = "America/Maceio";
  TimezoneEnum2["AMERICA_MANAGUA"] = "America/Managua";
  TimezoneEnum2["AMERICA_MANAUS"] = "America/Manaus";
  TimezoneEnum2["AMERICA_MARIGOT"] = "America/Marigot";
  TimezoneEnum2["AMERICA_MARTINIQUE"] = "America/Martinique";
  TimezoneEnum2["AMERICA_MATAMOROS"] = "America/Matamoros";
  TimezoneEnum2["AMERICA_MAZATLAN"] = "America/Mazatlan";
  TimezoneEnum2["AMERICA_MENDOZA"] = "America/Mendoza";
  TimezoneEnum2["AMERICA_MENOMINEE"] = "America/Menominee";
  TimezoneEnum2["AMERICA_MERIDA"] = "America/Merida";
  TimezoneEnum2["AMERICA_METLAKATLA"] = "America/Metlakatla";
  TimezoneEnum2["AMERICA_MEXICO_CITY"] = "America/Mexico_City";
  TimezoneEnum2["AMERICA_MIQUELON"] = "America/Miquelon";
  TimezoneEnum2["AMERICA_MONCTON"] = "America/Moncton";
  TimezoneEnum2["AMERICA_MONTERREY"] = "America/Monterrey";
  TimezoneEnum2["AMERICA_MONTEVIDEO"] = "America/Montevideo";
  TimezoneEnum2["AMERICA_MONTREAL"] = "America/Montreal";
  TimezoneEnum2["AMERICA_MONTSERRAT"] = "America/Montserrat";
  TimezoneEnum2["AMERICA_NASSAU"] = "America/Nassau";
  TimezoneEnum2["AMERICA_NEW_YORK"] = "America/New_York";
  TimezoneEnum2["AMERICA_NIPIGON"] = "America/Nipigon";
  TimezoneEnum2["AMERICA_NOME"] = "America/Nome";
  TimezoneEnum2["AMERICA_NORONHA"] = "America/Noronha";
  TimezoneEnum2["AMERICA_NORTH_DAKOTA_BEULAH"] = "America/North_Dakota/Beulah";
  TimezoneEnum2["AMERICA_NORTH_DAKOTA_CENTER"] = "America/North_Dakota/Center";
  TimezoneEnum2["AMERICA_NORTH_DAKOTA_NEW_SALEM"] = "America/North_Dakota/New_Salem";
  TimezoneEnum2["AMERICA_OJINAGA"] = "America/Ojinaga";
  TimezoneEnum2["AMERICA_PANAMA"] = "America/Panama";
  TimezoneEnum2["AMERICA_PANGNIRTUNG"] = "America/Pangnirtung";
  TimezoneEnum2["AMERICA_PARAMARIBO"] = "America/Paramaribo";
  TimezoneEnum2["AMERICA_PHOENIX"] = "America/Phoenix";
  TimezoneEnum2["AMERICA_PORT_AU_PRINCE"] = "America/Port-au-Prince";
  TimezoneEnum2["AMERICA_PORT_OF_SPAIN"] = "America/Port_of_Spain";
  TimezoneEnum2["AMERICA_PORTO_ACRE"] = "America/Porto_Acre";
  TimezoneEnum2["AMERICA_PORTO_VELHO"] = "America/Porto_Velho";
  TimezoneEnum2["AMERICA_PUERTO_RICO"] = "America/Puerto_Rico";
  TimezoneEnum2["AMERICA_PUNTA_ARENAS"] = "America/Punta_Arenas";
  TimezoneEnum2["AMERICA_RAINY_RIVER"] = "America/Rainy_River";
  TimezoneEnum2["AMERICA_RANKIN_INLET"] = "America/Rankin_Inlet";
  TimezoneEnum2["AMERICA_RECIFE"] = "America/Recife";
  TimezoneEnum2["AMERICA_REGINA"] = "America/Regina";
  TimezoneEnum2["AMERICA_RESOLUTE"] = "America/Resolute";
  TimezoneEnum2["AMERICA_RIO_BRANCO"] = "America/Rio_Branco";
  TimezoneEnum2["AMERICA_ROSARIO"] = "America/Rosario";
  TimezoneEnum2["AMERICA_SANTA_ISABEL"] = "America/Santa_Isabel";
  TimezoneEnum2["AMERICA_SANTAREM"] = "America/Santarem";
  TimezoneEnum2["AMERICA_SANTIAGO"] = "America/Santiago";
  TimezoneEnum2["AMERICA_SANTO_DOMINGO"] = "America/Santo_Domingo";
  TimezoneEnum2["AMERICA_SAO_PAULO"] = "America/Sao_Paulo";
  TimezoneEnum2["AMERICA_SCORESBYSUND"] = "America/Scoresbysund";
  TimezoneEnum2["AMERICA_SHIPROCK"] = "America/Shiprock";
  TimezoneEnum2["AMERICA_SITKA"] = "America/Sitka";
  TimezoneEnum2["AMERICA_ST_BARTHELEMY"] = "America/St_Barthelemy";
  TimezoneEnum2["AMERICA_ST_JOHNS"] = "America/St_Johns";
  TimezoneEnum2["AMERICA_ST_KITTS"] = "America/St_Kitts";
  TimezoneEnum2["AMERICA_ST_LUCIA"] = "America/St_Lucia";
  TimezoneEnum2["AMERICA_ST_THOMAS"] = "America/St_Thomas";
  TimezoneEnum2["AMERICA_ST_VINCENT"] = "America/St_Vincent";
  TimezoneEnum2["AMERICA_SWIFT_CURRENT"] = "America/Swift_Current";
  TimezoneEnum2["AMERICA_TEGUCIGALPA"] = "America/Tegucigalpa";
  TimezoneEnum2["AMERICA_THULE"] = "America/Thule";
  TimezoneEnum2["AMERICA_THUNDER_BAY"] = "America/Thunder_Bay";
  TimezoneEnum2["AMERICA_TIJUANA"] = "America/Tijuana";
  TimezoneEnum2["AMERICA_TORONTO"] = "America/Toronto";
  TimezoneEnum2["AMERICA_TORTOLA"] = "America/Tortola";
  TimezoneEnum2["AMERICA_VANCOUVER"] = "America/Vancouver";
  TimezoneEnum2["AMERICA_VIRGIN"] = "America/Virgin";
  TimezoneEnum2["AMERICA_WHITEHORSE"] = "America/Whitehorse";
  TimezoneEnum2["AMERICA_WINNIPEG"] = "America/Winnipeg";
  TimezoneEnum2["AMERICA_YAKUTAT"] = "America/Yakutat";
  TimezoneEnum2["AMERICA_YELLOWKNIFE"] = "America/Yellowknife";
  TimezoneEnum2["ANTARCTICA_CASEY"] = "Antarctica/Casey";
  TimezoneEnum2["ANTARCTICA_DAVIS"] = "Antarctica/Davis";
  TimezoneEnum2["ANTARCTICA_DUMONT_D_URVILLE"] = "Antarctica/DumontDUrville";
  TimezoneEnum2["ANTARCTICA_MACQUARIE"] = "Antarctica/Macquarie";
  TimezoneEnum2["ANTARCTICA_MAWSON"] = "Antarctica/Mawson";
  TimezoneEnum2["ANTARCTICA_MC_MURDO"] = "Antarctica/McMurdo";
  TimezoneEnum2["ANTARCTICA_PALMER"] = "Antarctica/Palmer";
  TimezoneEnum2["ANTARCTICA_ROTHERA"] = "Antarctica/Rothera";
  TimezoneEnum2["ANTARCTICA_SOUTH_POLE"] = "Antarctica/South_Pole";
  TimezoneEnum2["ANTARCTICA_SYOWA"] = "Antarctica/Syowa";
  TimezoneEnum2["ANTARCTICA_TROLL"] = "Antarctica/Troll";
  TimezoneEnum2["ANTARCTICA_VOSTOK"] = "Antarctica/Vostok";
  TimezoneEnum2["ARCTIC_LONGYEARBYEN"] = "Arctic/Longyearbyen";
  TimezoneEnum2["ASIA_ADEN"] = "Asia/Aden";
  TimezoneEnum2["ASIA_ALMATY"] = "Asia/Almaty";
  TimezoneEnum2["ASIA_AMMAN"] = "Asia/Amman";
  TimezoneEnum2["ASIA_ANADYR"] = "Asia/Anadyr";
  TimezoneEnum2["ASIA_AQTAU"] = "Asia/Aqtau";
  TimezoneEnum2["ASIA_AQTOBE"] = "Asia/Aqtobe";
  TimezoneEnum2["ASIA_ASHGABAT"] = "Asia/Ashgabat";
  TimezoneEnum2["ASIA_ASHKHABAD"] = "Asia/Ashkhabad";
  TimezoneEnum2["ASIA_ATYRAU"] = "Asia/Atyrau";
  TimezoneEnum2["ASIA_BAGHDAD"] = "Asia/Baghdad";
  TimezoneEnum2["ASIA_BAHRAIN"] = "Asia/Bahrain";
  TimezoneEnum2["ASIA_BAKU"] = "Asia/Baku";
  TimezoneEnum2["ASIA_BANGKOK"] = "Asia/Bangkok";
  TimezoneEnum2["ASIA_BARNAUL"] = "Asia/Barnaul";
  TimezoneEnum2["ASIA_BEIRUT"] = "Asia/Beirut";
  TimezoneEnum2["ASIA_BISHKEK"] = "Asia/Bishkek";
  TimezoneEnum2["ASIA_BRUNEI"] = "Asia/Brunei";
  TimezoneEnum2["ASIA_CALCUTTA"] = "Asia/Calcutta";
  TimezoneEnum2["ASIA_CHITA"] = "Asia/Chita";
  TimezoneEnum2["ASIA_CHOIBALSAN"] = "Asia/Choibalsan";
  TimezoneEnum2["ASIA_CHONGQING"] = "Asia/Chongqing";
  TimezoneEnum2["ASIA_CHUNGKING"] = "Asia/Chungking";
  TimezoneEnum2["ASIA_COLOMBO"] = "Asia/Colombo";
  TimezoneEnum2["ASIA_DACCA"] = "Asia/Dacca";
  TimezoneEnum2["ASIA_DAMASCUS"] = "Asia/Damascus";
  TimezoneEnum2["ASIA_DHAKA"] = "Asia/Dhaka";
  TimezoneEnum2["ASIA_DILI"] = "Asia/Dili";
  TimezoneEnum2["ASIA_DUBAI"] = "Asia/Dubai";
  TimezoneEnum2["ASIA_DUSHANBE"] = "Asia/Dushanbe";
  TimezoneEnum2["ASIA_FAMAGUSTA"] = "Asia/Famagusta";
  TimezoneEnum2["ASIA_GAZA"] = "Asia/Gaza";
  TimezoneEnum2["ASIA_HARBIN"] = "Asia/Harbin";
  TimezoneEnum2["ASIA_HEBRON"] = "Asia/Hebron";
  TimezoneEnum2["ASIA_HO_CHI_MINH"] = "Asia/Ho_Chi_Minh";
  TimezoneEnum2["ASIA_HONG_KONG"] = "Asia/Hong_Kong";
  TimezoneEnum2["ASIA_HOVD"] = "Asia/Hovd";
  TimezoneEnum2["ASIA_IRKUTSK"] = "Asia/Irkutsk";
  TimezoneEnum2["ASIA_ISTANBUL"] = "Asia/Istanbul";
  TimezoneEnum2["ASIA_JAKARTA"] = "Asia/Jakarta";
  TimezoneEnum2["ASIA_JAYAPURA"] = "Asia/Jayapura";
  TimezoneEnum2["ASIA_JERUSALEM"] = "Asia/Jerusalem";
  TimezoneEnum2["ASIA_KABUL"] = "Asia/Kabul";
  TimezoneEnum2["ASIA_KAMCHATKA"] = "Asia/Kamchatka";
  TimezoneEnum2["ASIA_KARACHI"] = "Asia/Karachi";
  TimezoneEnum2["ASIA_KASHGAR"] = "Asia/Kashgar";
  TimezoneEnum2["ASIA_KATHMANDU"] = "Asia/Kathmandu";
  TimezoneEnum2["ASIA_KATMANDU"] = "Asia/Katmandu";
  TimezoneEnum2["ASIA_KHANDYGA"] = "Asia/Khandyga";
  TimezoneEnum2["ASIA_KOLKATA"] = "Asia/Kolkata";
  TimezoneEnum2["ASIA_KRASNOYARSK"] = "Asia/Krasnoyarsk";
  TimezoneEnum2["ASIA_KUALA_LUMPUR"] = "Asia/Kuala_Lumpur";
  TimezoneEnum2["ASIA_KUCHING"] = "Asia/Kuching";
  TimezoneEnum2["ASIA_KUWAIT"] = "Asia/Kuwait";
  TimezoneEnum2["ASIA_MACAO"] = "Asia/Macao";
  TimezoneEnum2["ASIA_MACAU"] = "Asia/Macau";
  TimezoneEnum2["ASIA_MAGADAN"] = "Asia/Magadan";
  TimezoneEnum2["ASIA_MAKASSAR"] = "Asia/Makassar";
  TimezoneEnum2["ASIA_MANILA"] = "Asia/Manila";
  TimezoneEnum2["ASIA_MUSCAT"] = "Asia/Muscat";
  TimezoneEnum2["ASIA_NICOSIA"] = "Asia/Nicosia";
  TimezoneEnum2["ASIA_NOVOKUZNETSK"] = "Asia/Novokuznetsk";
  TimezoneEnum2["ASIA_NOVOSIBIRSK"] = "Asia/Novosibirsk";
  TimezoneEnum2["ASIA_OMSK"] = "Asia/Omsk";
  TimezoneEnum2["ASIA_ORAL"] = "Asia/Oral";
  TimezoneEnum2["ASIA_PHNOM_PENH"] = "Asia/Phnom_Penh";
  TimezoneEnum2["ASIA_PONTIANAK"] = "Asia/Pontianak";
  TimezoneEnum2["ASIA_PYONGYANG"] = "Asia/Pyongyang";
  TimezoneEnum2["ASIA_QATAR"] = "Asia/Qatar";
  TimezoneEnum2["ASIA_QOSTANAY"] = "Asia/Qostanay";
  TimezoneEnum2["ASIA_QYZYLORDA"] = "Asia/Qyzylorda";
  TimezoneEnum2["ASIA_RANGOON"] = "Asia/Rangoon";
  TimezoneEnum2["ASIA_RIYADH"] = "Asia/Riyadh";
  TimezoneEnum2["ASIA_SAIGON"] = "Asia/Saigon";
  TimezoneEnum2["ASIA_SAKHALIN"] = "Asia/Sakhalin";
  TimezoneEnum2["ASIA_SAMARKAND"] = "Asia/Samarkand";
  TimezoneEnum2["ASIA_SEOUL"] = "Asia/Seoul";
  TimezoneEnum2["ASIA_SHANGHAI"] = "Asia/Shanghai";
  TimezoneEnum2["ASIA_SINGAPORE"] = "Asia/Singapore";
  TimezoneEnum2["ASIA_SREDNEKOLYMSK"] = "Asia/Srednekolymsk";
  TimezoneEnum2["ASIA_TAIPEI"] = "Asia/Taipei";
  TimezoneEnum2["ASIA_TASHKENT"] = "Asia/Tashkent";
  TimezoneEnum2["ASIA_TBILISI"] = "Asia/Tbilisi";
  TimezoneEnum2["ASIA_TEHRAN"] = "Asia/Tehran";
  TimezoneEnum2["ASIA_TEL_AVIV"] = "Asia/Tel_Aviv";
  TimezoneEnum2["ASIA_THIMBU"] = "Asia/Thimbu";
  TimezoneEnum2["ASIA_THIMPHU"] = "Asia/Thimphu";
  TimezoneEnum2["ASIA_TOKYO"] = "Asia/Tokyo";
  TimezoneEnum2["ASIA_TOMSK"] = "Asia/Tomsk";
  TimezoneEnum2["ASIA_UJUNG_PANDANG"] = "Asia/Ujung_Pandang";
  TimezoneEnum2["ASIA_ULAANBAATAR"] = "Asia/Ulaanbaatar";
  TimezoneEnum2["ASIA_ULAN_BATOR"] = "Asia/Ulan_Bator";
  TimezoneEnum2["ASIA_URUMQI"] = "Asia/Urumqi";
  TimezoneEnum2["ASIA_UST_NERA"] = "Asia/Ust-Nera";
  TimezoneEnum2["ASIA_VIENTIANE"] = "Asia/Vientiane";
  TimezoneEnum2["ASIA_VLADIVOSTOK"] = "Asia/Vladivostok";
  TimezoneEnum2["ASIA_YAKUTSK"] = "Asia/Yakutsk";
  TimezoneEnum2["ASIA_YANGON"] = "Asia/Yangon";
  TimezoneEnum2["ASIA_YEKATERINBURG"] = "Asia/Yekaterinburg";
  TimezoneEnum2["ASIA_YEREVAN"] = "Asia/Yerevan";
  TimezoneEnum2["ATLANTIC_AZORES"] = "Atlantic/Azores";
  TimezoneEnum2["ATLANTIC_BERMUDA"] = "Atlantic/Bermuda";
  TimezoneEnum2["ATLANTIC_CANARY"] = "Atlantic/Canary";
  TimezoneEnum2["ATLANTIC_CAPE_VERDE"] = "Atlantic/Cape_Verde";
  TimezoneEnum2["ATLANTIC_FAEROE"] = "Atlantic/Faeroe";
  TimezoneEnum2["ATLANTIC_FAROE"] = "Atlantic/Faroe";
  TimezoneEnum2["ATLANTIC_JAN_MAYEN"] = "Atlantic/Jan_Mayen";
  TimezoneEnum2["ATLANTIC_MADEIRA"] = "Atlantic/Madeira";
  TimezoneEnum2["ATLANTIC_REYKJAVIK"] = "Atlantic/Reykjavik";
  TimezoneEnum2["ATLANTIC_SOUTH_GEORGIA"] = "Atlantic/South_Georgia";
  TimezoneEnum2["ATLANTIC_ST_HELENA"] = "Atlantic/St_Helena";
  TimezoneEnum2["ATLANTIC_STANLEY"] = "Atlantic/Stanley";
  TimezoneEnum2["AUSTRALIA_ACT"] = "Australia/ACT";
  TimezoneEnum2["AUSTRALIA_ADELAIDE"] = "Australia/Adelaide";
  TimezoneEnum2["AUSTRALIA_BRISBANE"] = "Australia/Brisbane";
  TimezoneEnum2["AUSTRALIA_BROKEN_HILL"] = "Australia/Broken_Hill";
  TimezoneEnum2["AUSTRALIA_CANBERRA"] = "Australia/Canberra";
  TimezoneEnum2["AUSTRALIA_CURRIE"] = "Australia/Currie";
  TimezoneEnum2["AUSTRALIA_DARWIN"] = "Australia/Darwin";
  TimezoneEnum2["AUSTRALIA_EUCLA"] = "Australia/Eucla";
  TimezoneEnum2["AUSTRALIA_HOBART"] = "Australia/Hobart";
  TimezoneEnum2["AUSTRALIA_LHI"] = "Australia/LHI";
  TimezoneEnum2["AUSTRALIA_LINDEMAN"] = "Australia/Lindeman";
  TimezoneEnum2["AUSTRALIA_LORD_HOWE"] = "Australia/Lord_Howe";
  TimezoneEnum2["AUSTRALIA_MELBOURNE"] = "Australia/Melbourne";
  TimezoneEnum2["AUSTRALIA_NORTH"] = "Australia/North";
  TimezoneEnum2["AUSTRALIA_NSW"] = "Australia/NSW";
  TimezoneEnum2["AUSTRALIA_PERTH"] = "Australia/Perth";
  TimezoneEnum2["AUSTRALIA_QUEENSLAND"] = "Australia/Queensland";
  TimezoneEnum2["AUSTRALIA_SOUTH"] = "Australia/South";
  TimezoneEnum2["AUSTRALIA_SYDNEY"] = "Australia/Sydney";
  TimezoneEnum2["AUSTRALIA_TASMANIA"] = "Australia/Tasmania";
  TimezoneEnum2["AUSTRALIA_VICTORIA"] = "Australia/Victoria";
  TimezoneEnum2["AUSTRALIA_WEST"] = "Australia/West";
  TimezoneEnum2["AUSTRALIA_YANCOWINNA"] = "Australia/Yancowinna";
  TimezoneEnum2["BRAZIL_ACRE"] = "Brazil/Acre";
  TimezoneEnum2["BRAZIL_DE_NORONHA"] = "Brazil/DeNoronha";
  TimezoneEnum2["BRAZIL_EAST"] = "Brazil/East";
  TimezoneEnum2["BRAZIL_WEST"] = "Brazil/West";
  TimezoneEnum2["CANADA_ATLANTIC"] = "Canada/Atlantic";
  TimezoneEnum2["CANADA_CENTRAL"] = "Canada/Central";
  TimezoneEnum2["CANADA_EASTERN"] = "Canada/Eastern";
  TimezoneEnum2["CANADA_MOUNTAIN"] = "Canada/Mountain";
  TimezoneEnum2["CANADA_NEWFOUNDLAND"] = "Canada/Newfoundland";
  TimezoneEnum2["CANADA_PACIFIC"] = "Canada/Pacific";
  TimezoneEnum2["CANADA_SASKATCHEWAN"] = "Canada/Saskatchewan";
  TimezoneEnum2["CANADA_YUKON"] = "Canada/Yukon";
  TimezoneEnum2["CET"] = "CET";
  TimezoneEnum2["CHILE_CONTINENTAL"] = "Chile/Continental";
  TimezoneEnum2["CHILE_EASTER_ISLAND"] = "Chile/EasterIsland";
  TimezoneEnum2["CST6_CDT"] = "CST6CDT";
  TimezoneEnum2["CUBA"] = "Cuba";
  TimezoneEnum2["EET"] = "EET";
  TimezoneEnum2["EGYPT"] = "Egypt";
  TimezoneEnum2["EIRE"] = "Eire";
  TimezoneEnum2["EST"] = "EST";
  TimezoneEnum2["EST5_EDT"] = "EST5EDT";
  TimezoneEnum2["ETC_GMT"] = "Etc/GMT";
  TimezoneEnum2["ETC_GMT_0"] = "Etc/GMT+0";
  TimezoneEnum2["ETC_GMT_MINUS_0"] = "Etc/GMT-0";
  TimezoneEnum2["ETC_GMT_MINUS_1"] = "Etc/GMT-1";
  TimezoneEnum2["ETC_GMT_MINUS_10"] = "Etc/GMT-10";
  TimezoneEnum2["ETC_GMT_MINUS_11"] = "Etc/GMT-11";
  TimezoneEnum2["ETC_GMT_MINUS_12"] = "Etc/GMT-12";
  TimezoneEnum2["ETC_GMT_MINUS_13"] = "Etc/GMT-13";
  TimezoneEnum2["ETC_GMT_MINUS_14"] = "Etc/GMT-14";
  TimezoneEnum2["ETC_GMT_MINUS_2"] = "Etc/GMT-2";
  TimezoneEnum2["ETC_GMT_MINUS_3"] = "Etc/GMT-3";
  TimezoneEnum2["ETC_GMT_MINUS_4"] = "Etc/GMT-4";
  TimezoneEnum2["ETC_GMT_MINUS_5"] = "Etc/GMT-5";
  TimezoneEnum2["ETC_GMT_MINUS_6"] = "Etc/GMT-6";
  TimezoneEnum2["ETC_GMT_MINUS_7"] = "Etc/GMT-7";
  TimezoneEnum2["ETC_GMT_MINUS_8"] = "Etc/GMT-8";
  TimezoneEnum2["ETC_GMT_MINUS_9"] = "Etc/GMT-9";
  TimezoneEnum2["ETC_GMT_PLUS_1"] = "Etc/GMT+1";
  TimezoneEnum2["ETC_GMT_PLUS_10"] = "Etc/GMT+10";
  TimezoneEnum2["ETC_GMT_PLUS_11"] = "Etc/GMT+11";
  TimezoneEnum2["ETC_GMT_PLUS_12"] = "Etc/GMT+12";
  TimezoneEnum2["ETC_GMT_PLUS_2"] = "Etc/GMT+2";
  TimezoneEnum2["ETC_GMT_PLUS_3"] = "Etc/GMT+3";
  TimezoneEnum2["ETC_GMT_PLUS_4"] = "Etc/GMT+4";
  TimezoneEnum2["ETC_GMT_PLUS_5"] = "Etc/GMT+5";
  TimezoneEnum2["ETC_GMT_PLUS_6"] = "Etc/GMT+6";
  TimezoneEnum2["ETC_GMT_PLUS_7"] = "Etc/GMT+7";
  TimezoneEnum2["ETC_GMT_PLUS_8"] = "Etc/GMT+8";
  TimezoneEnum2["ETC_GMT_PLUS_9"] = "Etc/GMT+9";
  TimezoneEnum2["ETC_GMT0"] = "Etc/GMT0";
  TimezoneEnum2["ETC_GREENWICH"] = "Etc/Greenwich";
  TimezoneEnum2["ETC_UCT"] = "Etc/UCT";
  TimezoneEnum2["ETC_UNIVERSAL"] = "Etc/Universal";
  TimezoneEnum2["ETC_UTC"] = "Etc/UTC";
  TimezoneEnum2["ETC_ZULU"] = "Etc/Zulu";
  TimezoneEnum2["EUROPE_AMSTERDAM"] = "Europe/Amsterdam";
  TimezoneEnum2["EUROPE_ANDORRA"] = "Europe/Andorra";
  TimezoneEnum2["EUROPE_ASTRAKHAN"] = "Europe/Astrakhan";
  TimezoneEnum2["EUROPE_ATHENS"] = "Europe/Athens";
  TimezoneEnum2["EUROPE_BELFAST"] = "Europe/Belfast";
  TimezoneEnum2["EUROPE_BELGRADE"] = "Europe/Belgrade";
  TimezoneEnum2["EUROPE_BERLIN"] = "Europe/Berlin";
  TimezoneEnum2["EUROPE_BRATISLAVA"] = "Europe/Bratislava";
  TimezoneEnum2["EUROPE_BRUSSELS"] = "Europe/Brussels";
  TimezoneEnum2["EUROPE_BUCHAREST"] = "Europe/Bucharest";
  TimezoneEnum2["EUROPE_BUDAPEST"] = "Europe/Budapest";
  TimezoneEnum2["EUROPE_BUSINGEN"] = "Europe/Busingen";
  TimezoneEnum2["EUROPE_CHISINAU"] = "Europe/Chisinau";
  TimezoneEnum2["EUROPE_COPENHAGEN"] = "Europe/Copenhagen";
  TimezoneEnum2["EUROPE_DUBLIN"] = "Europe/Dublin";
  TimezoneEnum2["EUROPE_GIBRALTAR"] = "Europe/Gibraltar";
  TimezoneEnum2["EUROPE_GUERNSEY"] = "Europe/Guernsey";
  TimezoneEnum2["EUROPE_HELSINKI"] = "Europe/Helsinki";
  TimezoneEnum2["EUROPE_ISLE_OF_MAN"] = "Europe/Isle_of_Man";
  TimezoneEnum2["EUROPE_ISTANBUL"] = "Europe/Istanbul";
  TimezoneEnum2["EUROPE_JERSEY"] = "Europe/Jersey";
  TimezoneEnum2["EUROPE_KALININGRAD"] = "Europe/Kaliningrad";
  TimezoneEnum2["EUROPE_KIEV"] = "Europe/Kiev";
  TimezoneEnum2["EUROPE_KIROV"] = "Europe/Kirov";
  TimezoneEnum2["EUROPE_LISBON"] = "Europe/Lisbon";
  TimezoneEnum2["EUROPE_LJUBLJANA"] = "Europe/Ljubljana";
  TimezoneEnum2["EUROPE_LONDON"] = "Europe/London";
  TimezoneEnum2["EUROPE_LUXEMBOURG"] = "Europe/Luxembourg";
  TimezoneEnum2["EUROPE_MADRID"] = "Europe/Madrid";
  TimezoneEnum2["EUROPE_MALTA"] = "Europe/Malta";
  TimezoneEnum2["EUROPE_MARIEHAMN"] = "Europe/Mariehamn";
  TimezoneEnum2["EUROPE_MINSK"] = "Europe/Minsk";
  TimezoneEnum2["EUROPE_MONACO"] = "Europe/Monaco";
  TimezoneEnum2["EUROPE_MOSCOW"] = "Europe/Moscow";
  TimezoneEnum2["EUROPE_NICOSIA"] = "Europe/Nicosia";
  TimezoneEnum2["EUROPE_OSLO"] = "Europe/Oslo";
  TimezoneEnum2["EUROPE_PARIS"] = "Europe/Paris";
  TimezoneEnum2["EUROPE_PODGORICA"] = "Europe/Podgorica";
  TimezoneEnum2["EUROPE_PRAGUE"] = "Europe/Prague";
  TimezoneEnum2["EUROPE_RIGA"] = "Europe/Riga";
  TimezoneEnum2["EUROPE_ROME"] = "Europe/Rome";
  TimezoneEnum2["EUROPE_SAMARA"] = "Europe/Samara";
  TimezoneEnum2["EUROPE_SAN_MARINO"] = "Europe/San_Marino";
  TimezoneEnum2["EUROPE_SARAJEVO"] = "Europe/Sarajevo";
  TimezoneEnum2["EUROPE_SARATOV"] = "Europe/Saratov";
  TimezoneEnum2["EUROPE_SIMFEROPOL"] = "Europe/Simferopol";
  TimezoneEnum2["EUROPE_SKOPJE"] = "Europe/Skopje";
  TimezoneEnum2["EUROPE_SOFIA"] = "Europe/Sofia";
  TimezoneEnum2["EUROPE_STOCKHOLM"] = "Europe/Stockholm";
  TimezoneEnum2["EUROPE_TALLINN"] = "Europe/Tallinn";
  TimezoneEnum2["EUROPE_TIRANE"] = "Europe/Tirane";
  TimezoneEnum2["EUROPE_TIRASPOL"] = "Europe/Tiraspol";
  TimezoneEnum2["EUROPE_ULYANOVSK"] = "Europe/Ulyanovsk";
  TimezoneEnum2["EUROPE_UZHGOROD"] = "Europe/Uzhgorod";
  TimezoneEnum2["EUROPE_VADUZ"] = "Europe/Vaduz";
  TimezoneEnum2["EUROPE_VATICAN"] = "Europe/Vatican";
  TimezoneEnum2["EUROPE_VIENNA"] = "Europe/Vienna";
  TimezoneEnum2["EUROPE_VILNIUS"] = "Europe/Vilnius";
  TimezoneEnum2["EUROPE_VOLGOGRAD"] = "Europe/Volgograd";
  TimezoneEnum2["EUROPE_WARSAW"] = "Europe/Warsaw";
  TimezoneEnum2["EUROPE_ZAGREB"] = "Europe/Zagreb";
  TimezoneEnum2["EUROPE_ZAPOROZHYE"] = "Europe/Zaporozhye";
  TimezoneEnum2["EUROPE_ZURICH"] = "Europe/Zurich";
  TimezoneEnum2["FACTORY"] = "Factory";
  TimezoneEnum2["GB"] = "GB";
  TimezoneEnum2["GB_EIRE"] = "GB-Eire";
  TimezoneEnum2["GMT"] = "GMT";
  TimezoneEnum2["GMT_MINUS_0"] = "GMT-0";
  TimezoneEnum2["GMT_PLUS_0"] = "GMT+0";
  TimezoneEnum2["GMT0"] = "GMT0";
  TimezoneEnum2["GREENWICH"] = "Greenwich";
  TimezoneEnum2["HONGKONG"] = "Hongkong";
  TimezoneEnum2["HST"] = "HST";
  TimezoneEnum2["ICELAND"] = "Iceland";
  TimezoneEnum2["INDIAN_ANTANANARIVO"] = "Indian/Antananarivo";
  TimezoneEnum2["INDIAN_CHAGOS"] = "Indian/Chagos";
  TimezoneEnum2["INDIAN_CHRISTMAS"] = "Indian/Christmas";
  TimezoneEnum2["INDIAN_COCOS"] = "Indian/Cocos";
  TimezoneEnum2["INDIAN_COMORO"] = "Indian/Comoro";
  TimezoneEnum2["INDIAN_KERGUELEN"] = "Indian/Kerguelen";
  TimezoneEnum2["INDIAN_MAHE"] = "Indian/Mahe";
  TimezoneEnum2["INDIAN_MALDIVES"] = "Indian/Maldives";
  TimezoneEnum2["INDIAN_MAURITIUS"] = "Indian/Mauritius";
  TimezoneEnum2["INDIAN_MAYOTTE"] = "Indian/Mayotte";
  TimezoneEnum2["INDIAN_REUNION"] = "Indian/Reunion";
  TimezoneEnum2["IRAN"] = "Iran";
  TimezoneEnum2["ISRAEL"] = "Israel";
  TimezoneEnum2["JAMAICA"] = "Jamaica";
  TimezoneEnum2["JAPAN"] = "Japan";
  TimezoneEnum2["KWAJALEIN"] = "Kwajalein";
  TimezoneEnum2["LIBYA"] = "Libya";
  TimezoneEnum2["MET"] = "MET";
  TimezoneEnum2["MEXICO_BAJA_NORTE"] = "Mexico/BajaNorte";
  TimezoneEnum2["MEXICO_BAJA_SUR"] = "Mexico/BajaSur";
  TimezoneEnum2["MEXICO_GENERAL"] = "Mexico/General";
  TimezoneEnum2["MST"] = "MST";
  TimezoneEnum2["MST7_MDT"] = "MST7MDT";
  TimezoneEnum2["NAVAJO"] = "Navajo";
  TimezoneEnum2["NZ"] = "NZ";
  TimezoneEnum2["NZ_CHAT"] = "NZ-CHAT";
  TimezoneEnum2["PACIFIC_APIA"] = "Pacific/Apia";
  TimezoneEnum2["PACIFIC_AUCKLAND"] = "Pacific/Auckland";
  TimezoneEnum2["PACIFIC_BOUGAINVILLE"] = "Pacific/Bougainville";
  TimezoneEnum2["PACIFIC_CHATHAM"] = "Pacific/Chatham";
  TimezoneEnum2["PACIFIC_CHUUK"] = "Pacific/Chuuk";
  TimezoneEnum2["PACIFIC_EASTER"] = "Pacific/Easter";
  TimezoneEnum2["PACIFIC_EFATE"] = "Pacific/Efate";
  TimezoneEnum2["PACIFIC_ENDERBURY"] = "Pacific/Enderbury";
  TimezoneEnum2["PACIFIC_FAKAOFO"] = "Pacific/Fakaofo";
  TimezoneEnum2["PACIFIC_FIJI"] = "Pacific/Fiji";
  TimezoneEnum2["PACIFIC_FUNAFUTI"] = "Pacific/Funafuti";
  TimezoneEnum2["PACIFIC_GALAPAGOS"] = "Pacific/Galapagos";
  TimezoneEnum2["PACIFIC_GAMBIER"] = "Pacific/Gambier";
  TimezoneEnum2["PACIFIC_GUADALCANAL"] = "Pacific/Guadalcanal";
  TimezoneEnum2["PACIFIC_GUAM"] = "Pacific/Guam";
  TimezoneEnum2["PACIFIC_HONOLULU"] = "Pacific/Honolulu";
  TimezoneEnum2["PACIFIC_JOHNSTON"] = "Pacific/Johnston";
  TimezoneEnum2["PACIFIC_KIRITIMATI"] = "Pacific/Kiritimati";
  TimezoneEnum2["PACIFIC_KOSRAE"] = "Pacific/Kosrae";
  TimezoneEnum2["PACIFIC_KWAJALEIN"] = "Pacific/Kwajalein";
  TimezoneEnum2["PACIFIC_MAJURO"] = "Pacific/Majuro";
  TimezoneEnum2["PACIFIC_MARQUESAS"] = "Pacific/Marquesas";
  TimezoneEnum2["PACIFIC_MIDWAY"] = "Pacific/Midway";
  TimezoneEnum2["PACIFIC_NAURU"] = "Pacific/Nauru";
  TimezoneEnum2["PACIFIC_NIUE"] = "Pacific/Niue";
  TimezoneEnum2["PACIFIC_NORFOLK"] = "Pacific/Norfolk";
  TimezoneEnum2["PACIFIC_NOUMEA"] = "Pacific/Noumea";
  TimezoneEnum2["PACIFIC_PAGO_PAGO"] = "Pacific/Pago_Pago";
  TimezoneEnum2["PACIFIC_PALAU"] = "Pacific/Palau";
  TimezoneEnum2["PACIFIC_PITCAIRN"] = "Pacific/Pitcairn";
  TimezoneEnum2["PACIFIC_POHNPEI"] = "Pacific/Pohnpei";
  TimezoneEnum2["PACIFIC_PONAPE"] = "Pacific/Ponape";
  TimezoneEnum2["PACIFIC_PORT_MORESBY"] = "Pacific/Port_Moresby";
  TimezoneEnum2["PACIFIC_RAROTONGA"] = "Pacific/Rarotonga";
  TimezoneEnum2["PACIFIC_SAIPAN"] = "Pacific/Saipan";
  TimezoneEnum2["PACIFIC_SAMOA"] = "Pacific/Samoa";
  TimezoneEnum2["PACIFIC_TAHITI"] = "Pacific/Tahiti";
  TimezoneEnum2["PACIFIC_TARAWA"] = "Pacific/Tarawa";
  TimezoneEnum2["PACIFIC_TONGATAPU"] = "Pacific/Tongatapu";
  TimezoneEnum2["PACIFIC_TRUK"] = "Pacific/Truk";
  TimezoneEnum2["PACIFIC_WAKE"] = "Pacific/Wake";
  TimezoneEnum2["PACIFIC_WALLIS"] = "Pacific/Wallis";
  TimezoneEnum2["PACIFIC_YAP"] = "Pacific/Yap";
  TimezoneEnum2["POLAND"] = "Poland";
  TimezoneEnum2["PORTUGAL"] = "Portugal";
  TimezoneEnum2["PRC"] = "PRC";
  TimezoneEnum2["PST8_PDT"] = "PST8PDT";
  TimezoneEnum2["ROC"] = "ROC";
  TimezoneEnum2["ROK"] = "ROK";
  TimezoneEnum2["SINGAPORE"] = "Singapore";
  TimezoneEnum2["TURKEY"] = "Turkey";
  TimezoneEnum2["UCT"] = "UCT";
  TimezoneEnum2["UNIVERSAL"] = "Universal";
  TimezoneEnum2["US_ALASKA"] = "US/Alaska";
  TimezoneEnum2["US_ALEUTIAN"] = "US/Aleutian";
  TimezoneEnum2["US_ARIZONA"] = "US/Arizona";
  TimezoneEnum2["US_CENTRAL"] = "US/Central";
  TimezoneEnum2["US_EAST_INDIANA"] = "US/East-Indiana";
  TimezoneEnum2["US_EASTERN"] = "US/Eastern";
  TimezoneEnum2["US_HAWAII"] = "US/Hawaii";
  TimezoneEnum2["US_INDIANA_STARKE"] = "US/Indiana-Starke";
  TimezoneEnum2["US_MICHIGAN"] = "US/Michigan";
  TimezoneEnum2["US_MOUNTAIN"] = "US/Mountain";
  TimezoneEnum2["US_PACIFIC"] = "US/Pacific";
  TimezoneEnum2["US_PACIFIC_NEW"] = "US/Pacific-New";
  TimezoneEnum2["US_SAMOA"] = "US/Samoa";
  TimezoneEnum2["UTC"] = "UTC";
  TimezoneEnum2["W_SU"] = "W-SU";
  TimezoneEnum2["WET"] = "WET";
  TimezoneEnum2["ZULU"] = "Zulu";
})(TimezoneEnum || (TimezoneEnum = {}));

// node_modules/@novu/shared/dist/esm/types/cron/cron.types.js
var CronExpressionEnum;
(function(CronExpressionEnum2) {
  CronExpressionEnum2["EVERY_SECOND"] = "* * * * * *";
  CronExpressionEnum2["EVERY_5_SECONDS"] = "*/5 * * * * *";
  CronExpressionEnum2["EVERY_10_SECONDS"] = "*/10 * * * * *";
  CronExpressionEnum2["EVERY_30_SECONDS"] = "*/30 * * * * *";
  CronExpressionEnum2["EVERY_MINUTE"] = "*/1 * * * *";
  CronExpressionEnum2["EVERY_5_MINUTES"] = "0 */5 * * * *";
  CronExpressionEnum2["EVERY_10_MINUTES"] = "0 */10 * * * *";
  CronExpressionEnum2["EVERY_30_MINUTES"] = "0 */30 * * * *";
  CronExpressionEnum2["EVERY_HOUR"] = "0 0-23/1 * * *";
  CronExpressionEnum2["EVERY_2_HOURS"] = "0 0-23/2 * * *";
  CronExpressionEnum2["EVERY_3_HOURS"] = "0 0-23/3 * * *";
  CronExpressionEnum2["EVERY_4_HOURS"] = "0 0-23/4 * * *";
  CronExpressionEnum2["EVERY_5_HOURS"] = "0 0-23/5 * * *";
  CronExpressionEnum2["EVERY_6_HOURS"] = "0 0-23/6 * * *";
  CronExpressionEnum2["EVERY_7_HOURS"] = "0 0-23/7 * * *";
  CronExpressionEnum2["EVERY_8_HOURS"] = "0 0-23/8 * * *";
  CronExpressionEnum2["EVERY_9_HOURS"] = "0 0-23/9 * * *";
  CronExpressionEnum2["EVERY_10_HOURS"] = "0 0-23/10 * * *";
  CronExpressionEnum2["EVERY_11_HOURS"] = "0 0-23/11 * * *";
  CronExpressionEnum2["EVERY_12_HOURS"] = "0 0-23/12 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_1AM"] = "0 01 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_2AM"] = "0 02 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_3AM"] = "0 03 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_4AM"] = "0 04 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_5AM"] = "0 05 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_6AM"] = "0 06 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_7AM"] = "0 07 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_8AM"] = "0 08 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_9AM"] = "0 09 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_10AM"] = "0 10 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_11AM"] = "0 11 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_NOON"] = "0 12 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_1PM"] = "0 13 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_2PM"] = "0 14 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_3PM"] = "0 15 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_4PM"] = "0 16 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_5PM"] = "0 17 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_6PM"] = "0 18 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_7PM"] = "0 19 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_8PM"] = "0 20 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_9PM"] = "0 21 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_10PM"] = "0 22 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_11PM"] = "0 23 * * *";
  CronExpressionEnum2["EVERY_DAY_AT_MIDNIGHT"] = "0 0 * * *";
  CronExpressionEnum2["EVERY_WEEK"] = "0 0 * * 0";
  CronExpressionEnum2["EVERY_WEEKDAY"] = "0 0 * * 1-5";
  CronExpressionEnum2["EVERY_WEEKEND"] = "0 0 * * 6,0";
  CronExpressionEnum2["EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT"] = "0 0 1 * *";
  CronExpressionEnum2["EVERY_1ST_DAY_OF_MONTH_AT_NOON"] = "0 12 1 * *";
  CronExpressionEnum2["EVERY_2ND_HOUR"] = "0 */2 * * *";
  CronExpressionEnum2["EVERY_2ND_HOUR_FROM_1AM_THROUGH_11PM"] = "0 1-23/2 * * *";
  CronExpressionEnum2["EVERY_2ND_MONTH"] = "0 0 1 */2 *";
  CronExpressionEnum2["EVERY_QUARTER"] = "0 0 1 */3 *";
  CronExpressionEnum2["EVERY_6_MONTHS"] = "0 0 1 */6 *";
  CronExpressionEnum2["EVERY_YEAR"] = "0 0 1 0 *";
  CronExpressionEnum2["EVERY_30_MINUTES_BETWEEN_9AM_AND_5PM"] = "0 */30 9-17 * * *";
  CronExpressionEnum2["EVERY_30_MINUTES_BETWEEN_9AM_AND_6PM"] = "0 */30 9-18 * * *";
  CronExpressionEnum2["EVERY_30_MINUTES_BETWEEN_10AM_AND_7PM"] = "0 */30 10-19 * * *";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_1AM"] = "0 0 01 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_2AM"] = "0 0 02 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_3AM"] = "0 0 03 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_4AM"] = "0 0 04 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_5AM"] = "0 0 05 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_6AM"] = "0 0 06 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_7AM"] = "0 0 07 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_8AM"] = "0 0 08 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_9AM"] = "0 0 09 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_09_30AM"] = "0 30 09 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_10AM"] = "0 0 10 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_11AM"] = "0 0 11 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_11_30AM"] = "0 30 11 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_12PM"] = "0 0 12 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_1PM"] = "0 0 13 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_2PM"] = "0 0 14 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_3PM"] = "0 0 15 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_4PM"] = "0 0 16 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_5PM"] = "0 0 17 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_6PM"] = "0 0 18 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_7PM"] = "0 0 19 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_8PM"] = "0 0 20 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_9PM"] = "0 0 21 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_10PM"] = "0 0 22 * * 1-5";
  CronExpressionEnum2["MONDAY_TO_FRIDAY_AT_11PM"] = "0 0 23 * * 1-5";
})(CronExpressionEnum || (CronExpressionEnum = {}));

// node_modules/@novu/shared/dist/esm/types/product-features/index.js
var ProductFeatureKeyEnum;
(function(ProductFeatureKeyEnum2) {
  ProductFeatureKeyEnum2[ProductFeatureKeyEnum2["TRANSLATIONS"] = 0] = "TRANSLATIONS";
})(ProductFeatureKeyEnum || (ProductFeatureKeyEnum = {}));

// node_modules/@novu/shared/dist/esm/types/resource-limiting/resource.types.js
var ResourceEnum;
(function(ResourceEnum2) {
  ResourceEnum2["EVENTS"] = "events";
})(ResourceEnum || (ResourceEnum = {}));

// node_modules/@novu/shared/dist/esm/types/files/index.js
var FileExtensionEnum;
(function(FileExtensionEnum2) {
  FileExtensionEnum2["JPEG"] = "jpeg";
  FileExtensionEnum2["PNG"] = "png";
  FileExtensionEnum2["JPG"] = "jpg";
})(FileExtensionEnum || (FileExtensionEnum = {}));
var MimeTypesEnum;
(function(MimeTypesEnum2) {
  MimeTypesEnum2["JPEG"] = "image/jpeg";
  MimeTypesEnum2["PNG"] = "image/png";
  MimeTypesEnum2["JPG"] = "image/jpeg";
})(MimeTypesEnum || (MimeTypesEnum = {}));
var FILE_EXTENSION_TO_MIME_TYPE = {
  [FileExtensionEnum.JPEG]: MimeTypesEnum.JPEG,
  [FileExtensionEnum.PNG]: MimeTypesEnum.PNG,
  [FileExtensionEnum.JPG]: MimeTypesEnum.JPG
};
var MIME_TYPE_TO_FILE_EXTENSION = {
  [MimeTypesEnum.JPEG]: FileExtensionEnum.JPEG,
  [MimeTypesEnum.PNG]: FileExtensionEnum.PNG,
  [MimeTypesEnum.JPG]: FileExtensionEnum.JPG
};

// node_modules/@novu/shared/dist/esm/types/storage/index.js
var UploadTypesEnum;
(function(UploadTypesEnum2) {
  UploadTypesEnum2["BRANDING"] = "BRANDING";
  UploadTypesEnum2["USER_PROFILE"] = "USER_PROFILE";
})(UploadTypesEnum || (UploadTypesEnum = {}));

// node_modules/@novu/shared/dist/esm/ui/marketing.js
var UTM_CAMPAIGN_QUERY_PARAM = "?utm_campaign=in-app";

// node_modules/@novu/shared/dist/esm/consts/providers/channels/email.js
var emailProviders = [
  {
    id: EmailProviderIdEnum.Novu,
    displayName: "Novu Email",
    channel: ChannelTypeEnum.EMAIL,
    credentials: [],
    docReference: `https://docs.novu.co/channels-and-providers/default-providers${UTM_CAMPAIGN_QUERY_PARAM}#novu-email-provider`,
    logoFileName: { light: "novu.png", dark: "novu.png" }
  },
  {
    id: EmailProviderIdEnum.Mailgun,
    displayName: "Mailgun",
    channel: ChannelTypeEnum.EMAIL,
    credentials: mailgunConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/mailgun${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "mailgun.svg", dark: "mailgun.svg" }
  },
  {
    id: EmailProviderIdEnum.Mailjet,
    displayName: "Mailjet",
    channel: ChannelTypeEnum.EMAIL,
    credentials: mailjetConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/mailjet${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "mailjet.png", dark: "mailjet.png" }
  },
  {
    id: EmailProviderIdEnum.Mailtrap,
    displayName: "Mailtrap",
    channel: ChannelTypeEnum.EMAIL,
    credentials: mailtrapConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/mailtrap${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "mailtrap.svg", dark: "mailtrap.svg" }
  },
  {
    id: EmailProviderIdEnum.Mandrill,
    displayName: "Mandrill",
    channel: ChannelTypeEnum.EMAIL,
    credentials: mandrillConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/mandrill${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "mandrill.svg", dark: "mandrill.svg" }
  },
  {
    id: EmailProviderIdEnum.Postmark,
    displayName: "Postmark",
    channel: ChannelTypeEnum.EMAIL,
    credentials: postmarkConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/postmark${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "postmark.png", dark: "postmark.png" }
  },
  {
    id: EmailProviderIdEnum.SendGrid,
    displayName: "SendGrid",
    channel: ChannelTypeEnum.EMAIL,
    credentials: sendgridConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/sendgrid${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sendgrid.png", dark: "sendgrid.png" }
  },
  {
    id: EmailProviderIdEnum.Sendinblue,
    displayName: "Sendinblue",
    channel: ChannelTypeEnum.EMAIL,
    credentials: sendinblueConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/sendinblue${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sendinblue.png", dark: "sendinblue.png" }
  },
  {
    id: EmailProviderIdEnum.SES,
    displayName: "SES",
    channel: ChannelTypeEnum.EMAIL,
    credentials: sesConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/amazonses${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "ses.svg", dark: "ses.svg" }
  },
  {
    id: EmailProviderIdEnum.NetCore,
    displayName: "Netcore",
    channel: ChannelTypeEnum.EMAIL,
    credentials: netCoreConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/netcore${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "netcore.png", dark: "netcore.png" }
  },
  {
    id: EmailProviderIdEnum.CustomSMTP,
    displayName: "Custom SMTP",
    channel: ChannelTypeEnum.EMAIL,
    credentials: nodemailerConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/custom-smtp${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "custom_smtp.svg", dark: "custom_smtp.svg" }
  },
  {
    id: EmailProviderIdEnum.MailerSend,
    displayName: "MailerSend",
    channel: ChannelTypeEnum.EMAIL,
    credentials: mailerSendConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/mailersend${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "mailersend.svg", dark: "mailersend.svg" }
  },
  {
    id: EmailProviderIdEnum.Outlook365,
    displayName: "Microsoft Outlook365",
    channel: ChannelTypeEnum.EMAIL,
    credentials: outlook365Config,
    docReference: `https://docs.novu.co/channels-and-providers/email/outlook365${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "outlook365.png", dark: "outlook365.png" }
  },
  {
    id: EmailProviderIdEnum.Infobip,
    displayName: "Infobip",
    channel: ChannelTypeEnum.EMAIL,
    credentials: infobipEmailConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/infobip${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "infobip.png", dark: "infobip.png" }
  },
  {
    id: EmailProviderIdEnum.Braze,
    displayName: "Braze",
    channel: ChannelTypeEnum.EMAIL,
    credentials: brazeEmailConfig,
    docReference: "https://www.braze.com/docs/api/endpoints/messaging/send_messages/post_send_messages/",
    logoFileName: { light: "braze.svg", dark: "braze.svg" }
  },
  {
    id: EmailProviderIdEnum.Resend,
    displayName: "Resend",
    channel: ChannelTypeEnum.EMAIL,
    credentials: resendConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/resend${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "resend.svg", dark: "resend.svg" }
  },
  {
    id: EmailProviderIdEnum.Plunk,
    displayName: "Plunk",
    channel: ChannelTypeEnum.EMAIL,
    credentials: plunkConfig,
    docReference: `https://docs.novu.co/channels/email/plunk${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "plunk.png", dark: "plunk.png" }
  },
  {
    id: EmailProviderIdEnum.SparkPost,
    displayName: "SparkPost",
    channel: ChannelTypeEnum.EMAIL,
    credentials: sparkpostConfig,
    docReference: `https://docs.novu.co/channels-and-providers/email/sparkpost${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sparkpost.svg", dark: "sparkpost.svg" }
  },
  {
    id: EmailProviderIdEnum.EmailWebhook,
    displayName: "Email Webhook",
    channel: ChannelTypeEnum.EMAIL,
    credentials: emailWebhookConfig,
    betaVersion: true,
    docReference: `https://docs.novu.co/channels/email/email-webhook${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "email_webhook.svg", dark: "email_webhook.svg" }
  }
];

// node_modules/@novu/shared/dist/esm/consts/providers/channels/sms.js
var smsProviders = [
  {
    id: SmsProviderIdEnum.Novu,
    displayName: "Novu SMS",
    channel: ChannelTypeEnum.SMS,
    credentials: [],
    docReference: `https://docs.novu.co/channels-and-providers/default-providers${UTM_CAMPAIGN_QUERY_PARAM}#novu-sms-provider`,
    logoFileName: { light: "novu.png", dark: "novu.png" }
  },
  {
    id: SmsProviderIdEnum.Nexmo,
    displayName: "Nexmo",
    channel: ChannelTypeEnum.SMS,
    credentials: nexmoConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/nexmo${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "nexmo.png", dark: "nexmo.png" }
  },
  {
    id: SmsProviderIdEnum.Plivo,
    displayName: "Plivo",
    channel: ChannelTypeEnum.SMS,
    credentials: plivoConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/plivo${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "plivo.png", dark: "plivo.png" }
  },
  {
    id: SmsProviderIdEnum.Sms77,
    displayName: "sms77",
    channel: ChannelTypeEnum.SMS,
    credentials: sms77Config,
    docReference: `https://docs.novu.co/channels-and-providers/sms/sms77${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sms77.svg", dark: "sms77.svg" }
  },
  {
    id: SmsProviderIdEnum.SNS,
    displayName: "SNS",
    channel: ChannelTypeEnum.SMS,
    credentials: snsConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/aws-sns${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sns.svg", dark: "sns.svg" }
  },
  {
    id: SmsProviderIdEnum.Telnyx,
    displayName: "Telnyx",
    channel: ChannelTypeEnum.SMS,
    credentials: telnyxConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/telnyx${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "telnyx.png", dark: "telnyx.png" }
  },
  {
    id: SmsProviderIdEnum.MessageBird,
    displayName: "MessageBird",
    channel: ChannelTypeEnum.SMS,
    credentials: messagebirdConfig,
    docReference: "https://developers.messagebird.com/quickstarts/sms-overview/",
    logoFileName: { light: "messagebird.png", dark: "messagebird.png" }
  },
  {
    id: SmsProviderIdEnum.Twilio,
    displayName: "Twilio",
    channel: ChannelTypeEnum.SMS,
    credentials: twilioConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/twilio${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "twilio.png", dark: "twilio.png" }
  },
  {
    id: SmsProviderIdEnum.Gupshup,
    displayName: "Gupshup",
    channel: ChannelTypeEnum.SMS,
    credentials: gupshupConfig,
    docReference: "https://docs.gupshup.io/docs/send-single-message",
    logoFileName: { light: "gupshup.png", dark: "gupshup.png" }
  },
  {
    id: SmsProviderIdEnum.Firetext,
    displayName: "Firetext",
    channel: ChannelTypeEnum.SMS,
    credentials: firetextConfig,
    docReference: "https://www.firetext.co.uk/docs",
    logoFileName: { light: "firetext.svg", dark: "firetext.svg" }
  },
  {
    id: SmsProviderIdEnum.Infobip,
    displayName: "Infobip",
    channel: ChannelTypeEnum.SMS,
    credentials: infobipSMSConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/infobip${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "infobip.png", dark: "infobip.png" }
  },
  {
    id: SmsProviderIdEnum.BurstSms,
    displayName: "BurstSMS",
    channel: ChannelTypeEnum.SMS,
    credentials: burstSmsConfig,
    docReference: "https://developer.transmitsms.com/",
    logoFileName: { light: "burst-sms.svg", dark: "burst-sms.svg" }
  },
  {
    id: SmsProviderIdEnum.BulkSms,
    displayName: "BulkSMS",
    channel: ChannelTypeEnum.SMS,
    credentials: bulkSmsConfig,
    docReference: "https://www.bulksms.com/developer/json/v1/",
    logoFileName: { light: "bulk-sms.png", dark: "bulk-sms.png" }
  },
  {
    id: SmsProviderIdEnum.ISendSms,
    displayName: "iSend SMS",
    channel: ChannelTypeEnum.SMS,
    credentials: iSendSmsConfig,
    docReference: "https://send.com.ly/developers/docs",
    logoFileName: { light: "isend-sms.svg", dark: "isend-sms.svg" }
  },
  {
    id: SmsProviderIdEnum.Clickatell,
    displayName: "clickatell",
    channel: ChannelTypeEnum.SMS,
    credentials: clickatellConfig,
    betaVersion: true,
    docReference: "https://docs.clickatell.com/",
    logoFileName: { light: "clickatell.png", dark: "clickatell.png" }
  },
  {
    id: SmsProviderIdEnum.FortySixElks,
    displayName: "46elks",
    channel: ChannelTypeEnum.SMS,
    credentials: fortySixElksConfig,
    docReference: "https://46elks.com/docs/send-sms",
    logoFileName: { light: "46elks.png", dark: "46elks.png" }
  },
  {
    id: SmsProviderIdEnum.Kannel,
    displayName: "Kannel SMS",
    channel: ChannelTypeEnum.SMS,
    credentials: kannelConfig,
    betaVersion: true,
    docReference: "https://www.kannel.org/doc.shtml",
    logoFileName: { light: "kannel.png", dark: "kannel.png" }
  },
  {
    id: SmsProviderIdEnum.Maqsam,
    displayName: "Maqsam",
    channel: ChannelTypeEnum.SMS,
    credentials: maqsamConfig,
    docReference: "https://portal.maqsam.com/docs/v2/sms",
    logoFileName: { light: "maqsam.png", dark: "maqsam.png" }
  },
  {
    id: SmsProviderIdEnum.SmsCentral,
    displayName: "SMS Central",
    channel: ChannelTypeEnum.SMS,
    credentials: smsCentralConfig,
    docReference: "https://www.smscentral.com.au/sms-api/",
    logoFileName: { light: "sms-central.png", dark: "sms-central.png" }
  },
  {
    id: SmsProviderIdEnum.Termii,
    displayName: "Termii",
    channel: ChannelTypeEnum.SMS,
    credentials: termiiConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/termii${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "termii.png", dark: "termii.png" }
  },
  {
    id: SmsProviderIdEnum.AfricasTalking,
    displayName: `Africa's Talking`,
    channel: ChannelTypeEnum.SMS,
    credentials: africasTalkingConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/africas-talking${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "africas-talking.svg", dark: "africas-talking.svg" }
  },
  {
    id: SmsProviderIdEnum.Sendchamp,
    displayName: `Sendchamp`,
    channel: ChannelTypeEnum.SMS,
    credentials: sendchampConfig,
    docReference: `https://docs.novu.co/channels-and-providers/sms/sendchamp${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "sendchamp.svg", dark: "sendchamp.svg" }
  },
  {
    id: SmsProviderIdEnum.GenericSms,
    displayName: `Generic SMS`,
    channel: ChannelTypeEnum.SMS,
    credentials: genericSmsConfig,
    docReference: `https://docs.novu.co/channels/sms/generic-sms${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "generic-sms.svg", dark: "generic-sms.svg" }
  },
  {
    id: SmsProviderIdEnum.Clicksend,
    displayName: `Clicksend`,
    channel: ChannelTypeEnum.SMS,
    credentials: clickSendConfig,
    docReference: "https://developers.clicksend.com/docs/rest/v3/?javascript--nodejs#send-sms",
    logoFileName: { light: "clicksend.png", dark: "clicksend.png" }
  },
  {
    id: SmsProviderIdEnum.Simpletexting,
    displayName: `SimpleTexting`,
    channel: ChannelTypeEnum.SMS,
    credentials: simpleTextingConfig,
    docReference: "https://simpletexting.com/api/docs/v2/",
    logoFileName: { light: "simpletexting.png", dark: "simpletexting.png" }
  },
  {
    id: SmsProviderIdEnum.Bandwidth,
    displayName: `Bandwidth`,
    channel: ChannelTypeEnum.SMS,
    credentials: bandwidthConfig,
    betaVersion: true,
    docReference: "https://dev.bandwidth.com/docs/messaging/createMessage",
    logoFileName: { light: "bandwidth.png", dark: "bandwidth.png" }
  },
  {
    id: SmsProviderIdEnum.AzureSms,
    displayName: `Azure Sms`,
    channel: ChannelTypeEnum.SMS,
    credentials: azureSmsConfig,
    docReference: "https://learn.microsoft.com/en-us/azure/communication-services/quickstarts/sms/receive-sms",
    logoFileName: { light: "azure-sms.png", dark: "azure-sms.png" }
  },
  {
    id: SmsProviderIdEnum.RingCentral,
    displayName: `RingCentral`,
    channel: ChannelTypeEnum.SMS,
    credentials: ringCentralConfig,
    docReference: "https://developers.ringcentral.com/guide/messaging",
    logoFileName: { light: "ring-central.svg", dark: "ring-central.svg" }
  },
  {
    id: SmsProviderIdEnum.BrevoSms,
    displayName: `Brevo`,
    channel: ChannelTypeEnum.SMS,
    credentials: brevoSmsConfig,
    docReference: "https://developers.brevo.com/reference/sendtransacsms",
    logoFileName: { light: "brevo.svg", dark: "brevo.svg" }
  },
  {
    id: SmsProviderIdEnum.EazySms,
    displayName: `Eazy`,
    channel: ChannelTypeEnum.SMS,
    credentials: eazySmsConfig,
    docReference: "https://developers.eazy.im/#678805af-be7b-4487-93a4-c1007b7920f5",
    logoFileName: { light: "eazy-sms.svg", dark: "eazy-sms.svg" }
  }
];

// node_modules/@novu/shared/dist/esm/consts/providers/channels/chat.js
var chatProviders = [
  {
    id: ChatProviderIdEnum.Slack,
    displayName: "Slack",
    channel: ChannelTypeEnum.CHAT,
    credentials: slackConfig,
    docReference: `https://docs.novu.co/channels-and-providers/chat/slack${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "slack.svg", dark: "slack.svg" }
  },
  {
    id: ChatProviderIdEnum.Discord,
    displayName: "Discord",
    channel: ChannelTypeEnum.CHAT,
    credentials: [],
    docReference: `https://docs.novu.co/channels-and-providers/chat/discord${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "discord.svg", dark: "discord.svg" }
  },
  {
    id: ChatProviderIdEnum.GrafanaOnCall,
    displayName: "Grafana On Call Webhook",
    channel: ChannelTypeEnum.CHAT,
    credentials: grafanaOnCallConfig,
    docReference: "https://grafana.com/docs/oncall/latest/integrations/webhook/",
    logoFileName: { light: "grafana-on-call.png", dark: "grafana-on-call.png" }
  },
  {
    id: ChatProviderIdEnum.MsTeams,
    displayName: "MSTeams",
    channel: ChannelTypeEnum.CHAT,
    credentials: [],
    docReference: `https://docs.novu.co/channels-and-providers/chat/ms-teams${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "msteams.svg", dark: "msteams.svg" }
  },
  {
    id: ChatProviderIdEnum.Mattermost,
    displayName: "Mattermost",
    channel: ChannelTypeEnum.CHAT,
    credentials: [],
    docReference: "https://developers.mattermost.com/integrate/webhooks/incoming/",
    logoFileName: { light: "mattermost.svg", dark: "mattermost.svg" }
  },
  {
    id: ChatProviderIdEnum.Ryver,
    displayName: "Ryver",
    channel: ChannelTypeEnum.CHAT,
    credentials: [],
    docReference: "https://api.ryver.com/ryvrest_api_examples.html#create-chat-message",
    logoFileName: { light: "ryver.png", dark: "ryver.png" }
  },
  {
    id: ChatProviderIdEnum.Zulip,
    displayName: "Zulip",
    channel: ChannelTypeEnum.CHAT,
    credentials: [],
    docReference: `https://docs.novu.co/channels-and-providers/chat/zulip${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "zulip.svg", dark: "zulip.svg" }
  },
  {
    id: ChatProviderIdEnum.GetStream,
    displayName: "GetStream",
    channel: ChannelTypeEnum.CHAT,
    credentials: getstreamConfig,
    docReference: "https://getstream.io/chat/docs/node/?language=javascript",
    logoFileName: { light: "getstream.svg", dark: "getstream.svg" }
  },
  {
    id: ChatProviderIdEnum.RocketChat,
    displayName: "Rocket.Chat",
    channel: ChannelTypeEnum.CHAT,
    credentials: rocketChatConfig,
    docReference: "https://developer.rocket.chat/reference/api/rest-api/endpoints",
    logoFileName: { light: "rocket-chat.svg", dark: "rocket-chat.svg" }
  },
  {
    id: ChatProviderIdEnum.WhatsAppBusiness,
    displayName: "WhatsApp Business",
    channel: ChannelTypeEnum.CHAT,
    credentials: whatsAppBusinessConfig,
    docReference: "https://developers.facebook.com/docs/whatsapp/cloud-api",
    logoFileName: { light: "whatsapp-business.svg", dark: "whatsapp-business.svg" }
  }
];

// node_modules/@novu/shared/dist/esm/consts/providers/channels/push.js
var pushProviders = [
  {
    id: PushProviderIdEnum.OneSignal,
    displayName: "OneSignal",
    channel: ChannelTypeEnum.PUSH,
    credentials: oneSignalConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/onesignal${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "one-signal.svg", dark: "one-signal.svg" }
  },
  {
    id: PushProviderIdEnum.Pushpad,
    displayName: "Pushpad",
    channel: ChannelTypeEnum.PUSH,
    credentials: pushpadConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/pushpad${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "pushpad.svg", dark: "pushpad.svg" }
  },
  {
    id: PushProviderIdEnum.FCM,
    displayName: "Firebase Cloud Messaging",
    channel: ChannelTypeEnum.PUSH,
    credentials: fcmConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/fcm${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "fcm.svg", dark: "fcm.svg" }
  },
  {
    id: PushProviderIdEnum.EXPO,
    displayName: "Expo Push",
    channel: ChannelTypeEnum.PUSH,
    credentials: expoConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/expo-push${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "expo.svg", dark: "expo.svg" }
  },
  {
    id: PushProviderIdEnum.APNS,
    displayName: "APNs",
    channel: ChannelTypeEnum.PUSH,
    credentials: apnsConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/apns${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "apns.png", dark: "apns.png" },
    betaVersion: true
  },
  {
    id: PushProviderIdEnum.PushWebhook,
    displayName: "Push Webhook",
    channel: ChannelTypeEnum.PUSH,
    credentials: pushWebhookConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/push-webhook${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "push-webhook.svg", dark: "push-webhook.svg" },
    betaVersion: true
  },
  {
    id: PushProviderIdEnum.PusherBeams,
    displayName: "Pusher Beams",
    channel: ChannelTypeEnum.PUSH,
    credentials: pusherBeamsConfig,
    docReference: `https://docs.novu.co/channels-and-providers/push/pusher-beams${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "pusher-beams.svg", dark: "pusher-beams.svg" }
  }
];

// node_modules/@novu/shared/dist/esm/consts/providers/channels/in-app.js
var inAppProviders = [
  {
    id: InAppProviderIdEnum.Novu,
    displayName: "Novu In-App",
    channel: ChannelTypeEnum.IN_APP,
    credentials: novuInAppConfig,
    docReference: `https://docs.novu.co/notification-center/introduction${UTM_CAMPAIGN_QUERY_PARAM}`,
    logoFileName: { light: "novu.png", dark: "novu.png" }
  }
];

// node_modules/@novu/shared/dist/esm/consts/providers/providers.js
var providers = [
  ...emailProviders,
  ...smsProviders,
  ...chatProviders,
  ...pushProviders,
  ...inAppProviders
];
var NOVU_PROVIDERS = [
  InAppProviderIdEnum.Novu,
  SmsProviderIdEnum.Novu,
  EmailProviderIdEnum.Novu
];
var NOVU_SMS_EMAIL_PROVIDERS = [SmsProviderIdEnum.Novu, EmailProviderIdEnum.Novu];

// node_modules/@novu/shared/dist/esm/entities/messages/action.enum.js
var ButtonTypeEnum;
(function(ButtonTypeEnum2) {
  ButtonTypeEnum2["PRIMARY"] = "primary";
  ButtonTypeEnum2["SECONDARY"] = "secondary";
  ButtonTypeEnum2["CLICKED"] = "clicked";
})(ButtonTypeEnum || (ButtonTypeEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/messages/messages.interface.js
var MessageActionStatusEnum;
(function(MessageActionStatusEnum2) {
  MessageActionStatusEnum2["PENDING"] = "pending";
  MessageActionStatusEnum2["DONE"] = "done";
})(MessageActionStatusEnum || (MessageActionStatusEnum = {}));

// node_modules/@novu/shared/dist/esm/consts/notification-item-buttons/notificationItemButton.js
var primaryButton = {
  key: ButtonTypeEnum.PRIMARY,
  displayName: "Primary"
};
var secondaryButton = {
  key: ButtonTypeEnum.SECONDARY,
  displayName: "Secondary"
};

// node_modules/@novu/shared/dist/esm/consts/handlebar-helpers/handlebarHelpers.js
var HandlebarHelpersEnum;
(function(HandlebarHelpersEnum2) {
  HandlebarHelpersEnum2["EQUALS"] = "equals";
  HandlebarHelpersEnum2["TITLECASE"] = "titlecase";
  HandlebarHelpersEnum2["UPPERCASE"] = "uppercase";
  HandlebarHelpersEnum2["LOWERCASE"] = "lowercase";
  HandlebarHelpersEnum2["PLURALIZE"] = "pluralize";
  HandlebarHelpersEnum2["DATEFORMAT"] = "dateFormat";
  HandlebarHelpersEnum2["UNIQUE"] = "unique";
  HandlebarHelpersEnum2["GROUP_BY"] = "groupBy";
  HandlebarHelpersEnum2["SORT_BY"] = "sortBy";
  HandlebarHelpersEnum2["NUMBERFORMAT"] = "numberFormat";
  HandlebarHelpersEnum2["I18N"] = "i18n";
  HandlebarHelpersEnum2["GT"] = "gt";
  HandlebarHelpersEnum2["GTE"] = "gte";
  HandlebarHelpersEnum2["LT"] = "lt";
  HandlebarHelpersEnum2["LTE"] = "lte";
  HandlebarHelpersEnum2["EQ"] = "eq";
  HandlebarHelpersEnum2["NE"] = "ne";
})(HandlebarHelpersEnum || (HandlebarHelpersEnum = {}));
var HandlebarHelpers = {
  [HandlebarHelpersEnum.EQUALS]: { description: "assert equal" },
  [HandlebarHelpersEnum.TITLECASE]: { description: "transform to TitleCase" },
  [HandlebarHelpersEnum.UPPERCASE]: { description: "transform to UPPERCASE" },
  [HandlebarHelpersEnum.LOWERCASE]: { description: "transform to lowercase" },
  [HandlebarHelpersEnum.PLURALIZE]: { description: "pluralize if needed" },
  [HandlebarHelpersEnum.DATEFORMAT]: { description: "format date" },
  [HandlebarHelpersEnum.UNIQUE]: { description: "filter unique values in an array" },
  [HandlebarHelpersEnum.GROUP_BY]: { description: "group by a property" },
  [HandlebarHelpersEnum.SORT_BY]: { description: "sort an array of objects by a property" },
  [HandlebarHelpersEnum.NUMBERFORMAT]: { description: "format number" },
  [HandlebarHelpersEnum.I18N]: { description: "translate" },
  [HandlebarHelpersEnum.GT]: { description: "greater than" },
  [HandlebarHelpersEnum.GTE]: { description: "greater than or equal to" },
  [HandlebarHelpersEnum.LT]: { description: "lesser than" },
  [HandlebarHelpersEnum.LTE]: { description: "lesser than or equal to" },
  [HandlebarHelpersEnum.EQ]: { description: "strict equal" },
  [HandlebarHelpersEnum.NE]: { description: "strict not equal to" }
};

// node_modules/@novu/shared/dist/esm/consts/password-helper/PasswordResetFlowEnum.js
var PasswordResetFlowEnum;
(function(PasswordResetFlowEnum2) {
  PasswordResetFlowEnum2["FORGOT_PASSWORD"] = "FORGOT_PASSWORD";
  PasswordResetFlowEnum2["USER_PROFILE"] = "USER_PROFILE";
})(PasswordResetFlowEnum || (PasswordResetFlowEnum = {}));

// node_modules/@novu/shared/dist/esm/consts/filters/filters.js
var FILTER_TO_LABEL = {
  [FilterPartTypeEnum.PAYLOAD]: "Payload",
  [FilterPartTypeEnum.TENANT]: "Tenant",
  [FilterPartTypeEnum.SUBSCRIBER]: "Subscriber",
  [FilterPartTypeEnum.WEBHOOK]: "Webhook",
  [FilterPartTypeEnum.IS_ONLINE]: "Is online",
  [FilterPartTypeEnum.IS_ONLINE_IN_LAST]: "Last time was online",
  [FilterPartTypeEnum.PREVIOUS_STEP]: "Previous step"
};

// node_modules/@novu/shared/dist/esm/consts/rate-limiting/apiRateLimits.js
var DEFAULT_API_RATE_LIMIT_SERVICE_MAXIMUM_CONFIG = {
  [ApiServiceLevelEnum.FREE]: {
    [ApiRateLimitCategoryEnum.TRIGGER]: 60,
    [ApiRateLimitCategoryEnum.CONFIGURATION]: 15,
    [ApiRateLimitCategoryEnum.GLOBAL]: 30
  },
  [ApiServiceLevelEnum.BUSINESS]: {
    [ApiRateLimitCategoryEnum.TRIGGER]: 600,
    [ApiRateLimitCategoryEnum.CONFIGURATION]: 150,
    [ApiRateLimitCategoryEnum.GLOBAL]: 300
  },
  [ApiServiceLevelEnum.ENTERPRISE]: {
    [ApiRateLimitCategoryEnum.TRIGGER]: 6e3,
    [ApiRateLimitCategoryEnum.CONFIGURATION]: 1500,
    [ApiRateLimitCategoryEnum.GLOBAL]: 3e3
  },
  [ApiServiceLevelEnum.UNLIMITED]: {
    [ApiRateLimitCategoryEnum.TRIGGER]: 6e3,
    [ApiRateLimitCategoryEnum.CONFIGURATION]: 1500,
    [ApiRateLimitCategoryEnum.GLOBAL]: 3e3
  }
};
var DEFAULT_API_RATE_LIMIT_ALGORITHM_CONFIG = {
  [ApiRateLimitAlgorithmEnum.BURST_ALLOWANCE]: 0.1,
  [ApiRateLimitAlgorithmEnum.WINDOW_DURATION]: 1
};
var DEFAULT_API_RATE_LIMIT_COST_CONFIG = {
  [ApiRateLimitCostEnum.SINGLE]: 1,
  [ApiRateLimitCostEnum.BULK]: 100
};

// node_modules/@novu/shared/dist/esm/consts/productFeatureEnabledForServiceLevel.js
var productFeatureEnabledForServiceLevel = Object.freeze({
  [ProductFeatureKeyEnum.TRANSLATIONS]: [ApiServiceLevelEnum.BUSINESS, ApiServiceLevelEnum.ENTERPRISE]
});

// node_modules/@novu/shared/dist/esm/dto/layout/layout.dto.js
var LayoutDto = class {
};

// node_modules/@novu/shared/dist/esm/entities/change/change.interface.js
var ChangeEntityTypeEnum;
(function(ChangeEntityTypeEnum2) {
  ChangeEntityTypeEnum2["FEED"] = "Feed";
  ChangeEntityTypeEnum2["MESSAGE_TEMPLATE"] = "MessageTemplate";
  ChangeEntityTypeEnum2["LAYOUT"] = "Layout";
  ChangeEntityTypeEnum2["DEFAULT_LAYOUT"] = "DefaultLayout";
  ChangeEntityTypeEnum2["NOTIFICATION_TEMPLATE"] = "NotificationTemplate";
  ChangeEntityTypeEnum2["NOTIFICATION_GROUP"] = "NotificationGroup";
  ChangeEntityTypeEnum2["TRANSLATION_GROUP"] = "TranslationGroup";
  ChangeEntityTypeEnum2["TRANSLATION"] = "Translation";
})(ChangeEntityTypeEnum || (ChangeEntityTypeEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/execution-details/execution-details.interface.js
var ExecutionDetailsSourceEnum;
(function(ExecutionDetailsSourceEnum2) {
  ExecutionDetailsSourceEnum2["CREDENTIALS"] = "Credentials";
  ExecutionDetailsSourceEnum2["INTERNAL"] = "Internal";
  ExecutionDetailsSourceEnum2["PAYLOAD"] = "Payload";
  ExecutionDetailsSourceEnum2["WEBHOOK"] = "Webhook";
})(ExecutionDetailsSourceEnum || (ExecutionDetailsSourceEnum = {}));
var ExecutionDetailsStatusEnum;
(function(ExecutionDetailsStatusEnum2) {
  ExecutionDetailsStatusEnum2["SUCCESS"] = "Success";
  ExecutionDetailsStatusEnum2["WARNING"] = "Warning";
  ExecutionDetailsStatusEnum2["FAILED"] = "Failed";
  ExecutionDetailsStatusEnum2["PENDING"] = "Pending";
  ExecutionDetailsStatusEnum2["QUEUED"] = "Queued";
  ExecutionDetailsStatusEnum2["READ_CONFIRMATION"] = "ReadConfirmation";
})(ExecutionDetailsStatusEnum || (ExecutionDetailsStatusEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/job/status.enum.js
var JobStatusEnum;
(function(JobStatusEnum2) {
  JobStatusEnum2["PENDING"] = "pending";
  JobStatusEnum2["QUEUED"] = "queued";
  JobStatusEnum2["RUNNING"] = "running";
  JobStatusEnum2["COMPLETED"] = "completed";
  JobStatusEnum2["FAILED"] = "failed";
  JobStatusEnum2["DELAYED"] = "delayed";
  JobStatusEnum2["CANCELED"] = "canceled";
  JobStatusEnum2["MERGED"] = "merged";
  JobStatusEnum2["SKIPPED"] = "skipped";
})(JobStatusEnum || (JobStatusEnum = {}));
var DigestCreationResultEnum;
(function(DigestCreationResultEnum2) {
  DigestCreationResultEnum2["MERGED"] = "MERGED";
  DigestCreationResultEnum2["CREATED"] = "CREATED";
  DigestCreationResultEnum2["SKIPPED"] = "SKIPPED";
})(DigestCreationResultEnum || (DigestCreationResultEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/log/log.enums.js
var LogStatusEnum;
(function(LogStatusEnum2) {
  LogStatusEnum2["ERROR"] = "error";
  LogStatusEnum2["SUCCESS"] = "success";
  LogStatusEnum2["INFO"] = "info";
})(LogStatusEnum || (LogStatusEnum = {}));
var LogCodeEnum;
(function(LogCodeEnum2) {
  LogCodeEnum2[LogCodeEnum2["TRIGGER_RECEIVED"] = 1e3] = "TRIGGER_RECEIVED";
  LogCodeEnum2[LogCodeEnum2["TEMPLATE_NOT_ACTIVE"] = 1001] = "TEMPLATE_NOT_ACTIVE";
  LogCodeEnum2[LogCodeEnum2["TEMPLATE_NOT_FOUND"] = 1002] = "TEMPLATE_NOT_FOUND";
  LogCodeEnum2[LogCodeEnum2["SMS_ERROR"] = 1004] = "SMS_ERROR";
  LogCodeEnum2[LogCodeEnum2["CHAT_ERROR"] = 1005] = "CHAT_ERROR";
  LogCodeEnum2[LogCodeEnum2["MISSING_SMS_PROVIDER"] = 1006] = "MISSING_SMS_PROVIDER";
  LogCodeEnum2[LogCodeEnum2["IN_APP_MESSAGE_CREATED"] = 1007] = "IN_APP_MESSAGE_CREATED";
  LogCodeEnum2[LogCodeEnum2["MAIL_PROVIDER_DELIVERY_ERROR"] = 1008] = "MAIL_PROVIDER_DELIVERY_ERROR";
  LogCodeEnum2[LogCodeEnum2["TRIGGER_PROCESSED"] = 1009] = "TRIGGER_PROCESSED";
  LogCodeEnum2[LogCodeEnum2["PUSH_ERROR"] = 1010] = "PUSH_ERROR";
  LogCodeEnum2[LogCodeEnum2["MISSING_PUSH_PROVIDER"] = 1011] = "MISSING_PUSH_PROVIDER";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_NOT_FOUND"] = 3001] = "SUBSCRIBER_NOT_FOUND";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_MISSING_EMAIL"] = 3002] = "SUBSCRIBER_MISSING_EMAIL";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_MISSING_PHONE"] = 3003] = "SUBSCRIBER_MISSING_PHONE";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_MISSING_CHAT_CHANNEL_ID"] = 3006] = "SUBSCRIBER_MISSING_CHAT_CHANNEL_ID";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_ID_MISSING"] = 3004] = "SUBSCRIBER_ID_MISSING";
  LogCodeEnum2[LogCodeEnum2["MISSING_EMAIL_INTEGRATION"] = 3005] = "MISSING_EMAIL_INTEGRATION";
  LogCodeEnum2[LogCodeEnum2["MISSING_SMS_INTEGRATION"] = 3007] = "MISSING_SMS_INTEGRATION";
  LogCodeEnum2[LogCodeEnum2["MISSING_CHAT_INTEGRATION"] = 3008] = "MISSING_CHAT_INTEGRATION";
  LogCodeEnum2[LogCodeEnum2["MISSING_PUSH_INTEGRATION"] = 3009] = "MISSING_PUSH_INTEGRATION";
  LogCodeEnum2[LogCodeEnum2["SUBSCRIBER_MISSING_PUSH"] = 3010] = "SUBSCRIBER_MISSING_PUSH";
  LogCodeEnum2[LogCodeEnum2["MISSING_PAYLOAD_VARIABLE"] = 3011] = "MISSING_PAYLOAD_VARIABLE";
  LogCodeEnum2[LogCodeEnum2["AVATAR_ACTOR_ERROR"] = 3012] = "AVATAR_ACTOR_ERROR";
  LogCodeEnum2[LogCodeEnum2["SYNTAX_ERROR_IN_EMAIL_EDITOR"] = 3013] = "SYNTAX_ERROR_IN_EMAIL_EDITOR";
  LogCodeEnum2[LogCodeEnum2["TOPIC_ERROR"] = 4001] = "TOPIC_ERROR";
  LogCodeEnum2[LogCodeEnum2["TOPIC_SUBSCRIBERS_ERROR"] = 4002] = "TOPIC_SUBSCRIBERS_ERROR";
})(LogCodeEnum || (LogCodeEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/notification-template/notification-template.interface.js
var NotificationTemplateTypeEnum;
(function(NotificationTemplateTypeEnum2) {
  NotificationTemplateTypeEnum2["REGULAR"] = "REGULAR";
  NotificationTemplateTypeEnum2["ECHO"] = "ECHO";
})(NotificationTemplateTypeEnum || (NotificationTemplateTypeEnum = {}));
var TriggerTypeEnum;
(function(TriggerTypeEnum2) {
  TriggerTypeEnum2["EVENT"] = "event";
})(TriggerTypeEnum || (TriggerTypeEnum = {}));
var TriggerContextTypeEnum;
(function(TriggerContextTypeEnum2) {
  TriggerContextTypeEnum2["TENANT"] = "tenant";
  TriggerContextTypeEnum2["ACTOR"] = "actor";
})(TriggerContextTypeEnum || (TriggerContextTypeEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/message-template/message-template.interface.js
var ReservedVariablesMap = {
  [TriggerContextTypeEnum.TENANT]: [{ name: "identifier", type: TemplateVariableTypeEnum.STRING }],
  [TriggerContextTypeEnum.ACTOR]: [{ name: "subscriberId", type: TemplateVariableTypeEnum.STRING }]
};

// node_modules/@novu/shared/dist/esm/entities/organization/member.enum.js
var MemberRoleEnum;
(function(MemberRoleEnum2) {
  MemberRoleEnum2["ADMIN"] = "admin";
  MemberRoleEnum2["MEMBER"] = "member";
})(MemberRoleEnum || (MemberRoleEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/organization/member.interface.js
var MemberStatusEnum;
(function(MemberStatusEnum2) {
  MemberStatusEnum2["NEW"] = "new";
  MemberStatusEnum2["ACTIVE"] = "active";
  MemberStatusEnum2["INVITED"] = "invited";
})(MemberStatusEnum || (MemberStatusEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/step/index.js
var DigestUnitEnum;
(function(DigestUnitEnum2) {
  DigestUnitEnum2["SECONDS"] = "seconds";
  DigestUnitEnum2["MINUTES"] = "minutes";
  DigestUnitEnum2["HOURS"] = "hours";
  DigestUnitEnum2["DAYS"] = "days";
  DigestUnitEnum2["WEEKS"] = "weeks";
  DigestUnitEnum2["MONTHS"] = "months";
})(DigestUnitEnum || (DigestUnitEnum = {}));
var DaysEnum;
(function(DaysEnum2) {
  DaysEnum2["MONDAY"] = "monday";
  DaysEnum2["TUESDAY"] = "tuesday";
  DaysEnum2["WEDNESDAY"] = "wednesday";
  DaysEnum2["THURSDAY"] = "thursday";
  DaysEnum2["FRIDAY"] = "friday";
  DaysEnum2["SATURDAY"] = "saturday";
  DaysEnum2["SUNDAY"] = "sunday";
})(DaysEnum || (DaysEnum = {}));
var DigestTypeEnum;
(function(DigestTypeEnum2) {
  DigestTypeEnum2["REGULAR"] = "regular";
  DigestTypeEnum2["BACKOFF"] = "backoff";
  DigestTypeEnum2["TIMED"] = "timed";
})(DigestTypeEnum || (DigestTypeEnum = {}));
var DelayTypeEnum;
(function(DelayTypeEnum2) {
  DelayTypeEnum2["REGULAR"] = "regular";
  DelayTypeEnum2["SCHEDULED"] = "scheduled";
})(DelayTypeEnum || (DelayTypeEnum = {}));
var MonthlyTypeEnum;
(function(MonthlyTypeEnum2) {
  MonthlyTypeEnum2["EACH"] = "each";
  MonthlyTypeEnum2["ON"] = "on";
})(MonthlyTypeEnum || (MonthlyTypeEnum = {}));
var OrdinalEnum;
(function(OrdinalEnum2) {
  OrdinalEnum2["FIRST"] = "1";
  OrdinalEnum2["SECOND"] = "2";
  OrdinalEnum2["THIRD"] = "3";
  OrdinalEnum2["FOURTH"] = "4";
  OrdinalEnum2["FIFTH"] = "5";
  OrdinalEnum2["LAST"] = "last";
})(OrdinalEnum || (OrdinalEnum = {}));
var OrdinalValueEnum;
(function(OrdinalValueEnum2) {
  OrdinalValueEnum2["DAY"] = "day";
  OrdinalValueEnum2["WEEKDAY"] = "weekday";
  OrdinalValueEnum2["WEEKEND"] = "weekend";
  OrdinalValueEnum2["SUNDAY"] = "sunday";
  OrdinalValueEnum2["MONDAY"] = "monday";
  OrdinalValueEnum2["TUESDAY"] = "tuesday";
  OrdinalValueEnum2["WEDNESDAY"] = "wednesday";
  OrdinalValueEnum2["THURSDAY"] = "thursday";
  OrdinalValueEnum2["FRIDAY"] = "friday";
  OrdinalValueEnum2["SATURDAY"] = "saturday";
})(OrdinalValueEnum || (OrdinalValueEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/subscriber-preference/subscriber-preference.interface.js
var PreferenceLevelEnum;
(function(PreferenceLevelEnum2) {
  PreferenceLevelEnum2["GLOBAL"] = "global";
  PreferenceLevelEnum2["TEMPLATE"] = "template";
})(PreferenceLevelEnum || (PreferenceLevelEnum = {}));

// node_modules/@novu/shared/dist/esm/entities/user/user.enums.js
var AuthProviderEnum;
(function(AuthProviderEnum2) {
  AuthProviderEnum2["GOOGLE"] = "google";
  AuthProviderEnum2["GITHUB"] = "github";
})(AuthProviderEnum || (AuthProviderEnum = {}));
var UserRoleEnum;
(function(UserRoleEnum2) {
  UserRoleEnum2["USER"] = "user";
})(UserRoleEnum || (UserRoleEnum = {}));

// node_modules/@novu/shared/dist/esm/utils/env.js
var hasCloudflareProxyContext = (context) => {
  var _a;
  return !!((_a = context === null || context === void 0 ? void 0 : context.cloudflare) === null || _a === void 0 ? void 0 : _a.env);
};
var hasCloudflareContext = (context) => {
  return !!(context === null || context === void 0 ? void 0 : context.env);
};
var getEnvVariable = (name, context) => {
  if (typeof process !== "undefined" && process.env && typeof process.env[name] === "string") {
    return process.env[name];
  }
  if (hasCloudflareProxyContext(context)) {
    return context.cloudflare.env[name] || "";
  }
  if (hasCloudflareContext(context)) {
    return context.env[name] || "";
  }
  if (context && typeof context[name] === "string") {
    return context[name];
  }
  try {
    return globalThis[name];
  } catch (_) {
  }
  return "";
};

// node_modules/@novu/node/build/module/lib/novu.interface.js
var WithHttp = class {
  http;
  constructor(http) {
    this.http = http;
  }
};

// node_modules/@novu/node/build/module/lib/subscribers/subscribers.js
var Subscribers = class extends WithHttp {
  async list(page = 0, limit = 10) {
    return await this.http.get(`/subscribers`, {
      params: {
        page,
        limit
      }
    });
  }
  async get(subscriberId) {
    return await this.http.get(`/subscribers/${subscriberId}`);
  }
  async identify(subscriberId, data) {
    return await this.http.post(`/subscribers`, {
      subscriberId,
      ...data
    });
  }
  async bulkCreate(subscribers) {
    return await this.http.post(`/subscribers/bulk`, {
      subscribers
    });
  }
  async update(subscriberId, data) {
    return await this.http.put(`/subscribers/${subscriberId}`, {
      ...data
    });
  }
  async setCredentials(subscriberId, providerId, credentials, integrationIdentifier) {
    return await this.http.put(`/subscribers/${subscriberId}/credentials`, {
      providerId,
      credentials: {
        ...credentials
      },
      ...integrationIdentifier && { integrationIdentifier }
    });
  }
  async deleteCredentials(subscriberId, providerId) {
    return await this.http.delete(`/subscribers/${subscriberId}/credentials/${providerId}`);
  }
  async unsetCredentials(subscriberId, providerId) {
    return await this.http.put(`/subscribers/${subscriberId}/credentials`, {
      providerId,
      credentials: { webhookUrl: void 0, deviceTokens: [] }
    });
  }
  async updateOnlineStatus(subscriberId, online) {
    return await this.http.patch(`/subscribers/${subscriberId}/online-status`, {
      online
    });
  }
  async delete(subscriberId) {
    return await this.http.delete(`/subscribers/${subscriberId}`);
  }
  async getPreference(subscriberId) {
    return await this.http.get(`/subscribers/${subscriberId}/preferences`);
  }
  async getGlobalPreference(subscriberId) {
    return await this.http.get(`/subscribers/${subscriberId}/preferences/${PreferenceLevelEnum.GLOBAL}`);
  }
  async getPreferenceByLevel(subscriberId, level) {
    return await this.http.get(`/subscribers/${subscriberId}/preferences/${level}`);
  }
  async updatePreference(subscriberId, templateId, data) {
    return await this.http.patch(`/subscribers/${subscriberId}/preferences/${templateId}`, {
      ...data
    });
  }
  async updateGlobalPreference(subscriberId, data) {
    return await this.http.patch(`/subscribers/${subscriberId}/preferences`, {
      ...data
    });
  }
  async getNotificationsFeed(subscriberId, { payload, ...rest } = {}) {
    const payloadString = payload ? Buffer.from(JSON.stringify(payload)).toString("base64") : void 0;
    return await this.http.get(`/subscribers/${subscriberId}/notifications/feed`, {
      params: {
        payload: payloadString,
        ...rest
      }
    });
  }
  async getUnseenCount(subscriberId, seen) {
    return await this.http.get(`/subscribers/${subscriberId}/notifications/unseen`, {
      params: {
        seen
      }
    });
  }
  async markMessageSeen(subscriberId, messageId) {
    return await this.http.post(`/subscribers/${subscriberId}/messages/markAs`, {
      messageId,
      mark: { seen: true }
    });
  }
  async markMessageRead(subscriberId, messageId) {
    return await this.http.post(`/subscribers/${subscriberId}/messages/markAs`, {
      messageId,
      mark: { read: true }
    });
  }
  async markMessageAs(subscriberId, messageId, mark) {
    return await this.http.post(`/subscribers/${subscriberId}/messages/markAs`, {
      messageId,
      mark
    });
  }
  async markAllMessagesAs(subscriberId, markAs, feedIdentifier) {
    return await this.http.post(`/subscribers/${subscriberId}/messages/mark-all`, { markAs, feedIdentifier });
  }
  async markMessageActionSeen(subscriberId, messageId, type, data) {
    return await this.http.post(`/subscribers/${subscriberId}/messages/${messageId}/actions/${type}`, {
      status: data.status,
      ...data?.payload && { payload: data.payload }
    });
  }
};

// node_modules/@novu/node/build/module/lib/novu.js
var import_events = __toESM(require_events());

// node_modules/@novu/node/build/module/lib/changes/changes.js
var Changes = class extends WithHttp {
  async get(data) {
    const { page, limit, promoted } = data;
    return await this.http.get(`/changes`, {
      params: {
        page,
        limit,
        promoted
      }
    });
  }
  async getCount() {
    return await this.http.get(`/changes/count`);
  }
  async applyOne(changeId) {
    return await this.http.post(`/changes/${changeId}/apply`, {});
  }
  async applyMany(changeIds) {
    return await this.http.post(`/changes/bulk/apply`, {
      changeIds
    });
  }
};

// node_modules/@novu/node/build/module/lib/events/events.js
var Events = class extends WithHttp {
  async trigger(workflowIdentifier, data) {
    return await this.http.post(`/events/trigger`, {
      name: workflowIdentifier,
      to: data.to,
      payload: {
        ...data?.payload
      },
      transactionId: data.transactionId,
      overrides: data.overrides || {},
      ...data.actor && { actor: data.actor },
      ...data.tenant && { tenant: data.tenant }
    });
  }
  async bulkTrigger(events) {
    return await this.http.post(`/events/trigger/bulk`, {
      events
    });
  }
  async broadcast(workflowIdentifier, data) {
    return await this.http.post(`/events/trigger/broadcast`, {
      name: workflowIdentifier,
      payload: {
        ...data?.payload
      },
      transactionId: data.transactionId,
      overrides: data.overrides || {},
      ...data.tenant && { tenant: data.tenant }
    });
  }
  async cancel(transactionId) {
    return await this.http.delete(`/events/trigger/${transactionId}`);
  }
};

// node_modules/@novu/node/build/module/lib/layouts/layouts.js
var Layouts = class extends WithHttp {
  async create(data) {
    return await this.http.post(`/layouts`, {
      name: data.name,
      identifier: data.identifier,
      description: data.description,
      content: data.content,
      variables: data.variables,
      isDefault: data.isDefault
    });
  }
  async list(data) {
    return await this.http.get(`/layouts`, {
      params: {
        ...data?.page?.toString() && { page: data.page },
        ...data?.pageSize && { pageSize: data.pageSize },
        ...data?.sortBy && { sortBy: data.sortBy },
        ...data?.orderBy && { orderBy: data.orderBy }
      }
    });
  }
  async get(layoutId) {
    return await this.http.get(`/layouts`, {
      params: {
        layoutId
      }
    });
  }
  async delete(layoutId) {
    return await this.http.delete(`/layouts/${layoutId}`);
  }
  async update(layoutId, data) {
    return await this.http.patch(`/layouts/${layoutId}`, {
      ...data.name && { name: data.name },
      ...data.identifier && { identifier: data.identifier },
      ...data.description && { description: data.description },
      ...data.content && { content: data.content },
      ...data.variables && { variables: data.variables },
      ...typeof data.isDefault === "boolean" && {
        isDefault: data.isDefault
      }
    });
  }
  async setDefault(layoutId) {
    return await this.http.post(`/layouts/${layoutId}/default`);
  }
};

// node_modules/@novu/node/build/module/lib/notification-groups/notification-groups.js
var NotificationGroups = class extends WithHttp {
  async create(name) {
    return await this.http.post(`/notification-groups`, { name });
  }
  async get() {
    return await this.http.get(`/notification-groups`);
  }
  async getOne(id) {
    return await this.http.get(`/notification-groups/${id}`);
  }
  async update(id, data) {
    return await this.http.patch(`/notification-groups/${id}`, {
      ...data
    });
  }
  async delete(id) {
    return await this.http.delete(`/notification-groups/${id}`);
  }
};

// node_modules/@novu/node/build/module/lib/notification-template/notification-template.js
var NotificationTemplates = class extends WithHttp {
  async getAll(page = 0, limit = 10) {
    return await this.http.get(`/notification-templates`, {
      params: { page, limit }
    });
  }
  async create(data) {
    return await this.http.post(`/notification-templates`, {
      ...data
    });
  }
  async update(templateId, data) {
    return await this.http.put(`/notification-templates/${templateId}`, {
      ...data
    });
  }
  async delete(templateId) {
    return await this.http.delete(`/notification-templates/${templateId}`);
  }
  async getOne(templateId) {
    return await this.http.get(`/notification-templates/${templateId}`);
  }
  async updateStatus(templateId, active) {
    return await this.http.put(`/notification-templates/${templateId}/status`, {
      active
    });
  }
};

// node_modules/@novu/node/build/module/lib/environments/environments.js
var Environments = class extends WithHttp {
  async getCurrent() {
    return await this.http.get("/environments/me");
  }
  async create(payload) {
    return await this.http.post("/environments", payload);
  }
  async getAll() {
    return await this.http.get("/environments");
  }
  async updateOne(id, payload) {
    return await this.http.put(`/environments/${id}`, payload);
  }
  async getApiKeys() {
    return await this.http.get("/environments/api-keys");
  }
  async regenerateApiKeys() {
    return await this.http.post("/environments/api-keys/regenerate");
  }
};

// node_modules/@novu/node/build/module/lib/feeds/feeds.js
var Feeds = class extends WithHttp {
  async create(name) {
    return await this.http.post(`/feeds`, { name });
  }
  async get() {
    return await this.http.get(`/feeds`);
  }
  async delete(feedId) {
    return await this.http.delete(`/feeds/${feedId}`);
  }
};

// node_modules/@novu/node/build/module/lib/topics/topics.js
var BASE_PATH = "/topics";
var Topics = class extends WithHttp {
  async create(data) {
    return await this.http.post(BASE_PATH, {
      key: data.key,
      name: data.name
    });
  }
  async addSubscribers(topicKey, data) {
    return await this.http.post(`${BASE_PATH}/${topicKey}/subscribers`, data);
  }
  async getSubscriber(topicKey, externalSubscriberId) {
    return await this.http.get(`${BASE_PATH}/${topicKey}/subscribers/${externalSubscriberId}`);
  }
  async checkSubscriber(topicKey, externalSubscriberId) {
    return await this.http.get(`${BASE_PATH}/${topicKey}/subscribers/${externalSubscriberId}`);
  }
  async removeSubscribers(topicKey, data) {
    return await this.http.post(`${BASE_PATH}/${topicKey}/subscribers/removal`, data);
  }
  async list(data) {
    return await this.http.get(BASE_PATH, {
      params: {
        ...data?.page?.toString() && { page: data.page },
        ...data?.pageSize && { pageSize: data.pageSize },
        ...data?.key && { key: data.key }
      }
    });
  }
  async delete(topicKey) {
    return await this.http.delete(`${BASE_PATH}/${topicKey}`);
  }
  async get(topicKey) {
    return await this.http.get(`${BASE_PATH}/${topicKey}`);
  }
  async rename(topicKey, newName) {
    return await this.http.patch(`${BASE_PATH}/${topicKey}`, {
      name: newName
    });
  }
};

// node_modules/@novu/node/build/module/lib/integrations/integrations.js
var Integrations = class extends WithHttp {
  async getAll() {
    return await this.http.get("/integrations");
  }
  async getActive() {
    return await this.http.get("/integrations/active");
  }
  async getInAppStatus() {
    return await this.http.get("/integrations/in-app/status");
  }
  async getWebhookProviderStatus(providerId) {
    return await this.http.get(`integrations/webhook/provider/${providerId}/status`);
  }
  async create(providerId, data) {
    return await this.http.post(`/integrations`, {
      providerId,
      ...data
    });
  }
  async update(integrationId, data) {
    return await this.http.put(`/integrations/${integrationId}`, {
      ...data
    });
  }
  async setIntegrationAsPrimary(integrationId) {
    return await this.http.post(`/integrations/${integrationId}/set-primary`, {});
  }
  async delete(integrationId) {
    return await this.http.delete(`/integrations/${integrationId}`);
  }
};

// node_modules/@novu/node/build/module/lib/messages/messages.js
var BASE_PATH2 = "/messages";
var Messages = class extends WithHttp {
  async list(data) {
    const queryParams = {};
    data?.page && (queryParams.page = data?.page);
    data?.limit && (queryParams.limit = data?.limit);
    data?.subscriberId && (queryParams.subscriberId = data?.subscriberId);
    data?.channel && (queryParams.channel = data?.channel);
    data?.transactionIds && (queryParams.transactionId = data?.transactionIds);
    return await this.http.get(BASE_PATH2, {
      params: queryParams
    });
  }
  async deleteById(messageId) {
    return await this.http.delete(`${BASE_PATH2}/${messageId}`);
  }
};

// node_modules/@novu/node/build/module/lib/tenants/tenants.js
var BASE_PATH3 = "/tenants";
var Tenants = class extends WithHttp {
  async create(identifier, data) {
    return await this.http.post(BASE_PATH3, {
      identifier,
      ...data
    });
  }
  async update(identifier, data) {
    return await this.http.patch(`${BASE_PATH3}/${identifier}`, {
      ...data
    });
  }
  async list(data) {
    return await this.http.get(BASE_PATH3, {
      params: {
        ...data?.page?.toString() && { page: data.page },
        ...data?.limit && { limit: data.limit }
      }
    });
  }
  async delete(identifier) {
    return await this.http.delete(`${BASE_PATH3}/${identifier}`);
  }
  async get(identifier) {
    return await this.http.get(`${BASE_PATH3}/${identifier}`);
  }
};

// node_modules/@novu/node/build/module/lib/execution-details/execution-details.js
var ExecutionDetails = class extends WithHttp {
  async get(data) {
    const { notificationId, subscriberId } = data;
    return await this.http.get(`/execution-details`, {
      params: { notificationId, subscriberId }
    });
  }
};

// node_modules/@novu/node/build/module/lib/inbound-parse/inbound-parse.js
var InboundParse = class extends WithHttp {
  async getMxStatus() {
    return await this.http.get(`/inbound-parse/mx/status`);
  }
};

// node_modules/@novu/node/build/module/lib/organizations/organizations.js
var Organizations = class extends WithHttp {
  list() {
    return this.http.get("/organizations");
  }
  create(payload) {
    return this.http.post("/organizations", payload);
  }
  rename(payload) {
    return this.http.patch("/organizations", payload);
  }
  getCurrent() {
    return this.http.get("/organizations/me");
  }
  removeMember(memberId) {
    return this.http.delete(`/organizations/members/${memberId}`);
  }
  updateMemberRole(memberId, payload) {
    return this.http.put(`/organizations/members/${memberId}/roles`, payload);
  }
  getMembers() {
    return this.http.get("/organizations/members");
  }
  updateBranding(payload) {
    return this.http.put("/organizations/branding", payload);
  }
};

// node_modules/@novu/node/build/module/lib/workflow-override/workflow-override.js
var WorkflowOverrides = class extends WithHttp {
  async updateOneById(overrideId, data) {
    return await this.http.put(`/workflow-overrides/${overrideId}`, {
      ...data
    });
  }
  async create(data) {
    return await this.http.post(`/workflow-overrides`, {
      ...data
    });
  }
  async getOneById(overrideId) {
    return await this.http.get(`/workflow-overrides/${overrideId}`);
  }
  async list(page = 0, limit = 10) {
    return await this.http.get(`/workflow-overrides`, {
      params: { page, limit }
    });
  }
  async getOneByTenantIdandWorkflowId(workflowId, tenantId) {
    return await this.http.get(`/workflow-overrides/workflows/${workflowId}/tenants/${tenantId}`);
  }
  async updateOneByTenantIdandWorkflowId(workflowId, tenantId, data) {
    return await this.http.put(`/workflow-overrides/workflows/${workflowId}/tenants/${tenantId}`, {
      ...data
    });
  }
  async delete(overrideId) {
    return await this.http.delete(`/workflow-overrides/${overrideId}`);
  }
};

// node_modules/axios-retry/lib/esm/index.js
var import_is_retry_allowed = __toESM(require_is_retry_allowed());
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
  try {
    var info = gen[key](arg);
    var value = info.value;
  } catch (error) {
    reject(error);
    return;
  }
  if (info.done) {
    resolve(value);
  } else {
    Promise.resolve(value).then(_next, _throw);
  }
}
function _asyncToGenerator(fn) {
  return function() {
    var self = this, args = arguments;
    return new Promise(function(resolve, reject) {
      var gen = fn.apply(self, args);
      function _next(value) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
      }
      function _throw(err) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
      }
      _next(void 0);
    });
  };
}
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function(sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function(key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function(key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var namespace = "axios-retry";
function isNetworkError(error) {
  var CODE_EXCLUDE_LIST = ["ERR_CANCELED", "ECONNABORTED"];
  return !error.response && Boolean(error.code) && // Prevents retrying cancelled requests
  !CODE_EXCLUDE_LIST.includes(error.code) && // Prevents retrying timed out & cancelled requests
  (0, import_is_retry_allowed.default)(error);
}
var SAFE_HTTP_METHODS = ["get", "head", "options"];
var IDEMPOTENT_HTTP_METHODS = SAFE_HTTP_METHODS.concat(["put", "delete"]);
function isRetryableError(error) {
  return error.code !== "ECONNABORTED" && (!error.response || error.response.status >= 500 && error.response.status <= 599);
}
function isSafeRequestError(error) {
  if (!error.config) {
    return false;
  }
  return isRetryableError(error) && SAFE_HTTP_METHODS.indexOf(error.config.method) !== -1;
}
function isIdempotentRequestError(error) {
  if (!error.config) {
    return false;
  }
  return isRetryableError(error) && IDEMPOTENT_HTTP_METHODS.indexOf(error.config.method) !== -1;
}
function isNetworkOrIdempotentRequestError(error) {
  return isNetworkError(error) || isIdempotentRequestError(error);
}
function noDelay() {
  return 0;
}
function exponentialDelay() {
  var retryNumber = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;
  var error = arguments.length > 1 ? arguments[1] : void 0;
  var delayFactor = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 100;
  var delay = Math.pow(2, retryNumber) * delayFactor;
  var randomSum = delay * 0.2 * Math.random();
  return delay + randomSum;
}
var DEFAULT_OPTIONS = {
  retries: 3,
  retryCondition: isNetworkOrIdempotentRequestError,
  retryDelay: noDelay,
  shouldResetTimeout: false,
  onRetry: () => {
  }
};
function getRequestOptions(config, defaultOptions) {
  return _objectSpread(_objectSpread(_objectSpread({}, DEFAULT_OPTIONS), defaultOptions), config[namespace]);
}
function getCurrentState(config, defaultOptions) {
  var currentState = getRequestOptions(config, defaultOptions);
  currentState.retryCount = currentState.retryCount || 0;
  config[namespace] = currentState;
  return currentState;
}
function fixConfig(axios, config) {
  if (axios.defaults.agent === config.agent) {
    delete config.agent;
  }
  if (axios.defaults.httpAgent === config.httpAgent) {
    delete config.httpAgent;
  }
  if (axios.defaults.httpsAgent === config.httpsAgent) {
    delete config.httpsAgent;
  }
}
function shouldRetry(_x, _x2) {
  return _shouldRetry.apply(this, arguments);
}
function _shouldRetry() {
  _shouldRetry = _asyncToGenerator(function* (currentState, error) {
    var {
      retries,
      retryCondition
    } = currentState;
    var shouldRetryOrPromise = currentState.retryCount < retries && retryCondition(error);
    if (typeof shouldRetryOrPromise === "object") {
      try {
        var shouldRetryPromiseResult = yield shouldRetryOrPromise;
        return shouldRetryPromiseResult !== false;
      } catch (_err) {
        return false;
      }
    }
    return shouldRetryOrPromise;
  });
  return _shouldRetry.apply(this, arguments);
}
function axiosRetry(axios, defaultOptions) {
  var requestInterceptorId = axios.interceptors.request.use((config) => {
    var currentState = getCurrentState(config, defaultOptions);
    currentState.lastRequestTime = Date.now();
    return config;
  });
  var responseInterceptorId = axios.interceptors.response.use(null, function() {
    var _ref = _asyncToGenerator(function* (error) {
      var {
        config
      } = error;
      if (!config) {
        return Promise.reject(error);
      }
      var currentState = getCurrentState(config, defaultOptions);
      if (yield shouldRetry(currentState, error)) {
        currentState.retryCount += 1;
        var {
          retryDelay,
          shouldResetTimeout,
          onRetry
        } = currentState;
        var delay = retryDelay(currentState.retryCount, error);
        fixConfig(axios, config);
        if (!shouldResetTimeout && config.timeout && currentState.lastRequestTime) {
          var lastRequestDuration = Date.now() - currentState.lastRequestTime;
          var timeout = config.timeout - lastRequestDuration - delay;
          if (timeout <= 0) {
            return Promise.reject(error);
          }
          config.timeout = timeout;
        }
        config.transformRequest = [(data) => data];
        yield onRetry(currentState.retryCount, error, config);
        return new Promise((resolve) => setTimeout(() => resolve(axios(config)), delay));
      }
      return Promise.reject(error);
    });
    return function(_x3) {
      return _ref.apply(this, arguments);
    };
  }());
  return {
    requestInterceptorId,
    responseInterceptorId
  };
}
axiosRetry.isNetworkError = isNetworkError;
axiosRetry.isSafeRequestError = isSafeRequestError;
axiosRetry.isIdempotentRequestError = isIdempotentRequestError;
axiosRetry.isNetworkOrIdempotentRequestError = isNetworkOrIdempotentRequestError;
axiosRetry.exponentialDelay = exponentialDelay;
axiosRetry.isRetryableError = isRetryableError;

// node_modules/@novu/node/build/module/lib/retry.js
var RETRYABLE_HTTP_CODES = [408, 422, 429];
var NON_IDEMPOTENT_METHODS = ["post", "patch"];
var IDEMPOTENCY_KEY = "Idempotency-Key";
var DEFAULT_RETRY_MAX = 0;
var DEFAULT_WAIT_MIN = 1;
var DEFAULT_WAIT_MAX = 30;
function makeRetryable(axios, config) {
  axios.interceptors.request.use((axiosConfig) => {
    if (axiosConfig.method && NON_IDEMPOTENT_METHODS.includes(axiosConfig.method)) {
      const idempotencyKey = axiosConfig.headers[IDEMPOTENCY_KEY];
      if (idempotencyKey) {
        return axiosConfig;
      }
      axiosConfig.headers[IDEMPOTENCY_KEY] = v4_default();
    }
    return axiosConfig;
  });
  const retryConfig = config?.retryConfig || {};
  const retries = retryConfig.retryMax || DEFAULT_RETRY_MAX;
  const minDelay = retryConfig.waitMin || DEFAULT_WAIT_MIN;
  const maxDelay = retryConfig.waitMax || DEFAULT_WAIT_MAX;
  const initialDelay = retryConfig.initialDelay || minDelay;
  const retryCondition = retryConfig.retryCondition || defaultRetryCondition;
  function backoff(retryCount) {
    if (retryCount === 1) {
      return initialDelay;
    }
    const delay = retryCount * minDelay;
    if (delay > maxDelay) {
      return maxDelay;
    }
    return delay;
  }
  axiosRetry(axios, {
    retries,
    retryCondition,
    retryDelay(retryCount) {
      return backoff(retryCount) * 1e3;
    },
    onRetry(_retryCount, error, requestConfig) {
      if (error.response?.status === 422 && requestConfig.headers && requestConfig.method && NON_IDEMPOTENT_METHODS.includes(requestConfig.method)) {
        requestConfig.headers[IDEMPOTENCY_KEY] = v4_default();
      }
    }
  });
}
function defaultRetryCondition(err) {
  if (isNetworkError(err)) {
    return true;
  }
  if (err.response && err.response.status >= 500 && err.response.status <= 599) {
    return true;
  }
  if (err.response && RETRYABLE_HTTP_CODES.includes(err.response.status)) {
    return true;
  }
  return false;
}

// node_modules/@novu/node/build/module/lib/novu.js
var Novu = class extends import_events.EventEmitter {
  apiKey;
  http;
  subscribers;
  environments;
  events;
  changes;
  layouts;
  notificationGroups;
  notificationTemplates;
  feeds;
  topics;
  integrations;
  messages;
  tenants;
  executionDetails;
  inboundParse;
  organizations;
  workflowOverrides;
  constructor(...args) {
    super();
    let apiKey;
    let config;
    if (arguments.length === 2) {
      apiKey = args[0];
      config = args[1];
    } else if (arguments.length === 1) {
      if (typeof args[0] === "object") {
        const { apiKey: key, ...rest } = args[0];
        apiKey = key;
        config = rest;
      } else {
        apiKey = args[0];
      }
    } else {
      apiKey = getEnvVariable("NOVU_API_KEY");
    }
    this.apiKey = apiKey;
    const axiosInstance = axios_default.create({
      baseURL: this.buildBackendUrl(config),
      headers: {
        Authorization: `ApiKey ${this.apiKey}`
      }
    });
    if (config?.retryConfig) {
      makeRetryable(axiosInstance, config);
    }
    this.http = axiosInstance;
    this.subscribers = new Subscribers(this.http);
    this.environments = new Environments(this.http);
    this.events = new Events(this.http);
    this.changes = new Changes(this.http);
    this.layouts = new Layouts(this.http);
    this.notificationGroups = new NotificationGroups(this.http);
    this.notificationTemplates = new NotificationTemplates(this.http);
    this.feeds = new Feeds(this.http);
    this.topics = new Topics(this.http);
    this.integrations = new Integrations(this.http);
    this.messages = new Messages(this.http);
    this.tenants = new Tenants(this.http);
    this.executionDetails = new ExecutionDetails(this.http);
    this.inboundParse = new InboundParse(this.http);
    this.organizations = new Organizations(this.http);
    this.workflowOverrides = new WorkflowOverrides(this.http);
    this.trigger = this.events.trigger;
    this.bulkTrigger = this.events.bulkTrigger;
    this.broadcast = this.events.broadcast;
  }
  trigger;
  bulkTrigger;
  broadcast;
  buildBackendUrl(config) {
    const novuApiVersion = "v1";
    if (!config?.backendUrl) {
      return `https://api.novu.co/${novuApiVersion}`;
    }
    return config?.backendUrl.includes("novu.co/v") ? config?.backendUrl : config?.backendUrl + `/${novuApiVersion}`;
  }
};
export {
  ButtonTypeEnum,
  ChannelCTATypeEnum,
  ChannelTypeEnum,
  ChatProviderIdEnum,
  EmailBlockTypeEnum,
  EmailProviderIdEnum,
  FilterPartTypeEnum,
  LayoutDto,
  MarkMessagesAsEnum,
  MessageActionStatusEnum,
  Novu,
  PreferenceLevelEnum,
  PushProviderIdEnum,
  SmsProviderIdEnum,
  StepTypeEnum,
  SystemAvatarIconEnum,
  TemplateVariableTypeEnum,
  TextAlignEnum,
  TriggerRecipientsTypeEnum,
  defaultRetryCondition
};
//# sourceMappingURL=@novu_node.js.map
