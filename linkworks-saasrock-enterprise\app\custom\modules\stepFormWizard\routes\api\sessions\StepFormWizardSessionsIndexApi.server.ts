import { ActionFunction, LoaderFunctionArgs } from "react-router";
import { FilterablePropertyDto } from "~/application/dtos/data/FilterablePropertyDto";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import { getTranslations } from "~/locale/i18next.server";
import { getStepFormWizards } from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import { deleteStepFormWizardSessionSteps } from "~/custom/modules/stepFormWizard/db/stepFormWizardSessionSteps.db.server";
import { StepFormWizardFilterMetadataDto } from "~/custom/modules/stepFormWizard/dtos/StepFormWizardFilterMetadataDto";
import StepFormWizardService from "~/custom/modules/stepFormWizard/services/StepFormWizardService";
import { adminGetAllTenantsIdsAndNames } from "~/utils/db/tenants.db.server";
import { getUsersById } from "~/utils/db/users.db.server";
import { getFiltersFromCurrentUrl, getPaginationFromCurrentUrl } from "~/utils/helpers/RowPaginationHelper";
import {
  deleteStepFormWizardSession,
  getStepFormWizardSession,
  getStepFormWizardSessionsWithPagination,
  groupStepFormWizardSessionsByUser,
  StepFormWizardSessionWithDetails,
} from "../../../db/stepFormWizardSessions.db.server";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

export namespace StepFormWizardSessionsIndexApi {
  export type LoaderData = {
    meta: MetaTagsDto;
    items: StepFormWizardSessionWithDetails[];
    pagination: PaginationDto;
    filterableProperties: FilterablePropertyDto[];
    metadata: StepFormWizardFilterMetadataDto;
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.view");
    const { t } = await getTranslations(request);
    const allStepFormWizards = await getStepFormWizards({});
    const usersInSessions = await groupStepFormWizardSessionsByUser();
    const users = await getUsersById(usersInSessions.map((x) => x.userId));
    const filterableProperties: FilterablePropertyDto[] = [
      {
        name: "stepFormWizardId",
        title: t("stepFormWizard.title"),
        options: [
          ...allStepFormWizards.map((i) => {
            return { value: i.id, name: i.title };
          }),
        ],
      },
      {
        name: "userId",
        title: t("models.user.object"),
        options: [
          ...users.map((i) => {
            return { value: i.id, name: i.email + " - " + i.firstName + " " + i.lastName };
          }),
        ],
      },
      {
        name: "tenantId",
        title: t("models.tenant.object"),
        options: [
          { name: "{Admin}", value: "null" },
          ...(await adminGetAllTenantsIdsAndNames()).map((i) => {
            return { value: i.id, name: i.name };
          }),
        ],
      },
    ];
    const filters = getFiltersFromCurrentUrl(request, filterableProperties);
    const urlSearchParams = new URL(request.url).searchParams;
    const currentPagination = getPaginationFromCurrentUrl(urlSearchParams);
    const tenantId = filters.properties.find((f) => f.name === "tenantId")?.value;
    const { items, pagination } = await getStepFormWizardSessionsWithPagination({
      pagination: currentPagination,
      where: {
        stepFormWizardId: params.id,
        tenantId: tenantId === "null" ? null : (tenantId ?? undefined),
      },
    });
    const data: LoaderData = {
      meta: [{ title: `${t("stepFormWizard.title")} | ${process.env.APP_NAME}` }],
      items,
      pagination,
      filterableProperties,
      metadata: await StepFormWizardService.getMetadata(),
    };
    return data;
  };

  export type ActionData = {
    error?: string;
    success?: string;
  };
  export const action: ActionFunction = async ({ request, params }) => {
    await verifyUserHasPermission(request, "admin.stepFormWizard.update");
    const { t } = await getTranslations(request);
    const form = await request.formData();
    const action = form.get("action");
    if (action === "delete") {
      await verifyUserHasPermission(request, "admin.stepFormWizard.delete");
      const id = form.get("id")?.toString() ?? "";
      if (!id) {
        return Response.json({ error: "Session ID is required" }, { status: 400 });
      }
      const session = await getStepFormWizardSession(id);
      await deleteStepFormWizardSessionSteps(session!.sessionSteps.map((s) => s.id));
      await deleteStepFormWizardSession(id);
      return Response.json({ success: "Step form wizard session deleted" });
    } else {
      return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
    }
  };
}
