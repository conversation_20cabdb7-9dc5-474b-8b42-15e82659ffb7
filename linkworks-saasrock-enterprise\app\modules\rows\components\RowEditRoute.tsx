import { ReactNode, useEffect, useState } from "react";
// import { EntityFormSkeleton } from "~/components/ui/skeletons";
import { useTranslation } from "react-i18next";
import { Outlet, useLoaderData, useNavigate, useParams, useNavigation } from "react-router";
import RowForm from "~/components/entities/rows/RowForm";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { getEntityPermission, getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import NewPageLayout from "~/components/ui/layouts/NewPageLayout";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { Rows_Edit } from "../routes/Rows_Edit.server";

type EditRowOptions = {
  hideTitle?: boolean;
  hideMenu?: boolean;
  hideShare?: boolean;
  hideTags?: boolean;
  hideTasks?: boolean;
  hideActivity?: boolean;
  disableUpdate?: boolean;
  disableDelete?: boolean;
};

interface Props {
  layout?: "edit" | "simple";
  children?: ReactNode;
  title?: ReactNode;
  rowFormChildren?: ReactNode;
  options?: EditRowOptions;
}

export default function RowEditRoute({ rowFormChildren, options }: Props) {
  const { rowData, routes, allEntities, relationshipRows } = useLoaderData<Rows_Edit.LoaderData>();
  const appOrAdminData = useAppOrAdminData();
  const navigate = useNavigate();
  // const navigation = useNavigation();
  const params = useParams();
  const { t } = useTranslation();

  // const [showSkeleton, setShowSkeleton] = useState(false);

  // useEffect(() => {
  //   if (rowData.item && navigation.state === "idle" && !navigation.location) {
  //     setShowSkeleton(true);
  //     const timer = setTimeout(() => {
  //       setShowSkeleton(false);
  //     }, 700);

  //     return () => clearTimeout(timer);
  //   }
  // }, [rowData.item?.id, navigation.state, navigation.location]);

  return (
    <NewPageLayout
      title={rowData?.entity?.isStepFormWizard ? "" : t("shared.edit") + " " + t(rowData.entity.title)}
      menu={EntityHelper.getLayoutBreadcrumbsMenu({ type: "edit", t, appOrAdminData, entity: rowData.entity, item: rowData.item, params, routes })}
      entity={rowData.entity}
      item={rowData.item}
    >
      {/* {showSkeleton ? (
        <EntityFormSkeleton />
      ) : (
        <> */}
          <RowForm
            allEntities={allEntities}
            entity={rowData.entity}
            routes={routes}
            item={rowData.item}
            editing={true}
            canDelete={
              !options?.disableDelete &&
              getUserHasPermission(appOrAdminData, getEntityPermission(rowData.entity, "delete")) &&
              (rowData.rowPermissions.canDelete || appOrAdminData.isSuperUser)
            }
            canUpdate={
              !options?.disableUpdate &&
              getUserHasPermission(appOrAdminData, getEntityPermission(rowData.entity, "update")) &&
              (rowData.rowPermissions.canDelete || appOrAdminData.isSuperUser)
            }
            relationshipRows={relationshipRows}
            onCancel={() => navigate(EntityHelper.getRoutes({ routes, entity: rowData.entity, item: rowData.item })?.overview ?? "")}
            promptFlows={rowData.allPromptFlows}
          >
            {rowFormChildren}
          </RowForm>
          <Outlet />
        {/* </>
      )} */}
    </NewPageLayout>
  );
}
