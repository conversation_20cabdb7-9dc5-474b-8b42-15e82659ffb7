-- AlterTable
ALTER TABLE "User" ADD COLUMN     "locale" TEXT;

-- CreateTable
CREATE TABLE "Tag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" INTEGER,

    CONSTRAINT "Tag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TagUser" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,

    CONSTRAINT "TagUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TagTenant" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,

    CONSTRAINT "TagTenant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizard" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "realtime" BOOLEAN NOT NULL DEFAULT false,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "canBeDismissed" BOOLEAN NOT NULL DEFAULT true,
    "height" TEXT,

    CONSTRAINT "StepFormWizard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardFilter" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "stepFormWizardId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT,

    CONSTRAINT "StepFormWizardFilter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardStep" (
    "id" TEXT NOT NULL,
    "stepFormWizardId" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "block" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardStep_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSession" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "stepFormWizardId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tenantId" TEXT,
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "dismissedAt" TIMESTAMP(3),
    "createdRealtime" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StepFormWizardSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionAction" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "stepFormWizardSessionId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardSessionAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionFilterMatch" (
    "id" TEXT NOT NULL,
    "stepFormWizardFilterId" TEXT NOT NULL,
    "stepFormWizardSessionId" TEXT NOT NULL,

    CONSTRAINT "StepFormWizardSessionFilterMatch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StepFormWizardSessionStep" (
    "id" TEXT NOT NULL,
    "stepFormWizardSessionId" TEXT NOT NULL,
    "stepId" TEXT NOT NULL,
    "seenAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "StepFormWizardSessionStep_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Tag_name_key" ON "Tag"("name");

-- CreateIndex
CREATE UNIQUE INDEX "StepFormWizardSession_stepFormWizardId_userId_tenantId_key" ON "StepFormWizardSession"("stepFormWizardId", "userId", "tenantId");

-- AddForeignKey
ALTER TABLE "TagUser" ADD CONSTRAINT "TagUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TagUser" ADD CONSTRAINT "TagUser_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "Tag"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TagTenant" ADD CONSTRAINT "TagTenant_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TagTenant" ADD CONSTRAINT "TagTenant_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "Tag"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardFilter" ADD CONSTRAINT "StepFormWizardFilter_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardStep" ADD CONSTRAINT "StepFormWizardStep_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_stepFormWizardId_fkey" FOREIGN KEY ("stepFormWizardId") REFERENCES "StepFormWizard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSession" ADD CONSTRAINT "StepFormWizardSession_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionAction" ADD CONSTRAINT "StepFormWizardSessionAction_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionFilterMatch" ADD CONSTRAINT "StepFormWizardSessionFilterMatch_stepFormWizardFilterId_fkey" FOREIGN KEY ("stepFormWizardFilterId") REFERENCES "StepFormWizardFilter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionFilterMatch" ADD CONSTRAINT "StepFormWizardSessionFilterMatch_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionStep" ADD CONSTRAINT "StepFormWizardSessionStep_stepFormWizardSessionId_fkey" FOREIGN KEY ("stepFormWizardSessionId") REFERENCES "StepFormWizardSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StepFormWizardSessionStep" ADD CONSTRAINT "StepFormWizardSessionStep_stepId_fkey" FOREIGN KEY ("stepId") REFERENCES "StepFormWizardStep"("id") ON DELETE CASCADE ON UPDATE CASCADE;
