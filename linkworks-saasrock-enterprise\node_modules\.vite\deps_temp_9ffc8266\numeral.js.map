{"version": 3, "sources": ["../../numeral/numeral.js"], "sourcesContent": ["/*! @preserve\n * numeral.js\n * version : 2.0.6\n * author : <PERSON>\n * license : MIT\n * http://adamwdraper.github.com/Numeral-js/\n */\n\n(function (global, factory) {\n    if (typeof define === 'function' && define.amd) {\n        define(factory);\n    } else if (typeof module === 'object' && module.exports) {\n        module.exports = factory();\n    } else {\n        global.numeral = factory();\n    }\n}(this, function () {\n    /************************************\n        Variables\n    ************************************/\n\n    var numeral,\n        _,\n        VERSION = '2.0.6',\n        formats = {},\n        locales = {},\n        defaults = {\n            currentLocale: 'en',\n            zeroFormat: null,\n            nullFormat: null,\n            defaultFormat: '0,0',\n            scalePercentBy100: true\n        },\n        options = {\n            currentLocale: defaults.currentLocale,\n            zeroFormat: defaults.zeroFormat,\n            nullFormat: defaults.nullFormat,\n            defaultFormat: defaults.defaultFormat,\n            scalePercentBy100: defaults.scalePercentBy100\n        };\n\n\n    /************************************\n        Constructors\n    ************************************/\n\n    // Numeral prototype object\n    function Numeral(input, number) {\n        this._input = input;\n\n        this._value = number;\n    }\n\n    numeral = function(input) {\n        var value,\n            kind,\n            unformatFunction,\n            regexp;\n\n        if (numeral.isNumeral(input)) {\n            value = input.value();\n        } else if (input === 0 || typeof input === 'undefined') {\n            value = 0;\n        } else if (input === null || _.isNaN(input)) {\n            value = null;\n        } else if (typeof input === 'string') {\n            if (options.zeroFormat && input === options.zeroFormat) {\n                value = 0;\n            } else if (options.nullFormat && input === options.nullFormat || !input.replace(/[^0-9]+/g, '').length) {\n                value = null;\n            } else {\n                for (kind in formats) {\n                    regexp = typeof formats[kind].regexps.unformat === 'function' ? formats[kind].regexps.unformat() : formats[kind].regexps.unformat;\n\n                    if (regexp && input.match(regexp)) {\n                        unformatFunction = formats[kind].unformat;\n\n                        break;\n                    }\n                }\n\n                unformatFunction = unformatFunction || numeral._.stringToNumber;\n\n                value = unformatFunction(input);\n            }\n        } else {\n            value = Number(input)|| null;\n        }\n\n        return new Numeral(input, value);\n    };\n\n    // version number\n    numeral.version = VERSION;\n\n    // compare numeral object\n    numeral.isNumeral = function(obj) {\n        return obj instanceof Numeral;\n    };\n\n    // helper functions\n    numeral._ = _ = {\n        // formats numbers separators, decimals places, signs, abbreviations\n        numberToFormat: function(value, format, roundingFunction) {\n            var locale = locales[numeral.options.currentLocale],\n                negP = false,\n                optDec = false,\n                leadingCount = 0,\n                abbr = '',\n                trillion = 1000000000000,\n                billion = 1000000000,\n                million = 1000000,\n                thousand = 1000,\n                decimal = '',\n                neg = false,\n                abbrForce, // force abbreviation\n                abs,\n                min,\n                max,\n                power,\n                int,\n                precision,\n                signed,\n                thousands,\n                output;\n\n            // make sure we never format a null value\n            value = value || 0;\n\n            abs = Math.abs(value);\n\n            // see if we should use parentheses for negative number or if we should prefix with a sign\n            // if both are present we default to parentheses\n            if (numeral._.includes(format, '(')) {\n                negP = true;\n                format = format.replace(/[\\(|\\)]/g, '');\n            } else if (numeral._.includes(format, '+') || numeral._.includes(format, '-')) {\n                signed = numeral._.includes(format, '+') ? format.indexOf('+') : value < 0 ? format.indexOf('-') : -1;\n                format = format.replace(/[\\+|\\-]/g, '');\n            }\n\n            // see if abbreviation is wanted\n            if (numeral._.includes(format, 'a')) {\n                abbrForce = format.match(/a(k|m|b|t)?/);\n\n                abbrForce = abbrForce ? abbrForce[1] : false;\n\n                // check for space before abbreviation\n                if (numeral._.includes(format, ' a')) {\n                    abbr = ' ';\n                }\n\n                format = format.replace(new RegExp(abbr + 'a[kmbt]?'), '');\n\n                if (abs >= trillion && !abbrForce || abbrForce === 't') {\n                    // trillion\n                    abbr += locale.abbreviations.trillion;\n                    value = value / trillion;\n                } else if (abs < trillion && abs >= billion && !abbrForce || abbrForce === 'b') {\n                    // billion\n                    abbr += locale.abbreviations.billion;\n                    value = value / billion;\n                } else if (abs < billion && abs >= million && !abbrForce || abbrForce === 'm') {\n                    // million\n                    abbr += locale.abbreviations.million;\n                    value = value / million;\n                } else if (abs < million && abs >= thousand && !abbrForce || abbrForce === 'k') {\n                    // thousand\n                    abbr += locale.abbreviations.thousand;\n                    value = value / thousand;\n                }\n            }\n\n            // check for optional decimals\n            if (numeral._.includes(format, '[.]')) {\n                optDec = true;\n                format = format.replace('[.]', '.');\n            }\n\n            // break number and format\n            int = value.toString().split('.')[0];\n            precision = format.split('.')[1];\n            thousands = format.indexOf(',');\n            leadingCount = (format.split('.')[0].split(',')[0].match(/0/g) || []).length;\n\n            if (precision) {\n                if (numeral._.includes(precision, '[')) {\n                    precision = precision.replace(']', '');\n                    precision = precision.split('[');\n                    decimal = numeral._.toFixed(value, (precision[0].length + precision[1].length), roundingFunction, precision[1].length);\n                } else {\n                    decimal = numeral._.toFixed(value, precision.length, roundingFunction);\n                }\n\n                int = decimal.split('.')[0];\n\n                if (numeral._.includes(decimal, '.')) {\n                    decimal = locale.delimiters.decimal + decimal.split('.')[1];\n                } else {\n                    decimal = '';\n                }\n\n                if (optDec && Number(decimal.slice(1)) === 0) {\n                    decimal = '';\n                }\n            } else {\n                int = numeral._.toFixed(value, 0, roundingFunction);\n            }\n\n            // check abbreviation again after rounding\n            if (abbr && !abbrForce && Number(int) >= 1000 && abbr !== locale.abbreviations.trillion) {\n                int = String(Number(int) / 1000);\n\n                switch (abbr) {\n                    case locale.abbreviations.thousand:\n                        abbr = locale.abbreviations.million;\n                        break;\n                    case locale.abbreviations.million:\n                        abbr = locale.abbreviations.billion;\n                        break;\n                    case locale.abbreviations.billion:\n                        abbr = locale.abbreviations.trillion;\n                        break;\n                }\n            }\n\n\n            // format number\n            if (numeral._.includes(int, '-')) {\n                int = int.slice(1);\n                neg = true;\n            }\n\n            if (int.length < leadingCount) {\n                for (var i = leadingCount - int.length; i > 0; i--) {\n                    int = '0' + int;\n                }\n            }\n\n            if (thousands > -1) {\n                int = int.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, '$1' + locale.delimiters.thousands);\n            }\n\n            if (format.indexOf('.') === 0) {\n                int = '';\n            }\n\n            output = int + decimal + (abbr ? abbr : '');\n\n            if (negP) {\n                output = (negP && neg ? '(' : '') + output + (negP && neg ? ')' : '');\n            } else {\n                if (signed >= 0) {\n                    output = signed === 0 ? (neg ? '-' : '+') + output : output + (neg ? '-' : '+');\n                } else if (neg) {\n                    output = '-' + output;\n                }\n            }\n\n            return output;\n        },\n        // unformats numbers separators, decimals places, signs, abbreviations\n        stringToNumber: function(string) {\n            var locale = locales[options.currentLocale],\n                stringOriginal = string,\n                abbreviations = {\n                    thousand: 3,\n                    million: 6,\n                    billion: 9,\n                    trillion: 12\n                },\n                abbreviation,\n                value,\n                i,\n                regexp;\n\n            if (options.zeroFormat && string === options.zeroFormat) {\n                value = 0;\n            } else if (options.nullFormat && string === options.nullFormat || !string.replace(/[^0-9]+/g, '').length) {\n                value = null;\n            } else {\n                value = 1;\n\n                if (locale.delimiters.decimal !== '.') {\n                    string = string.replace(/\\./g, '').replace(locale.delimiters.decimal, '.');\n                }\n\n                for (abbreviation in abbreviations) {\n                    regexp = new RegExp('[^a-zA-Z]' + locale.abbreviations[abbreviation] + '(?:\\\\)|(\\\\' + locale.currency.symbol + ')?(?:\\\\))?)?$');\n\n                    if (stringOriginal.match(regexp)) {\n                        value *= Math.pow(10, abbreviations[abbreviation]);\n                        break;\n                    }\n                }\n\n                // check for negative number\n                value *= (string.split('-').length + Math.min(string.split('(').length - 1, string.split(')').length - 1)) % 2 ? 1 : -1;\n\n                // remove non numbers\n                string = string.replace(/[^0-9\\.]+/g, '');\n\n                value *= Number(string);\n            }\n\n            return value;\n        },\n        isNaN: function(value) {\n            return typeof value === 'number' && isNaN(value);\n        },\n        includes: function(string, search) {\n            return string.indexOf(search) !== -1;\n        },\n        insert: function(string, subString, start) {\n            return string.slice(0, start) + subString + string.slice(start);\n        },\n        reduce: function(array, callback /*, initialValue*/) {\n            if (this === null) {\n                throw new TypeError('Array.prototype.reduce called on null or undefined');\n            }\n\n            if (typeof callback !== 'function') {\n                throw new TypeError(callback + ' is not a function');\n            }\n\n            var t = Object(array),\n                len = t.length >>> 0,\n                k = 0,\n                value;\n\n            if (arguments.length === 3) {\n                value = arguments[2];\n            } else {\n                while (k < len && !(k in t)) {\n                    k++;\n                }\n\n                if (k >= len) {\n                    throw new TypeError('Reduce of empty array with no initial value');\n                }\n\n                value = t[k++];\n            }\n            for (; k < len; k++) {\n                if (k in t) {\n                    value = callback(value, t[k], k, t);\n                }\n            }\n            return value;\n        },\n        /**\n         * Computes the multiplier necessary to make x >= 1,\n         * effectively eliminating miscalculations caused by\n         * finite precision.\n         */\n        multiplier: function (x) {\n            var parts = x.toString().split('.');\n\n            return parts.length < 2 ? 1 : Math.pow(10, parts[1].length);\n        },\n        /**\n         * Given a variable number of arguments, returns the maximum\n         * multiplier that must be used to normalize an operation involving\n         * all of them.\n         */\n        correctionFactor: function () {\n            var args = Array.prototype.slice.call(arguments);\n\n            return args.reduce(function(accum, next) {\n                var mn = _.multiplier(next);\n                return accum > mn ? accum : mn;\n            }, 1);\n        },\n        /**\n         * Implementation of toFixed() that treats floats more like decimals\n         *\n         * Fixes binary rounding issues (eg. (0.615).toFixed(2) === '0.61') that present\n         * problems for accounting- and finance-related software.\n         */\n        toFixed: function(value, maxDecimals, roundingFunction, optionals) {\n            var splitValue = value.toString().split('.'),\n                minDecimals = maxDecimals - (optionals || 0),\n                boundedPrecision,\n                optionalsRegExp,\n                power,\n                output;\n\n            // Use the smallest precision value possible to avoid errors from floating point representation\n            if (splitValue.length === 2) {\n              boundedPrecision = Math.min(Math.max(splitValue[1].length, minDecimals), maxDecimals);\n            } else {\n              boundedPrecision = minDecimals;\n            }\n\n            power = Math.pow(10, boundedPrecision);\n\n            // Multiply up by precision, round accurately, then divide and use native toFixed():\n            output = (roundingFunction(value + 'e+' + boundedPrecision) / power).toFixed(boundedPrecision);\n\n            if (optionals > maxDecimals - boundedPrecision) {\n                optionalsRegExp = new RegExp('\\\\.?0{1,' + (optionals - (maxDecimals - boundedPrecision)) + '}$');\n                output = output.replace(optionalsRegExp, '');\n            }\n\n            return output;\n        }\n    };\n\n    // avaliable options\n    numeral.options = options;\n\n    // avaliable formats\n    numeral.formats = formats;\n\n    // avaliable formats\n    numeral.locales = locales;\n\n    // This function sets the current locale.  If\n    // no arguments are passed in, it will simply return the current global\n    // locale key.\n    numeral.locale = function(key) {\n        if (key) {\n            options.currentLocale = key.toLowerCase();\n        }\n\n        return options.currentLocale;\n    };\n\n    // This function provides access to the loaded locale data.  If\n    // no arguments are passed in, it will simply return the current\n    // global locale object.\n    numeral.localeData = function(key) {\n        if (!key) {\n            return locales[options.currentLocale];\n        }\n\n        key = key.toLowerCase();\n\n        if (!locales[key]) {\n            throw new Error('Unknown locale : ' + key);\n        }\n\n        return locales[key];\n    };\n\n    numeral.reset = function() {\n        for (var property in defaults) {\n            options[property] = defaults[property];\n        }\n    };\n\n    numeral.zeroFormat = function(format) {\n        options.zeroFormat = typeof(format) === 'string' ? format : null;\n    };\n\n    numeral.nullFormat = function (format) {\n        options.nullFormat = typeof(format) === 'string' ? format : null;\n    };\n\n    numeral.defaultFormat = function(format) {\n        options.defaultFormat = typeof(format) === 'string' ? format : '0.0';\n    };\n\n    numeral.register = function(type, name, format) {\n        name = name.toLowerCase();\n\n        if (this[type + 's'][name]) {\n            throw new TypeError(name + ' ' + type + ' already registered.');\n        }\n\n        this[type + 's'][name] = format;\n\n        return format;\n    };\n\n\n    numeral.validate = function(val, culture) {\n        var _decimalSep,\n            _thousandSep,\n            _currSymbol,\n            _valArray,\n            _abbrObj,\n            _thousandRegEx,\n            localeData,\n            temp;\n\n        //coerce val to string\n        if (typeof val !== 'string') {\n            val += '';\n\n            if (console.warn) {\n                console.warn('Numeral.js: Value is not string. It has been co-erced to: ', val);\n            }\n        }\n\n        //trim whitespaces from either sides\n        val = val.trim();\n\n        //if val is just digits return true\n        if (!!val.match(/^\\d+$/)) {\n            return true;\n        }\n\n        //if val is empty return false\n        if (val === '') {\n            return false;\n        }\n\n        //get the decimal and thousands separator from numeral.localeData\n        try {\n            //check if the culture is understood by numeral. if not, default it to current locale\n            localeData = numeral.localeData(culture);\n        } catch (e) {\n            localeData = numeral.localeData(numeral.locale());\n        }\n\n        //setup the delimiters and currency symbol based on culture/locale\n        _currSymbol = localeData.currency.symbol;\n        _abbrObj = localeData.abbreviations;\n        _decimalSep = localeData.delimiters.decimal;\n        if (localeData.delimiters.thousands === '.') {\n            _thousandSep = '\\\\.';\n        } else {\n            _thousandSep = localeData.delimiters.thousands;\n        }\n\n        // validating currency symbol\n        temp = val.match(/^[^\\d]+/);\n        if (temp !== null) {\n            val = val.substr(1);\n            if (temp[0] !== _currSymbol) {\n                return false;\n            }\n        }\n\n        //validating abbreviation symbol\n        temp = val.match(/[^\\d]+$/);\n        if (temp !== null) {\n            val = val.slice(0, -1);\n            if (temp[0] !== _abbrObj.thousand && temp[0] !== _abbrObj.million && temp[0] !== _abbrObj.billion && temp[0] !== _abbrObj.trillion) {\n                return false;\n            }\n        }\n\n        _thousandRegEx = new RegExp(_thousandSep + '{2}');\n\n        if (!val.match(/[^\\d.,]/g)) {\n            _valArray = val.split(_decimalSep);\n            if (_valArray.length > 2) {\n                return false;\n            } else {\n                if (_valArray.length < 2) {\n                    return ( !! _valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx));\n                } else {\n                    if (_valArray[0].length === 1) {\n                        return ( !! _valArray[0].match(/^\\d+$/) && !_valArray[0].match(_thousandRegEx) && !! _valArray[1].match(/^\\d+$/));\n                    } else {\n                        return ( !! _valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx) && !! _valArray[1].match(/^\\d+$/));\n                    }\n                }\n            }\n        }\n\n        return false;\n    };\n\n\n    /************************************\n        Numeral Prototype\n    ************************************/\n\n    numeral.fn = Numeral.prototype = {\n        clone: function() {\n            return numeral(this);\n        },\n        format: function(inputString, roundingFunction) {\n            var value = this._value,\n                format = inputString || options.defaultFormat,\n                kind,\n                output,\n                formatFunction;\n\n            // make sure we have a roundingFunction\n            roundingFunction = roundingFunction || Math.round;\n\n            // format based on value\n            if (value === 0 && options.zeroFormat !== null) {\n                output = options.zeroFormat;\n            } else if (value === null && options.nullFormat !== null) {\n                output = options.nullFormat;\n            } else {\n                for (kind in formats) {\n                    if (format.match(formats[kind].regexps.format)) {\n                        formatFunction = formats[kind].format;\n\n                        break;\n                    }\n                }\n\n                formatFunction = formatFunction || numeral._.numberToFormat;\n\n                output = formatFunction(value, format, roundingFunction);\n            }\n\n            return output;\n        },\n        value: function() {\n            return this._value;\n        },\n        input: function() {\n            return this._input;\n        },\n        set: function(value) {\n            this._value = Number(value);\n\n            return this;\n        },\n        add: function(value) {\n            var corrFactor = _.correctionFactor.call(null, this._value, value);\n\n            function cback(accum, curr, currI, O) {\n                return accum + Math.round(corrFactor * curr);\n            }\n\n            this._value = _.reduce([this._value, value], cback, 0) / corrFactor;\n\n            return this;\n        },\n        subtract: function(value) {\n            var corrFactor = _.correctionFactor.call(null, this._value, value);\n\n            function cback(accum, curr, currI, O) {\n                return accum - Math.round(corrFactor * curr);\n            }\n\n            this._value = _.reduce([value], cback, Math.round(this._value * corrFactor)) / corrFactor;\n\n            return this;\n        },\n        multiply: function(value) {\n            function cback(accum, curr, currI, O) {\n                var corrFactor = _.correctionFactor(accum, curr);\n                return Math.round(accum * corrFactor) * Math.round(curr * corrFactor) / Math.round(corrFactor * corrFactor);\n            }\n\n            this._value = _.reduce([this._value, value], cback, 1);\n\n            return this;\n        },\n        divide: function(value) {\n            function cback(accum, curr, currI, O) {\n                var corrFactor = _.correctionFactor(accum, curr);\n                return Math.round(accum * corrFactor) / Math.round(curr * corrFactor);\n            }\n\n            this._value = _.reduce([this._value, value], cback);\n\n            return this;\n        },\n        difference: function(value) {\n            return Math.abs(numeral(this._value).subtract(value).value());\n        }\n    };\n\n    /************************************\n        Default Locale && Format\n    ************************************/\n\n    numeral.register('locale', 'en', {\n        delimiters: {\n            thousands: ',',\n            decimal: '.'\n        },\n        abbreviations: {\n            thousand: 'k',\n            million: 'm',\n            billion: 'b',\n            trillion: 't'\n        },\n        ordinal: function(number) {\n            var b = number % 10;\n            return (~~(number % 100 / 10) === 1) ? 'th' :\n                (b === 1) ? 'st' :\n                (b === 2) ? 'nd' :\n                (b === 3) ? 'rd' : 'th';\n        },\n        currency: {\n            symbol: '$'\n        }\n    });\n\n    \n\n(function() {\n        numeral.register('format', 'bps', {\n            regexps: {\n                format: /(BPS)/,\n                unformat: /(BPS)/\n            },\n            format: function(value, format, roundingFunction) {\n                var space = numeral._.includes(format, ' BPS') ? ' ' : '',\n                    output;\n\n                value = value * 10000;\n\n                // check for space before BPS\n                format = format.replace(/\\s?BPS/, '');\n\n                output = numeral._.numberToFormat(value, format, roundingFunction);\n\n                if (numeral._.includes(output, ')')) {\n                    output = output.split('');\n\n                    output.splice(-1, 0, space + 'BPS');\n\n                    output = output.join('');\n                } else {\n                    output = output + space + 'BPS';\n                }\n\n                return output;\n            },\n            unformat: function(string) {\n                return +(numeral._.stringToNumber(string) * 0.0001).toFixed(15);\n            }\n        });\n})();\n\n\n(function() {\n        var decimal = {\n            base: 1000,\n            suffixes: ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n        },\n        binary = {\n            base: 1024,\n            suffixes: ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB']\n        };\n\n    var allSuffixes =  decimal.suffixes.concat(binary.suffixes.filter(function (item) {\n            return decimal.suffixes.indexOf(item) < 0;\n        }));\n        var unformatRegex = allSuffixes.join('|');\n        // Allow support for BPS (http://www.investopedia.com/terms/b/basispoint.asp)\n        unformatRegex = '(' + unformatRegex.replace('B', 'B(?!PS)') + ')';\n\n    numeral.register('format', 'bytes', {\n        regexps: {\n            format: /([0\\s]i?b)/,\n            unformat: new RegExp(unformatRegex)\n        },\n        format: function(value, format, roundingFunction) {\n            var output,\n                bytes = numeral._.includes(format, 'ib') ? binary : decimal,\n                suffix = numeral._.includes(format, ' b') || numeral._.includes(format, ' ib') ? ' ' : '',\n                power,\n                min,\n                max;\n\n            // check for space before\n            format = format.replace(/\\s?i?b/, '');\n\n            for (power = 0; power <= bytes.suffixes.length; power++) {\n                min = Math.pow(bytes.base, power);\n                max = Math.pow(bytes.base, power + 1);\n\n                if (value === null || value === 0 || value >= min && value < max) {\n                    suffix += bytes.suffixes[power];\n\n                    if (min > 0) {\n                        value = value / min;\n                    }\n\n                    break;\n                }\n            }\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            return output + suffix;\n        },\n        unformat: function(string) {\n            var value = numeral._.stringToNumber(string),\n                power,\n                bytesMultiplier;\n\n            if (value) {\n                for (power = decimal.suffixes.length - 1; power >= 0; power--) {\n                    if (numeral._.includes(string, decimal.suffixes[power])) {\n                        bytesMultiplier = Math.pow(decimal.base, power);\n\n                        break;\n                    }\n\n                    if (numeral._.includes(string, binary.suffixes[power])) {\n                        bytesMultiplier = Math.pow(binary.base, power);\n\n                        break;\n                    }\n                }\n\n                value *= (bytesMultiplier || 1);\n            }\n\n            return value;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'currency', {\n        regexps: {\n            format: /(\\$)/\n        },\n        format: function(value, format, roundingFunction) {\n            var locale = numeral.locales[numeral.options.currentLocale],\n                symbols = {\n                    before: format.match(/^([\\+|\\-|\\(|\\s|\\$]*)/)[0],\n                    after: format.match(/([\\+|\\-|\\)|\\s|\\$]*)$/)[0]\n                },\n                output,\n                symbol,\n                i;\n\n            // strip format of spaces and $\n            format = format.replace(/\\s?\\$\\s?/, '');\n\n            // format the number\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            // update the before and after based on value\n            if (value >= 0) {\n                symbols.before = symbols.before.replace(/[\\-\\(]/, '');\n                symbols.after = symbols.after.replace(/[\\-\\)]/, '');\n            } else if (value < 0 && (!numeral._.includes(symbols.before, '-') && !numeral._.includes(symbols.before, '('))) {\n                symbols.before = '-' + symbols.before;\n            }\n\n            // loop through each before symbol\n            for (i = 0; i < symbols.before.length; i++) {\n                symbol = symbols.before[i];\n\n                switch (symbol) {\n                    case '$':\n                        output = numeral._.insert(output, locale.currency.symbol, i);\n                        break;\n                    case ' ':\n                        output = numeral._.insert(output, ' ', i + locale.currency.symbol.length - 1);\n                        break;\n                }\n            }\n\n            // loop through each after symbol\n            for (i = symbols.after.length - 1; i >= 0; i--) {\n                symbol = symbols.after[i];\n\n                switch (symbol) {\n                    case '$':\n                        output = i === symbols.after.length - 1 ? output + locale.currency.symbol : numeral._.insert(output, locale.currency.symbol, -(symbols.after.length - (1 + i)));\n                        break;\n                    case ' ':\n                        output = i === symbols.after.length - 1 ? output + ' ' : numeral._.insert(output, ' ', -(symbols.after.length - (1 + i) + locale.currency.symbol.length - 1));\n                        break;\n                }\n            }\n\n\n            return output;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'exponential', {\n        regexps: {\n            format: /(e\\+|e-)/,\n            unformat: /(e\\+|e-)/\n        },\n        format: function(value, format, roundingFunction) {\n            var output,\n                exponential = typeof value === 'number' && !numeral._.isNaN(value) ? value.toExponential() : '0e+0',\n                parts = exponential.split('e');\n\n            format = format.replace(/e[\\+|\\-]{1}0/, '');\n\n            output = numeral._.numberToFormat(Number(parts[0]), format, roundingFunction);\n\n            return output + 'e' + parts[1];\n        },\n        unformat: function(string) {\n            var parts = numeral._.includes(string, 'e+') ? string.split('e+') : string.split('e-'),\n                value = Number(parts[0]),\n                power = Number(parts[1]);\n\n            power = numeral._.includes(string, 'e-') ? power *= -1 : power;\n\n            function cback(accum, curr, currI, O) {\n                var corrFactor = numeral._.correctionFactor(accum, curr),\n                    num = (accum * corrFactor) * (curr * corrFactor) / (corrFactor * corrFactor);\n                return num;\n            }\n\n            return numeral._.reduce([value, Math.pow(10, power)], cback, 1);\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'ordinal', {\n        regexps: {\n            format: /(o)/\n        },\n        format: function(value, format, roundingFunction) {\n            var locale = numeral.locales[numeral.options.currentLocale],\n                output,\n                ordinal = numeral._.includes(format, ' o') ? ' ' : '';\n\n            // check for space before\n            format = format.replace(/\\s?o/, '');\n\n            ordinal += locale.ordinal(value);\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            return output + ordinal;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'percentage', {\n        regexps: {\n            format: /(%)/,\n            unformat: /(%)/\n        },\n        format: function(value, format, roundingFunction) {\n            var space = numeral._.includes(format, ' %') ? ' ' : '',\n                output;\n\n            if (numeral.options.scalePercentBy100) {\n                value = value * 100;\n            }\n\n            // check for space before %\n            format = format.replace(/\\s?\\%/, '');\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            if (numeral._.includes(output, ')')) {\n                output = output.split('');\n\n                output.splice(-1, 0, space + '%');\n\n                output = output.join('');\n            } else {\n                output = output + space + '%';\n            }\n\n            return output;\n        },\n        unformat: function(string) {\n            var number = numeral._.stringToNumber(string);\n            if (numeral.options.scalePercentBy100) {\n                return number * 0.01;\n            }\n            return number;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'time', {\n        regexps: {\n            format: /(:)/,\n            unformat: /(:)/\n        },\n        format: function(value, format, roundingFunction) {\n            var hours = Math.floor(value / 60 / 60),\n                minutes = Math.floor((value - (hours * 60 * 60)) / 60),\n                seconds = Math.round(value - (hours * 60 * 60) - (minutes * 60));\n\n            return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ':' + (seconds < 10 ? '0' + seconds : seconds);\n        },\n        unformat: function(string) {\n            var timeArray = string.split(':'),\n                seconds = 0;\n\n            // turn hours and minutes into seconds and add them all up\n            if (timeArray.length === 3) {\n                // hours\n                seconds = seconds + (Number(timeArray[0]) * 60 * 60);\n                // minutes\n                seconds = seconds + (Number(timeArray[1]) * 60);\n                // seconds\n                seconds = seconds + Number(timeArray[2]);\n            } else if (timeArray.length === 2) {\n                // minutes\n                seconds = seconds + (Number(timeArray[0]) * 60);\n                // seconds\n                seconds = seconds + Number(timeArray[1]);\n            }\n            return Number(seconds);\n        }\n    });\n})();\n\nreturn numeral;\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,KAAC,SAAU,QAAQ,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,OAAO;AAAA,MAClB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AACrD,eAAO,UAAU,QAAQ;AAAA,MAC7B,OAAO;AACH,eAAO,UAAU,QAAQ;AAAA,MAC7B;AAAA,IACJ,GAAE,SAAM,WAAY;AAKhB,UAAI,SACA,GACA,UAAU,SACV,UAAU,CAAC,GACX,UAAU,CAAC,GACX,WAAW;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,mBAAmB;AAAA,MACvB,GACA,UAAU;AAAA,QACN,eAAe,SAAS;AAAA,QACxB,YAAY,SAAS;AAAA,QACrB,YAAY,SAAS;AAAA,QACrB,eAAe,SAAS;AAAA,QACxB,mBAAmB,SAAS;AAAA,MAChC;AAQJ,eAAS,QAAQ,OAAO,QAAQ;AAC5B,aAAK,SAAS;AAEd,aAAK,SAAS;AAAA,MAClB;AAEA,gBAAU,SAAS,OAAO;AACtB,YAAI,OACA,MACA,kBACA;AAEJ,YAAI,QAAQ,UAAU,KAAK,GAAG;AAC1B,kBAAQ,MAAM,MAAM;AAAA,QACxB,WAAW,UAAU,KAAK,OAAO,UAAU,aAAa;AACpD,kBAAQ;AAAA,QACZ,WAAW,UAAU,QAAQ,EAAE,MAAM,KAAK,GAAG;AACzC,kBAAQ;AAAA,QACZ,WAAW,OAAO,UAAU,UAAU;AAClC,cAAI,QAAQ,cAAc,UAAU,QAAQ,YAAY;AACpD,oBAAQ;AAAA,UACZ,WAAW,QAAQ,cAAc,UAAU,QAAQ,cAAc,CAAC,MAAM,QAAQ,YAAY,EAAE,EAAE,QAAQ;AACpG,oBAAQ;AAAA,UACZ,OAAO;AACH,iBAAK,QAAQ,SAAS;AAClB,uBAAS,OAAO,QAAQ,IAAI,EAAE,QAAQ,aAAa,aAAa,QAAQ,IAAI,EAAE,QAAQ,SAAS,IAAI,QAAQ,IAAI,EAAE,QAAQ;AAEzH,kBAAI,UAAU,MAAM,MAAM,MAAM,GAAG;AAC/B,mCAAmB,QAAQ,IAAI,EAAE;AAEjC;AAAA,cACJ;AAAA,YACJ;AAEA,+BAAmB,oBAAoB,QAAQ,EAAE;AAEjD,oBAAQ,iBAAiB,KAAK;AAAA,UAClC;AAAA,QACJ,OAAO;AACH,kBAAQ,OAAO,KAAK,KAAI;AAAA,QAC5B;AAEA,eAAO,IAAI,QAAQ,OAAO,KAAK;AAAA,MACnC;AAGA,cAAQ,UAAU;AAGlB,cAAQ,YAAY,SAAS,KAAK;AAC9B,eAAO,eAAe;AAAA,MAC1B;AAGA,cAAQ,IAAI,IAAI;AAAA;AAAA,QAEZ,gBAAgB,SAAS,OAAO,QAAQ,kBAAkB;AACtD,cAAI,SAAS,QAAQ,QAAQ,QAAQ,aAAa,GAC9C,OAAO,OACP,SAAS,OACT,eAAe,GACf,OAAO,IACP,WAAW,MACX,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,IACV,MAAM,OACN,WACA,KACA,KACA,KACA,OACA,KACA,WACA,QACA,WACA;AAGJ,kBAAQ,SAAS;AAEjB,gBAAM,KAAK,IAAI,KAAK;AAIpB,cAAI,QAAQ,EAAE,SAAS,QAAQ,GAAG,GAAG;AACjC,mBAAO;AACP,qBAAS,OAAO,QAAQ,YAAY,EAAE;AAAA,UAC1C,WAAW,QAAQ,EAAE,SAAS,QAAQ,GAAG,KAAK,QAAQ,EAAE,SAAS,QAAQ,GAAG,GAAG;AAC3E,qBAAS,QAAQ,EAAE,SAAS,QAAQ,GAAG,IAAI,OAAO,QAAQ,GAAG,IAAI,QAAQ,IAAI,OAAO,QAAQ,GAAG,IAAI;AACnG,qBAAS,OAAO,QAAQ,YAAY,EAAE;AAAA,UAC1C;AAGA,cAAI,QAAQ,EAAE,SAAS,QAAQ,GAAG,GAAG;AACjC,wBAAY,OAAO,MAAM,aAAa;AAEtC,wBAAY,YAAY,UAAU,CAAC,IAAI;AAGvC,gBAAI,QAAQ,EAAE,SAAS,QAAQ,IAAI,GAAG;AAClC,qBAAO;AAAA,YACX;AAEA,qBAAS,OAAO,QAAQ,IAAI,OAAO,OAAO,UAAU,GAAG,EAAE;AAEzD,gBAAI,OAAO,YAAY,CAAC,aAAa,cAAc,KAAK;AAEpD,sBAAQ,OAAO,cAAc;AAC7B,sBAAQ,QAAQ;AAAA,YACpB,WAAW,MAAM,YAAY,OAAO,WAAW,CAAC,aAAa,cAAc,KAAK;AAE5E,sBAAQ,OAAO,cAAc;AAC7B,sBAAQ,QAAQ;AAAA,YACpB,WAAW,MAAM,WAAW,OAAO,WAAW,CAAC,aAAa,cAAc,KAAK;AAE3E,sBAAQ,OAAO,cAAc;AAC7B,sBAAQ,QAAQ;AAAA,YACpB,WAAW,MAAM,WAAW,OAAO,YAAY,CAAC,aAAa,cAAc,KAAK;AAE5E,sBAAQ,OAAO,cAAc;AAC7B,sBAAQ,QAAQ;AAAA,YACpB;AAAA,UACJ;AAGA,cAAI,QAAQ,EAAE,SAAS,QAAQ,KAAK,GAAG;AACnC,qBAAS;AACT,qBAAS,OAAO,QAAQ,OAAO,GAAG;AAAA,UACtC;AAGA,gBAAM,MAAM,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC;AACnC,sBAAY,OAAO,MAAM,GAAG,EAAE,CAAC;AAC/B,sBAAY,OAAO,QAAQ,GAAG;AAC9B,0BAAgB,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,GAAG;AAEtE,cAAI,WAAW;AACX,gBAAI,QAAQ,EAAE,SAAS,WAAW,GAAG,GAAG;AACpC,0BAAY,UAAU,QAAQ,KAAK,EAAE;AACrC,0BAAY,UAAU,MAAM,GAAG;AAC/B,wBAAU,QAAQ,EAAE,QAAQ,OAAQ,UAAU,CAAC,EAAE,SAAS,UAAU,CAAC,EAAE,QAAS,kBAAkB,UAAU,CAAC,EAAE,MAAM;AAAA,YACzH,OAAO;AACH,wBAAU,QAAQ,EAAE,QAAQ,OAAO,UAAU,QAAQ,gBAAgB;AAAA,YACzE;AAEA,kBAAM,QAAQ,MAAM,GAAG,EAAE,CAAC;AAE1B,gBAAI,QAAQ,EAAE,SAAS,SAAS,GAAG,GAAG;AAClC,wBAAU,OAAO,WAAW,UAAU,QAAQ,MAAM,GAAG,EAAE,CAAC;AAAA,YAC9D,OAAO;AACH,wBAAU;AAAA,YACd;AAEA,gBAAI,UAAU,OAAO,QAAQ,MAAM,CAAC,CAAC,MAAM,GAAG;AAC1C,wBAAU;AAAA,YACd;AAAA,UACJ,OAAO;AACH,kBAAM,QAAQ,EAAE,QAAQ,OAAO,GAAG,gBAAgB;AAAA,UACtD;AAGA,cAAI,QAAQ,CAAC,aAAa,OAAO,GAAG,KAAK,OAAQ,SAAS,OAAO,cAAc,UAAU;AACrF,kBAAM,OAAO,OAAO,GAAG,IAAI,GAAI;AAE/B,oBAAQ,MAAM;AAAA,cACV,KAAK,OAAO,cAAc;AACtB,uBAAO,OAAO,cAAc;AAC5B;AAAA,cACJ,KAAK,OAAO,cAAc;AACtB,uBAAO,OAAO,cAAc;AAC5B;AAAA,cACJ,KAAK,OAAO,cAAc;AACtB,uBAAO,OAAO,cAAc;AAC5B;AAAA,YACR;AAAA,UACJ;AAIA,cAAI,QAAQ,EAAE,SAAS,KAAK,GAAG,GAAG;AAC9B,kBAAM,IAAI,MAAM,CAAC;AACjB,kBAAM;AAAA,UACV;AAEA,cAAI,IAAI,SAAS,cAAc;AAC3B,qBAAS,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,KAAK;AAChD,oBAAM,MAAM;AAAA,YAChB;AAAA,UACJ;AAEA,cAAI,YAAY,IAAI;AAChB,kBAAM,IAAI,SAAS,EAAE,QAAQ,2BAA2B,OAAO,OAAO,WAAW,SAAS;AAAA,UAC9F;AAEA,cAAI,OAAO,QAAQ,GAAG,MAAM,GAAG;AAC3B,kBAAM;AAAA,UACV;AAEA,mBAAS,MAAM,WAAW,OAAO,OAAO;AAExC,cAAI,MAAM;AACN,sBAAU,QAAQ,MAAM,MAAM,MAAM,UAAU,QAAQ,MAAM,MAAM;AAAA,UACtE,OAAO;AACH,gBAAI,UAAU,GAAG;AACb,uBAAS,WAAW,KAAK,MAAM,MAAM,OAAO,SAAS,UAAU,MAAM,MAAM;AAAA,YAC/E,WAAW,KAAK;AACZ,uBAAS,MAAM;AAAA,YACnB;AAAA,UACJ;AAEA,iBAAO;AAAA,QACX;AAAA;AAAA,QAEA,gBAAgB,SAAS,QAAQ;AAC7B,cAAI,SAAS,QAAQ,QAAQ,aAAa,GACtC,iBAAiB,QACjB,gBAAgB;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,SAAS;AAAA,YACT,UAAU;AAAA,UACd,GACA,cACA,OACA,GACA;AAEJ,cAAI,QAAQ,cAAc,WAAW,QAAQ,YAAY;AACrD,oBAAQ;AAAA,UACZ,WAAW,QAAQ,cAAc,WAAW,QAAQ,cAAc,CAAC,OAAO,QAAQ,YAAY,EAAE,EAAE,QAAQ;AACtG,oBAAQ;AAAA,UACZ,OAAO;AACH,oBAAQ;AAER,gBAAI,OAAO,WAAW,YAAY,KAAK;AACnC,uBAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,WAAW,SAAS,GAAG;AAAA,YAC7E;AAEA,iBAAK,gBAAgB,eAAe;AAChC,uBAAS,IAAI,OAAO,cAAc,OAAO,cAAc,YAAY,IAAI,eAAe,OAAO,SAAS,SAAS,eAAe;AAE9H,kBAAI,eAAe,MAAM,MAAM,GAAG;AAC9B,yBAAS,KAAK,IAAI,IAAI,cAAc,YAAY,CAAC;AACjD;AAAA,cACJ;AAAA,YACJ;AAGA,sBAAU,OAAO,MAAM,GAAG,EAAE,SAAS,KAAK,IAAI,OAAO,MAAM,GAAG,EAAE,SAAS,GAAG,OAAO,MAAM,GAAG,EAAE,SAAS,CAAC,KAAK,IAAI,IAAI;AAGrH,qBAAS,OAAO,QAAQ,cAAc,EAAE;AAExC,qBAAS,OAAO,MAAM;AAAA,UAC1B;AAEA,iBAAO;AAAA,QACX;AAAA,QACA,OAAO,SAAS,OAAO;AACnB,iBAAO,OAAO,UAAU,YAAY,MAAM,KAAK;AAAA,QACnD;AAAA,QACA,UAAU,SAAS,QAAQ,QAAQ;AAC/B,iBAAO,OAAO,QAAQ,MAAM,MAAM;AAAA,QACtC;AAAA,QACA,QAAQ,SAAS,QAAQ,WAAW,OAAO;AACvC,iBAAO,OAAO,MAAM,GAAG,KAAK,IAAI,YAAY,OAAO,MAAM,KAAK;AAAA,QAClE;AAAA,QACA,QAAQ,SAAS,OAAO,UAA6B;AACjD,cAAI,SAAS,MAAM;AACf,kBAAM,IAAI,UAAU,oDAAoD;AAAA,UAC5E;AAEA,cAAI,OAAO,aAAa,YAAY;AAChC,kBAAM,IAAI,UAAU,WAAW,oBAAoB;AAAA,UACvD;AAEA,cAAI,IAAI,OAAO,KAAK,GAChB,MAAM,EAAE,WAAW,GACnB,IAAI,GACJ;AAEJ,cAAI,UAAU,WAAW,GAAG;AACxB,oBAAQ,UAAU,CAAC;AAAA,UACvB,OAAO;AACH,mBAAO,IAAI,OAAO,EAAE,KAAK,IAAI;AACzB;AAAA,YACJ;AAEA,gBAAI,KAAK,KAAK;AACV,oBAAM,IAAI,UAAU,6CAA6C;AAAA,YACrE;AAEA,oBAAQ,EAAE,GAAG;AAAA,UACjB;AACA,iBAAO,IAAI,KAAK,KAAK;AACjB,gBAAI,KAAK,GAAG;AACR,sBAAQ,SAAS,OAAO,EAAE,CAAC,GAAG,GAAG,CAAC;AAAA,YACtC;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,YAAY,SAAU,GAAG;AACrB,cAAI,QAAQ,EAAE,SAAS,EAAE,MAAM,GAAG;AAElC,iBAAO,MAAM,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,MAAM;AAAA,QAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,kBAAkB,WAAY;AAC1B,cAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAE/C,iBAAO,KAAK,OAAO,SAAS,OAAO,MAAM;AACrC,gBAAI,KAAK,EAAE,WAAW,IAAI;AAC1B,mBAAO,QAAQ,KAAK,QAAQ;AAAA,UAChC,GAAG,CAAC;AAAA,QACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,SAAS,SAAS,OAAO,aAAa,kBAAkB,WAAW;AAC/D,cAAI,aAAa,MAAM,SAAS,EAAE,MAAM,GAAG,GACvC,cAAc,eAAe,aAAa,IAC1C,kBACA,iBACA,OACA;AAGJ,cAAI,WAAW,WAAW,GAAG;AAC3B,+BAAmB,KAAK,IAAI,KAAK,IAAI,WAAW,CAAC,EAAE,QAAQ,WAAW,GAAG,WAAW;AAAA,UACtF,OAAO;AACL,+BAAmB;AAAA,UACrB;AAEA,kBAAQ,KAAK,IAAI,IAAI,gBAAgB;AAGrC,oBAAU,iBAAiB,QAAQ,OAAO,gBAAgB,IAAI,OAAO,QAAQ,gBAAgB;AAE7F,cAAI,YAAY,cAAc,kBAAkB;AAC5C,8BAAkB,IAAI,OAAO,cAAc,aAAa,cAAc,qBAAqB,IAAI;AAC/F,qBAAS,OAAO,QAAQ,iBAAiB,EAAE;AAAA,UAC/C;AAEA,iBAAO;AAAA,QACX;AAAA,MACJ;AAGA,cAAQ,UAAU;AAGlB,cAAQ,UAAU;AAGlB,cAAQ,UAAU;AAKlB,cAAQ,SAAS,SAAS,KAAK;AAC3B,YAAI,KAAK;AACL,kBAAQ,gBAAgB,IAAI,YAAY;AAAA,QAC5C;AAEA,eAAO,QAAQ;AAAA,MACnB;AAKA,cAAQ,aAAa,SAAS,KAAK;AAC/B,YAAI,CAAC,KAAK;AACN,iBAAO,QAAQ,QAAQ,aAAa;AAAA,QACxC;AAEA,cAAM,IAAI,YAAY;AAEtB,YAAI,CAAC,QAAQ,GAAG,GAAG;AACf,gBAAM,IAAI,MAAM,sBAAsB,GAAG;AAAA,QAC7C;AAEA,eAAO,QAAQ,GAAG;AAAA,MACtB;AAEA,cAAQ,QAAQ,WAAW;AACvB,iBAAS,YAAY,UAAU;AAC3B,kBAAQ,QAAQ,IAAI,SAAS,QAAQ;AAAA,QACzC;AAAA,MACJ;AAEA,cAAQ,aAAa,SAAS,QAAQ;AAClC,gBAAQ,aAAa,OAAO,WAAY,WAAW,SAAS;AAAA,MAChE;AAEA,cAAQ,aAAa,SAAU,QAAQ;AACnC,gBAAQ,aAAa,OAAO,WAAY,WAAW,SAAS;AAAA,MAChE;AAEA,cAAQ,gBAAgB,SAAS,QAAQ;AACrC,gBAAQ,gBAAgB,OAAO,WAAY,WAAW,SAAS;AAAA,MACnE;AAEA,cAAQ,WAAW,SAAS,MAAM,MAAM,QAAQ;AAC5C,eAAO,KAAK,YAAY;AAExB,YAAI,KAAK,OAAO,GAAG,EAAE,IAAI,GAAG;AACxB,gBAAM,IAAI,UAAU,OAAO,MAAM,OAAO,sBAAsB;AAAA,QAClE;AAEA,aAAK,OAAO,GAAG,EAAE,IAAI,IAAI;AAEzB,eAAO;AAAA,MACX;AAGA,cAAQ,WAAW,SAAS,KAAK,SAAS;AACtC,YAAI,aACA,cACA,aACA,WACA,UACA,gBACA,YACA;AAGJ,YAAI,OAAO,QAAQ,UAAU;AACzB,iBAAO;AAEP,cAAI,QAAQ,MAAM;AACd,oBAAQ,KAAK,8DAA8D,GAAG;AAAA,UAClF;AAAA,QACJ;AAGA,cAAM,IAAI,KAAK;AAGf,YAAI,CAAC,CAAC,IAAI,MAAM,OAAO,GAAG;AACtB,iBAAO;AAAA,QACX;AAGA,YAAI,QAAQ,IAAI;AACZ,iBAAO;AAAA,QACX;AAGA,YAAI;AAEA,uBAAa,QAAQ,WAAW,OAAO;AAAA,QAC3C,SAAS,GAAG;AACR,uBAAa,QAAQ,WAAW,QAAQ,OAAO,CAAC;AAAA,QACpD;AAGA,sBAAc,WAAW,SAAS;AAClC,mBAAW,WAAW;AACtB,sBAAc,WAAW,WAAW;AACpC,YAAI,WAAW,WAAW,cAAc,KAAK;AACzC,yBAAe;AAAA,QACnB,OAAO;AACH,yBAAe,WAAW,WAAW;AAAA,QACzC;AAGA,eAAO,IAAI,MAAM,SAAS;AAC1B,YAAI,SAAS,MAAM;AACf,gBAAM,IAAI,OAAO,CAAC;AAClB,cAAI,KAAK,CAAC,MAAM,aAAa;AACzB,mBAAO;AAAA,UACX;AAAA,QACJ;AAGA,eAAO,IAAI,MAAM,SAAS;AAC1B,YAAI,SAAS,MAAM;AACf,gBAAM,IAAI,MAAM,GAAG,EAAE;AACrB,cAAI,KAAK,CAAC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM,SAAS,WAAW,KAAK,CAAC,MAAM,SAAS,WAAW,KAAK,CAAC,MAAM,SAAS,UAAU;AAChI,mBAAO;AAAA,UACX;AAAA,QACJ;AAEA,yBAAiB,IAAI,OAAO,eAAe,KAAK;AAEhD,YAAI,CAAC,IAAI,MAAM,UAAU,GAAG;AACxB,sBAAY,IAAI,MAAM,WAAW;AACjC,cAAI,UAAU,SAAS,GAAG;AACtB,mBAAO;AAAA,UACX,OAAO;AACH,gBAAI,UAAU,SAAS,GAAG;AACtB,qBAAS,CAAC,CAAE,UAAU,CAAC,EAAE,MAAM,WAAW,KAAK,CAAC,UAAU,CAAC,EAAE,MAAM,cAAc;AAAA,YACrF,OAAO;AACH,kBAAI,UAAU,CAAC,EAAE,WAAW,GAAG;AAC3B,uBAAS,CAAC,CAAE,UAAU,CAAC,EAAE,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,EAAE,MAAM,cAAc,KAAK,CAAC,CAAE,UAAU,CAAC,EAAE,MAAM,OAAO;AAAA,cACnH,OAAO;AACH,uBAAS,CAAC,CAAE,UAAU,CAAC,EAAE,MAAM,WAAW,KAAK,CAAC,UAAU,CAAC,EAAE,MAAM,cAAc,KAAK,CAAC,CAAE,UAAU,CAAC,EAAE,MAAM,OAAO;AAAA,cACvH;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAOA,cAAQ,KAAK,QAAQ,YAAY;AAAA,QAC7B,OAAO,WAAW;AACd,iBAAO,QAAQ,IAAI;AAAA,QACvB;AAAA,QACA,QAAQ,SAAS,aAAa,kBAAkB;AAC5C,cAAI,QAAQ,KAAK,QACb,SAAS,eAAe,QAAQ,eAChC,MACA,QACA;AAGJ,6BAAmB,oBAAoB,KAAK;AAG5C,cAAI,UAAU,KAAK,QAAQ,eAAe,MAAM;AAC5C,qBAAS,QAAQ;AAAA,UACrB,WAAW,UAAU,QAAQ,QAAQ,eAAe,MAAM;AACtD,qBAAS,QAAQ;AAAA,UACrB,OAAO;AACH,iBAAK,QAAQ,SAAS;AAClB,kBAAI,OAAO,MAAM,QAAQ,IAAI,EAAE,QAAQ,MAAM,GAAG;AAC5C,iCAAiB,QAAQ,IAAI,EAAE;AAE/B;AAAA,cACJ;AAAA,YACJ;AAEA,6BAAiB,kBAAkB,QAAQ,EAAE;AAE7C,qBAAS,eAAe,OAAO,QAAQ,gBAAgB;AAAA,UAC3D;AAEA,iBAAO;AAAA,QACX;AAAA,QACA,OAAO,WAAW;AACd,iBAAO,KAAK;AAAA,QAChB;AAAA,QACA,OAAO,WAAW;AACd,iBAAO,KAAK;AAAA,QAChB;AAAA,QACA,KAAK,SAAS,OAAO;AACjB,eAAK,SAAS,OAAO,KAAK;AAE1B,iBAAO;AAAA,QACX;AAAA,QACA,KAAK,SAAS,OAAO;AACjB,cAAI,aAAa,EAAE,iBAAiB,KAAK,MAAM,KAAK,QAAQ,KAAK;AAEjE,mBAAS,MAAM,OAAO,MAAM,OAAO,GAAG;AAClC,mBAAO,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,UAC/C;AAEA,eAAK,SAAS,EAAE,OAAO,CAAC,KAAK,QAAQ,KAAK,GAAG,OAAO,CAAC,IAAI;AAEzD,iBAAO;AAAA,QACX;AAAA,QACA,UAAU,SAAS,OAAO;AACtB,cAAI,aAAa,EAAE,iBAAiB,KAAK,MAAM,KAAK,QAAQ,KAAK;AAEjE,mBAAS,MAAM,OAAO,MAAM,OAAO,GAAG;AAClC,mBAAO,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,UAC/C;AAEA,eAAK,SAAS,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,KAAK,MAAM,KAAK,SAAS,UAAU,CAAC,IAAI;AAE/E,iBAAO;AAAA,QACX;AAAA,QACA,UAAU,SAAS,OAAO;AACtB,mBAAS,MAAM,OAAO,MAAM,OAAO,GAAG;AAClC,gBAAI,aAAa,EAAE,iBAAiB,OAAO,IAAI;AAC/C,mBAAO,KAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,MAAM,OAAO,UAAU,IAAI,KAAK,MAAM,aAAa,UAAU;AAAA,UAC9G;AAEA,eAAK,SAAS,EAAE,OAAO,CAAC,KAAK,QAAQ,KAAK,GAAG,OAAO,CAAC;AAErD,iBAAO;AAAA,QACX;AAAA,QACA,QAAQ,SAAS,OAAO;AACpB,mBAAS,MAAM,OAAO,MAAM,OAAO,GAAG;AAClC,gBAAI,aAAa,EAAE,iBAAiB,OAAO,IAAI;AAC/C,mBAAO,KAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,MAAM,OAAO,UAAU;AAAA,UACxE;AAEA,eAAK,SAAS,EAAE,OAAO,CAAC,KAAK,QAAQ,KAAK,GAAG,KAAK;AAElD,iBAAO;AAAA,QACX;AAAA,QACA,YAAY,SAAS,OAAO;AACxB,iBAAO,KAAK,IAAI,QAAQ,KAAK,MAAM,EAAE,SAAS,KAAK,EAAE,MAAM,CAAC;AAAA,QAChE;AAAA,MACJ;AAMA,cAAQ,SAAS,UAAU,MAAM;AAAA,QAC7B,YAAY;AAAA,UACR,WAAW;AAAA,UACX,SAAS;AAAA,QACb;AAAA,QACA,eAAe;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,QACd;AAAA,QACA,SAAS,SAAS,QAAQ;AACtB,cAAI,IAAI,SAAS;AACjB,iBAAQ,CAAC,EAAE,SAAS,MAAM,QAAQ,IAAK,OAClC,MAAM,IAAK,OACX,MAAM,IAAK,OACX,MAAM,IAAK,OAAO;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,MACJ,CAAC;AAIL,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,OAAO;AAAA,UAC9B,SAAS;AAAA,YACL,QAAQ;AAAA,YACR,UAAU;AAAA,UACd;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,MAAM,IAAI,MAAM,IACnD;AAEJ,oBAAQ,QAAQ;AAGhB,qBAAS,OAAO,QAAQ,UAAU,EAAE;AAEpC,qBAAS,QAAQ,EAAE,eAAe,OAAO,QAAQ,gBAAgB;AAEjE,gBAAI,QAAQ,EAAE,SAAS,QAAQ,GAAG,GAAG;AACjC,uBAAS,OAAO,MAAM,EAAE;AAExB,qBAAO,OAAO,IAAI,GAAG,QAAQ,KAAK;AAElC,uBAAS,OAAO,KAAK,EAAE;AAAA,YAC3B,OAAO;AACH,uBAAS,SAAS,QAAQ;AAAA,YAC9B;AAEA,mBAAO;AAAA,UACX;AAAA,UACA,UAAU,SAAS,QAAQ;AACvB,mBAAO,EAAE,QAAQ,EAAE,eAAe,MAAM,IAAI,MAAQ,QAAQ,EAAE;AAAA,UAClE;AAAA,QACJ,CAAC;AAAA,MACT,GAAG;AAGH,OAAC,WAAW;AACJ,YAAI,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAClE,GACA,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU,CAAC,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,QAC1E;AAEJ,YAAI,cAAe,QAAQ,SAAS,OAAO,OAAO,SAAS,OAAO,SAAU,MAAM;AAC1E,iBAAO,QAAQ,SAAS,QAAQ,IAAI,IAAI;AAAA,QAC5C,CAAC,CAAC;AACF,YAAI,gBAAgB,YAAY,KAAK,GAAG;AAExC,wBAAgB,MAAM,cAAc,QAAQ,KAAK,SAAS,IAAI;AAElE,gBAAQ,SAAS,UAAU,SAAS;AAAA,UAChC,SAAS;AAAA,YACL,QAAQ;AAAA,YACR,UAAU,IAAI,OAAO,aAAa;AAAA,UACtC;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,QACA,QAAQ,QAAQ,EAAE,SAAS,QAAQ,IAAI,IAAI,SAAS,SACpD,SAAS,QAAQ,EAAE,SAAS,QAAQ,IAAI,KAAK,QAAQ,EAAE,SAAS,QAAQ,KAAK,IAAI,MAAM,IACvF,OACA,KACA;AAGJ,qBAAS,OAAO,QAAQ,UAAU,EAAE;AAEpC,iBAAK,QAAQ,GAAG,SAAS,MAAM,SAAS,QAAQ,SAAS;AACrD,oBAAM,KAAK,IAAI,MAAM,MAAM,KAAK;AAChC,oBAAM,KAAK,IAAI,MAAM,MAAM,QAAQ,CAAC;AAEpC,kBAAI,UAAU,QAAQ,UAAU,KAAK,SAAS,OAAO,QAAQ,KAAK;AAC9D,0BAAU,MAAM,SAAS,KAAK;AAE9B,oBAAI,MAAM,GAAG;AACT,0BAAQ,QAAQ;AAAA,gBACpB;AAEA;AAAA,cACJ;AAAA,YACJ;AAEA,qBAAS,QAAQ,EAAE,eAAe,OAAO,QAAQ,gBAAgB;AAEjE,mBAAO,SAAS;AAAA,UACpB;AAAA,UACA,UAAU,SAAS,QAAQ;AACvB,gBAAI,QAAQ,QAAQ,EAAE,eAAe,MAAM,GACvC,OACA;AAEJ,gBAAI,OAAO;AACP,mBAAK,QAAQ,QAAQ,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS;AAC3D,oBAAI,QAAQ,EAAE,SAAS,QAAQ,QAAQ,SAAS,KAAK,CAAC,GAAG;AACrD,oCAAkB,KAAK,IAAI,QAAQ,MAAM,KAAK;AAE9C;AAAA,gBACJ;AAEA,oBAAI,QAAQ,EAAE,SAAS,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AACpD,oCAAkB,KAAK,IAAI,OAAO,MAAM,KAAK;AAE7C;AAAA,gBACJ;AAAA,cACJ;AAEA,uBAAU,mBAAmB;AAAA,YACjC;AAEA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAGH,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,YAAY;AAAA,UACvC,SAAS;AAAA,YACL,QAAQ;AAAA,UACZ;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,aAAa,GACtD,UAAU;AAAA,cACN,QAAQ,OAAO,MAAM,sBAAsB,EAAE,CAAC;AAAA,cAC9C,OAAO,OAAO,MAAM,sBAAsB,EAAE,CAAC;AAAA,YACjD,GACA,QACA,QACA;AAGJ,qBAAS,OAAO,QAAQ,YAAY,EAAE;AAGtC,qBAAS,QAAQ,EAAE,eAAe,OAAO,QAAQ,gBAAgB;AAGjE,gBAAI,SAAS,GAAG;AACZ,sBAAQ,SAAS,QAAQ,OAAO,QAAQ,UAAU,EAAE;AACpD,sBAAQ,QAAQ,QAAQ,MAAM,QAAQ,UAAU,EAAE;AAAA,YACtD,WAAW,QAAQ,MAAM,CAAC,QAAQ,EAAE,SAAS,QAAQ,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,SAAS,QAAQ,QAAQ,GAAG,IAAI;AAC5G,sBAAQ,SAAS,MAAM,QAAQ;AAAA,YACnC;AAGA,iBAAK,IAAI,GAAG,IAAI,QAAQ,OAAO,QAAQ,KAAK;AACxC,uBAAS,QAAQ,OAAO,CAAC;AAEzB,sBAAQ,QAAQ;AAAA,gBACZ,KAAK;AACD,2BAAS,QAAQ,EAAE,OAAO,QAAQ,OAAO,SAAS,QAAQ,CAAC;AAC3D;AAAA,gBACJ,KAAK;AACD,2BAAS,QAAQ,EAAE,OAAO,QAAQ,KAAK,IAAI,OAAO,SAAS,OAAO,SAAS,CAAC;AAC5E;AAAA,cACR;AAAA,YACJ;AAGA,iBAAK,IAAI,QAAQ,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,uBAAS,QAAQ,MAAM,CAAC;AAExB,sBAAQ,QAAQ;AAAA,gBACZ,KAAK;AACD,2BAAS,MAAM,QAAQ,MAAM,SAAS,IAAI,SAAS,OAAO,SAAS,SAAS,QAAQ,EAAE,OAAO,QAAQ,OAAO,SAAS,QAAQ,EAAE,QAAQ,MAAM,UAAU,IAAI,GAAG;AAC9J;AAAA,gBACJ,KAAK;AACD,2BAAS,MAAM,QAAQ,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,EAAE,OAAO,QAAQ,KAAK,EAAE,QAAQ,MAAM,UAAU,IAAI,KAAK,OAAO,SAAS,OAAO,SAAS,EAAE;AAC5J;AAAA,cACR;AAAA,YACJ;AAGA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAGH,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,eAAe;AAAA,UAC1C,SAAS;AAAA,YACL,QAAQ;AAAA,YACR,UAAU;AAAA,UACd;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,QACA,cAAc,OAAO,UAAU,YAAY,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,MAAM,cAAc,IAAI,QAC7F,QAAQ,YAAY,MAAM,GAAG;AAEjC,qBAAS,OAAO,QAAQ,gBAAgB,EAAE;AAE1C,qBAAS,QAAQ,EAAE,eAAe,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ,gBAAgB;AAE5E,mBAAO,SAAS,MAAM,MAAM,CAAC;AAAA,UACjC;AAAA,UACA,UAAU,SAAS,QAAQ;AACvB,gBAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,GACjF,QAAQ,OAAO,MAAM,CAAC,CAAC,GACvB,QAAQ,OAAO,MAAM,CAAC,CAAC;AAE3B,oBAAQ,QAAQ,EAAE,SAAS,QAAQ,IAAI,IAAI,SAAS,KAAK;AAEzD,qBAAS,MAAM,OAAO,MAAM,OAAO,GAAG;AAClC,kBAAI,aAAa,QAAQ,EAAE,iBAAiB,OAAO,IAAI,GACnD,MAAO,QAAQ,cAAe,OAAO,eAAe,aAAa;AACrE,qBAAO;AAAA,YACX;AAEA,mBAAO,QAAQ,EAAE,OAAO,CAAC,OAAO,KAAK,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC;AAAA,UAClE;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAGH,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,WAAW;AAAA,UACtC,SAAS;AAAA,YACL,QAAQ;AAAA,UACZ;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,aAAa,GACtD,QACA,UAAU,QAAQ,EAAE,SAAS,QAAQ,IAAI,IAAI,MAAM;AAGvD,qBAAS,OAAO,QAAQ,QAAQ,EAAE;AAElC,uBAAW,OAAO,QAAQ,KAAK;AAE/B,qBAAS,QAAQ,EAAE,eAAe,OAAO,QAAQ,gBAAgB;AAEjE,mBAAO,SAAS;AAAA,UACpB;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAGH,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,cAAc;AAAA,UACzC,SAAS;AAAA,YACL,QAAQ;AAAA,YACR,UAAU;AAAA,UACd;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,QAAQ,QAAQ,EAAE,SAAS,QAAQ,IAAI,IAAI,MAAM,IACjD;AAEJ,gBAAI,QAAQ,QAAQ,mBAAmB;AACnC,sBAAQ,QAAQ;AAAA,YACpB;AAGA,qBAAS,OAAO,QAAQ,SAAS,EAAE;AAEnC,qBAAS,QAAQ,EAAE,eAAe,OAAO,QAAQ,gBAAgB;AAEjE,gBAAI,QAAQ,EAAE,SAAS,QAAQ,GAAG,GAAG;AACjC,uBAAS,OAAO,MAAM,EAAE;AAExB,qBAAO,OAAO,IAAI,GAAG,QAAQ,GAAG;AAEhC,uBAAS,OAAO,KAAK,EAAE;AAAA,YAC3B,OAAO;AACH,uBAAS,SAAS,QAAQ;AAAA,YAC9B;AAEA,mBAAO;AAAA,UACX;AAAA,UACA,UAAU,SAAS,QAAQ;AACvB,gBAAI,SAAS,QAAQ,EAAE,eAAe,MAAM;AAC5C,gBAAI,QAAQ,QAAQ,mBAAmB;AACnC,qBAAO,SAAS;AAAA,YACpB;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAGH,OAAC,WAAW;AACJ,gBAAQ,SAAS,UAAU,QAAQ;AAAA,UACnC,SAAS;AAAA,YACL,QAAQ;AAAA,YACR,UAAU;AAAA,UACd;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ,kBAAkB;AAC9C,gBAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,EAAE,GAClC,UAAU,KAAK,OAAO,QAAS,QAAQ,KAAK,MAAO,EAAE,GACrD,UAAU,KAAK,MAAM,QAAS,QAAQ,KAAK,KAAO,UAAU,EAAG;AAEnE,mBAAO,QAAQ,OAAO,UAAU,KAAK,MAAM,UAAU,WAAW,OAAO,UAAU,KAAK,MAAM,UAAU;AAAA,UAC1G;AAAA,UACA,UAAU,SAAS,QAAQ;AACvB,gBAAI,YAAY,OAAO,MAAM,GAAG,GAC5B,UAAU;AAGd,gBAAI,UAAU,WAAW,GAAG;AAExB,wBAAU,UAAW,OAAO,UAAU,CAAC,CAAC,IAAI,KAAK;AAEjD,wBAAU,UAAW,OAAO,UAAU,CAAC,CAAC,IAAI;AAE5C,wBAAU,UAAU,OAAO,UAAU,CAAC,CAAC;AAAA,YAC3C,WAAW,UAAU,WAAW,GAAG;AAE/B,wBAAU,UAAW,OAAO,UAAU,CAAC,CAAC,IAAI;AAE5C,wBAAU,UAAU,OAAO,UAAU,CAAC,CAAC;AAAA,YAC3C;AACA,mBAAO,OAAO,OAAO;AAAA,UACzB;AAAA,QACJ,CAAC;AAAA,MACL,GAAG;AAEH,aAAO;AAAA,IACP,CAAC;AAAA;AAAA;", "names": []}