import { ActionFunction, LoaderFunctionArgs, MetaFunction } from "react-router";
import ServerError from "~/components/ui/errors/ServerError";
import { StepFormWizardSessionOverviewApi } from "~/custom/modules/stepFormWizard/routes/api/sessions/StepFormWizardSessionOverviewApi.server";
import StepFormWizardSessionOverviewRoute from "~/custom/modules/stepFormWizard/routes/components/sessions/StepFormWizardSessionOverviewRoute";

export const meta: MetaFunction<typeof loader> = ({ data }) => data?.meta || [];
export const loader = (args: LoaderFunctionArgs) => StepFormWizardSessionOverviewApi.loader(args);
export const action: ActionFunction = (args) => StepFormWizardSessionOverviewApi.action(args);

export default () => <StepFormWizardSessionOverviewRoute />;

export function ErrorBoundary() {
  return <ServerError />;
}
