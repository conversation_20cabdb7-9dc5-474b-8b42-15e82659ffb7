import { useTranslation } from "react-i18next";
import { Outlet, useLoaderData, useParams } from "react-router";
import { getStepFormWizard, StepFormWizardWithDetails } from "~/custom/modules/stepFormWizard/db/stepFormWizard.db.server";
import { LoaderFunctionArgs, redirect } from "react-router";
import EditPageLayout from "~/components/ui/layouts/EditPageLayout";
import TabsVertical from "~/components/ui/tabs/TabsVertical";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

type LoaderData = {
  item: StepFormWizardWithDetails;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.stepFormWizard.update");
  const item = await getStepFormWizard(params.id!);
  if (!item) {
    throw redirect("/admin/step-form-wizard/step-form-wizards");
  }
  return Response.json({ item });
};

export default function () {
  const { t } = useTranslation();
  const data = useLoaderData<LoaderData>();
  const params = useParams();

  // useEffect(() => {
  //   if (UrlUtils.stripTrailingSlash(location.pathname) === `/admin/accounts/users/pages/edit/${params.id}`) {
  //     navigate(`/admin/accounts/users/pages/edit/${params.id}/blocks`);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [location.pathname]);

  return (
    <div>
      <EditPageLayout
        title={`${t(data.item.title)}`}
        menu={[
          {
            title: t("stepFormWizard.object.plural"),
            routePath: "/admin/step-form-wizard/step-form-wizards",
          },
          {
            title: data.item.title,
          },
        ]}
        withHome={false}
      >
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-12 xl:gap-12">
          <div className="lg:col-span-3">
            <TabsVertical
              exact={true}
              tabs={[
                {
                  name: t("shared.overview"),
                  routePath: `/admin/step-form-wizard/step-form-wizards/${params.id}`,
                },
                {
                  name: t("stepFormWizard.object.steps") + ` (${data.item.filters.length})`,
                  routePath: `/admin/step-form-wizard/step-form-wizards/${params.id}/steps`,
                },
                {
                  name: t("stepFormWizard.object.filters") + ` (${data.item.filters.length})`,
                  routePath: `/admin/step-form-wizard/step-form-wizards/${params.id}/filters`,
                },
                {
                  name: t("stepFormWizard.session.plural") + ` (${data.item.sessions.length})`,
                  routePath: `/admin/step-form-wizard/step-form-wizards/${params.id}/sessions`,
                },
                {
                  name: t("shared.dangerZone"),
                  routePath: `/admin/step-form-wizard/step-form-wizards/${params.id}/danger`,
                },
              ]}
            />
          </div>
          <div className="lg:col-span-9">
            <div className="w-full">
              <Outlet />
            </div>
          </div>
        </div>
      </EditPageLayout>
    </div>
  );
}
