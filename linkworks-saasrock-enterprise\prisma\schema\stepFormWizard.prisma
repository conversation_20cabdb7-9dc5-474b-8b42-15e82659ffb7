model StepFormWizard {
  id             String                  @id @default(cuid())
  createdAt      DateTime                @default(now())
  updatedAt      DateTime                @updatedAt
  title          String
  type           String // modal, page
  realtime       Boolean                 @default(false)
  active         Boolean                 @default(false)
  canBeDismissed Boolean                 @default(true)
  height         String?
  progressBar    String?
  entity         String?
  entityModel    Entity?
  filters        StepFormWizardFilter[]
  steps          StepFormWizardStep[]
  sessions       StepFormWizardSession[]
  rows           Row[]
}

model StepFormWizardFilter {
  id               String                             @id @default(cuid())
  createdAt        DateTime                           @default(now())
  stepFormWizardId String
  stepFormWizard   StepFormWizard                     @relation(fields: [stepFormWizardId], references: [id], onDelete: Cascade)
  type             String
  value            String?
  matches          StepFormWizardSessionFilterMatch[]
}

model StepFormWizardStep {
  id               String                      @id @default(cuid())
  stepFormWizardId String
  stepFormWizard   StepFormWizard              @relation(fields: [stepFormWizardId], references: [id], onDelete: Cascade)
  order            Int
  block            String
  sessionSteps     StepFormWizardSessionStep[]
}

model StepFormWizardSession {
  id               String                             @id @default(cuid())
  createdAt        DateTime                           @default(now())
  updatedAt        DateTime                           @updatedAt
  stepFormWizardId String
  stepFormWizard   StepFormWizard                     @relation(fields: [stepFormWizardId], references: [id], onDelete: Cascade)
  userId           String
  user             User                               @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenantId         String?
  tenant           Tenant?                            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  status           String // active, completed, dismissed
  startedAt        DateTime?
  completedAt      DateTime?
  dismissedAt      DateTime?
  createdRealtime  Boolean                            @default(false)
  matches          StepFormWizardSessionFilterMatch[]
  sessionSteps     StepFormWizardSessionStep[]
  actions          StepFormWizardSessionAction[]
  sessionRow       Row?

  // logic app session state
  logicAppRunId    String?
  callBackURL      String?
  currentStepIndex String?
  error            String[]

  // removed unique constraint to allow users 
  // to have multiple stepFormWizard sessions per user
  // @@unique([stepFormWizardId, userId, tenantId])
}

model StepFormWizardSessionAction {
  id                      String                @id @default(cuid())
  createdAt               DateTime              @default(now())
  stepFormWizardSessionId String
  stepFormWizardSession   StepFormWizardSession @relation(fields: [stepFormWizardSessionId], references: [id], onDelete: Cascade)
  type                    String
  name                    String
  value                   String
}

model StepFormWizardSessionFilterMatch {
  id                      String                @id @default(cuid())
  stepFormWizardFilterId  String
  stepFormWizardFilter    StepFormWizardFilter  @relation(fields: [stepFormWizardFilterId], references: [id], onDelete: Cascade)
  stepFormWizardSessionId String
  stepFormWizardSession   StepFormWizardSession @relation(fields: [stepFormWizardSessionId], references: [id], onDelete: Cascade)
}

model StepFormWizardSessionStep {
  id                      String                @id @default(cuid())
  stepFormWizardSessionId String
  stepFormWizardSession   StepFormWizardSession @relation(fields: [stepFormWizardSessionId], references: [id], onDelete: Cascade)
  stepId                  String
  step                    StepFormWizardStep    @relation(fields: [stepId], references: [id], onDelete: Cascade)
  seenAt                  DateTime?
  completedAt             DateTime?
  status                  LogicAppStatus        @default(PENDING)
  webHookTriggeredAt      DateTime?
  error                   String[]
}

enum LogicAppStatus {
  PENDING
  SUCCESS
  FAILED
}
