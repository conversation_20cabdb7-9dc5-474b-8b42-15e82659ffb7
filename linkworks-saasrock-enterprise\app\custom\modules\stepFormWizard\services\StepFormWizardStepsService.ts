import { TFunction } from "i18next";
import { StepFormWizardBlockDto } from "../blocks/StepFormWizardBlockUtils";
import { StepFormWizardWithDetails, updateStepFormWizard } from "../db/stepFormWizard.db.server";
import { deleteStepFormWizardSessions } from "../db/stepFormWizardSessions.db.server";

async function setSteps({ item, form, t }: { item: StepFormWizardWithDetails; form: FormData; t: TFunction }) {
  const block: StepFormWizardBlockDto = JSON.parse(form.get("block")?.toString() ?? "{}");
  if (!block) {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
  await updateStepFormWizard(item.id, {
    type: block.style as "modal" | "page",
    height: block.height,
    progressBar: block.progressBar,
    entity: block.entity,
    canBeDismissed: block.canBeDismissed,
    steps: block.steps.map((f, idx) => {
      return { order: idx + 1, block: JSON.stringify(f) };
    }),
  });
  await deleteStepFormWizardSessions(item.id);
}
export default {
  setSteps,
};
