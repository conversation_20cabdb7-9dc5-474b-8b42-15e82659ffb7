import { Entity } from "@prisma/client";
import { ActionFunction, LoaderFunctionArgs, redirect, useLoaderData } from "react-router";
import Constants from "~/application/Constants";
import EntityForm from "~/components/entities/EntityForm";
import { getTranslations } from "~/locale/i18next.server";
import { updateEntity, getEntityBySlug, getEntityById } from "~/utils/db/entities/entities.db.server";
import { createEntityPermissions, deleteEntityPermissions } from "~/utils/db/permissions/permissions.db.server";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";

type LoaderData = {
  item: Entity;
};
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.entities.view");
  const item = await getEntityBySlug({ tenantId: null, slug: params.entity ?? "" });
  if (!item) {
    return redirect("/admin/entities");
  }

  const data: LoaderData = {
    item,
  };
  return data;
};

export const action: ActionFunction = async ({ request, params }) => {
  await verifyUserHasPermission(request, "admin.entities.update");
  const { t } = await getTranslations(request);

  const item = await getEntityBySlug({ tenantId: null, slug: params.entity ?? "" });
  if (!item) {
    return redirect("/admin/entities");
  }

  const form = await request.formData();
  const action = form.get("action")?.toString() ?? "";
  if (action === "edit") {
    const name = form.get("name")?.toString() ?? "";
    const slug = form.get("slug")?.toString().toLowerCase() ?? "";
    const order = Number(form.get("order"));
    const prefix = form.get("prefix")?.toString() ?? "";
    const title = form.get("title")?.toString() ?? "";
    const titlePlural = form.get("title-plural")?.toString() ?? "";
    const isAutogenerated = Boolean(form.get("is-autogenerated"));
    const isStepFormWizard = Boolean(form.get("is-stepFormWizard"));
    const hasApi = Boolean(form.get("has-api"));
    const icon = form.get("icon")?.toString() ?? "";
    const active = Boolean(form.get("active"));
    const type = form.get("type")?.toString() ?? "";

    const showInSidebar = Boolean(form.get("show-in-sidebar"));
    const hasTags = Boolean(form.get("has-tags"));
    const hasComments = Boolean(form.get("has-comments"));
    const hasTasks = Boolean(form.get("has-tasks"));
    const hasActivity = Boolean(form.get("has-activity"));
    const hasBulkDelete = Boolean(form.get("has-bulk-delete"));
    const hasViews = Boolean(form.get("has-views"));

    const defaultVisibility = form.get("default-visibility")?.toString() ?? Constants.DEFAULT_ROW_VISIBILITY;

    const onCreated = form.get("onCreated")?.toString() ?? "redirectToOverview";
    const onEdit = form.get("onEdit")?.toString() ?? "editRoute";

    const errors = await EntitiesApi.validateEntity({ tenantId: null, name, slug, order, prefix, entity: item });
    if (errors.length > 0) {
      return Response.json({ error: errors.join(", ") }, { status: 400 });
    }

    try {
      await updateEntity(item.id ?? "", {
        name,
        slug,
        order,
        prefix,
        title,
        titlePlural,
        isAutogenerated,
        isStepFormWizard,
        hasApi,
        icon,
        active,
        type,
        showInSidebar,
        hasTags,
        hasComments,
        hasTasks,
        hasActivity,
        hasBulkDelete,
        hasViews,
        defaultVisibility,
        onCreated,
        onEdit,
      });
      await deleteEntityPermissions(item);
      const updatedEntity = await getEntityById({ tenantId: null, id: item.id });
      if (updatedEntity) {
        await createEntityPermissions(updatedEntity);
      }

      return redirect("/admin/entities");
    } catch (error: any) {
      return Response.json({ error: error.message }, { status: 400 });
    }
  } else {
    return Response.json({ error: t("shared.invalidForm") }, { status: 400 });
  }
};

export default function EditEntityIndexRoute() {
  const data = useLoaderData<LoaderData>();
  return <EntityForm item={data.item} />;
}
