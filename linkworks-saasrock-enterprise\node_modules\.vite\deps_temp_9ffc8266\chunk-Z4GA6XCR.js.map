{"version": 3, "sources": ["../../d3-interpolate/src/zoom.js", "../../d3-color/src/define.js", "../../d3-color/src/color.js", "../../d3-color/src/math.js", "../../d3-color/src/lab.js", "../../d3-color/src/cubehelix.js", "../../d3-interpolate/src/basis.js", "../../d3-interpolate/src/basisClosed.js", "../../d3-interpolate/src/constant.js", "../../d3-interpolate/src/color.js", "../../d3-interpolate/src/rgb.js", "../../d3-interpolate/src/numberArray.js", "../../d3-interpolate/src/array.js", "../../d3-interpolate/src/date.js", "../../d3-interpolate/src/number.js", "../../d3-interpolate/src/object.js", "../../d3-interpolate/src/string.js", "../../d3-interpolate/src/value.js", "../../d3-interpolate/src/round.js", "../../d3-interpolate/src/transform/decompose.js", "../../d3-interpolate/src/transform/parse.js", "../../d3-interpolate/src/transform/index.js", "../../d3-interpolate/src/hsl.js", "../../d3-interpolate/src/hcl.js", "../../d3-interpolate/src/cubehelix.js", "../../d3-interpolate/src/piecewise.js"], "sourcesContent": ["var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export const radians = Math.PI / 180;\nexport const degrees = 180 / Math.PI;\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\n// https://observablehq.com/@mbostock/lab-and-rgb\nconst K = 18,\n    Xn = 0.96422,\n    Yn = 1,\n    Zn = 0.82521,\n    t0 = 4 / 29,\n    t1 = 6 / 29,\n    t2 = 3 * t1 * t1,\n    t3 = t1 * t1 * t1;\n\nfunction labConvert(o) {\n  if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);\n  if (o instanceof Hcl) return hcl2lab(o);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = rgb2lrgb(o.r),\n      g = rgb2lrgb(o.g),\n      b = rgb2lrgb(o.b),\n      y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;\n  if (r === g && g === b) x = z = y; else {\n    x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);\n    z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);\n  }\n  return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);\n}\n\nexport function gray(l, opacity) {\n  return new Lab(l, 0, 0, opacity == null ? 1 : opacity);\n}\n\nexport default function lab(l, a, b, opacity) {\n  return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);\n}\n\nexport function Lab(l, a, b, opacity) {\n  this.l = +l;\n  this.a = +a;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Lab, lab, extend(Color, {\n  brighter(k) {\n    return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  darker(k) {\n    return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  rgb() {\n    var y = (this.l + 16) / 116,\n        x = isNaN(this.a) ? y : y + this.a / 500,\n        z = isNaN(this.b) ? y : y - this.b / 200;\n    x = Xn * lab2xyz(x);\n    y = Yn * lab2xyz(y);\n    z = Zn * lab2xyz(z);\n    return new Rgb(\n      lrgb2rgb( 3.1338561 * x - 1.6168667 * y - 0.4906146 * z),\n      lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z),\n      lrgb2rgb( 0.0719453 * x - 0.2289914 * y + 1.4052427 * z),\n      this.opacity\n    );\n  }\n}));\n\nfunction xyz2lab(t) {\n  return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;\n}\n\nfunction lab2xyz(t) {\n  return t > t1 ? t * t * t : t2 * (t - t0);\n}\n\nfunction lrgb2rgb(x) {\n  return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);\n}\n\nfunction rgb2lrgb(x) {\n  return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);\n}\n\nfunction hclConvert(o) {\n  if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);\n  if (!(o instanceof Lab)) o = labConvert(o);\n  if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0 < o.l && o.l < 100 ? 0 : NaN, o.l, o.opacity);\n  var h = Math.atan2(o.b, o.a) * degrees;\n  return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);\n}\n\nexport function lch(l, c, h, opacity) {\n  return arguments.length === 1 ? hclConvert(l) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function hcl(h, c, l, opacity) {\n  return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function Hcl(h, c, l, opacity) {\n  this.h = +h;\n  this.c = +c;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\nfunction hcl2lab(o) {\n  if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);\n  var h = o.h * radians;\n  return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);\n}\n\ndefine(Hcl, hcl, extend(Color, {\n  brighter(k) {\n    return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);\n  },\n  darker(k) {\n    return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);\n  },\n  rgb() {\n    return hcl2lab(this).rgb();\n  }\n}));\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb, darker, brighter} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\nvar A = -0.14861,\n    B = +1.78277,\n    C = -0.29227,\n    D = -0.90649,\n    E = +1.97294,\n    ED = E * D,\n    EB = E * B,\n    BC_DA = B * C - D * A;\n\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n      bl = b - l,\n      k = (E * (g - l) - C * bl) / D,\n      s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)), // NaN if l=0 or l=1\n      h = s ? Math.atan2(k, bl) * degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\n\nexport default function cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\n\nexport function Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Cubehelix, cubehelix, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * radians,\n        l = +this.l,\n        a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n        cosh = Math.cos(h),\n        sinh = Math.sin(h);\n    return new Rgb(\n      255 * (l + a * (A * cosh + B * sinh)),\n      255 * (l + a * (C * cosh + D * sinh)),\n      255 * (l + a * (E * cosh)),\n      this.opacity\n    );\n  }\n}));\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "import {hsl as colorHsl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hsl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hsl(hue);\nexport var hslLong = hsl(color);\n", "import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n", "import {cubehelix as colorCubehelix} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction cubehelix(hue) {\n  return (function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n          s = color(start.s, end.s),\n          l = color(start.l, end.l),\n          opacity = color(start.opacity, end.opacity);\n      return function(t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n\n    return cubehelix;\n  })(1);\n}\n\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);\n", "import {default as value} from \"./value.js\";\n\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0, n = values.length - 1, v = values[0], I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function(t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}\n"], "mappings": ";AAAA,IAAI,WAAW;AAEf,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AAEA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AAEA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI;AAC5C;AAEA,IAAO,eAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAIhD,WAAS,KAAK,IAAI,IAAI;AACpB,QAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,GACA;AAGJ,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,IAAI,KAAK,EAAE,IAAI;AACxB,UAAI,SAAS,GAAG;AACd,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAGK;AACH,UAAI,KAAK,KAAK,KAAK,EAAE,GACjB,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,GACzC,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;AAC7C,WAAK,KAAK,MAAM;AAChB,UAAI,SAAS,GAAG;AACd,YAAI,IAAI,IAAI,GACR,SAAS,KAAK,EAAE,GAChB,IAAI,MAAM,OAAO,OAAO,SAAS,KAAK,MAAM,IAAI,EAAE,IAAI,KAAK,EAAE;AACjE,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,SAAS,KAAK,MAAM,IAAI,EAAE;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAEA,MAAE,WAAW,IAAI,MAAO,MAAM,KAAK;AAEnC,WAAO;AAAA,EACT;AAEA,OAAK,MAAM,SAAS,GAAG;AACrB,QAAI,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACrD,WAAO,QAAQ,IAAI,IAAI,EAAE;AAAA,EAC3B;AAEA,SAAO;AACT,EAAG,KAAK,OAAO,GAAG,CAAC;;;ACtEJ,SAAR,eAAiB,aAAa,SAAS,WAAW;AACvD,cAAY,YAAY,QAAQ,YAAY;AAC5C,YAAU,cAAc;AAC1B;AAEO,SAAS,OAAO,QAAQ,YAAY;AACzC,MAAI,YAAY,OAAO,OAAO,OAAO,SAAS;AAC9C,WAAS,OAAO,WAAY,WAAU,GAAG,IAAI,WAAW,GAAG;AAC3D,SAAO;AACT;;;ACPO,SAAS,QAAQ;AAAC;AAElB,IAAI,SAAS;AACb,IAAI,WAAW,IAAI;AAE1B,IAAI,MAAM;AAAV,IACI,MAAM;AADV,IAEI,MAAM;AAFV,IAGI,QAAQ;AAHZ,IAII,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAJ/D,IAKI,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAL/D,IAMI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AANxE,IAOI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAPxE,IAQI,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAR/D,IASI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAExE,IAAI,QAAQ;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AAEA,eAAO,OAAO,OAAO;AAAA,EACnB,KAAK,UAAU;AACb,WAAO,OAAO,OAAO,IAAI,KAAK,eAAa,MAAM,QAAQ;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,IAAI,EAAE,YAAY;AAAA,EAChC;AAAA,EACA,KAAK;AAAA;AAAA,EACL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AAED,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,IAAI,EAAE,WAAW;AAC/B;AAEA,SAAS,kBAAkB;AACzB,SAAO,WAAW,IAAI,EAAE,UAAU;AACpC;AAEA,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AAEe,SAAR,MAAuB,QAAQ;AACpC,MAAI,GAAG;AACP,YAAU,SAAS,IAAI,KAAK,EAAE,YAAY;AAC1C,UAAQ,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,IACtF,MAAM,IAAI,IAAI,IAAK,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,IAAI,MAAS,IAAI,OAAQ,IAAM,IAAI,IAAM,CAAC,IAChH,MAAM,IAAI,KAAK,KAAK,KAAK,KAAM,KAAK,KAAK,KAAM,KAAK,IAAI,MAAO,IAAI,OAAQ,GAAI,IAC/E,MAAM,IAAI,KAAM,KAAK,KAAK,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,IAAI,OAAU,IAAI,OAAQ,IAAM,IAAI,MAAQ,GAAI,IACtJ,SACC,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAC5D,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,CAAC,KAChG,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAC7D,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC,KACjG,IAAI,aAAa,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,CAAC,KACrE,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,IAC1E,MAAM,eAAe,MAAM,IAAI,KAAK,MAAM,MAAM,CAAC,IACjD,WAAW,gBAAgB,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,IACnD;AACR;AAEA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,KAAM,KAAK,IAAI,KAAM,IAAI,KAAM,CAAC;AAC3D;AAEA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG;AACxB,MAAI,KAAK,EAAG,KAAI,IAAI,IAAI;AACxB,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAC3B;AAEO,SAAS,WAAW,GAAG;AAC5B,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI;AACnB,MAAI,EAAE,IAAI;AACV,SAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AACzC;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,QAAI,KAAK,OAAO,WAAW,KAAK,IAAI,UAAU,CAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,MAAM;AACJ,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,WAAQ,QAAQ,KAAK,KAAK,KAAK,IAAI,UAC3B,QAAQ,KAAK,KAAK,KAAK,IAAI,WAC3B,QAAQ,KAAK,KAAK,KAAK,IAAI,WAC3B,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,KAAK;AAAA;AAAA,EACL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AACZ,CAAC,CAAC;AAEF,SAAS,gBAAgB;AACvB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACpD;AAEA,SAAS,iBAAiB;AACxB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,WAAW,GAAG,CAAC;AAC1G;AAEA,SAAS,gBAAgB;AACvB,QAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,SAAO,GAAG,MAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG;AACzH;AAEA,SAAS,OAAO,SAAS;AACvB,SAAO,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,CAAC;AAC9D;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC1D;AAEA,SAAS,IAAI,OAAO;AAClB,UAAQ,OAAO,KAAK;AACpB,UAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,EAAE;AACpD;AAEA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG;AACxB,MAAI,KAAK,EAAG,KAAI,IAAI,IAAI;AAAA,WACf,KAAK,KAAK,KAAK,EAAG,KAAI,IAAI;AAAA,WAC1B,KAAK,EAAG,KAAI;AACrB,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAC3B;AAEO,SAAS,WAAW,GAAG;AAC5B,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI;AACnB,MAAI,aAAa,IAAK,QAAO;AAC7B,MAAI,EAAE,IAAI;AACV,MAAI,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,IAAI,KACJ,IAAI,MAAM,KACV,KAAK,MAAM,OAAO;AACtB,MAAI,GAAG;AACL,QAAI,MAAM,IAAK,MAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,aAClC,MAAM,IAAK,MAAK,IAAI,KAAK,IAAI;AAAA,QACjC,MAAK,IAAI,KAAK,IAAI;AACvB,SAAK,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;AACrC,SAAK;AAAA,EACP,OAAO;AACL,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,EAC3B;AACA,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO;AACnC;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEA,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AAC7B,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,QAAI,KAAK,OAAO,WAAW,KAAK,IAAI,UAAU,CAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,MAAM;AACJ,QAAI,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAClC,IAAI,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,GACzC,IAAI,KAAK,GACT,KAAK,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,GACjC,KAAK,IAAI,IAAI;AACjB,WAAO,IAAI;AAAA,MACT,QAAQ,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC5C,QAAQ,GAAG,IAAI,EAAE;AAAA,MACjB,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC3C,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,YAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC,OAC1C,KAAK,KAAK,KAAK,KAAK,KAAK,OACzB,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,YAAY;AACV,UAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,WAAO,GAAG,MAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG;AAAA,EACvI;AACF,CAAC,CAAC;AAEF,SAAS,OAAO,OAAO;AACrB,WAAS,SAAS,KAAK;AACvB,SAAO,QAAQ,IAAI,QAAQ,MAAM;AACnC;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAC5C;AAGA,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC1B,UAAQ,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAChC,IAAI,MAAM,KACV,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,KACvC,MAAM;AACd;;;AC3YO,IAAM,UAAU,KAAK,KAAK;AAC1B,IAAM,UAAU,MAAM,KAAK;;;ACIlC,IAAM,IAAI;AAAV,IACI,KAAK;AADT,IAEI,KAAK;AAFT,IAGI,KAAK;AAHT,IAII,KAAK,IAAI;AAJb,IAKI,KAAK,IAAI;AALb,IAMI,KAAK,IAAI,KAAK;AANlB,IAOI,KAAK,KAAK,KAAK;AAEnB,SAAS,WAAW,GAAG;AACrB,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,aAAa,IAAK,QAAO,QAAQ,CAAC;AACtC,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,IAAI,SAAS,EAAE,CAAC,GAChB,IAAI,SAAS,EAAE,CAAC,GAChB,IAAI,SAAS,EAAE,CAAC,GAChB,IAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE,GAAG,GAAG;AAC1E,MAAI,MAAM,KAAK,MAAM,EAAG,KAAI,IAAI;AAAA,OAAQ;AACtC,QAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE;AAChE,QAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE;AAAA,EAClE;AACA,SAAO,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,OAAO;AACtE;AAMe,SAAR,IAAqB,GAAG,GAAG,GAAG,SAAS;AAC5C,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,WAAO,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,OAAO,GAAG;AACR,WAAO,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM;AACJ,QAAI,KAAK,KAAK,IAAI,MAAM,KACpB,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,KACrC,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI;AACzC,QAAI,KAAK,QAAQ,CAAC;AAClB,QAAI,KAAK,QAAQ,CAAC;AAClB,QAAI,KAAK,QAAQ,CAAC;AAClB,WAAO,IAAI;AAAA,MACT,SAAU,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC;AAAA,MACvD,SAAS,aAAa,IAAI,YAAY,IAAI,WAAY,CAAC;AAAA,MACvD,SAAU,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC;AAAA,MACvD,KAAK;AAAA,IACP;AAAA,EACF;AACF,CAAC,CAAC;AAEF,SAAS,QAAQ,GAAG;AAClB,SAAO,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK;AAChD;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI;AACxC;AAEA,SAAS,SAAS,GAAG;AACnB,SAAO,OAAO,KAAK,WAAY,QAAQ,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAC5E;AAEA,SAAS,SAAS,GAAG;AACnB,UAAQ,KAAK,QAAQ,UAAU,IAAI,QAAQ,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG;AAC9E;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAG,QAAO,IAAI,IAAI,KAAK,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,IAAI,KAAK,EAAE,GAAG,EAAE,OAAO;AAC9F,MAAI,IAAI,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI;AAC/B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,GAAG,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO;AACtF;AAMO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,MAAM,EAAE,CAAC,EAAG,QAAO,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,OAAO;AACnD,MAAI,IAAI,EAAE,IAAI;AACd,SAAO,IAAI,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO;AACrE;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,OAAO,GAAG;AACR,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM;AACJ,WAAO,QAAQ,IAAI,EAAE,IAAI;AAAA,EAC3B;AACF,CAAC,CAAC;;;ACtHF,IAAI,IAAI;AAAR,IACI,IAAI;AADR,IAEI,IAAI;AAFR,IAGI,IAAI;AAHR,IAII,IAAI;AAJR,IAKI,KAAK,IAAI;AALb,IAMI,KAAK,IAAI;AANb,IAOI,QAAQ,IAAI,IAAI,IAAI;AAExB,SAAS,iBAAiB,GAAG;AAC3B,MAAI,aAAa,UAAW,QAAO,IAAI,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AACzE,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,KAClD,KAAK,IAAI,GACT,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,GAC7B,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,IAAI,KAC/C,IAAI,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI,UAAU,MAAM;AAChD,SAAO,IAAI,UAAU,IAAI,IAAI,IAAI,MAAM,GAAG,GAAG,GAAG,EAAE,OAAO;AAC3D;AAEe,SAAR,UAA2B,GAAG,GAAG,GAAG,SAAS;AAClD,SAAO,UAAU,WAAW,IAAI,iBAAiB,CAAC,IAAI,IAAI,UAAU,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAC5G;AAEO,SAAS,UAAU,GAAG,GAAG,GAAG,SAAS;AAC1C,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,WAAW,WAAW,OAAO,OAAO;AAAA,EACzC,SAAS,GAAG;AACV,QAAI,KAAK,OAAO,WAAW,KAAK,IAAI,UAAU,CAAC;AAC/C,WAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EAC/D;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAC3C,WAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EAC/D;AAAA,EACA,MAAM;AACJ,QAAI,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,OAAO,SACzC,IAAI,CAAC,KAAK,GACV,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAC1CA,QAAO,KAAK,IAAI,CAAC,GACjBC,QAAO,KAAK,IAAI,CAAC;AACrB,WAAO,IAAI;AAAA,MACT,OAAO,IAAI,KAAK,IAAID,QAAO,IAAIC;AAAA,MAC/B,OAAO,IAAI,KAAK,IAAID,QAAO,IAAIC;AAAA,MAC/B,OAAO,IAAI,KAAK,IAAID;AAAA,MACpB,KAAK;AAAA,IACP;AAAA,EACF;AACF,CAAC,CAAC;;;AC5DK,SAAS,MAAME,KAAI,IAAI,IAAI,IAAI,IAAI;AACxC,MAAIC,MAAKD,MAAKA,KAAIE,MAAKD,MAAKD;AAC5B,WAAS,IAAI,IAAIA,MAAK,IAAIC,MAAKC,OAAM,MAC9B,IAAI,IAAID,MAAK,IAAIC,OAAM,MACvB,IAAI,IAAIF,MAAK,IAAIC,MAAK,IAAIC,OAAM,KACjCA,MAAK,MAAM;AACnB;AAEe,SAAR,cAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO,SAAS;AACxB,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,IAAK,IAAI,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,GACjE,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK,IACtC,KAAK,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK;AAC9C,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;AChBe,SAAR,oBAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO;AACf,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,GAC3C,KAAK,QAAQ,IAAI,IAAI,KAAK,CAAC,GAC3B,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,QAAQ,IAAI,KAAK,CAAC,GACvB,KAAK,QAAQ,IAAI,KAAK,CAAC;AAC3B,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;ACZA,IAAO,mBAAQ,OAAK,MAAM;;;ACE1B,SAAS,OAAO,GAAG,GAAG;AACpB,SAAO,SAAS,GAAG;AACjB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAEA,SAAS,YAAY,GAAG,GAAG,GAAG;AAC5B,SAAO,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,SAAS,GAAG;AACxE,WAAO,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AACF;AAEO,SAAS,IAAI,GAAG,GAAG;AACxB,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,MAAM,KAAK,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AAC3G;AAEO,SAAS,MAAM,GAAG;AACvB,UAAQ,IAAI,CAAC,OAAO,IAAI,UAAU,SAAS,GAAG,GAAG;AAC/C,WAAO,IAAI,IAAI,YAAY,GAAG,GAAG,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,EACjE;AACF;AAEe,SAAR,QAAyB,GAAG,GAAG;AACpC,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,OAAO,GAAG,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AACrD;;;ACvBA,IAAO,cAAS,SAAS,SAAS,GAAG;AACnC,MAAIC,SAAQ,MAAM,CAAC;AAEnB,WAASC,KAAI,OAAO,KAAK;AACvB,QAAI,IAAID,QAAO,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC9D,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAQ,MAAM,SAAS,IAAI,OAAO;AAChD,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,EAAAC,KAAI,QAAQ;AAEZ,SAAOA;AACT,EAAG,CAAC;AAEJ,SAAS,UAAU,QAAQ;AACzB,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,OAAO,QACX,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,GAAGD;AACP,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,MAAAA,SAAQ,IAAS,OAAO,CAAC,CAAC;AAC1B,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAAA,IACpB;AACA,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,IAAAA,OAAM,UAAU;AAChB,WAAO,SAAS,GAAG;AACjB,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,aAAOA,SAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEO,IAAI,WAAW,UAAU,aAAK;AAC9B,IAAI,iBAAiB,UAAU,mBAAW;;;ACtDlC,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAI,CAAC,EAAG,KAAI,CAAC;AACb,MAAI,IAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,GACvC,IAAI,EAAE,MAAM,GACZ;AACJ,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,IAAI;AACvD,WAAO;AAAA,EACT;AACF;AAEO,SAAS,cAAc,GAAG;AAC/B,SAAO,YAAY,OAAO,CAAC,KAAK,EAAE,aAAa;AACjD;;;ACNO,SAAS,aAAa,GAAG,GAAG;AACjC,MAAI,KAAK,IAAI,EAAE,SAAS,GACpB,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,MAAM,IAAI,GAClC,IAAI,IAAI,MAAM,EAAE,GAChB,IAAI,IAAI,MAAM,EAAE,GAChB;AAEJ,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,cAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChD,SAAO,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAE9B,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACtC,WAAO;AAAA,EACT;AACF;;;ACrBe,SAAR,aAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,oBAAI;AACZ,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,EAAE,QAAQ,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,EACzC;AACF;;;ACLe,SAAR,eAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3B;AACF;;;ACFe,SAAR,eAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,CAAC,GACL,IAAI,CAAC,GACL;AAEJ,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,KAAI,CAAC;AAC9C,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,KAAI,CAAC;AAE9C,OAAK,KAAK,GAAG;AACX,QAAI,KAAK,GAAG;AACV,QAAE,CAAC,IAAI,cAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB,OAAO;AACL,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACZ;AAAA,EACF;AAEA,SAAO,SAAS,GAAG;AACjB,SAAK,KAAK,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC1B,WAAO;AAAA,EACT;AACF;;;ACpBA,IAAI,MAAM;AAAV,IACI,MAAM,IAAI,OAAO,IAAI,QAAQ,GAAG;AAEpC,SAAS,KAAK,GAAG;AACf,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,IAAI,GAAG;AACd,SAAO,SAAS,GAAG;AACjB,WAAO,EAAE,CAAC,IAAI;AAAA,EAChB;AACF;AAEe,SAAR,eAAiB,GAAG,GAAG;AAC5B,MAAI,KAAK,IAAI,YAAY,IAAI,YAAY,GACrC,IACA,IACA,IACA,IAAI,IACJ,IAAI,CAAC,GACL,IAAI,CAAC;AAGT,MAAI,IAAI,IAAI,IAAI,IAAI;AAGpB,UAAQ,KAAK,IAAI,KAAK,CAAC,OACf,KAAK,IAAI,KAAK,CAAC,IAAI;AACzB,SAAK,KAAK,GAAG,SAAS,IAAI;AACxB,WAAK,EAAE,MAAM,IAAI,EAAE;AACnB,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB;AACA,SAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI;AACjC,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB,OAAO;AACL,QAAE,EAAE,CAAC,IAAI;AACT,QAAE,KAAK,EAAC,GAAM,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IAClC;AACA,SAAK,IAAI;AAAA,EACX;AAGA,MAAI,KAAK,EAAE,QAAQ;AACjB,SAAK,EAAE,MAAM,EAAE;AACf,QAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,QACb,GAAE,EAAE,CAAC,IAAI;AAAA,EAChB;AAIA,SAAO,EAAE,SAAS,IAAK,EAAE,CAAC,IACpB,IAAI,EAAE,CAAC,EAAE,CAAC,IACV,KAAK,CAAC,KACL,IAAI,EAAE,QAAQ,SAAS,GAAG;AACzB,aAASE,KAAI,GAAG,GAAGA,KAAI,GAAG,EAAEA,GAAG,IAAG,IAAI,EAAEA,EAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACtD,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB;AACR;;;ACrDe,SAAR,cAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,OAAO,GAAG;AAClB,SAAO,KAAK,QAAQ,MAAM,YAAY,iBAAS,CAAC,KACzC,MAAM,WAAW,iBAClB,MAAM,YAAa,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,eAAO,iBAClD,aAAa,QAAQ,cACrB,aAAa,OAAO,eACpB,cAAc,CAAC,IAAI,sBACnB,MAAM,QAAQ,CAAC,IAAI,eACnB,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,aAAa,cAAc,MAAM,CAAC,IAAI,iBAClF,gBAAQ,GAAG,CAAC;AACpB;;;ACrBe,SAAR,cAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EACvC;AACF;;;ACJA,IAAIC,WAAU,MAAM,KAAK;AAElB,IAAI,WAAW;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AAEe,SAAR,kBAAiB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK;AACzD,MAAI,QAAQ,IAAI,IAAI,IAAI,EAAG,MAAK,IAAI,OAAO,KAAK,IAAI;AACpD,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK,QAAQ,SAAS;AAC1E,MAAI,IAAI,IAAI,IAAI,EAAG,KAAI,CAAC,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC;AAC7D,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ,KAAK,MAAM,GAAG,CAAC,IAAIA;AAAA,IAC3B,OAAO,KAAK,KAAK,KAAK,IAAIA;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACF;;;ACvBA,IAAI;AAGG,SAAS,SAAS,OAAO;AAC9B,QAAM,IAAI,KAAK,OAAO,cAAc,aAAa,YAAY,iBAAiB,QAAQ,EAAE;AACxF,SAAO,EAAE,aAAa,WAAW,kBAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzE;AAEO,SAAS,SAAS,OAAO;AAC9B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,CAAC,QAAS,WAAU,SAAS,gBAAgB,8BAA8B,GAAG;AAClF,UAAQ,aAAa,aAAa,KAAK;AACvC,MAAI,EAAE,QAAQ,QAAQ,UAAU,QAAQ,YAAY,GAAI,QAAO;AAC/D,UAAQ,MAAM;AACd,SAAO,kBAAU,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvE;;;ACdA,SAAS,qBAAqB,OAAO,SAAS,SAAS,UAAU;AAE/D,WAAS,IAAI,GAAG;AACd,WAAO,EAAE,SAAS,EAAE,IAAI,IAAI,MAAM;AAAA,EACpC;AAEA,WAAS,UAAU,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACvC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,cAAc,MAAM,SAAS,MAAM,OAAO;AACzD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,MAAM,IAAI;AACnB,QAAE,KAAK,eAAe,KAAK,UAAU,KAAK,OAAO;AAAA,IACnD;AAAA,EACF;AAEA,WAAS,OAAO,GAAG,GAAG,GAAG,GAAG;AAC1B,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,IAAI,IAAK,MAAK;AAAA,eAAc,IAAI,IAAI,IAAK,MAAK;AACtD,QAAE,KAAK,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,WAAW,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAO,GAAG,CAAC,EAAC,CAAC;AAAA,IAC7E,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,YAAY,IAAI,QAAQ;AAAA,IAC1C;AAAA,EACF;AAEA,WAAS,MAAM,GAAG,GAAG,GAAG,GAAG;AACzB,QAAI,MAAM,GAAG;AACX,QAAE,KAAK,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAO,GAAG,CAAC,EAAC,CAAC;AAAA,IAC5E,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,IAAI,QAAQ;AAAA,IACzC;AAAA,EACF;AAEA,WAAS,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACnC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AACtD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG;AAAA,IAChD;AAAA,EACF;AAEA,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,CAAC,GACL,IAAI,CAAC;AACT,QAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AACzB,cAAU,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,GAAG,CAAC;AACtE,WAAO,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAC/B,UAAM,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC;AAC5B,UAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAClD,QAAI,IAAI;AACR,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI,IAAI,EAAE,QAAQ;AAC1B,aAAO,EAAE,IAAI,EAAG,IAAG,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACvC,aAAO,EAAE,KAAK,EAAE;AAAA,IAClB;AAAA,EACF;AACF;AAEO,IAAI,0BAA0B,qBAAqB,UAAU,QAAQ,OAAO,MAAM;AAClF,IAAI,0BAA0B,qBAAqB,UAAU,MAAM,KAAK,GAAG;;;AC3DlF,SAASC,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5D,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQD,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjB9B,SAASE,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5D,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQD,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjB9B,SAASE,WAAUC,MAAK;AACtB,SAAQ,SAAS,eAAe,GAAG;AACjC,QAAI,CAAC;AAEL,aAASD,WAAU,OAAO,KAAK;AAC7B,UAAI,IAAIC,MAAK,QAAQ,UAAe,KAAK,GAAG,IAAI,MAAM,UAAe,GAAG,GAAG,CAAC,GACxE,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,aAAO,SAAS,GAAG;AACjB,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,EAAE,KAAK,IAAI,GAAG,CAAC,CAAC;AAC1B,cAAM,UAAU,QAAQ,CAAC;AACzB,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAEA,IAAAD,WAAU,QAAQ;AAElB,WAAOA;AAAA,EACT,EAAG,CAAC;AACN;AAEA,IAAO,oBAAQA,WAAU,GAAG;AACrB,IAAI,gBAAgBA,WAAU,OAAK;;;AC1B3B,SAAR,UAA2B,aAAa,QAAQ;AACrD,MAAI,WAAW,OAAW,UAAS,aAAa,cAAc;AAC9D,MAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAC5E,SAAO,IAAI,EAAG,GAAE,CAAC,IAAI,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC;AACnD,SAAO,SAAS,GAAG;AACjB,QAAIE,KAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AACvD,WAAO,EAAEA,EAAC,EAAE,IAAIA,EAAC;AAAA,EACnB;AACF;", "names": ["cosh", "sinh", "t1", "t2", "t3", "color", "rgb", "i", "degrees", "hsl", "hue", "hcl", "hue", "cubehelix", "hue", "i"]}