{"version": 3, "sources": ["../../@tiptap/extension-horizontal-rule/src/horizontal-rule.ts"], "sourcesContent": ["import {\n  canInsertNode, isNodeSelection, mergeAttributes, Node, nodeInputRule,\n} from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () => ({ chain, state }) => {\n          // Check if we can insert the node at the current selection\n          if (!canInsertNode(state, state.schema.nodes[this.name])) {\n            return false\n          }\n\n          const { selection } = state\n          const { $from: $originFrom, $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if ($originFrom.parentOffset === 0) {\n            currentChain.insertContentAt(\n              {\n                from: Math.max($originFrom.pos - 1, 0),\n                to: $originTo.pos,\n              },\n              {\n                type: this.name,\n              },\n            )\n          } else if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;AA8Ba,IAAA,iBAAiB,KAAK,OAA8B;EAC/D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,OAAO;EAEP,YAAS;AACP,WAAO,CAAC,EAAE,KAAK,KAAI,CAAE;;EAGvB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG5E,cAAW;AACT,WAAO;MACL,mBACE,MAAM,CAAC,EAAE,OAAO,MAAK,MAAM;AAEzB,YAAI,CAAC,cAAc,OAAO,MAAM,OAAO,MAAM,KAAK,IAAI,CAAC,GAAG;AACxD,iBAAO;;AAGT,cAAM,EAAE,UAAS,IAAK;AACtB,cAAM,EAAE,OAAO,aAAa,KAAK,UAAS,IAAK;AAE/C,cAAM,eAAe,MAAK;AAE1B,YAAI,YAAY,iBAAiB,GAAG;AAClC,uBAAa,gBACX;YACE,MAAM,KAAK,IAAI,YAAY,MAAM,GAAG,CAAC;YACrC,IAAI,UAAU;aAEhB;YACE,MAAM,KAAK;UACZ,CAAA;mBAEM,gBAAgB,SAAS,GAAG;AACrC,uBAAa,gBAAgB,UAAU,KAAK;YAC1C,MAAM,KAAK;UACZ,CAAA;eACI;AACL,uBAAa,cAAc,EAAE,MAAM,KAAK,KAAI,CAAE;;AAGhD,eACE,aAEG,QAAQ,CAAC,EAAE,IAAI,SAAQ,MAAM;;AAC5B,cAAI,UAAU;AACZ,kBAAM,EAAE,IAAG,IAAK,GAAG;AACnB,kBAAM,WAAW,IAAI,IAAG;AAExB,gBAAI,IAAI,WAAW;AACjB,kBAAI,IAAI,UAAU,aAAa;AAC7B,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC;yBAChD,IAAI,UAAU,SAAS;AAChC,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;qBAChD;AACL,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;;mBAElD;AAEL,oBAAM,QAAO,KAAA,IAAI,OAAO,KAAK,aAAa,iBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,OAAM;AAE7D,kBAAI,MAAM;AACR,mBAAG,OAAO,UAAU,IAAI;AACxB,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,WAAW,CAAC,CAAC;;;AAI9D,eAAG,eAAc;;AAGnB,iBAAO;QACT,CAAC,EACA,IAAG;;;;EAMhB,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;", "names": []}