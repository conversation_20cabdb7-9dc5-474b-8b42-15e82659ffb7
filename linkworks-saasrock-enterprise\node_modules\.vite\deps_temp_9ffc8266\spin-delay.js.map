{"version": 3, "sources": ["../../spin-delay/src/index.ts"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface SpinDelayOptions {\n  delay?: number;\n  minDuration?: number;\n}\n\ntype State = 'IDLE' | 'DELAY' | 'DISPLAY' | 'EXPIRE';\n\nexport const defaultOptions = {\n  delay: 500,\n  minDuration: 200,\n};\n\nexport function useSpinDelay(\n  loading: boolean,\n  options?: SpinDelayOptions,\n): boolean {\n  options = Object.assign({}, defaultOptions, options);\n\n  const [state, setState] = useState<State>('IDLE');\n  const timeout = useRef(null);\n\n  useEffect(() => {\n    if (loading && state === 'IDLE') {\n      clearTimeout(timeout.current);\n\n      timeout.current = setTimeout(() => {\n        if (!loading) {\n          return setState('IDLE');\n        }\n\n        timeout.current = setTimeout(() => {\n          setState('EXPIRE');\n        }, options.minDuration);\n\n        setState('DISPLAY');\n      }, options.delay);\n\n      setState('DELAY');\n    }\n\n    if (!loading && state !== 'DISPLAY') {\n      clearTimeout(timeout.current);\n      setState('IDLE');\n    }\n  }, [loading, state, options.delay, options.minDuration]);\n\n  useEffect(() => {\n    return () => clearTimeout(timeout.current);\n  }, []);\n\n  return state === 'DISPLAY' || state === 'EXPIRE';\n}\n\nexport default useSpinDelay;\n"], "mappings": ";;;;;;;;;;;;;YASaA,iBAAiB;QAC5BC,OAAO;QACPC,aAAa;MAFe;eAKdC,aACdC,SACAC,SAAAA;AAEAA,kBAAUC,OAAOC,OAAO,CAAA,GAAIP,gBAAgBK,OAAlC;AAEV,cAAM,CAACG,OAAOC,QAAR,IAAoBC,MAAAA,SAAgB,MAAR;AAClC,cAAMC,UAAUC,MAAAA,OAAO,IAAD;AAEtBC,cAAAA,UAAU,MAAA;AACR,cAAIT,WAAWI,UAAU,QAAQ;AAC/BM,yBAAaH,QAAQI,OAAT;AAEZJ,oBAAQI,UAAUC,WAAW,MAAA;AAC3B,kBAAI,CAACZ,SAAS;AACZ,uBAAOK,SAAS,MAAD;cAChB;AAEDE,sBAAQI,UAAUC,WAAW,MAAA;AAC3BP,yBAAS,QAAD;cACT,GAAEJ,QAAQH,WAFiB;AAI5BO,uBAAS,SAAD;YACT,GAAEJ,QAAQJ,KAViB;AAY5BQ,qBAAS,OAAD;UACT;AAED,cAAI,CAACL,WAAWI,UAAU,WAAW;AACnCM,yBAAaH,QAAQI,OAAT;AACZN,qBAAS,MAAD;UACT;QACF,GAAE,CAACL,SAASI,OAAOH,QAAQJ,OAAOI,QAAQH,WAAxC,CAvBM;AAyBTW,cAAAA,UAAU,MAAA;AACR,iBAAO,MAAMC,aAAaH,QAAQI,OAAT;QAC1B,GAAE,CAAA,CAFM;AAIT,eAAOP,UAAU,aAAaA,UAAU;MACzC;;;;;;;", "names": ["defaultOptions", "delay", "minDuration", "useSpinDelay", "loading", "options", "Object", "assign", "state", "setState", "useState", "timeout", "useRef", "useEffect", "clearTimeout", "current", "setTimeout"]}